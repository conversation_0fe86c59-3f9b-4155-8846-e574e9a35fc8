"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5834],{5834:(be,b,l)=>{l.r(b),l.d(b,{DuplicateInvoiceComponent:()=>ge});var y=l(467),r=l(9842),R=l(4433),F=l(5644),P=l(1110),e=l(4438),T=l(4006),S=l(6146),I=l(9079),O=l(3492),k=l(6508),g=l.n(k),$=l(4978),B=l(9248),U=l(1328),L=l(3202),W=l(7086),V=l(8556),X=l(344),f=l(9417),G=l(6463),A=l(1970),K=l(7572),j=l(6473),M=l(33),w=l(5277),Q=l(6617),N=l(1556),x=l(1875),E=l(5909),z=l(97),Y=l(2387),J=l(9211),Z=l(3989),H=l(6757),C=l(177);const q=["selectSearchClientElement"],ee=()=>({required:"Client is required"}),te=()=>({required:"Invoice date is required"}),ie=a=>({"pl-8":a}),ne=()=>({required:"Due date is required"});function oe(a,p){1&a&&(e.j41(0,"p",10),e.EFF(1," Loading... "),e.k0s())}function ae(a,p){if(1&a&&(e.j41(0,"div",11)(1,"p",40),e.EFF(2),e.k0s()()),2&a){const o=e.XpG();e.R7$(2),e.SpI(" ",o.businessInfo.businessPhoneNumber," ")}}function se(a,p){if(1&a&&(e.j41(0,"p",12),e.EFF(1),e.k0s()),2&a){const o=e.XpG();e.R7$(),e.SpI(" ",o.businessInfo.businessAddress," ")}}function le(a,p){if(1&a&&e.nrm(0,"ngx-avatars",42),2&a){const o=e.XpG().$implicit;e.Y8G("size",32)("name",o.label)}}function re(a,p){1&a&&(e.j41(0,"div",43),e.nrm(1,"img",46),e.k0s())}function ce(a,p){if(1&a&&(e.j41(0,"p",45),e.EFF(1),e.k0s()),2&a){const o=e.XpG().$implicit;e.R7$(),e.SpI(" ",o.metadata.description," ")}}function ue(a,p){if(1&a){const o=e.RV6();e.j41(0,"div",41),e.bIt("click",function(){const i=e.eBV(o).$implicit,n=e.XpG();return e.Njj(n.handleSelectProject(i))}),e.DNE(1,le,1,2,"ngx-avatars",42)(2,re,2,0,"div",43),e.j41(3,"div",9)(4,"p",44),e.EFF(5),e.k0s(),e.DNE(6,ce,2,1,"p",45),e.k0s()()}if(2&a){const o=p.$implicit,t=e.XpG();e.AVh("selected",o.value===t.f.projectId.value),e.Y8G("ngClass",e.eq3(6,ie,"project"==(null==o||null==o.metadata?null:o.metadata.type))),e.R7$(),e.vxM("client"==(null==o||null==o.metadata?null:o.metadata.type)?1:2),e.R7$(4),e.SpI(" ",o.label," "),e.R7$(),e.vxM(null!=o.metadata&&o.metadata.description?6:-1)}}function me(a,p){if(1&a&&e.nrm(0,"app-inno-form-datepicker",18),2&a){const o=e.XpG();e.Y8G("formControl",o.f.dueDate)("value",o.f.dueDate.value)("errorMessages",e.lJ4(3,ne))}}function pe(a,p){if(1&a&&(e.j41(0,"p",48),e.EFF(1),e.k0s()),2&a){let o;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(o=null==t||null==t.project?null:t.project.projectName)&&void 0!==o?o:""," ")}}function _e(a,p){if(1&a&&(e.j41(0,"p",48),e.EFF(1),e.k0s()),2&a){let o;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(o=null==t?null:t.projectName)&&void 0!==o?o:""," ")}}function de(a,p){if(1&a&&(e.j41(0,"p",48),e.EFF(1),e.k0s()),2&a){let o;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(o=null==t||null==t.item?null:t.item.itemName)&&void 0!==o?o:""," ")}}function ve(a,p){if(1&a&&e.EFF(0),2&a){let o;const t=e.XpG().$implicit;e.SpI(" ",null!==(o=null==t||null==t.service?null:t.service.serviceName)&&void 0!==o?o:""," - ")}}function fe(a,p){if(1&a){const o=e.RV6();e.j41(0,"p",54),e.bIt("click",function(){e.eBV(o);const i=e.XpG(),n=i.$implicit,s=i.$index,c=e.XpG();return e.Njj(c.handleModifyTaxes(null==n?null:n.taxes,s))}),e.EFF(1),e.k0s()}if(2&a){const o=e.XpG().$implicit,t=e.XpG();e.R7$(),e.SpI(" ",t.getNameSelectedTaxes(null==o?null:o.taxes)," ")}}function he(a,p){if(1&a){const o=e.RV6();e.j41(0,"p",55),e.bIt("click",function(){e.eBV(o);const i=e.XpG(),n=i.$implicit,s=i.$index,c=e.XpG();return e.Njj(c.handleModifyTaxes(null==n?null:n.taxes,s))}),e.EFF(1," + Add Taxes "),e.k0s()}}function Ie(a,p){if(1&a){const o=e.RV6();e.j41(0,"div",27)(1,"div",47),e.DNE(2,pe,2,1,"p",48)(3,_e,2,1,"p",48)(4,de,2,1,"p",48),e.j41(5,"p",49),e.DNE(6,ve,1,1),e.EFF(7),e.nI1(8,"date"),e.k0s(),e.j41(9,"p",49),e.EFF(10),e.k0s()(),e.j41(11,"p",49),e.EFF(12),e.nI1(13,"formatNumber"),e.k0s(),e.j41(14,"p",49),e.EFF(15),e.nI1(16,"decimal"),e.nI1(17,"formatNumber"),e.k0s(),e.DNE(18,fe,2,1,"p",50)(19,he,2,0,"p",51),e.j41(20,"p",52),e.EFF(21),e.nI1(22,"decimal"),e.nI1(23,"formatNumber"),e.k0s(),e.j41(24,"app-inno-table-action",53),e.bIt("onEdit",function(){const i=e.eBV(o),n=i.$implicit,s=i.$index,c=e.XpG();return e.Njj(c.handleModifyInvoiceItem(s,n))})("onDelete",function(){const i=e.eBV(o).$index,n=e.XpG();return e.Njj(n.handleDeleteInvoiceItem(i))}),e.k0s()()}if(2&a){let o,t,i;const n=p.$implicit,s=e.XpG();e.R7$(2),e.vxM(null!=n&&n.project?2:3),e.R7$(2),e.vxM(null!=n&&null!=n.item&&n.item.itemName?4:-1),e.R7$(2),e.vxM(null!=n&&null!=n.service&&n.service.serviceName?6:-1),e.R7$(),e.Lme(" ",s.getFullName(null==n?null:n.user)," - ",e.i5U(8,10,n.dateSelectItem,"MMM, d y")," "),e.R7$(3),e.SpI(" ",null!==(o=null==n?null:n.description)&&void 0!==o?o:""," "),e.R7$(2),e.SpI(" $",e.bMT(13,13,null!==(t=null==n?null:n.rate)&&void 0!==t?t:0)," "),e.R7$(3),e.SpI(" ",e.bMT(17,18,e.i5U(16,15,null!==(i=null==n?null:n.qty)&&void 0!==i?i:0,2))," "),e.R7$(3),e.vxM((null==n?null:n.taxes.length)>0&&""!=s.getNameSelectedTaxes(null==n?null:n.taxes)?18:19),e.R7$(3),e.SpI(" $",e.bMT(23,23,e.i5U(22,20,s.calculateTotalInvoiceItem(null==n?null:n.rate,null==n?null:n.qty),2))," ")}}function xe(a,p){if(1&a&&(e.j41(0,"div",34)(1,"div",56)(2,"p",57),e.EFF(3),e.k0s(),e.j41(4,"p",57),e.EFF(5),e.k0s()(),e.j41(6,"p",33),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&a){const o=p.$implicit;e.R7$(3),e.Lme(" ",o.name," (",o.amount,"%) "),e.R7$(2),e.SpI(" #",o.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,o.total,2))," ")}}function De(a,p){if(1&a){const o=e.RV6();e.j41(0,"div",58)(1,"button",59),e.bIt("click",function(){e.eBV(o);const i=e.XpG();return e.Njj(i.handleSave())}),e.EFF(2," Save "),e.k0s()()}}let ge=(()=>{var a;class p{static getComponent(){return p}constructor(t,i,n,s,c,u,m){(0,r.A)(this,"dialogRef",void 0),(0,r.A)(this,"modifyInvoiceItemDialog",void 0),(0,r.A)(this,"modifyTaxesDialog",void 0),(0,r.A)(this,"addNewItemDialog",void 0),(0,r.A)(this,"selectTimeTrackingDialog",void 0),(0,r.A)(this,"sendInvoiceDialog",void 0),(0,r.A)(this,"data",void 0),(0,r.A)(this,"invoiceNumber","0000001"),(0,r.A)(this,"inforUser",void 0),(0,r.A)(this,"title",""),(0,r.A)(this,"invoiceForm",void 0),(0,r.A)(this,"itemInvoice2",[]),(0,r.A)(this,"calculateTotalInvoiceItem",E.R2),(0,r.A)(this,"getNameSelectedTaxes",E.Xj),(0,r.A)(this,"projectAndClientOptions",[]),(0,r.A)(this,"subtotal",0),(0,r.A)(this,"totalAmount",0),(0,r.A)(this,"selectedDateStart",this.formatDate(new Date)),(0,r.A)(this,"selectedDateEnd",this.formatDate(new Date)),(0,r.A)(this,"taxArray",[]),(0,r.A)(this,"InforInvoice",void 0),(0,r.A)(this,"formBuilder",(0,e.WQX)(f.ze)),(0,r.A)(this,"base64",void 0),(0,r.A)(this,"filename",void 0),(0,r.A)(this,"payment",[]),(0,r.A)(this,"sumtax",0),(0,r.A)(this,"listTax",[]),(0,r.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,r.A)(this,"router",(0,e.WQX)(M.Ix)),(0,r.A)(this,"_toastService",(0,e.WQX)(O.f)),(0,r.A)(this,"_storeService",(0,e.WQX)(P.n)),(0,r.A)(this,"_invoiceService",(0,e.WQX)(F.p)),(0,r.A)(this,"dataService",(0,e.WQX)(w.u)),(0,r.A)(this,"dropdownOptionService",(0,e.WQX)(G.R)),(0,r.A)(this,"layoutUtilsService",(0,e.WQX)(N.Z)),(0,r.A)(this,"selectSearchClientElement",void 0),this.dialogRef=t,this.modifyInvoiceItemDialog=i,this.modifyTaxesDialog=n,this.addNewItemDialog=s,this.selectTimeTrackingDialog=c,this.sendInvoiceDialog=u,this.data=m,this.title=this.data.isInvoice?"Duplicate Invoice":"Duplicate Estimate",this.invoiceForm=this.formBuilder.group({clientId:["",f.k0.compose([f.k0.required])],invoiceDate:[null,f.k0.compose([f.k0.required])],dueDate:[null,f.k0.compose([f.k0.required])],invoiceNumber:[],projectId:[],notes:[""],itemInvoice:[]}),this.invoiceForm.get("itemInvoice")?.valueChanges.subscribe(_=>{this.subtotal=0,_?.forEach(v=>{const d=(0,E.R2)(v?.rate,v?.qty);this.subtotal=Math.floor(100*(this.subtotal+d))/100}),this.calculateAllTax()}),this.invoiceForm.get("invoiceNumber")?.disable(),this.inforUser=this._storeService.get_InforUser()}handleSelectProject(t){var i=this;return(0,y.A)(function*(){let n="",s="";"client"==t.metadata?.type?(n=t.value,s=""):(n=t.metadata?.objectClient?.id,s=t.value);const u=i.f.clientId.value===n,m=[...i.f.itemInvoice?.value??[]];if(!u&&m.length){if(!(yield i.layoutUtilsService.alertConfirm({title:"Warning",description:"You are changing the client, and the invoices will be reset. Are you sure you want to continue?"})))return;i.f.itemInvoice.setValue([])}i.f.clientId.setValue(n),i.f.projectId.setValue(s),i.selectSearchClientElement.handleCloseSearchResult()})()}_handleData(t){this.f.notes.setValue(t?.notes),this.f.itemInvoice.setValue(t?.itemInvoices),this.f.invoiceDate.setValue(t?.invoiceDate),this.f.dueDate.setValue(this.data.isInvoice?t?.dueDate:new Date),this.f.clientId.setValue(t?.clientId),this.f.projectId.setValue(t?.projectId)}GetInvoiceById(t){this._invoiceService.GetInvoiceById(t).pipe((0,I.pQ)(this.destroyRef)).subscribe(i=>{i&&(this.InforInvoice=i,this._handleData(i),this.calculateAllTax())})}CountInvoiceByCompany(){this._invoiceService.CountInvoiceByCompany().pipe((0,I.pQ)(this.destroyRef)).subscribe(t=>t?0==t?(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber=(t+1).toString().padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)))}ngOnInit(){this.dropdownOptionService.getDropdownOptionsProjectAndClient().then(t=>this.projectAndClientOptions=t),this.GetInvoiceById(this.data?.id),this.data.isInvoice?this.CountInvoiceByCompany():this.CountEstimate()}getFullName(t){return t?t?.firstName&&t?.lastName?t?.firstName+" "+t?.lastName:t?.email??"":this.inforUser?.firstName&&this.inforUser?.lastName?this.inforUser?.firstName+" "+this.inforUser?.lastName:this.inforUser?.email??""}get businessInfo(){const t=this._storeService.get_UserBusiness();return{businessName:t?.company?.businessName??"",businessPhoneNumber:t?.company?.phone??"",businessAddress:(0,j.Aw)({addressLine1:t?.company?.adress??"",addressLine2:t?.company?.adress2??"",stateProvince:t?.company?.province??"",postalCode:t?.company?.postalCode??"",country:t?.company?.country??""})}}get f(){return this.invoiceForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(t=>{t.markAsTouched()})}formatDate(t){return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}convertToHours(t){const[i,n]=t.split(":").map(Number);return i+n/60}calculateAllTax(){this.taxArray=[];let t=0;this.sumtax=0,this.f.itemInvoice.value.forEach(i=>{i.taxes.forEach(n=>{if(n?.companyTax){t+=Math.floor(isNaN(i.rate*i.qty*(n?.companyTax?.amount/100))?0:i.rate*i.qty*(n?.companyTax?.amount/100)*100)/100;const s=Number(Math.floor(100*(isNaN(i.rate*i.qty*(n?.companyTax?.amount/100))?0:i.rate*i.qty*(n?.companyTax?.amount/100)))/100)||0,c=this.taxArray.find(u=>u.name===n?.companyTax?.name);c?(c.total+=s,c.amount=n.companyTax?.amount??0):this.taxArray.push({name:n.companyTax?.name,total:s,numberTax:n.companyTax?.taxeNumber,amount:n.companyTax?.amount})}else if(n.selected){t+=Math.floor(isNaN(i.rate*i.qty*(n?.amount/100))?0:i.rate*i.qty*(n?.amount/100)*100)/100;const s=Math.floor(100*(isNaN(i.rate*i.qty*(n?.amount/100))?0:i.rate*i.qty*(n?.amount/100)))/100,c=this.taxArray.find(u=>u.name===n?.name);c?(c.total+=s,c.amount=n?.amount):this.taxArray.push({name:n?.name,total:s,numberTax:n?.taxeNumber,amount:n?.amount})}})}),this.sumtax+=t,this.totalAmount=this.subtotal+(isNaN(this.sumtax)?0:this.sumtax)}_formatTotal(t){return Math.floor(1e3*t)/1e3}handleDeleteInvoiceItem(t){const i=[...this.f.itemInvoice?.value??[]];i?.length&&(i.splice(t,1),this.f.itemInvoice.setValue(i))}CountEstimate(){this._invoiceService.CountEstimate().pipe((0,I.pQ)(this.destroyRef)).subscribe(t=>t?0==t?(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber=(t+1).toString().padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)))}handleCancel(){this.dialogRef.close()}get getInvoicePayload(){let t;return this.invoiceForm.invalid?null:(t=this.data.isInvoice?{clientId:this.f.clientId.value,invoiceNumber:this.f.invoiceNumber.value,invoiceDate:g().utc(this.f.invoiceDate.value).toDate(),dueDate:g().utc(this.f.dueDate.value).toDate(),reference:"",isEstimate:!this.data.isInvoice,notes:this.f.notes.value,projectId:this.f.projectId.value,payments:this.payment,taxes:this.listTax,base64:this.base64,filename:this.filename,itemInvoices:this.f.itemInvoice.value.map(({user:i,project:n,id:s,service:c,...u})=>({...u,taxes:u.taxes.some(m=>m.companyTax)?u.taxes.map(({companyTax:m,..._})=>_):u.taxes.filter(m=>m.selected)})),paidAmount:this.subtotal,taxAmount:this.sumtax,totalAmount:this.totalAmount,rate:0,status:0,timeAmount:0}:{clientId:this.f.clientId.value,invoiceNumber:this.f.invoiceNumber.value,invoiceDate:g().utc(this.f.invoiceDate.value).toDate(),reference:"",isEstimate:!this.data.isInvoice,notes:this.f.notes.value,projectId:this.f.projectId.value,payments:this.payment,taxes:this.listTax,base64:this.base64,filename:this.filename,itemInvoices:this.f.itemInvoice.value.map(({user:i,project:n,id:s,service:c,...u})=>({...u,taxes:u.taxes.some(m=>m.companyTax)?u.taxes.map(({companyTax:m,..._})=>_):u.taxes.filter(m=>m.selected)})),paidAmount:this.subtotal,taxAmount:this.sumtax,totalAmount:this.totalAmount,rate:0,status:0,timeAmount:0},t)}handleAddUnBillTime(){const t=this.f.clientId.value;if(!t)return void this._toastService.showWarning("No selected client","Please select a client to add the time.");const i=this.projectAndClientOptions.find(m=>m.value===t)?.metadata?.client;if(!i)return this._toastService.showWarning("Not fount client");const n=[...this.f.itemInvoice?.value??[]],s=n.map(m=>m.trackingId).filter(m=>m);this.selectTimeTrackingDialog.open({client:i,listIdTimeTrackingSelected:s}).then(m=>{m.afterClosed().subscribe(_=>{_?.length&&(n.push(..._),this.f.itemInvoice.setValue(n))})})}handleClose(){this.dialogRef.close()}handleSave(){this.invoiceForm.invalid?this.markAllControlsAsTouched():this._invoiceService.CreatedInvoice(this.getInvoicePayload).pipe((0,I.pQ)(this.destroyRef)).subscribe(t=>{t&&(this.dataService.triggerRefreshInvoice(),this.dialogRef.close(t),this._toastService.showSuccess("Save","Success"))})}handleAddNewItem(){const t=this.addNewItemDialog.open(this.getInvoicePayload),i=[...this.f.itemInvoice?.value??[]];t.then(n=>{n.afterClosed().subscribe(s=>{if(!s?.length)return;const c=s.map(({id:u,taxes:m,itemName:_,serviceName:v,projectName:d,...h})=>({...h,description:_,projectName:""==d?null:d??null,serviceName:""==v?null:v??null,user:this.inforUser,taxes:m?.map(({id:Ee,itemId:D,serviceId:ye,...Ce})=>Ce)}));i.push(...c),this.f.itemInvoice.setValue(i)})})}handleSendInvoice(){if(this.invoiceForm.invalid)return this.markAllControlsAsTouched(),void this._toastService.showWarning("Please fill in all the invoice information completely."," ");this.sendInvoiceDialog.open(this.getInvoicePayload).then(i=>{i.afterClosed().subscribe(n=>{n&&(this.dataService.triggerRefreshInvoice(),this.dialogRef.close())})})}handleModifyInvoiceItem(t,i){this.modifyInvoiceItemDialog.open(i).then(s=>{s.afterClosed().subscribe(c=>{if(!c)return;const u=this.f.itemInvoice.value??[];void 0===t?(u.push(c),this.f.itemInvoice.setValue(u)):(u[t]=c,this.f.itemInvoice.setValue(u))})})}handleSelectClient(t){this.f.clientId.setValue(t.value),this.selectSearchClientElement.handleCloseSearchResult()}RouterSetting(){this.dialogRef.close(),this.router.navigate(["/settings/business"])}handleChangePicture(t){var i=this;return(0,y.A)(function*(){const n=t?.[0],{base64:s,fileName:c}=yield(0,j.EX)(n);i.base64=s,i.filename=c})()}handleModifyTaxes(t,i){let n=[];t.forEach(u=>{u.companyTax?(u.companyTax.selected=!0,n.push(u.companyTax)):n.push(u)});const s=structuredClone(this.f.itemInvoice.value);this.modifyTaxesDialog.open(n.filter(u=>u.selected)).then(u=>{u.afterClosed().subscribe(m=>{if(!m)return void this.f.itemInvoice.setValue(s);const _=this.f.itemInvoice.value??[];s.forEach((v,d)=>{i!=d&&(_[d].taxes=v.taxes)}),_[i].taxes=m.taxes.filter(v=>1==v.selected),this.f.itemInvoice.setValue(_),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemInvoice.value.forEach(d=>{d.taxes.length>0?m.taxes.filter(h=>1==h.selected).forEach(h=>{d.taxes.some(D=>D.companyTax?D?.companyTax.name===h?.name:D.name===h.name)||d.taxes.push(h)}):m.taxes.forEach(h=>{d.taxes.push(h)})}),this.calculateAllTax())})})}drop(t){t.previousContainer===t.container?(0,x.HD)(t.container.data,t.previousIndex,t.currentIndex):(0,x.eg)(t.previousContainer.data,t.container.data,t.previousIndex,t.currentIndex)}CheckIsNaN(t){return isNaN(t)?0:t}}return a=p,(0,r.A)(p,"\u0275fac",function(t){return new(t||a)(e.rXU(T.CP),e.rXU(z.r),e.rXU(Y.I),e.rXU(J.z),e.rXU(Z.d),e.rXU(H.o),e.rXU(T.Vh))}),(0,r.A)(p,"\u0275cmp",e.VBU({type:a,selectors:[["app-edit-invoice"]],viewQuery:function(t,i){if(1&t&&e.GBs(q,5),2&t){let n;e.mGM(n=e.lsd())&&(i.selectSearchClientElement=n.first)}},standalone:!0,features:[e.Jv_([N.Z]),e.aNF],decls:82,vars:42,consts:[["selectSearchClientElement",""],["projectOptionTemplate",""],["customSubmitNewInvoice",""],[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"flex","w-full","gap-[18px]","xl:flex-row","flex-col"],[1,"w-[160px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'",3,"onChange"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","text-text-xs-regular","text-text-secondary"],[1,"button-link-primary","mt-[3px]",3,"click"],[1,"w-full","grid","md:grid-cols-2","lg:grid-cols-4","gap-[16px]"],["label","Sent to Client/Project","placeholder","Select client/project",3,"options","formControl","projectId","value","errorMessages","customOptionTemplate"],["type","number","label","Invoice Number","placeholder","Enter invoice number",3,"formControl","value"],["label","Issue Date","placeholder","Select issue date",3,"formControl","value","errorMessages"],["label","Due Date","placeholder","Select due date",3,"formControl","value","errorMessages"],["label","Description","placeholder","A brief description of the invoice details.",3,"formControl"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],[1,"flex","gap-1.5"],[1,"button-outline-primary","button-size-md","mt-[16px]",3,"click"],["type","button",1,"button-outline","button-size-md","mt-[16px]",3,"click"],["cdkDropList","",1,"overflow-auto","w-full",3,"cdkDropListDropped","cdkDropListData"],[1,"invoiceTableLayout"],[1,"text-text-tertiary","text-text-sm-semibold"],["cdkDrag","",1,"invoiceTableLayout"],[1,"mt-[8px]","button-size-md","button-outline-primary","w-full","border-dashed","justify-center",3,"click"],["src","../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"block"],[1,"button-link-primary"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["footer",""],[3,"onCancel","customSubmitButton"],[1,"text-text-xs-regular","text-text-secondary"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"flex","flex-col"],[1,"text-text-primary","text-text-md-semibold"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","cursor-pointer"],[1,"text-blue-500","text-sm","cursor-pointer"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],[1,"text-text-primary","text-text-md-regular","cursor-pointer",3,"click"],[1,"text-blue-500","text-sm","cursor-pointer",3,"click"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"],[1,"flex","items-center","gap-[12px]"],[1,"button-outline-primary","button-size-md",3,"click"]],template:function(t,i){if(1&t){const n=e.RV6();e.j41(0,"app-inno-modal-wrapper",3),e.bIt("onClose",function(){return e.eBV(n),e.Njj(i.handleClose())}),e.j41(1,"div",4)(2,"div",5)(3,"div",6)(4,"app-inno-upload",7),e.bIt("onChange",function(c){return e.eBV(n),e.Njj(i.handleChangePicture(c))}),e.k0s()(),e.j41(5,"div",8)(6,"div",9),e.DNE(7,oe,2,0,"p",10)(8,ae,3,1,"div",11)(9,se,2,1,"p",12),e.j41(10,"button",13),e.bIt("click",function(){return e.eBV(n),e.Njj(i.RouterSetting())}),e.EFF(11," Edit Business Information "),e.k0s()(),e.j41(12,"div",14)(13,"app-inno-form-select-search",15,0),e.DNE(15,ue,7,8,"ng-template",null,1,e.C5r),e.k0s(),e.nrm(17,"app-inno-form-input",16)(18,"app-inno-form-datepicker",17),e.DNE(19,me,1,4,"app-inno-form-datepicker",18),e.k0s(),e.j41(20,"div",9),e.nrm(21,"app-inno-form-textarea",19),e.k0s()()(),e.j41(22,"div",20)(23,"div",21)(24,"button",22),e.bIt("click",function(){return e.eBV(n),e.Njj(i.handleAddUnBillTime())}),e.EFF(25," Add Unbill Time "),e.k0s(),e.j41(26,"button",23),e.bIt("click",function(){return e.eBV(n),e.Njj(i.handleAddNewItem())}),e.EFF(27," + Add New Item "),e.k0s()(),e.j41(28,"div",24),e.bIt("cdkDropListDropped",function(c){return e.eBV(n),e.Njj(i.drop(c))}),e.j41(29,"div",25)(30,"p",26),e.EFF(31," Invoice Item "),e.k0s(),e.j41(32,"p",26),e.EFF(33," Rate "),e.k0s(),e.j41(34,"p",26),e.EFF(35," Quantity "),e.k0s(),e.j41(36,"p",26),e.EFF(37," Tax "),e.k0s(),e.j41(38,"p",26),e.EFF(39," Line Total "),e.k0s()(),e.Z7z(40,Ie,25,25,"div",27,e.fX1),e.k0s(),e.j41(42,"button",28),e.bIt("click",function(){return e.eBV(n),e.Njj(i.handleModifyInvoiceItem())}),e.nrm(43,"img",29),e.EFF(44," Add new line "),e.k0s()(),e.j41(45,"div",30)(46,"div",31)(47,"p",32),e.EFF(48," Subtotal "),e.k0s(),e.j41(49,"p",33),e.EFF(50),e.nI1(51,"decimal"),e.nI1(52,"formatNumber"),e.k0s()(),e.Z7z(53,xe,10,9,"div",34,e.fX1),e.j41(55,"div",31)(56,"p",32),e.EFF(57," Tax "),e.k0s(),e.j41(58,"p",33),e.EFF(59),e.nI1(60,"decimal"),e.nI1(61,"formatNumber"),e.k0s()(),e.j41(62,"div",31)(63,"div",35)(64,"p",32),e.EFF(65," Discount "),e.k0s(),e.j41(66,"button",36),e.EFF(67," Add discount "),e.k0s()(),e.j41(68,"p",33),e.EFF(69," $0 "),e.k0s()(),e.j41(70,"div",31)(71,"p",32),e.EFF(72),e.nI1(73,"async"),e.k0s(),e.j41(74,"p",37),e.EFF(75),e.nI1(76,"decimal"),e.nI1(77,"formatNumber"),e.k0s()()()(),e.j41(78,"div",38)(79,"app-inno-modal-footer",39),e.bIt("onCancel",function(){return e.eBV(n),e.Njj(i.handleCancel())}),e.k0s(),e.DNE(80,De,3,0,"ng-template",null,2,e.C5r),e.k0s()()}if(2&t){const n=e.sdS(16),s=e.sdS(81);e.Y8G("title",i.title),e.R7$(7),e.vxM(i.businessInfo.businessName?-1:7),e.R7$(),e.vxM(i.businessInfo.businessPhoneNumber?8:-1),e.R7$(),e.vxM(i.businessInfo.businessAddress?9:-1),e.R7$(4),e.Y8G("options",i.projectAndClientOptions)("formControl",i.f.clientId)("projectId",i.f.projectId.value)("value",i.f.clientId.value)("errorMessages",e.lJ4(40,ee))("customOptionTemplate",n),e.R7$(4),e.Y8G("formControl",i.f.invoiceNumber)("value",i.f.invoiceNumber.value),e.R7$(),e.Y8G("formControl",i.f.invoiceDate)("value",i.f.invoiceDate.value)("errorMessages",e.lJ4(41,te)),e.R7$(),e.vxM(i.data.isInvoice?19:-1),e.R7$(2),e.Y8G("formControl",i.f.notes),e.R7$(7),e.Y8G("cdkDropListData",i.f.itemInvoice.value),e.R7$(12),e.Dyx(i.f.itemInvoice.value),e.R7$(10),e.SpI(" $",e.bMT(52,26,e.i5U(51,23,i.subtotal,2))," "),e.R7$(3),e.Dyx(i.taxArray),e.R7$(6),e.SpI(" $",e.bMT(61,31,e.i5U(60,28,i.CheckIsNaN(i.sumtax),2))," "),e.R7$(13),e.SpI(" Amount Due (",e.bMT(73,33,i._storeService.curencyCompany),") "),e.R7$(3),e.SpI(" $",e.bMT(77,38,e.i5U(76,35,i.totalAmount,2))," "),e.R7$(4),e.Y8G("customSubmitButton",s)}},dependencies:[S.G,C.YU,f.BC,f.l_,C.Jj,C.vh,A.mC,A.fw,M.iI,$.I,B.M,U.a,L.k,W.C,V.K,X.k,K.P,Q.p,R.Q,x.O7,x.T1],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(200px,1fr) 100px 100px 100px 200px 100px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),p})()}}]);