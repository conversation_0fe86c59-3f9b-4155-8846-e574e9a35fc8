"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9775],{9775:(ia,mi,dt)=>{dt.d(mi,{AG:()=>xs,K7:()=>Us,Ur:()=>ks,Vv:()=>Ve,Xf:()=>Gs,hU:()=>Yi,lr:()=>Ne});var oe,e=dt(9163),It=dt(6623),v=dt(4395),St=dt(3735),qt=dt(8031),fe=function(){var u=function(s,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var n in a)a.hasOwnProperty(n)&&(i[n]=a[n])})(s,t)};return function(s,t){function i(){this.constructor=s}u(s,t),s.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}}(),x=function(u,s,t,i){var l,a=arguments.length,n=a<3?s:null===i?i=Object.getOwnPropertyDescriptor(s,t):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(u,s,t,i);else for(var r=u.length-1;r>=0;r--)(l=u[r])&&(n=(a<3?l(n):a>3?l(s,t,n):l(s,t))||n);return a>3&&n&&Object.defineProperty(s,t,n),n},U="e-other-month",Ut="e-other-year",bt="e-calendar",ve="e-device",Qt="e-year",$t="e-month",ye="e-decade",be="e-icons",S="e-disabled",et="e-overlay",Dt="e-week-number",X="e-selected",z="e-focused-date",lt="e-focused-cell",xt="e-month-hide",De="e-today",Ot="e-zoomin",ke="e-calendar-day-header-lg",Gt=864e5,Me=function(u){function s(t,i){var a=u.call(this,t,i)||this;return a.effect="",a.isPopupClicked=!1,a.isDateSelected=!0,a.isTodayClicked=!1,a.preventChange=!1,a.previousDates=!1,a}return fe(s,u),s.prototype.render=function(){this.rangeValidation(this.min,this.max),this.calendarEleCopy=this.element.cloneNode(!0),"Islamic"===this.calendarMode&&(+this.min.setSeconds(0)==+new Date(1900,0,1,0,0,0)&&(this.min=new Date(1944,2,18)),+this.max==+new Date(2099,11,31)&&(this.max=new Date(2069,10,16))),this.globalize=new e.DL(this.locale),((0,e.hX)(this.firstDayOfWeek)||this.firstDayOfWeek>6||this.firstDayOfWeek<0)&&this.setProperties({firstDayOfWeek:this.globalize.getFirstDayOfWeek()},!0),this.todayDisabled=!1,this.todayDate=new Date((new Date).setHours(0,0,0,0)),"calendar"===this.getModuleName()?(this.element.classList.add(bt),this.enableRtl&&this.element.classList.add("e-rtl"),e.Pw.isDevice&&this.element.classList.add(ve),(0,e.uK)(this.element,{"data-role":"calendar"}),this.tabIndex=this.element.hasAttribute("tabindex")?this.element.getAttribute("tabindex"):"0",this.element.setAttribute("tabindex",this.tabIndex)):(this.calendarElement=this.createElement("div"),this.calendarElement.classList.add(bt),this.enableRtl&&this.calendarElement.classList.add("e-rtl"),e.Pw.isDevice&&this.calendarElement.classList.add(ve),(0,e.uK)(this.calendarElement,{"data-role":"calendar"})),!(0,e.hX)((0,e.kp)(this.element,"fieldset"))&&(0,e.kp)(this.element,"fieldset").disabled&&(this.enabled=!1),this.createHeader(),this.createContent(),this.wireEvents()},s.prototype.rangeValidation=function(t,i){(0,e.hX)(t)&&this.setProperties({min:new Date(1900,0,1)},!0),(0,e.hX)(i)&&this.setProperties({max:new Date(2099,11,31)},!0)},s.prototype.getDefaultKeyConfig=function(){return this.defaultKeyConfigs={controlUp:"ctrl+38",controlDown:"ctrl+40",moveDown:"downarrow",moveUp:"uparrow",moveLeft:"leftarrow",moveRight:"rightarrow",select:"enter",home:"home",end:"end",pageUp:"pageup",pageDown:"pagedown",shiftPageUp:"shift+pageup",shiftPageDown:"shift+pagedown",controlHome:"ctrl+home",controlEnd:"ctrl+end",altUpArrow:"alt+uparrow",spacebar:"space",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"},this.defaultKeyConfigs},s.prototype.validateDate=function(t){this.setProperties({min:this.checkDateValue(new Date(this.checkValue(this.min)))},!0),this.setProperties({max:this.checkDateValue(new Date(this.checkValue(this.max)))},!0),this.currentDate=this.currentDate?this.currentDate:new Date((new Date).setHours(0,0,0,0)),!(0,e.hX)(t)&&this.min<=this.max&&t>=this.min&&t<=this.max&&(this.currentDate=new Date(this.checkValue(t)))},s.prototype.setOverlayIndex=function(t,i,a,n){if(n&&!(0,e.hX)(i)&&!(0,e.hX)(a)&&!(0,e.hX)(t)){var l=parseInt(i.style.zIndex,10)?parseInt(i.style.zIndex,10):1e3;a.style.zIndex=(l-1).toString(),t.style.zIndex=l.toString()}},s.prototype.minMaxUpdate=function(t){+this.min<=+this.max?(0,e.vy)([this.element],et):(this.setProperties({min:this.min},!0),(0,e.iQ)([this.element],et)),this.min=(0,e.hX)(this.min)||!+this.min?this.min=new Date(1900,0,1):this.min,this.max=(0,e.hX)(this.max)||!+this.max?this.max=new Date(2099,11,31):this.max,+this.min<=+this.max&&t&&+t<=+this.max&&+t>=+this.min?this.currentDate=new Date(this.checkValue(t)):+this.min<=+this.max&&!t&&+this.currentDate>+this.max?this.currentDate=new Date(this.checkValue(this.max)):+this.currentDate<+this.min&&(this.currentDate=new Date(this.checkValue(this.min)))},s.prototype.createHeader=function(){var n={tabindex:"0"};this.headerElement=this.createElement("div",{className:"e-header"});var l=this.createElement("div",{className:"e-icon-container"});this.previousIcon=this.createElement("button",{className:"e-prev",attrs:{type:"button"}}),(0,e.CF)(this.previousIcon,{duration:400,selector:".e-prev",isCenterRipple:!0}),(0,e.uK)(this.previousIcon,{"aria-disabled":"false","aria-label":"previous month"}),(0,e.uK)(this.previousIcon,n),this.nextIcon=this.createElement("button",{className:"e-next",attrs:{type:"button"}}),(0,e.CF)(this.nextIcon,{selector:".e-next",duration:400,isCenterRipple:!0}),"daterangepicker"===this.getModuleName()&&((0,e.uK)(this.previousIcon,{tabIndex:"-1"}),(0,e.uK)(this.nextIcon,{tabIndex:"-1"})),(0,e.uK)(this.nextIcon,{"aria-disabled":"false","aria-label":"next month"}),(0,e.uK)(this.nextIcon,n),this.headerTitleElement=this.createElement("div",{className:"e-day e-title"}),(0,e.uK)(this.headerTitleElement,{"aria-atomic":"true","aria-live":"assertive","aria-label":"title"}),(0,e.uK)(this.headerTitleElement,n),this.headerElement.appendChild(this.headerTitleElement),this.previousIcon.appendChild(this.createElement("span",{className:"e-date-icon-prev "+be})),this.nextIcon.appendChild(this.createElement("span",{className:"e-date-icon-next  "+be})),l.appendChild(this.previousIcon),l.appendChild(this.nextIcon),this.headerElement.appendChild(l),"calendar"===this.getModuleName()?this.element.appendChild(this.headerElement):this.calendarElement.appendChild(this.headerElement),this.adjustLongHeaderSize()},s.prototype.createContent=function(){this.contentElement=this.createElement("div",{className:"e-content"}),this.table=this.createElement("table",{attrs:{class:"e-calendar-content-table",tabIndex:"0",role:"grid","aria-activedescendant":"","aria-labelledby":this.element.id}}),"calendar"===this.getModuleName()?this.element.appendChild(this.contentElement):this.calendarElement.appendChild(this.contentElement),this.contentElement.appendChild(this.table),this.createContentHeader(),this.createContentBody(),this.showTodayButton&&this.createContentFooter(),"daterangepicker"!==this.getModuleName()&&(e.Jm.add(this.table,"focus",this.addContentFocus,this),e.Jm.add(this.table,"blur",this.removeContentFocus,this))},s.prototype.addContentFocus=function(t){var i=this.tableBodyElement.querySelector("tr td.e-focused-date"),a=this.tableBodyElement.querySelector("tr td.e-selected");(0,e.hX)(a)?(0,e.hX)(i)||i.classList.add(lt):a.classList.add(lt)},s.prototype.removeContentFocus=function(t){var i=(0,e.hX)(this.tableBodyElement)?null:this.tableBodyElement.querySelector("tr td.e-focused-date"),a=(0,e.hX)(this.tableBodyElement)?null:this.tableBodyElement.querySelector("tr td.e-selected");(0,e.hX)(a)?(0,e.hX)(i)||i.classList.remove(lt):a.classList.remove(lt)},s.prototype.getCultureValues=function(){var i,t=[],a=(0,e.hX)(this.dayHeaderFormat)?null:"days.stand-alone."+this.dayHeaderFormat.toLowerCase();if(i="en"!==this.locale&&"en-US"!==this.locale||(0,e.hX)(a)?this.getCultureObjects(e.pE,""+this.locale):(0,e._W)(a,(0,e.P)()),!(0,e.hX)(i))for(var n=0,l=Object.keys(i);n<l.length;n++)t.push((0,e._W)(l[n],i));return t},s.prototype.toCapitalize=function(t){return!(0,e.hX)(t)&&t.length?t[0].toUpperCase()+t.slice(1):t},s.prototype.createContentHeader=function(){"calendar"===this.getModuleName()?(0,e.hX)(this.element.querySelectorAll(".e-content .e-week-header")[0])||(0,e.Yo)(this.element.querySelectorAll(".e-content .e-week-header")[0]):(0,e.hX)(this.calendarElement.querySelectorAll(".e-content .e-week-header")[0])||(0,e.Yo)(this.calendarElement.querySelectorAll(".e-content .e-week-header")[0]);var i="";(this.firstDayOfWeek>6||this.firstDayOfWeek<0)&&this.setProperties({firstDayOfWeek:0},!0),this.tableHeadElement=this.createElement("thead",{className:"e-week-header"}),this.weekNumber&&(i+='<th class="e-week-number" aria-hidden="true"></th>',"calendar"===this.getModuleName()?(0,e.iQ)([this.element],""+Dt):(0,e.iQ)([this.calendarElement],""+Dt));var a=this.getCultureValues().length>0&&this.getCultureValues()?this.shiftArray(this.getCultureValues().length>0&&this.getCultureValues(),this.firstDayOfWeek):null;if(!(0,e.hX)(a))for(var n=0;n<=6;n++)i+='<th  class="">'+this.toCapitalize(a[n])+"</th>";this.tableHeadElement.innerHTML=i="<tr>"+i+"</tr>",this.table.appendChild(this.tableHeadElement)},s.prototype.createContentBody=function(){switch("calendar"===this.getModuleName()?(0,e.hX)(this.element.querySelectorAll(".e-content tbody")[0])||(0,e.Yo)(this.element.querySelectorAll(".e-content tbody")[0]):(0,e.hX)(this.calendarElement.querySelectorAll(".e-content tbody")[0])||(0,e.Yo)(this.calendarElement.querySelectorAll(".e-content tbody")[0]),this.start){case"Year":this.renderYears();break;case"Decade":this.renderDecades();break;default:this.renderMonths()}},s.prototype.updateFooter=function(){this.todayElement.textContent=this.l10.getConstant("today"),this.todayElement.setAttribute("aria-label",this.l10.getConstant("today")),this.todayElement.setAttribute("tabindex","0")},s.prototype.createContentFooter=function(){if(this.showTodayButton){var t=new Date(+this.min),i=new Date(+this.max);this.globalize=new e.DL(this.locale),this.l10=new e.Wo(this.getModuleName(),{today:"Today"},this.locale),this.todayElement=this.createElement("button",{attrs:{role:"button"}}),(0,e.CF)(this.todayElement),this.updateFooter(),(0,e.iQ)([this.todayElement],["e-btn",De,"e-flat","e-primary","e-css"]),(!(+new Date(t.setHours(0,0,0,0))<=+this.todayDate&&+this.todayDate<=+new Date(i.setHours(0,0,0,0)))||this.todayDisabled)&&(0,e.iQ)([this.todayElement],S),this.footer=this.createElement("div",{className:"e-footer-container"}),this.footer.appendChild(this.todayElement),"calendar"===this.getModuleName()&&this.element.appendChild(this.footer),"datepicker"===this.getModuleName()&&this.calendarElement.appendChild(this.footer),"datetimepicker"===this.getModuleName()&&this.calendarElement.appendChild(this.footer),this.todayElement.classList.contains(S)||e.Jm.add(this.todayElement,"click",this.todayButtonClick,this)}},s.prototype.wireEvents=function(t,i,a,n){e.Jm.add(this.headerTitleElement,"click",this.navigateTitle,this),this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,this.keyConfigs),this.keyboardModule="calendar"===this.getModuleName()?new e.j9(this.element,{eventName:"keydown",keyAction:this.keyActionHandle.bind(this),keyConfigs:this.defaultKeyConfigs}):new e.j9(this.calendarElement,{eventName:"keydown",keyAction:this.keyActionHandle.bind(this),keyConfigs:this.defaultKeyConfigs})},s.prototype.dateWireEvents=function(t,i,a,n){this.defaultKeyConfigs=this.getDefaultKeyConfig(),this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,a),this.serverModuleName=n},s.prototype.todayButtonClick=function(t,i,a){this.showTodayButton&&(this.effect=this.currentView()===this.depth?"":"e-zoomin",this.getViewNumber(this.start)>=this.getViewNumber(this.depth)?this.navigateTo(this.depth,new Date(this.checkValue(i)),a):this.navigateTo("Month",new Date(this.checkValue(i)),a))},s.prototype.resetCalendar=function(){this.calendarElement&&(0,e.Yo)(this.calendarElement),this.tableBodyElement&&(0,e.Yo)(this.tableBodyElement),this.table&&(0,e.Yo)(this.table),this.tableHeadElement&&(0,e.Yo)(this.tableHeadElement),this.nextIcon&&(0,e.Yo)(this.nextIcon),this.previousIcon&&(0,e.Yo)(this.previousIcon),this.footer&&(0,e.Yo)(this.footer),this.todayElement=null,this.renderDayCellArgs=null,this.calendarElement=this.tableBodyElement=this.footer=this.tableHeadElement=this.nextIcon=this.previousIcon=this.table=null},s.prototype.keyActionHandle=function(t,i,a){if(null!==this.calendarElement||"escape"!==t.action){var l,n=this.tableBodyElement.querySelector("tr td.e-focused-date");l=a?(0,e.hX)(n)||+i!==parseInt(n.getAttribute("id").split("_")[0],10)?this.tableBodyElement.querySelector("tr td.e-selected"):n:this.tableBodyElement.querySelector("tr td.e-selected");var r=this.getViewNumber(this.currentView()),h=this.getViewNumber(this.depth),o=r===h&&this.getViewNumber(this.start)>=h;switch(this.effect="",t.action){case"moveLeft":"daterangepicker"!==this.getModuleName()&&!(0,e.hX)(t.target)&&(this.keyboardNavigate(-1,r,t,this.max,this.min),t.preventDefault());break;case"moveRight":"daterangepicker"!==this.getModuleName()&&!(0,e.hX)(t.target)&&(this.keyboardNavigate(1,r,t,this.max,this.min),t.preventDefault());break;case"moveUp":"daterangepicker"!==this.getModuleName()&&!(0,e.hX)(t.target)&&(this.keyboardNavigate(0===r?-7:-4,r,t,this.max,this.min),t.preventDefault());break;case"moveDown":"daterangepicker"!==this.getModuleName()&&!(0,e.hX)(t.target)&&(this.keyboardNavigate(0===r?7:4,r,t,this.max,this.min),t.preventDefault());break;case"select":if(t.target===this.headerTitleElement)this.navigateTitle(t);else if(t.target!==this.previousIcon||t.target.className.includes(S))if(t.target!==this.nextIcon||t.target.className.includes(S))if(t.target!==this.todayElement||t.target.className.includes(S)){var p=(0,e.hX)(n)?l:n;if(!(0,e.hX)(p)&&!p.classList.contains(S))if(o){var c=new Date(parseInt(""+p.id,0));this.selectDate(t,c,p),("datepicker"===this.getModuleName()||"datetimepicker"===this.getModuleName())&&(this.isAngular?this.inputElement.focus():this.element.focus())}else t.target.className.includes(S)||this.contentClick(null,--r,p,i)}else this.todayButtonClick(t,i),("datepicker"===this.getModuleName()||"datetimepicker"===this.getModuleName())&&(this.isAngular?this.inputElement.focus():this.element.focus());else this.navigateNext(t);else this.navigatePrevious(t);break;case"controlUp":this.title(),t.preventDefault();break;case"controlDown":(!(0,e.hX)(n)&&!o||!(0,e.hX)(l)&&!o)&&this.contentClick(null,--r,n||l,i),t.preventDefault();break;case"home":this.currentDate=this.firstDay(this.currentDate),(0,e.Yo)(this.tableBodyElement),0===r?this.renderMonths(t):1===r?this.renderYears(t):this.renderDecades(t),t.preventDefault();break;case"end":this.currentDate=this.lastDay(this.currentDate,r),(0,e.Yo)(this.tableBodyElement),0===r?this.renderMonths(t):1===r?this.renderYears(t):this.renderDecades(t),t.preventDefault();break;case"pageUp":this.addMonths(this.currentDate,-1),this.navigateTo("Month",this.currentDate),t.preventDefault();break;case"pageDown":this.addMonths(this.currentDate,1),this.navigateTo("Month",this.currentDate),t.preventDefault();break;case"shiftPageUp":this.addYears(this.currentDate,-1),this.navigateTo("Month",this.currentDate),t.preventDefault();break;case"shiftPageDown":this.addYears(this.currentDate,1),this.navigateTo("Month",this.currentDate),t.preventDefault();break;case"controlHome":this.navigateTo("Month",new Date(this.currentDate.getFullYear(),0,1)),t.preventDefault();break;case"controlEnd":this.navigateTo("Month",new Date(this.currentDate.getFullYear(),11,31)),t.preventDefault();break;case"tab":("datepicker"===this.getModuleName()||"datetimepicker"===this.getModuleName())&&t.target===this.todayElement&&(t.preventDefault(),this.isAngular?this.inputElement.focus():this.element.focus(),this.hide());break;case"shiftTab":("datepicker"===this.getModuleName()||"datetimepicker"===this.getModuleName())&&t.target===this.headerTitleElement&&(t.preventDefault(),this.isAngular?this.inputElement.focus():this.element.focus(),this.hide());break;case"escape":("datepicker"===this.getModuleName()||"datetimepicker"===this.getModuleName())&&(t.target===this.headerTitleElement||t.target===this.previousIcon||t.target===this.nextIcon||t.target===this.todayElement)&&this.hide()}}},s.prototype.keyboardNavigate=function(t,i,a,n,l){var r=new Date(this.checkValue(this.currentDate));switch(i){case 2:this.addYears(this.currentDate,t),this.isMonthYearRange(this.currentDate)?((0,e.Yo)(this.tableBodyElement),this.renderDecades(a)):this.currentDate=r;break;case 1:this.addMonths(this.currentDate,t),this.isMonthYearRange(this.currentDate)?((0,e.Yo)(this.tableBodyElement),this.renderYears(a)):this.currentDate=r;break;case 0:this.addDay(this.currentDate,t,a,n,l),this.isMinMaxRange(this.currentDate)?((0,e.Yo)(this.tableBodyElement),this.renderMonths(a)):this.currentDate=r}},s.prototype.preRender=function(t){var i=this;this.navigatePreviousHandler=this.navigatePrevious.bind(this),this.navigateNextHandler=this.navigateNext.bind(this),this.defaultKeyConfigs=this.getDefaultKeyConfig(),this.navigateHandler=function(a){i.triggerNavigate(a)}},s.prototype.minMaxDate=function(t){var i=new Date(new Date(+t).setHours(0,0,0,0)),a=new Date(new Date(+this.min).setHours(0,0,0,0)),n=new Date(new Date(+this.max).setHours(0,0,0,0));return(+i==+a||+i==+n)&&(+t<+this.min&&(t=new Date(+this.min)),+t>+this.max&&(t=new Date(+this.max))),t},s.prototype.renderMonths=function(t,i,a){var l,n=this.weekNumber?8:7;l="Gregorian"===this.calendarMode?this.renderDays(this.currentDate,i,null,null,a,t):(0,e.hX)(this.islamicModule)?null:this.islamicModule.islamicRenderDays(this.currentDate,i),this.createContentHeader(),"Gregorian"===this.calendarMode?this.renderTemplate(l,n,$t,t,i):(0,e.hX)(this.islamicModule)||this.islamicModule.islamicRenderTemplate(l,n,$t,t,i)},s.prototype.renderDays=function(t,i,a,n,l,r){var f,h=[],p=l?new Date(+t):this.getDate(new Date,this.timezone),c=new Date(this.checkValue(t)),g=c.getMonth();this.titleUpdate(t,"days");var d=c;for(c=new Date(d.getFullYear(),d.getMonth(),0,d.getHours(),d.getMinutes(),d.getSeconds(),d.getMilliseconds());c.getDay()!==this.firstDayOfWeek;)this.setStartDate(c,-1*Gt);for(var y=0;y<42;++y){var b=this.createElement("td",{className:"e-cell"}),m=this.createElement("span");if(y%7==0&&this.weekNumber){var A="FirstDay"===this.weekRule?6:"FirstFourDayWeek"===this.weekRule?3:0,M=new Date(c.getFullYear(),c.getMonth(),c.getDate()+A);m.textContent=""+this.getWeek(M),b.appendChild(m),(0,e.iQ)([b],""+Dt),h.push(b)}f=new Date(+c),c=this.minMaxDate(c);var V={type:"dateTime",skeleton:"full"},W=this.globalize.parseDate(this.globalize.formatDate(c,V),V),w=this.dayCell(c),j=this.globalize.formatDate(c,{type:"date",skeleton:"full"}),G=this.createElement("span");G.textContent=this.globalize.formatDate(c,{format:"d",type:"date",skeleton:"yMd"});var H=this.min>c||this.max<c;H?((0,e.iQ)([w],S),(0,e.iQ)([w],et)):G.setAttribute("title",""+j),g!==c.getMonth()&&((0,e.iQ)([w],U),G.setAttribute("aria-disabled","true")),(0===c.getDay()||6===c.getDay())&&(0,e.iQ)([w],"e-weekend"),w.appendChild(G),this.renderDayCellArgs={date:c,isDisabled:!1,element:w,isOutOfRange:H};var J=this.renderDayCellArgs;if(this.renderDayCellEvent(J),J.isDisabled){var q=new Date(this.checkValue(i)),vt=new Date(this.checkValue(J.date));if(a){if(!(0,e.hX)(n)&&n.length>0)for(var rt=0;rt<n.length;rt++)+new Date(this.globalize.formatDate(J.date,{type:"date",skeleton:"yMd"}))==+new Date(this.globalize.formatDate(n[rt],{type:"date",skeleton:"yMd"}))&&(n.splice(rt,1),rt=-1)}else q&&+q==+vt&&this.setProperties({value:null},!0)}this.renderDayCellArgs.isDisabled&&!w.classList.contains(X)&&((0,e.iQ)([w],S),(0,e.iQ)([w],et),G.setAttribute("aria-disabled","true"),+this.renderDayCellArgs.date==+this.todayDate&&(this.todayDisabled=!0));var me=w.classList.contains(U),gt=w.classList.contains(S);gt||e.Jm.add(w,"click",this.clickHandler,this);var zt=void 0;if(!(0,e.hX)(r)&&"click"===r.type&&(zt=r.currentTarget),!a||(0,e.hX)(n)||gt)!gt&&this.getDateVal(c,i)&&(0,e.iQ)([w],X);else{for(var Kt=0;Kt<n.length;Kt++){var ci={format:null,type:"date",skeleton:"short",calendar:"Gregorian"===this.calendarMode?"gregorian":"islamic"};if((this.globalize.formatDate(c,ci)===this.globalize.formatDate(n[Kt],ci)&&this.getDateVal(c,n[Kt])||this.getDateVal(c,i))&&(0,e.iQ)([w],X),!(0,e.hX)(zt)&&zt.innerText===w.innerText&&this.previousDates&&w.classList.contains(X)&&zt.classList.contains(X)){(0,e.vy)([w],X),this.previousDates=!1;for(var yt=this.copyValues(n),Jt=0;Jt<yt.length;Jt++){var di={format:null,type:"date",skeleton:"short",calendar:"Gregorian"===this.calendarMode?"gregorian":"islamic"};this.globalize.formatDate(W,di)===this.globalize.formatDate(yt[Jt],di)&&(rt=yt.indexOf(yt[Jt]),yt.splice(rt,1),n.splice(rt,1))}this.setProperties({values:yt},!0)}else this.updateFocus(me,gt,c,w,t)}n.length<=0&&this.updateFocus(me,gt,c,w,t)}this.updateFocus(me,gt,c,w,t),!(0,e.hX)(W)&&W.getFullYear()===p.getFullYear()&&W.getMonth()===p.getMonth()&&W.getDate()===p.getDate()&&(0,e.iQ)([w],De),h.push(this.renderDayCellArgs.element),this.addDay(c=new Date(+f),1,null,this.max,this.min)}return h},s.prototype.updateFocus=function(t,i,a,n,l){l.getDate()!==a.getDate()||t||i?(l>=this.max&&parseInt(n.id,0)===+this.max&&!t&&!i&&(0,e.iQ)([n],z),l<=this.min&&parseInt(n.id,0)===+this.min&&!t&&!i&&(0,e.iQ)([n],z)):(0,e.iQ)([n],z)},s.prototype.renderYears=function(t,i){this.removeTableHeadElement();var n=[],l=(0,e.hX)(i),r=new Date(this.checkValue(this.currentDate)),h=r.getMonth(),o=r.getFullYear(),p=r,c=p.getFullYear(),f=new Date(this.checkValue(this.min)).getFullYear(),g=new Date(this.checkValue(this.min)).getMonth(),d=new Date(this.checkValue(this.max)).getFullYear(),y=new Date(this.checkValue(this.max)).getMonth();p.setMonth(0),this.titleUpdate(this.currentDate,"months"),p.setDate(1);for(var b=0;b<12;++b){var m=this.dayCell(p),A=this.createElement("span"),M=i&&i.getMonth()===p.getMonth(),V=i&&i.getFullYear()===o&&M,W=this.globalize.formatDate(p,{type:"date",format:"MMM y"});A.textContent=this.toCapitalize(this.globalize.formatDate(p,{format:null,type:"dateTime",skeleton:"MMM"})),this.min&&(c<f||b<g&&c===f)||this.max&&(c>d||b>y&&c>=d)?(0,e.iQ)([m],S):!l&&V?(0,e.iQ)([m],X):p.getMonth()===h&&this.currentDate.getMonth()===h&&(0,e.iQ)([m],z),p.setDate(1),p.setMonth(p.getMonth()+1),m.classList.contains(S)||(e.Jm.add(m,"click",this.clickHandler,this),A.setAttribute("title",""+W)),m.appendChild(A),n.push(m)}this.renderTemplate(n,4,Qt,t,i)},s.prototype.renderDecades=function(t,i){this.removeTableHeadElement();var l=[],r=new Date(this.checkValue(this.currentDate));r.setMonth(0),r.setDate(1);var h=r.getFullYear(),o=new Date(r.setFullYear(h-h%10)),p=new Date(r.setFullYear(h-h%10+9)),c=o.getFullYear(),f=p.getFullYear(),g=this.globalize.formatDate(o,{format:null,type:"dateTime",skeleton:"y"}),d=this.globalize.formatDate(p,{format:null,type:"dateTime",skeleton:"y"});this.headerTitleElement.textContent=g+" - "+d;for(var b=new Date(h-h%10-1,0,1).getFullYear(),m=0;m<12;++m){var A=b+m;r.setFullYear(A);var M=this.dayCell(r),V=this.createElement("span");V.textContent=this.globalize.formatDate(r,{format:null,type:"dateTime",skeleton:"y"}),A<c||A>f?((0,e.iQ)([M],Ut),V.setAttribute("aria-disabled","true"),!(0,e.hX)(i)&&r.getFullYear()===i.getFullYear()&&(0,e.iQ)([M],X),(A<new Date(this.checkValue(this.min)).getFullYear()||A>new Date(this.checkValue(this.max)).getFullYear())&&(0,e.iQ)([M],S)):A<new Date(this.checkValue(this.min)).getFullYear()||A>new Date(this.checkValue(this.max)).getFullYear()?(0,e.iQ)([M],S):(0,e.hX)(i)||r.getFullYear()!==i.getFullYear()?r.getFullYear()===this.currentDate.getFullYear()&&!M.classList.contains(S)&&(0,e.iQ)([M],z):(0,e.iQ)([M],X),M.classList.contains(S)||(e.Jm.add(M,"click",this.clickHandler,this),V.setAttribute("title",""+V.textContent)),M.appendChild(V),l.push(M)}this.renderTemplate(l,4,"e-decade",t,i)},s.prototype.dayCell=function(t){var l,a={skeleton:"full",type:"dateTime",calendar:"Gregorian"===this.calendarMode?"gregorian":"islamic"},n=this.globalize.parseDate(this.globalize.formatDate(t,a),a);(0,e.hX)(n)||(l=n.valueOf());var r={className:"e-cell",attrs:{id:""+(0,e.Lz)(""+l),"aria-selected":"false"}};return this.createElement("td",r)},s.prototype.firstDay=function(t){var i="Decade"!==this.currentView()?this.tableBodyElement.querySelectorAll("td:not(."+U+"):not(."+Dt+")"):this.tableBodyElement.querySelectorAll("td:not(."+Ut);if(i.length)for(var a=0;a<i.length;a++)if(!i[a].classList.contains(S)){t=new Date(parseInt(i[a].id,0));break}return t},s.prototype.lastDay=function(t,i){var a=new Date(t.getFullYear(),t.getMonth()+1,0);if(2!==i){var n=Math.abs(a.getTimezoneOffset()-this.firstDay(t).getTimezoneOffset());return n&&a.setHours(this.firstDay(t).getHours()+n/60),this.findLastDay(a)}return this.findLastDay(this.firstDay(a))},s.prototype.checkDateValue=function(t){return!(0,e.hX)(t)&&t instanceof Date&&!isNaN(+t)?t:null},s.prototype.findLastDay=function(t){var i="Decade"===this.currentView()?this.tableBodyElement.querySelectorAll("td:not(."+Ut):this.tableBodyElement.querySelectorAll("td:not(."+U+"):not(."+Dt+")");if(i.length)for(var a=i.length-1;a>=0;a--)if(!i[a].classList.contains(S)){t=new Date(parseInt(i[a].id,0));break}return t},s.prototype.removeTableHeadElement=function(){"calendar"===this.getModuleName()?(0,e.hX)(this.element.querySelectorAll(".e-content table thead")[0])||(0,e.Yo)(this.tableHeadElement):(0,e.hX)(this.calendarElement.querySelectorAll(".e-content table thead")[0])||(0,e.Yo)(this.tableHeadElement)},s.prototype.renderTemplate=function(t,i,a,n,l){var h,r=this.getViewNumber(this.currentView());this.tableBodyElement=this.createElement("tbody"),this.table.appendChild(this.tableBodyElement),(0,e.vy)([this.contentElement,this.headerElement],[$t,ye,Qt]),(0,e.iQ)([this.contentElement,this.headerElement],[a]);for(var f=i,g=0,d=0;d<t.length/i;++d){for(h=this.createElement("tr"),g=0+g;g<f;g++)!t[g].classList.contains("e-week-number")&&!(0,e.hX)(t[g].children[0])&&((0,e.iQ)([t[g].children[0]],["e-day"]),(0,e.CF)(t[g].children[0],{duration:600,isCenterRipple:!0})),h.appendChild(t[g]),this.weekNumber&&7===g&&t[7].classList.contains(U)&&(0,e.iQ)([h],xt),!this.weekNumber&&6===g&&t[6].classList.contains(U)&&(0,e.iQ)([h],xt),this.weekNumber?41===g&&t[41].classList.contains(U)&&(0,e.iQ)([h],xt):35===g&&t[35].classList.contains(U)&&(0,e.iQ)([h],xt);f+=i,g+=0,this.tableBodyElement.appendChild(h)}this.table.querySelector("tbody").className=this.effect,"Gregorian"===this.calendarMode?this.iconHandler():this.islamicModule.islamicIconHandler(),(r!==this.getViewNumber(this.currentView())||0===r&&r!==this.getViewNumber(this.currentView()))&&this.navigateHandler(n),this.setAriaActiveDescendant()},s.prototype.clickHandler=function(t,i){this.clickEventEmitter(t);var a=t.currentTarget,n=this.getViewNumber(this.currentView());a.classList.contains(U)?this.contentClick(t,0,null,i):n===this.getViewNumber(this.depth)&&this.getViewNumber(this.start)>=this.getViewNumber(this.depth)||2===n?this.contentClick(t,1,null,i):a.classList.contains(U)||0!==n?this.contentClick(t,0,a,i):this.selectDate(t,this.getIdValue(t,null),null),"calendar"===this.getModuleName()&&this.table.focus()},s.prototype.clickEventEmitter=function(t){t.preventDefault()},s.prototype.contentClick=function(t,i,a,n){var l=this.getViewNumber(this.currentView()),r=this.getIdValue(t,a);switch(i){case 0:l===this.getViewNumber(this.depth)&&this.getViewNumber(this.start)>=this.getViewNumber(this.depth)?((0,e.Yo)(this.tableBodyElement),this.currentDate=r,this.effect=Ot,this.renderMonths(t)):("Gregorian"===this.calendarMode?(this.currentDate.setMonth(r.getMonth()),r.getMonth()>0&&this.currentDate.getMonth()!==r.getMonth()&&this.currentDate.setDate(0),this.currentDate.setFullYear(r.getFullYear())):this.currentDate=r,this.effect=Ot,(0,e.Yo)(this.tableBodyElement),this.renderMonths(t));break;case 1:if(l===this.getViewNumber(this.depth)&&this.getViewNumber(this.start)>=this.getViewNumber(this.depth))this.selectDate(t,r,null);else{if("Gregorian"===this.calendarMode)this.currentDate.setFullYear(r.getFullYear());else{this.islamicPreviousHeader=this.headerElement.textContent;var h=this.islamicModule.getIslamicDate(r);this.currentDate=this.islamicModule.toGregorian(h.year,h.month,1)}this.effect=Ot,(0,e.Yo)(this.tableBodyElement),this.renderYears(t)}}},s.prototype.switchView=function(t,i,a,n){switch(t){case 0:(0,e.Yo)(this.tableBodyElement),this.renderMonths(i,null,n);break;case 1:(0,e.Yo)(this.tableBodyElement),this.renderYears(i);break;case 2:(0,e.Yo)(this.tableBodyElement),this.renderDecades(i)}},s.prototype.getModuleName=function(){return"calendar"},s.prototype.requiredModules=function(){var t=[];return"Islamic"===this.calendarMode&&t.push({args:[this],member:"islamic",name:"Islamic"}),t},s.prototype.getPersistData=function(){return this.addOnPersist(["value"])},s.prototype.onPropertyChanged=function(t,i,a,n){this.effect="";for(var l=0,r=Object.keys(t);l<r.length;l++){var h=r[l];switch(h){case"enableRtl":t.enableRtl?"calendar"===this.getModuleName()?this.element.classList.add("e-rtl"):this.calendarElement.classList.add("e-rtl"):"calendar"===this.getModuleName()?this.element.classList.remove("e-rtl"):this.calendarElement.classList.remove("e-rtl");break;case"dayHeaderFormat":this.getCultureValues(),("datepicker"!==this.getModuleName()||this.calendarElement)&&this.createContentHeader(),this.adjustLongHeaderSize();break;case"min":case"max":this.rangeValidation(this.min,this.max),this.setProperties("min"===h?{min:this.checkDateValue(new Date(this.checkValue(t.min)))}:{max:this.checkDateValue(new Date(this.checkValue(t.max)))},!0),this.setProperties({start:this.currentView()},!0),this.tableBodyElement&&(0,e.Yo)(this.tableBodyElement),this.minMaxUpdate(),a&&this.validateValues(a,n),("datepicker"!==this.getModuleName()||this.calendarElement)&&this.createContentBody(),(this.todayDate<this.min||this.max<this.todayDate)&&this.footer&&this.todayElement?((0,e.Yo)(this.todayElement),(0,e.Yo)(this.footer),this.todayElement=this.footer=null,this.createContentFooter()):this.footer&&this.todayElement&&this.todayElement.classList.contains("e-disabled")&&((0,e.vy)([this.todayElement],S),(0,e.Yo)(this.todayElement),(0,e.Yo)(this.footer),this.todayElement=this.footer=null,this.createContentFooter());break;case"start":case"depth":case"weekNumber":case"firstDayOfWeek":case"weekRule":this.checkView(),("datepicker"!==this.getModuleName()||this.calendarElement)&&(this.createContentHeader(),this.createContentBody());break;case"locale":this.globalize=new e.DL(this.locale),("datepicker"!==this.getModuleName()||this.calendarElement)&&(this.createContentHeader(),this.createContentBody()),"calendar"===this.getModuleName()&&(this.l10=new e.Wo(this.getModuleName(),{today:"Today"},this.locale)),this.l10.setLocale(this.locale),this.showTodayButton&&this.updateFooter();break;case"showTodayButton":t.showTodayButton?this.createContentFooter():!(0,e.hX)(this.todayElement)&&!(0,e.hX)(this.footer)&&((0,e.Yo)(this.todayElement),(0,e.Yo)(this.footer),this.todayElement=this.footer=void 0),this.setProperties({showTodayButton:t.showTodayButton},!0)}}},s.prototype.validateValues=function(t,i){if(t&&!(0,e.hX)(i)&&i.length>0){for(var a=this.copyValues(i),n=0;n<a.length;n++){var h,r="Gregorian"===this.calendarMode?"gregorian":"islamic";h=this.globalize.formatDate(a[n],"Gregorian"===this.calendarMode?{type:"date",skeleton:"yMd"}:{type:"dateTime",skeleton:"full",calendar:"islamic"});var c=this.globalize.formatDate(this.min,{type:"date",skeleton:"yMd",calendar:r}),d=this.globalize.formatDate(this.max,{type:"date",skeleton:"yMd",calendar:r});(+new Date(h)<+new Date(c)||+new Date(h)>+new Date(d))&&(a.splice(n,1),n=-1)}this.setProperties({values:a},!0)}},s.prototype.setValueUpdate=function(){(0,e.hX)(this.tableBodyElement)||((0,e.Yo)(this.tableBodyElement),this.setProperties({start:this.currentView()},!0),this.createContentBody())},s.prototype.copyValues=function(t){var i=[];if(!(0,e.hX)(t)&&t.length>0)for(var a=0;a<t.length;a++)i.push(new Date(+t[a]));return i},s.prototype.titleUpdate=function(t,i){var n,l,a=new e.DL(this.locale),r="Gregorian"===this.calendarMode?"gregorian":"islamic";switch("Gregorian"===this.calendarMode?(n=a.formatDate(t,{type:"dateTime",skeleton:"yMMMM",calendar:r}),l=a.formatDate(t,{format:null,type:"dateTime",skeleton:"y",calendar:r})):(n=a.formatDate(t,{type:"dateTime",format:"MMMM y",calendar:r}),l=a.formatDate(t,{type:"dateTime",format:"y",calendar:r})),i){case"days":this.headerTitleElement.textContent=this.toCapitalize(n);break;case"months":this.headerTitleElement.textContent=l}},s.prototype.setActiveDescendant=function(){var t,l,i=this.tableBodyElement.querySelector("tr td.e-focused-date"),a=this.tableBodyElement.querySelector("tr td.e-selected"),n="Gregorian"===this.calendarMode?"gregorian":"islamic",r=this.currentView();return l=this.globalize.formatDate(this.currentDate,"Month"===r?{type:"date",skeleton:"full",calendar:n}:"Year"===r?"islamic"!==n?{type:"date",skeleton:"yMMMM",calendar:n}:{type:"date",skeleton:"GyMMM",calendar:n}:{format:null,type:"date",skeleton:"y",calendar:n}),(a||i)&&((0,e.hX)(a)||a.setAttribute("aria-selected","true"),(i||a).setAttribute("aria-label",l),t=(i||a).getAttribute("id")),t},s.prototype.iconHandler=function(){switch(new Date(this.checkValue(this.currentDate)).setDate(1),this.currentView()){case"Month":this.previousIconHandler(this.compareMonth(new Date(this.checkValue(this.currentDate)),this.min)<1),this.nextIconHandler(this.compareMonth(new Date(this.checkValue(this.currentDate)),this.max)>-1);break;case"Year":this.previousIconHandler(this.compareYear(new Date(this.checkValue(this.currentDate)),this.min)<1),this.nextIconHandler(this.compareYear(new Date(this.checkValue(this.currentDate)),this.max)>-1);break;case"Decade":this.previousIconHandler(this.compareDecade(new Date(this.checkValue(this.currentDate)),this.min)<1),this.nextIconHandler(this.compareDecade(new Date(this.checkValue(this.currentDate)),this.max)>-1)}},s.prototype.destroy=function(){("calendar"===this.getModuleName()&&this.element||this.calendarElement&&this.element)&&(0,e.vy)([this.element],[bt]),"calendar"===this.getModuleName()&&this.element&&((0,e.hX)(this.headerTitleElement)||e.Jm.remove(this.headerTitleElement,"click",this.navigateTitle),this.todayElement&&e.Jm.remove(this.todayElement,"click",this.todayButtonClick),this.previousIconHandler(!0),this.nextIconHandler(!0),this.keyboardModule.destroy(),this.element.removeAttribute("data-role"),(0,e.hX)(this.calendarEleCopy.getAttribute("tabindex"))?this.element.removeAttribute("tabindex"):this.element.setAttribute("tabindex",this.tabIndex)),this.element&&(this.element.innerHTML=""),this.todayElement=null,this.tableBodyElement=null,this.todayButtonEvent=null,this.renderDayCellArgs=null,this.headerElement=null,this.nextIcon=null,this.table=null,this.tableHeadElement=null,this.previousIcon=null,this.headerTitleElement=null,this.footer=null,this.contentElement=null,u.prototype.destroy.call(this)},s.prototype.title=function(t){var i=this.getViewNumber(this.currentView());this.effect=Ot,this.switchView(++i,t)},s.prototype.getViewNumber=function(t){return"Month"===t?0:"Year"===t?1:2},s.prototype.navigateTitle=function(t){t.preventDefault(),this.title(t)},s.prototype.previous=function(){this.effect="";var t=this.getViewNumber(this.currentView());switch(this.currentView()){case"Month":this.addMonths(this.currentDate,-1),this.switchView(t);break;case"Year":this.addYears(this.currentDate,-1),this.switchView(t);break;case"Decade":this.addYears(this.currentDate,-10),this.switchView(t)}},s.prototype.navigatePrevious=function(t){!e.Pw.isDevice&&t.preventDefault(),"Gregorian"===this.calendarMode?this.previous():this.islamicModule.islamicPrevious(),this.triggerNavigate(t)},s.prototype.next=function(){this.effect="";var t=this.getViewNumber(this.currentView());switch(this.currentView()){case"Month":this.addMonths(this.currentDate,1),this.switchView(t);break;case"Year":this.addYears(this.currentDate,1),this.switchView(t);break;case"Decade":this.addYears(this.currentDate,10),this.switchView(t)}},s.prototype.navigateNext=function(t){!e.Pw.isDevice&&t.preventDefault(),"Gregorian"===this.calendarMode?this.next():this.islamicModule.islamicNext(),this.triggerNavigate(t)},s.prototype.navigateTo=function(t,i,a){+i>=+this.min&&+i<=+this.max&&(this.currentDate=i),+i<=+this.min&&(this.currentDate=new Date(this.checkValue(this.min))),+i>=+this.max&&(this.currentDate=new Date(this.checkValue(this.max))),this.getViewNumber(this.depth)>=this.getViewNumber(t)&&(this.getViewNumber(this.depth)<=this.getViewNumber(this.start)||this.getViewNumber(this.depth)===this.getViewNumber(t))&&(t=this.depth),this.switchView(this.getViewNumber(t),null,null,a)},s.prototype.currentView=function(){return!(0,e.hX)(this.contentElement)&&this.contentElement.classList.contains(Qt)?"Year":!(0,e.hX)(this.contentElement)&&this.contentElement.classList.contains(ye)?"Decade":"Month"},s.prototype.getDateVal=function(t,i){return!(0,e.hX)(i)&&t.getDate()===i.getDate()&&t.getMonth()===i.getMonth()&&t.getFullYear()===i.getFullYear()},s.prototype.getCultureObjects=function(t,i){var a=(0,e.hX)(this.dayHeaderFormat)?null:".dates.calendars.gregorian.days.format."+this.dayHeaderFormat.toLowerCase(),n=(0,e.hX)(this.dayHeaderFormat)?null:".dates.calendars.islamic.days.format."+this.dayHeaderFormat.toLowerCase();return(0,e._W)("Gregorian"===this.calendarMode?"main."+this.locale+a:"main."+this.locale+n,t)},s.prototype.getWeek=function(t){var i=new Date(this.checkValue(t)).valueOf(),a=new Date(t.getFullYear(),0,1).valueOf();return Math.ceil((i-a+Gt)/Gt/7)},s.prototype.setStartDate=function(t,i){var a=t.getTimezoneOffset(),n=new Date(t.getTime()+i),l=n.getTimezoneOffset()-a;t.setTime(n.getTime()+6e4*l)},s.prototype.addMonths=function(t,i){if("Gregorian"===this.calendarMode){var a=t.getDate();t.setDate(1),t.setMonth(t.getMonth()+i),t.setDate(Math.min(a,this.getMaxDays(t)))}else{var n=this.islamicModule.getIslamicDate(t);this.currentDate=this.islamicModule.toGregorian(n.year,n.month+i,1)}},s.prototype.addYears=function(t,i){if("Gregorian"===this.calendarMode){var a=t.getDate();t.setDate(1),t.setFullYear(t.getFullYear()+i),t.setDate(Math.min(a,this.getMaxDays(t)))}else{var n=this.islamicModule.getIslamicDate(t);this.currentDate=this.islamicModule.toGregorian(n.year+i,n.month,1)}},s.prototype.getIdValue=function(t,i){var l={type:"dateTime",skeleton:"full",calendar:"Gregorian"===this.calendarMode?"gregorian":"islamic"},r=this.globalize.formatDate(new Date(parseInt(""+(t?t.currentTarget:i).getAttribute("id"),0)),l),h=this.globalize.parseDate(r,l),o=h.valueOf()-h.valueOf()%1e3;return new Date(o)},s.prototype.adjustLongHeaderSize=function(){(0,e.vy)([this.element],ke),"Wide"===this.dayHeaderFormat&&(0,e.iQ)(["calendar"===this.getModuleName()?this.element:this.calendarElement],ke)},s.prototype.selectDate=function(t,i,a,n,l){var r=a||t.currentTarget;if(this.isDateSelected=!1,"Decade"===this.currentView())this.setDateDecade(this.currentDate,i.getFullYear());else if("Year"===this.currentView())this.setDateYear(this.currentDate,i);else{if(n&&!this.checkPresentDate(i,l)){var h=this.copyValues(l);!(0,e.hX)(l)&&h.length>0?(h.push(new Date(this.checkValue(i))),this.setProperties({values:h},!0),this.setProperties({value:l[l.length-1]},!0)):this.setProperties({values:[new Date(this.checkValue(i))]},!0)}else this.setProperties({value:new Date(this.checkValue(i))},!0);this.currentDate=new Date(this.checkValue(i))}var o=(0,e.kp)(r,"."+bt);if((0,e.hX)(o)&&(o=this.tableBodyElement),!n&&!(0,e.hX)(o.querySelector("."+X))&&(0,e.vy)([o.querySelector("."+X)],X),!n&&!(0,e.hX)(o.querySelector("."+z))&&(0,e.vy)([o.querySelector("."+z)],z),!n&&!(0,e.hX)(o.querySelector("."+lt))&&(0,e.vy)([o.querySelector("."+lt)],lt),n){h=this.copyValues(l);for(var p=Array.prototype.slice.call(this.tableBodyElement.querySelectorAll("td")),c=0;c<p.length;c++){var f=o.querySelectorAll("td."+z)[0],g=o.querySelectorAll("td."+X)[0];p[c]===f&&(0,e.vy)([p[c]],z),p[c]===g&&!this.checkPresentDate(new Date(parseInt(g.getAttribute("id").split("_")[0],10)),l)&&(0,e.vy)([p[c]],X)}if(r.classList.contains(X)){(0,e.vy)([r],X);for(var d=0;d<h.length;d++){var b={format:null,type:"date",skeleton:"short",calendar:"Gregorian"===this.calendarMode?"gregorian":"islamic"};this.globalize.formatDate(i,b)===this.globalize.formatDate(h[d],b)&&(c=h.indexOf(h[d]),h.splice(c,1),(0,e.iQ)([r],z))}this.setProperties({values:h},!0)}else(0,e.iQ)([r],X)}else(0,e.iQ)([r],X);this.isDateSelected=!0},s.prototype.checkPresentDate=function(t,i){var a=!1;if(!(0,e.hX)(i))for(var n=0;n<i.length;n++){var l="Gregorian"===this.calendarMode?"gregorian":"islamic";this.globalize.formatDate(t,{format:null,type:"date",skeleton:"short",calendar:l})===this.globalize.formatDate(i[n],{format:null,type:"date",skeleton:"short",calendar:l})&&(a=!0)}return a},s.prototype.setAriaActiveDescendant=function(){(0,e.uK)(this.table,{"aria-activedescendant":""+this.setActiveDescendant()})},s.prototype.previousIconHandler=function(t){t?(0,e.hX)(this.previousIcon)||(e.Jm.remove(this.previousIcon,"click",this.navigatePreviousHandler),(0,e.iQ)([this.previousIcon],""+S),(0,e.iQ)([this.previousIcon],""+et),this.previousIcon.setAttribute("aria-disabled","true")):(e.Jm.add(this.previousIcon,"click",this.navigatePreviousHandler),(0,e.vy)([this.previousIcon],""+S),(0,e.vy)([this.previousIcon],""+et),this.previousIcon.setAttribute("aria-disabled","false"))},s.prototype.renderDayCellEvent=function(t){(0,e.X$)(this.renderDayCellArgs,{name:"renderDayCell"}),this.trigger("renderDayCell",t)},s.prototype.navigatedEvent=function(t){(0,e.X$)(this.navigatedArgs,{name:"navigated",event:t}),this.trigger("navigated",this.navigatedArgs)},s.prototype.triggerNavigate=function(t){this.navigatedArgs={view:this.currentView(),date:this.currentDate},this.navigatedEvent(t)},s.prototype.nextIconHandler=function(t){t?(0,e.hX)(this.previousIcon)||(e.Jm.remove(this.nextIcon,"click",this.navigateNextHandler),(0,e.iQ)([this.nextIcon],S),(0,e.iQ)([this.nextIcon],et),this.nextIcon.setAttribute("aria-disabled","true")):(e.Jm.add(this.nextIcon,"click",this.navigateNextHandler),(0,e.vy)([this.nextIcon],S),(0,e.vy)([this.nextIcon],et),this.nextIcon.setAttribute("aria-disabled","false"))},s.prototype.compare=function(t,i,a){var l,r,n=i.getFullYear();return l=n,r=0,a&&(l=(n-=n%a)-n%a+a-1),t.getFullYear()>l?r=1:t.getFullYear()<n&&(r=-1),r},s.prototype.isMinMaxRange=function(t){return+t>=+this.min&&+t<=+this.max},s.prototype.isMonthYearRange=function(t){if("Gregorian"===this.calendarMode)return t.getMonth()>=this.min.getMonth()&&t.getFullYear()>=this.min.getFullYear()&&t.getMonth()<=this.max.getMonth()&&t.getFullYear()<=this.max.getFullYear();var i=this.islamicModule.getIslamicDate(t);return i.month>=this.islamicModule.getIslamicDate(new Date(1944,1,18)).month&&i.year>=this.islamicModule.getIslamicDate(new Date(1944,1,18)).year&&i.month<=this.islamicModule.getIslamicDate(new Date(2069,1,16)).month&&i.year<=this.islamicModule.getIslamicDate(new Date(2069,1,16)).year},s.prototype.compareYear=function(t,i){return this.compare(t,i,0)},s.prototype.compareDecade=function(t,i){return this.compare(t,i,10)},s.prototype.shiftArray=function(t,i){return t.slice(i).concat(t.slice(0,i))},s.prototype.addDay=function(t,i,a,n,l){var r=i,h=new Date(+t);if(!(0,e.hX)(this.tableBodyElement)&&!(0,e.hX)(a)){for(;this.findNextTD(new Date(+t),r,n,l);)r+=i;var o=new Date(h.setDate(h.getDate()+r));r=+o>+n||+o<+l?r===i?i-i:i:r}t.setDate(t.getDate()+r)},s.prototype.findNextTD=function(t,i,a,n){var l=new Date(t.setDate(t.getDate()+i)),r=[],h=!1;if(r=(!(0,e.hX)(l)&&l.getMonth())===(!(0,e.hX)(this.currentDate)&&this.currentDate.getMonth())?("Gregorian"===this.calendarMode?this.renderDays(l):this.islamicModule.islamicRenderDays(this.currentDate,l)).filter(function(c){return c.classList.contains(S)}):this.tableBodyElement.querySelectorAll("td."+S),+l<=+a&&+l>=+n&&r.length)for(var p=0;p<r.length&&!(h=+l==+new Date(parseInt(r[p].id,0)));p++);return h},s.prototype.getMaxDays=function(t){var i,a=new Date(this.checkValue(t));i=28;for(var n=a.getMonth();a.getMonth()===n;)++i,a.setDate(i);return i-1},s.prototype.setDateDecade=function(t,i){t.setFullYear(i),this.setProperties({value:new Date(this.checkValue(t))},!0)},s.prototype.setDateYear=function(t,i){t.setFullYear(i.getFullYear(),i.getMonth(),t.getDate()),i.getMonth()!==t.getMonth()&&(t.setDate(0),this.currentDate=new Date(this.checkValue(i))),this.setProperties({value:new Date(this.checkValue(t))},!0)},s.prototype.compareMonth=function(t,i){return t.getFullYear()>i.getFullYear()?1:t.getFullYear()<i.getFullYear()?-1:t.getMonth()===i.getMonth()?0:t.getMonth()>i.getMonth()?1:-1},s.prototype.checkValue=function(t){return t instanceof Date?t.toUTCString():""+t},s.prototype.checkView=function(){"Decade"!==this.start&&"Year"!==this.start&&this.setProperties({start:"Month"},!0),"Decade"!==this.depth&&"Year"!==this.depth&&this.setProperties({depth:"Month"},!0),this.getViewNumber(this.depth)>this.getViewNumber(this.start)&&this.setProperties({depth:"Month"},!0)},s.prototype.getDate=function(t,i){return i&&(t=new Date(t.toLocaleString("en-US",{timeZone:i}))),t},x([(0,e.mA)(new Date(1900,0,1))],s.prototype,"min",void 0),x([(0,e.mA)(!0)],s.prototype,"enabled",void 0),x([(0,e.mA)(null)],s.prototype,"cssClass",void 0),x([(0,e.mA)(new Date(2099,11,31))],s.prototype,"max",void 0),x([(0,e.mA)(null)],s.prototype,"firstDayOfWeek",void 0),x([(0,e.mA)("Gregorian")],s.prototype,"calendarMode",void 0),x([(0,e.mA)("Month")],s.prototype,"start",void 0),x([(0,e.mA)("Month")],s.prototype,"depth",void 0),x([(0,e.mA)(!1)],s.prototype,"weekNumber",void 0),x([(0,e.mA)("FirstDay")],s.prototype,"weekRule",void 0),x([(0,e.mA)(!0)],s.prototype,"showTodayButton",void 0),x([(0,e.mA)("Short")],s.prototype,"dayHeaderFormat",void 0),x([(0,e.mA)(!1)],s.prototype,"enablePersistence",void 0),x([(0,e.mA)(null)],s.prototype,"keyConfigs",void 0),x([(0,e.mA)(null)],s.prototype,"serverTimezoneOffset",void 0),x([(0,e.Jh)()],s.prototype,"created",void 0),x([(0,e.Jh)()],s.prototype,"destroyed",void 0),x([(0,e.Jh)()],s.prototype,"navigated",void 0),x([(0,e.Jh)()],s.prototype,"renderDayCell",void 0),x([e.kc],s)}(e.uA),Ve=function(u){function s(t,i){return u.call(this,t,i)||this}return fe(s,u),s.prototype.render=function(){if("Islamic"===this.calendarMode&&void 0===this.islamicModule&&(0,e.$8)("Requires the injectable Islamic modules to render Calendar in Islamic mode"),this.isMultiSelection&&"object"==typeof this.values&&!(0,e.hX)(this.values)&&this.values.length>0){for(var t=[],i=[],a=0;a<this.values.length;a++)-1===t.indexOf(+this.values[a])&&(t.push(+this.values[a]),i.push(this.values[a]));this.setProperties({values:i},!0);for(var n=0;n<this.values.length;n++)if(!this.checkDateValue(this.values[n]))if("string"==typeof this.values[n]&&this.checkDateValue(new Date(this.checkValue(this.values[n])))){var l=new Date(this.checkValue(this.values[n]));this.values.splice(n,1),this.values.splice(n,0,l)}else this.values.splice(n,1);this.setProperties({value:this.values[this.values.length-1]},!0),this.previousValues=this.values.length}if(this.validateDate(),this.minMaxUpdate(),"calendar"===this.getModuleName()&&(this.setEnable(this.enabled),this.setClass(this.cssClass)),u.prototype.render.call(this),"calendar"===this.getModuleName()){var r=(0,e.kp)(this.element,"form");r&&e.Jm.add(r,"reset",this.formResetHandler.bind(this)),this.setTimeZone(this.serverTimezoneOffset)}this.renderComplete()},s.prototype.setEnable=function(t){t?(0,e.vy)([this.element],S):(0,e.iQ)([this.element],S)},s.prototype.setClass=function(t,i){(0,e.hX)(i)||(i=i.replace(/\s+/g," ").trim()),(0,e.hX)(t)||(t=t.replace(/\s+/g," ").trim()),!(0,e.hX)(i)&&""!==i&&(0,e.vy)([this.element],i.split(" ")),(0,e.hX)(t)||(0,e.iQ)([this.element],t.split(" "))},s.prototype.isDayLightSaving=function(){var t=new Date(this.value.getFullYear(),6,1).getTimezoneOffset(),i=new Date(this.value.getFullYear(),0,1).getTimezoneOffset();return this.value.getTimezoneOffset()<Math.max(i,t)},s.prototype.setTimeZone=function(t){if(!(0,e.hX)(this.serverTimezoneOffset)&&this.value){var n=t+(new Date).getTimezoneOffset()/60;n=this.isDayLightSaving()?n--:n,this.value=new Date(this.value.getTime()+60*n*60*1e3)}},s.prototype.formResetHandler=function(){this.setProperties({value:null},!0)},s.prototype.validateDate=function(){"string"==typeof this.value&&this.setProperties({value:this.checkDateValue(new Date(this.checkValue(this.value)))},!0),u.prototype.validateDate.call(this,this.value),!(0,e.hX)(this.value)&&this.min<=this.max&&this.value>=this.min&&this.value<=this.max&&(this.currentDate=new Date(this.checkValue(this.value))),isNaN(+this.value)&&this.setProperties({value:null},!0)},s.prototype.minMaxUpdate=function(){"calendar"===this.getModuleName()&&(!(0,e.hX)(this.value)&&this.value<=this.min&&this.min<=this.max?(this.setProperties({value:this.min},!0),this.changedArgs={value:this.value}):!(0,e.hX)(this.value)&&this.value>=this.max&&this.min<=this.max&&(this.setProperties({value:this.max},!0),this.changedArgs={value:this.value})),"calendar"===this.getModuleName()||(0,e.hX)(this.value)?u.prototype.minMaxUpdate.call(this,this.value):!(0,e.hX)(this.value)&&this.value<this.min&&this.min<=this.max?u.prototype.minMaxUpdate.call(this,this.min):!(0,e.hX)(this.value)&&this.value>this.max&&this.min<=this.max&&u.prototype.minMaxUpdate.call(this,this.max)},s.prototype.generateTodayVal=function(t){var i=new Date;return(0,e.hX)(this.timezone)||(i=u.prototype.getDate.call(this,i,this.timezone)),t&&(0,e.hX)(this.timezone)?(i.setHours(t.getHours()),i.setMinutes(t.getMinutes()),i.setSeconds(t.getSeconds()),i.setMilliseconds(t.getMilliseconds())):i=new Date(i.getFullYear(),i.getMonth(),i.getDate(),0,0,0,0),i},s.prototype.todayButtonClick=function(t){if(this.showTodayButton){var i=this.generateTodayVal(this.value);if(this.setProperties({value:i},!0),this.isTodayClicked=!0,this.todayButtonEvent=t,this.isMultiSelection){var a=this.copyValues(this.values);u.prototype.checkPresentDate.call(this,i,this.values)||(a.push(i),this.setProperties({values:a}))}u.prototype.todayButtonClick.call(this,t,new Date(+this.value))}},s.prototype.keyActionHandle=function(t){u.prototype.keyActionHandle.call(this,t,this.value,this.isMultiSelection)},s.prototype.preRender=function(){var t=this;this.changeHandler=function(i){t.triggerChange(i)},this.checkView(),u.prototype.preRender.call(this,this.value)},s.prototype.createContent=function(){this.previousDate=this.value,this.previousDateTime=this.value,u.prototype.createContent.call(this)},s.prototype.minMaxDate=function(t){return u.prototype.minMaxDate.call(this,t)},s.prototype.renderMonths=function(t,i,a){u.prototype.renderMonths.call(this,t,this.value,a)},s.prototype.renderDays=function(t,i,a,n,l,r){var h=u.prototype.renderDays.call(this,t,this.value,this.isMultiSelection,this.values,l,r);return this.isMultiSelection&&u.prototype.validateValues.call(this,this.isMultiSelection,this.values),h},s.prototype.renderYears=function(t){"Gregorian"===this.calendarMode?u.prototype.renderYears.call(this,t,this.value):this.islamicModule.islamicRenderYears(t,this.value)},s.prototype.renderDecades=function(t){"Gregorian"===this.calendarMode?u.prototype.renderDecades.call(this,t,this.value):this.islamicModule.islamicRenderDecade(t,this.value)},s.prototype.renderTemplate=function(t,i,a,n){"Gregorian"===this.calendarMode?u.prototype.renderTemplate.call(this,t,i,a,n,this.value):this.islamicModule.islamicRenderTemplate(t,i,a,n,this.value),this.changedArgs={value:this.value,values:this.values},n&&"click"===n.type&&n.currentTarget.classList.contains(U)?this.changeHandler(n):this.changeHandler()},s.prototype.clickHandler=function(t){var i=t.currentTarget;if(this.isPopupClicked=!0,i.classList.contains(U))if(this.isMultiSelection){var a=this.copyValues(this.values);-1===a.toString().indexOf(this.getIdValue(t,null).toString())?(a.push(this.getIdValue(t,null)),this.setProperties({values:a},!0),this.setProperties({value:this.values[this.values.length-1]},!0)):this.previousDates=!0}else this.setProperties({value:this.getIdValue(t,null)},!0);var n=this.currentView();u.prototype.clickHandler.call(this,t,this.value),this.isMultiSelection&&this.currentDate!==this.value&&!(0,e.hX)(this.tableBodyElement.querySelectorAll("."+z)[0])&&"Year"===n&&this.tableBodyElement.querySelectorAll("."+z)[0].classList.remove(z)},s.prototype.switchView=function(t,i,a,n){u.prototype.switchView.call(this,t,i,this.isMultiSelection,n)},s.prototype.getModuleName=function(){return u.prototype.getModuleName.call(this),"calendar"},s.prototype.getPersistData=function(){return u.prototype.getPersistData.call(this),this.addOnPersist(["value","values"])},s.prototype.onPropertyChanged=function(t,i){this.effect="",this.rangeValidation(this.min,this.max);for(var a=0,n=Object.keys(t);a<n.length;a++)switch(n[a]){case"value":this.isDateSelected&&("string"==typeof t.value?this.setProperties({value:new Date(this.checkValue(t.value))},!0):t.value=new Date(this.checkValue(t.value)),isNaN(+this.value)&&this.setProperties({value:i.value},!0),this.update());break;case"values":if(this.isDateSelected){if("string"==typeof t.values||"number"==typeof t.values)this.setProperties({values:null},!0);else{for(var r=this.copyValues(this.values),h=0;h<r.length;h++){var o=r[h];this.checkDateValue(o)&&!u.prototype.checkPresentDate.call(this,o,r)&&r.push(o)}this.setProperties({values:r},!0),this.values.length>0&&this.setProperties({value:t.values[t.values.length-1]},!0)}this.validateValues(this.isMultiSelection,this.values),this.update()}break;case"isMultiSelection":this.isDateSelected&&(this.setProperties({isMultiSelection:t.isMultiSelection},!0),this.update());break;case"enabled":this.setEnable(this.enabled);break;case"cssClass":"calendar"===this.getModuleName()&&this.setClass(t.cssClass,i.cssClass);break;default:u.prototype.onPropertyChanged.call(this,t,i,this.isMultiSelection,this.values)}this.preventChange=this.isAngular&&this.preventChange?!this.preventChange:this.preventChange},s.prototype.destroy=function(){if(u.prototype.destroy.call(this),"calendar"===this.getModuleName()){this.changedArgs=null;var t=(0,e.kp)(this.element,"form");t&&e.Jm.remove(t,"reset",this.formResetHandler.bind(this))}},s.prototype.navigateTo=function(t,i,a){this.minMaxUpdate(),u.prototype.navigateTo.call(this,t,i,a)},s.prototype.currentView=function(){return u.prototype.currentView.call(this)},s.prototype.addDate=function(t){if("string"!=typeof t&&"number"!=typeof t){var i=this.copyValues(this.values);if("object"==typeof t&&t.length>0)for(var a=t,n=0;n<a.length;n++)this.checkDateValue(a[n])&&!u.prototype.checkPresentDate.call(this,a[n],i)&&(!(0,e.hX)(i)&&i.length>0?i.push(a[n]):i=[new Date(+a[n])]);else this.checkDateValue(t)&&!u.prototype.checkPresentDate.call(this,t,i)&&(!(0,e.hX)(i)&&i.length>0?i.push(t):i=[new Date(+t)]);this.setProperties({values:i},!0),this.isMultiSelection&&this.setProperties({value:this.values[this.values.length-1]},!0),this.validateValues(this.isMultiSelection,i),this.update(),this.changedArgs={value:this.value,values:this.values},this.changeHandler()}},s.prototype.removeDate=function(t){if("string"!=typeof t&&"number"!=typeof t&&!(0,e.hX)(this.values)&&this.values.length>0){var i=this.copyValues(this.values);if("object"==typeof t&&t.length>0)for(var a=t,n=0;n<a.length;n++)for(var l=0;l<i.length;l++)+i[l]==+a[n]&&i.splice(l,1);else for(l=0;l<i.length;l++)+i[l]==+t&&i.splice(l,1);this.setProperties({values:i},!1),this.update(),this.isMultiSelection&&this.setProperties({value:this.values[this.values.length-1]},!0),this.changedArgs={value:this.value,values:this.values},this.changeHandler()}},s.prototype.setTodayDate=function(t){var i=new Date(+t);this.setProperties({value:i},!0),u.prototype.todayButtonClick.call(this,null,i,!0)},s.prototype.update=function(){this.validateDate(),this.minMaxUpdate(),u.prototype.setValueUpdate.call(this)},s.prototype.selectDate=function(t,i,a){u.prototype.selectDate.call(this,t,i,a,this.isMultiSelection,this.values),this.isMultiSelection&&!(0,e.hX)(this.values)&&this.values.length>0&&this.setProperties({value:this.values[this.values.length-1]},!0),this.changedArgs={value:this.value,values:this.values},this.changeHandler(t)},s.prototype.changeEvent=function(t){((this.value&&this.value.valueOf())!==(this.previousDate&&+this.previousDate.valueOf())||this.isMultiSelection)&&(this.isAngular&&this.preventChange?this.preventChange=!1:this.trigger("change",this.changedArgs),this.previousDate=new Date(+this.value))},s.prototype.triggerChange=function(t){!(0,e.hX)(this.todayButtonEvent)&&this.isTodayClicked&&(t=this.todayButtonEvent,this.isTodayClicked=!1),this.changedArgs.event=t||null,this.changedArgs.isInteracted=!(0,e.hX)(t),(0,e.hX)(this.value)||this.setProperties({value:this.value},!0),this.isMultiSelection||+this.value===Number.NaN||((0,e.hX)(this.value)||(0,e.hX)(this.previousDate))&&(null!==this.previousDate||isNaN(+this.value))?!(0,e.hX)(this.values)&&this.previousValues!==this.values.length&&(this.changeEvent(t),this.previousValues=this.values.length):this.changeEvent(t)},x([(0,e.mA)(null)],s.prototype,"value",void 0),x([(0,e.mA)(null)],s.prototype,"values",void 0),x([(0,e.mA)(!1)],s.prototype,"isMultiSelection",void 0),x([(0,e.Jh)()],s.prototype,"change",void 0),x([e.kc],s)}(Me),ht="e-other-month",it="e-disabled",Ae="e-overlay",Et="e-selected",Zt="e-focused-date",Nt="e-month-hide",Yi=function(){function u(s){this.calendarInstance=s}return u.prototype.getModuleName=function(){return"islamic"},u.prototype.islamicTitleUpdate=function(s,t){var i=new e.DL(this.calendarInstance.locale);switch(t){case"days":this.calendarInstance.headerTitleElement.textContent=i.formatDate(s,{type:"dateTime",format:"MMMMyyyy",calendar:"islamic"});break;case"months":this.calendarInstance.headerTitleElement.textContent=i.formatDate(s,{type:"dateTime",format:"yyyy",calendar:"islamic"})}},u.prototype.islamicRenderDays=function(s,t,i,a){var h,n=[],r=new Date(this.islamicInValue(s));this.islamicTitleUpdate(s,"days");var o=this.getIslamicDate(r),p=this.toGregorian(o.year,o.month,1),c=o.month;for(r=p;r.getDay()!==this.calendarInstance.firstDayOfWeek;)this.calendarInstance.setStartDate(r,-864e5);for(var f=0;f<42;++f){var g=this.calendarInstance.createElement("td",{className:"e-cell"}),d=this.calendarInstance.createElement("span");f%7==0&&this.calendarInstance.weekNumber&&(d.textContent=""+this.calendarInstance.getWeek(r),g.appendChild(d),(0,e.iQ)([g],"e-week-number"),n.push(g)),h=new Date(+r),r=this.calendarInstance.minMaxDate(r);var y={type:"dateTime",skeleton:"full",calendar:"islamic"},b=this.calendarInstance.globalize.parseDate(this.calendarInstance.globalize.formatDate(r,y),y),m=this.islamicDayCell(r),A=this.calendarInstance.globalize.formatDate(r,{type:"date",skeleton:"full",calendar:"islamic"}),M=this.calendarInstance.createElement("span");M.textContent=this.calendarInstance.globalize.formatDate(r,{type:"date",skeleton:"d",calendar:"islamic"});var V=this.calendarInstance.min>r||this.calendarInstance.max<r;V?((0,e.iQ)([m],it),(0,e.iQ)([m],Ae)):M.setAttribute("title",""+A),c!==this.getIslamicDate(r).month&&(0,e.iQ)([m],ht),(0===r.getDay()||6===r.getDay())&&(0,e.iQ)([m],"e-weekend"),m.appendChild(M),this.calendarInstance.renderDayCellArgs={date:r,isDisabled:!1,element:m,isOutOfRange:V};var w=this.calendarInstance.renderDayCellArgs;if(this.calendarInstance.renderDayCellEvent(w),w.isDisabled)if(this.calendarInstance.isMultiSelection){if(!(0,e.hX)(this.calendarInstance.values)&&this.calendarInstance.values.length>0)for(var j=0;j<a.length;j++)+new Date(this.calendarInstance.globalize.formatDate(w.date,{type:"date",skeleton:"yMd",calendar:"islamic"}))==+new Date(this.calendarInstance.globalize.formatDate(this.calendarInstance.values[j],{type:"date",skeleton:"yMd",calendar:"islamic"}))&&(this.calendarInstance.values.splice(j,1),j=-1)}else t&&+t==+w.date&&this.calendarInstance.setProperties({value:null},!0);this.calendarInstance.renderDayCellArgs.isDisabled&&!m.classList.contains(Et)&&((0,e.iQ)([m],it),(0,e.iQ)([m],Ae),+this.calendarInstance.renderDayCellArgs.date==+this.calendarInstance.todayDate&&(this.calendarInstance.todayDisabled=!0));var J=m.classList.contains(ht),q=m.classList.contains(it);if(q||e.Jm.add(m,"click",this.calendarInstance.clickHandler,this.calendarInstance),!this.calendarInstance.isMultiSelection||(0,e.hX)(this.calendarInstance.values)||J||q)J||q||!this.calendarInstance.getDateVal(r,t)?this.calendarInstance.updateFocus(J,q,r,m,s):(0,e.iQ)([m],Et);else{for(var vt=0;vt<this.calendarInstance.values.length;vt++)this.calendarInstance.globalize.formatDate(r,{type:"date",skeleton:"short",calendar:"islamic"})===this.calendarInstance.globalize.formatDate(this.calendarInstance.values[vt],{type:"date",skeleton:"short",calendar:"islamic"})&&this.calendarInstance.getDateVal(r,this.calendarInstance.values[vt])?(0,e.iQ)([m],Et):this.calendarInstance.updateFocus(J,q,r,m,s);this.calendarInstance.values.length<=0&&this.calendarInstance.updateFocus(J,q,r,m,s)}b.getDate()===(new Date).getDate()&&b.getMonth()===(new Date).getMonth()&&b.getFullYear()===(new Date).getFullYear()&&(0,e.iQ)([m],"e-today"),r=new Date(+h),n.push(this.calendarInstance.renderDayCellArgs.element),this.calendarInstance.addDay(r,1,null,this.calendarInstance.max,this.calendarInstance.min)}return n},u.prototype.islamicIconHandler=function(){new Date(this.islamicInValue(this.calendarInstance.currentDate)).setDate(1);var s=new Date(this.islamicInValue(this.calendarInstance.currentDate));switch(this.calendarInstance.currentView()){case"Month":var t=this.islamicCompareMonth(s,this.calendarInstance.min)<1,i=this.islamicCompareMonth(s,this.calendarInstance.max)>-1;this.calendarInstance.previousIconHandler(t),this.calendarInstance.nextIconHandler(i);break;case"Year":var a=this.hijriCompareYear(s,this.calendarInstance.min)<1,n=this.hijriCompareYear(s,this.calendarInstance.max)>-1;this.calendarInstance.previousIconHandler(a),this.calendarInstance.nextIconHandler(n);break;case"Decade":var r=e.cg.toGregorian(1361,1,1),h=this.hijriCompareDecade(s,this.calendarInstance.min)<1,o=this.hijriCompareDecade(s,this.calendarInstance.max)>-1;h=e.cg.toGregorian(this.calendarInstance.headerTitleElement.textContent.split("-")[0].trim(),1,1).getFullYear()===r.getFullYear()||h,this.calendarInstance.previousIconHandler(h),this.calendarInstance.nextIconHandler(o)}},u.prototype.islamicNext=function(){this.calendarInstance.effect="";var s=this.calendarInstance.getViewNumber(this.calendarInstance.currentView()),t=this.getIslamicDate(this.calendarInstance.currentDate);switch(this.calendarInstance.currentView()){case"Year":this.calendarInstance.currentDate=this.toGregorian(t.year+1,t.month,1),this.calendarInstance.switchView(s);break;case"Month":this.calendarInstance.currentDate=this.toGregorian(t.year,t.month+1,1),this.calendarInstance.switchView(s);break;case"Decade":this.calendarInstance.nextIconClicked=!0,t.year-this.calendarInstance.headerElement.textContent.split("-")[0].trim()==1&&(t.year=t.year-this.calendarInstance.headerElement.textContent.split("-")[0].trim()==1?t.year+1:t.year),this.calendarInstance.currentDate=this.toGregorian(t.year+10,t.month,1),this.calendarInstance.switchView(s)}},u.prototype.islamicPrevious=function(){var s=this.calendarInstance.getViewNumber(this.calendarInstance.currentView());this.calendarInstance.effect="";var t=this.getIslamicDate(this.calendarInstance.currentDate);switch(this.calendarInstance.currentView()){case"Month":this.calendarInstance.currentDate=this.toGregorian(t.year,t.month-1,1),this.calendarInstance.switchView(s);break;case"Year":this.calendarInstance.currentDate=this.toGregorian(t.year-1,t.month,1),this.calendarInstance.switchView(s);break;case"Decade":this.calendarInstance.previousIconClicked=!0,this.calendarInstance.currentDate=this.toGregorian(t.year-10,t.month-1,1),this.calendarInstance.switchView(s)}},u.prototype.islamicRenderYears=function(s,t){this.calendarInstance.removeTableHeadElement();var a=[],n=(0,e.hX)(t),r=new Date(this.islamicInValue(this.calendarInstance.currentDate)),h=this.getIslamicDate(r);r=e.cg.toGregorian(h.year,1,1);var p=h.month,c=h.year,f=h.year,g=this.getIslamicDate(this.calendarInstance.min).year,d=this.getIslamicDate(this.calendarInstance.min).month,y=this.getIslamicDate(this.calendarInstance.max).year,b=this.getIslamicDate(this.calendarInstance.max).month;this.islamicTitleUpdate(this.calendarInstance.currentDate,"months");for(var m=1;m<=12;++m){var A=this.getIslamicDate(r),M=e.cg.toGregorian(A.year,m,1),V=this.islamicDayCell(r=M),W=this.calendarInstance.createElement("span"),w=t&&this.getIslamicDate(t).month===this.getIslamicDate(r).month,j=t&&this.getIslamicDate(t).year===c&&w;W.textContent=this.calendarInstance.globalize.formatDate(r,{type:"dateTime",format:"MMM",calendar:"islamic"}),this.calendarInstance.min&&(f<g||m<d&&f===g)||this.calendarInstance.max&&(f>y||m>b&&f>=y)?(0,e.iQ)([V],it):!n&&j?(0,e.iQ)([V],Et):this.getIslamicDate(r).month===p&&this.getIslamicDate(this.calendarInstance.currentDate).month===p&&(0,e.iQ)([V],Zt),V.classList.contains(it)||e.Jm.add(V,"click",this.calendarInstance.clickHandler,this.calendarInstance),V.appendChild(W),a.push(V)}this.islamicRenderTemplate(a,4,"e-year",s,t)},u.prototype.islamicRenderDecade=function(s,t){this.calendarInstance.removeTableHeadElement();var n=[],l=new Date(this.islamicInValue(this.calendarInstance.currentDate)),r=this.getIslamicDate(l),o=(l=e.cg.toGregorian(r.year,1,1)).getFullYear(),p=new Date(this.islamicInValue(o-o%10)),c=new Date(this.islamicInValue(o-o%10+9)),f=p.getFullYear(),g=c.getFullYear(),d=this.calendarInstance.globalize.formatDate(p,{type:"dateTime",format:"y",calendar:"islamic"}),y=this.calendarInstance.globalize.formatDate(c,{type:"dateTime",format:"y",calendar:"islamic"});"ar"===this.calendarInstance.locale&&(d=Number(d.replace(/[\u0660\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669]/g,function(q){return String.fromCharCode(q.charCodeAt(0)-1632+48)})),y=Number(y.replace(/[\u0660\u0661\u0662\u0663\u0664\u0665\u0666\u0667\u0668\u0669]/g,function(q){return String.fromCharCode(q.charCodeAt(0)-1632+48)})));var b=this.calendarInstance.headerElement.textContent.split("-");if((!(0,e.hX)(s)&&b[0]!==d&&"home"===s.action||!(0,e.hX)(s)&&"keydown"===s.type&&"end"===s.action)&&(d=this.calendarInstance.headerElement.textContent.split("-")[0].trim(),y=this.calendarInstance.headerElement.textContent.split("-")[1].trim()),this.calendarInstance.islamicPreviousHeader&&(d=this.calendarInstance.islamicPreviousHeader.split("-")[0].trim(),y=this.calendarInstance.islamicPreviousHeader.split("-")[1].trim(),this.calendarInstance.islamicPreviousHeader=null),this.calendarInstance.previousIconClicked){var m=0;for(m=0;m<=b.length;m++)((y=y-b[m]==2||b[m]-y==2?(parseInt(y,10)+1).toString():y-b[m]==3||b[m]-y==3?(parseInt(y,10)+2).toString():y-b[m]==4||b[m]-y==4?(parseInt(y,10)+3).toString():y-b[m]==5||b[m]-y==5?(parseInt(y,10)+4).toString():y)-b[m]==0||b[m]-y==0)&&(y=(parseInt(y,10)-1).toString());(y-b[m]==8||b[m]-y==8)&&(y=(parseInt(y,10)-9).toString(),d=(parseInt(y,10)-9).toString()),(y-b[m]==7||b[m]-y==7)&&(y=(parseInt(y,10)-8).toString(),d=(parseInt(y,10)-9).toString()),y-(d=y-d==10?(parseInt(d,10)+1).toString():y-d==11?(parseInt(d,10)+2).toString():y-d==12?(parseInt(d,10)+3).toString():d)==8&&(d=(parseInt(d,10)-1).toString())}if(this.calendarInstance.nextIconClicked){for(m=0;m<=b.length;m++)(d-b[m]==0||b[m]-d==0)&&(d=(parseInt(d,10)+1).toString()),d-b[m]==2&&d>b[m].trim()&&(d=(parseInt(d,10)-1).toString()),b[m]-d==1&&d<b[m].trim()&&(d=(parseInt(d,10)+2).toString());d-this.calendarInstance.headerTitleElement.textContent.split("-")[1].trim()>1&&(d=(parseInt(this.calendarInstance.headerTitleElement.textContent.split("-")[1].trim(),10)+1).toString(),y=(parseInt(d,10)+9).toString()),y=(y=y-d==10?(parseInt(y,10)-1).toString():y)-d==7?(parseInt(y,10)+2).toString():y-d==8?(parseInt(y,10)+1).toString():y}if("ar"===this.calendarInstance.locale){var A=this.calendarInstance.globalize.formatDate(p,{type:"dateTime",format:"y",calendar:"islamic"}),M=this.calendarInstance.globalize.formatDate(c,{type:"dateTime",format:"y",calendar:"islamic"});this.calendarInstance.headerTitleElement.textContent=A+" - "+M}else this.calendarInstance.headerTitleElement.textContent=d+" - "+y;this.calendarInstance.nextIconClicked=this.calendarInstance.previousIconClicked=!1;var V=(parseInt(d,10)-2).toString();f=Math.round(.97*parseInt(d,10)+622),g=Math.round(.97*parseInt(y,10)+622);for(var W=Math.round(.97*parseInt(V,10)+622),w=1;w<=12;++w){var j=W+w;if(l.setFullYear(j),l.setDate(1),l.setMonth(0),this.getIslamicDate(l).year-r.year>1&&(l.setMonth(1),w-=1,l.setFullYear(l.getFullYear()-1)),r=this.getIslamicDate(l),l=e.cg.toGregorian(r.year,1,1),r.year===parseInt(d,10)-1||r.year>=d&&r.year<=g||r.year===parseInt(y,10)+1){var H=this.islamicDayCell(l);(0,e.uK)(H,{role:"gridcell"});var J=this.calendarInstance.createElement("span");J.textContent=this.calendarInstance.globalize.formatDate(l,{type:"dateTime",format:"y",calendar:"islamic"}),r.year===parseInt(d,10)-1||j<f||j>g&&r.year!==parseInt(y,10)?(0,e.iQ)([H],ht):j<new Date(this.islamicInValue(this.calendarInstance.min)).getFullYear()||j>new Date(this.islamicInValue(this.calendarInstance.max)).getFullYear()?(0,e.iQ)([H],it):(0,e.hX)(t)||this.getIslamicDate(l).year!==this.getIslamicDate(t).year?l.getFullYear()===this.calendarInstance.currentDate.getFullYear()&&!H.classList.contains(it)&&(0,e.iQ)([H],Zt):(0,e.iQ)([H],Et),H.classList.contains(it)||e.Jm.add(H,"click",this.calendarInstance.clickHandler,this.calendarInstance),H.appendChild(J),(!(0,e.hX)(s)&&"home"===s.action&&r.year.toString()===d||!(0,e.hX)(s)&&"end"===s.action&&r.year.toString()===y)&&(0,e.iQ)([H],Zt),n.push(H)}}this.islamicRenderTemplate(n,4,"e-decade",s,t)},u.prototype.islamicDayCell=function(s){var t={skeleton:"full",type:"dateTime",calendar:"islamic"},i=this.calendarInstance.globalize.formatDate(s,t),n=this.calendarInstance.globalize.parseDate(i,t).valueOf(),l={className:"e-cell",attrs:{id:""+(0,e.Lz)(""+n),"aria-selected":"false",role:"gridcell"}};return this.calendarInstance.createElement("td",l)},u.prototype.islamicRenderTemplate=function(s,t,i,a,n){var r,l=this.calendarInstance.getViewNumber(this.calendarInstance.currentView());this.calendarInstance.tableBodyElement=this.calendarInstance.createElement("tbody"),this.calendarInstance.table.appendChild(this.calendarInstance.tableBodyElement),(0,e.vy)([this.calendarInstance.contentElement,this.calendarInstance.headerElement],["e-month","e-decade","e-year"]),(0,e.iQ)([this.calendarInstance.contentElement,this.calendarInstance.headerElement],[i]);for(var c=t,f=0,g=0;g<Math.round(s.length/t);++g){for(r=this.calendarInstance.createElement("tr",{attrs:{role:"row"}}),f=0+f;f<c;f++)!s[f].classList.contains("e-week-number")&&!(0,e.hX)(s[f].children[0])&&((0,e.iQ)([s[f].children[0]],["e-day"]),(0,e.CF)(s[f].children[0],{duration:600,isCenterRipple:!0})),r.appendChild(s[f]),this.calendarInstance.weekNumber&&7===f&&s[7].classList.contains(ht)&&(0,e.iQ)([r],Nt),!this.calendarInstance.weekNumber&&6===f&&s[6].classList.contains(ht)&&(0,e.iQ)([r],Nt),this.calendarInstance.weekNumber?41===f&&s[41].classList.contains(ht)&&(0,e.iQ)([r],Nt):35===f&&s[35].classList.contains(ht)&&(0,e.iQ)([r],Nt);c+=t,f+=0,this.calendarInstance.tableBodyElement.appendChild(r)}this.calendarInstance.table.querySelector("tbody").className=this.calendarInstance.effect,this.islamicIconHandler(),(l!==this.calendarInstance.getViewNumber(this.calendarInstance.currentView())||0===l&&l!==this.calendarInstance.getViewNumber(this.calendarInstance.currentView()))&&this.calendarInstance.navigateHandler(a),this.calendarInstance.setAriaActiveDescendant(),this.calendarInstance.changedArgs={value:this.calendarInstance.value,values:this.calendarInstance.values},this.calendarInstance.changeHandler()},u.prototype.islamicCompareMonth=function(s,t){var i=this.getIslamicDate(s),a=this.getIslamicDate(t);return i.year>a.year?1:i.year<a.year?-1:i.month===a.month?0:i.month>a.month?1:-1},u.prototype.islamicCompare=function(s,t,i){var r,h,a=this.getIslamicDate(s),l=this.getIslamicDate(t).year;return r=l,h=0,i&&(r=(l-=l%i)-l%i+i-1),a.year>r?h=1:("Decade"===this.calendarInstance.currentView()&&a.year<l&&!(s.getFullYear()>=2060&&s.getFullYear()<=2069)||a.year<l&&"Year"===this.calendarInstance.currentView())&&(h=-1),h},u.prototype.getIslamicDate=function(s){return e.cg.getHijriDate(s)},u.prototype.toGregorian=function(s,t,i){return e.cg.toGregorian(s,t,i)},u.prototype.hijriCompareYear=function(s,t){return this.islamicCompare(s,t,0)},u.prototype.hijriCompareDecade=function(s,t){return this.islamicCompare(s,t,10)},u.prototype.destroy=function(){this.calendarInstance=null},u.prototype.islamicInValue=function(s){return s instanceof Date?s.toUTCString():""+s},u}(),Bi=function(){var u=function(s,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var n in a)a.hasOwnProperty(n)&&(i[n]=a[n])})(s,t)};return function(s,t){function i(){this.constructor=s}u(s,t),s.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}}(),I=function(u,s,t,i){var l,a=arguments.length,n=a<3?s:null===i?i=Object.getOwnPropertyDescriptor(s,t):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(u,s,t,i);else for(var r=u.length-1;r>=0;r--)(l=u[r])&&(n=(a<3?l(n):a>3?l(s,t,n):l(s,t))||n);return a>3&&n&&Object.defineProperty(s,t,n),n},Pt="e-datepicker",Ie="e-popup-wrapper",_t="e-input-focus",Se="e-error",Xt="e-active",xe="e-date-overflow",Wt="e-selected",te="e-non-edit",Oe=["title","class","style"],Ne=function(u){function s(t,i){var a=u.call(this,t,i)||this;return a.isDateIconClicked=!1,a.isAltKeyPressed=!1,a.isInteracted=!0,a.invalidValueString=null,a.checkPreviousValue=null,a.maskedDateValue="",a.preventChange=!1,a.isIconClicked=!1,a.isDynamicValueChanged=!1,a.moduleName=a.getModuleName(),a.isFocused=!1,a.isBlur=!1,a.isKeyAction=!1,a.datepickerOptions=t,a}return Bi(s,u),s.prototype.render=function(){this.initialize(),this.bindEvents(),"Never"!==this.floatLabelType&&v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon"),!(0,e.hX)((0,e.kp)(this.element,"fieldset"))&&(0,e.kp)(this.element,"fieldset").disabled&&(this.enabled=!1),this.renderComplete(),this.setTimeZone(this.serverTimezoneOffset)},s.prototype.setTimeZone=function(t){if(!(0,e.hX)(this.serverTimezoneOffset)&&this.value){var n=t+(new Date).getTimezoneOffset()/60;n=this.isDayLightSaving()?n--:n,this.value=new Date(this.value.getTime()+60*n*60*1e3),this.updateInput()}},s.prototype.isDayLightSaving=function(){var t=new Date(this.value.getFullYear(),0,1).getTimezoneOffset(),i=new Date(this.value.getFullYear(),6,1).getTimezoneOffset();return this.value.getTimezoneOffset()<Math.max(t,i)},s.prototype.setAllowEdit=function(){this.allowEdit?this.readonly||this.inputElement.removeAttribute("readonly"):(0,e.uK)(this.inputElement,{readonly:""}),this.updateIconState()},s.prototype.updateIconState=function(){this.allowEdit||!this.inputWrapper||this.readonly?this.inputWrapper&&(0,e.vy)([this.inputWrapper.container],[te]):""===this.inputElement.value?(0,e.vy)([this.inputWrapper.container],[te]):(0,e.iQ)([this.inputWrapper.container],[te])},s.prototype.initialize=function(){this.checkInvalidValue(this.value),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"}),this.createInput(),this.updateHtmlAttributeToWrapper(),this.setAllowEdit(),!this.enableMask||this.value||!this.maskedDateValue||"Always"!==this.floatLabelType&&this.floatLabelType&&this.placeholder?this.enableMask||this.updateInput(!0):(this.updateInput(!0),this.updateInputValue(this.maskedDateValue)),this.previousElementValue=this.inputElement.value,this.previousDate=(0,e.hX)(this.value)?null:new Date(+this.value),this.inputElement.setAttribute("value",this.inputElement.value),this.inputValueCopy=this.value},s.prototype.createInput=function(){if("datepicker"===this.getModuleName()){var i={placeholder:this.placeholder};this.globalize=new e.DL(this.locale),this.l10n=new e.Wo("datepicker",i,this.locale),this.setProperties({placeholder:this.placeholder||this.l10n.getConstant("placeholder")},!0)}this.fullScreenMode&&e.Pw.isDevice&&(this.cssClass+=" e-popup-expand");var a=this.cssClass;!(0,e.hX)(this.cssClass)&&""!==this.cssClass&&(a=this.cssClass.replace(/\s+/g," ").trim()),this.inputWrapper=v.pd.createInput({element:this.inputElement,floatLabelType:this.floatLabelType,bindClearAction:!this.enableMask,properties:{readonly:this.readonly,placeholder:this.placeholder,cssClass:a,enabled:this.enabled,enableRtl:this.enableRtl,showClearButton:this.showClearButton},buttons:["e-input-group-icon e-date-icon e-icons"]},this.createElement),this.setWidth(this.width),this.inputElement.setAttribute("name",""!==this.inputElement.name?""+this.inputElement.getAttribute("name"):""+this.element.id),(0,e.uK)(this.inputElement,{"aria-atomic":"true","aria-expanded":"false",role:"combobox",autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false","aria-invalid":"false"}),this.inputElement.hasAttribute("aria-label")||this.inputElement.setAttribute("aria-label",this.getModuleName()),this.enabled?(this.inputElement.setAttribute("aria-disabled","false"),this.inputElement.setAttribute("tabindex",this.tabIndex)):(this.inputElement.setAttribute("aria-disabled","true"),this.inputElement.tabIndex=-1),v.pd.addAttributes({"aria-label":"select",role:"button"},this.inputWrapper.buttons[0]),(0,e.iQ)([this.inputWrapper.container],"e-date-wrapper")},s.prototype.updateInput=function(t,i){if(void 0===t&&(t=!1),void 0===i&&(i=!1),this.value&&!this.isCalendar()&&this.disabledDates(t,i),isNaN(+new Date(this.checkValue(this.value)))&&this.setProperties({value:null},!0),this.strictMode&&(u.prototype.validateDate.call(this),this.minMaxUpdates(),u.prototype.minMaxUpdate.call(this)),!(0,e.hX)(this.value)){var n=this.value,l=void 0,r=(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString;l="datetimepicker"===this.getModuleName()?this.globalize.formatDate(this.value,"Gregorian"===this.calendarMode?{format:r,type:"dateTime",skeleton:"yMd"}:{format:r,type:"dateTime",skeleton:"yMd",calendar:"islamic"}):this.globalize.formatDate(this.value,"Gregorian"===this.calendarMode?{format:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}),(+n<=+this.max&&+n>=+this.min||!this.strictMode&&(+n>=+this.max||!+this.value||!+this.value||+n<=+this.min))&&this.updateInputValue(l)}(0,e.hX)(this.value)&&this.strictMode&&(this.enableMask?(this.updateInputValue(this.maskedDateValue),this.notify("createMask",{module:"MaskedDateTime"})):this.updateInputValue("")),!this.strictMode&&(0,e.hX)(this.value)&&this.invalidValueString&&this.updateInputValue(this.invalidValueString),this.changedArgs={value:this.value},this.errorClass(),this.updateIconState()},s.prototype.minMaxUpdates=function(){!(0,e.hX)(this.value)&&this.value<this.min&&this.min<=this.max&&this.strictMode?(this.setProperties({value:this.min},!0),this.changedArgs={value:this.value}):!(0,e.hX)(this.value)&&this.value>this.max&&this.min<=this.max&&this.strictMode&&(this.setProperties({value:this.max},!0),this.changedArgs={value:this.value})},s.prototype.checkStringValue=function(t){var i=null,a=null,n=null;if("datetimepicker"===this.getModuleName()){var l=new e.DL(this.locale);"Gregorian"===this.calendarMode?(a={format:this.dateTimeFormat,type:"dateTime",skeleton:"yMd"},n={format:l.getDatePattern({skeleton:"yMd"}),type:"dateTime"}):(a={format:this.dateTimeFormat,type:"dateTime",skeleton:"yMd",calendar:"islamic"},n={format:l.getDatePattern({skeleton:"yMd"}),type:"dateTime",calendar:"islamic"})}else a="Gregorian"===this.calendarMode?{format:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"};return i=this.checkDateValue(this.globalize.parseDate(this.getAmPmValue(t),a)),(0,e.hX)(i)&&"datetimepicker"===this.getModuleName()&&(i=this.checkDateValue(this.globalize.parseDate(this.getAmPmValue(t),n))),i},s.prototype.checkInvalidValue=function(t){if(!(t instanceof Date||(0,e.hX)(t))){var i=null,a=t;if("number"==typeof t&&(a=t.toString()),"datetimepicker"===this.getModuleName()){var r=new e.DL(this.locale);r.getDatePattern({skeleton:"yMd"})}var h=!1;if("string"!=typeof a)a=null,h=!0;else if("string"==typeof a&&(a=a.trim()),!(i=this.checkStringValue(a))){var p=null;p=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,!/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/.test(a)&&!p.test(a)||/^[a-zA-Z0-9- ]*$/.test(a)||isNaN(+new Date(this.checkValue(a)))?h=!0:i=new Date(a)}h?(this.strictMode||(this.invalidValueString=a),this.setProperties({value:null},!0)):this.setProperties({value:i},!0)}},s.prototype.bindInputEvent=function(){(!(0,e.hX)(this.formatString)||this.enableMask)&&(this.enableMask||-1===this.formatString.indexOf("y")?e.Jm.add(this.inputElement,"input",this.inputHandler,this):e.Jm.remove(this.inputElement,"input",this.inputHandler))},s.prototype.bindEvents=function(){e.Jm.add(this.inputWrapper.buttons[0],"mousedown",this.dateIconHandler,this),e.Jm.add(this.inputElement,"mouseup",this.mouseUpHandler,this),e.Jm.add(this.inputElement,"focus",this.inputFocusHandler,this),e.Jm.add(this.inputElement,"blur",this.inputBlurHandler,this),e.Jm.add(this.inputElement,"keyup",this.keyupHandler,this),this.enableMask&&e.Jm.add(this.inputElement,"keydown",this.keydownHandler,this),this.bindInputEvent(),e.Jm.add(this.inputElement,"change",this.inputChangeHandler,this),this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown touchstart",this.resetHandler,this),this.formElement&&e.Jm.add(this.formElement,"reset",this.resetFormHandler,this),this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,this.keyConfigs),this.keyboardModules=new e.j9(this.inputElement,{eventName:"keydown",keyAction:this.inputKeyActionHandle.bind(this),keyConfigs:this.defaultKeyConfigs})},s.prototype.keydownHandler=function(t){switch(t.code){case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"ArrowDown":case"Home":case"End":case"Backspace":case"Delete":this.enableMask&&!this.popupObj&&!this.readonly&&("Delete"!==t.code&&"Backspace"!==t.code&&t.preventDefault(),this.notify("keyDownHandler",{module:"MaskedDateTime",e:t}))}},s.prototype.unBindEvents=function(){(0,e.hX)(this.inputWrapper)||e.Jm.remove(this.inputWrapper.buttons[0],"mousedown",this.dateIconHandler),e.Jm.remove(this.inputElement,"mouseup",this.mouseUpHandler),e.Jm.remove(this.inputElement,"focus",this.inputFocusHandler),e.Jm.remove(this.inputElement,"blur",this.inputBlurHandler),e.Jm.remove(this.inputElement,"change",this.inputChangeHandler),e.Jm.remove(this.inputElement,"keyup",this.keyupHandler),this.enableMask&&e.Jm.remove(this.inputElement,"keydown",this.keydownHandler),this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.remove(this.inputWrapper.clearButton,"mousedown touchstart",this.resetHandler),this.formElement&&e.Jm.remove(this.formElement,"reset",this.resetFormHandler)},s.prototype.resetFormHandler=function(){if(this.enabled&&!this.inputElement.disabled){var t=this.inputElement.getAttribute("value");("EJS-DATEPICKER"===this.element.tagName||"EJS-DATETIMEPICKER"===this.element.tagName)&&(t="",this.inputValueCopy=null,this.inputElement.setAttribute("value","")),this.setProperties({value:this.inputValueCopy},!0),this.restoreValue(),this.inputElement&&(this.updateInputValue(t),this.errorClass())}},s.prototype.restoreValue=function(){this.currentDate=this.value?this.value:new Date,this.previousDate=this.value,this.previousElementValue=(0,e.hX)(this.inputValueCopy)?"":this.globalize.formatDate(this.inputValueCopy,{format:this.formatString,type:"dateTime",skeleton:"yMd"})},s.prototype.inputChangeHandler=function(t){this.enabled&&t.stopPropagation()},s.prototype.bindClearEvent=function(){this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown touchstart",this.resetHandler,this)},s.prototype.resetHandler=function(t){this.enabled&&(t.preventDefault(),this.clear(t))},s.prototype.mouseUpHandler=function(t){this.enableMask&&(t.preventDefault(),this.notify("setMaskSelection",{module:"MaskedDateTime"}))},s.prototype.clear=function(t){if(this.setProperties({value:null},!0),this.enableMask||this.updateInputValue(""),this.trigger("cleared",{event:t}),this.invalidValueString="",this.updateInput(),this.popupUpdate(),this.changeEvent(t),this.enableMask&&this.notify("clearHandler",{module:"MaskedDateTime"}),(0,e.kp)(this.element,"form")){var a=this.element,n=document.createEvent("KeyboardEvent");n.initEvent("keyup",!1,!0),a.dispatchEvent(n)}},s.prototype.preventEventBubbling=function(t){t.preventDefault(),this.interopAdaptor.invokeMethodAsync("OnDateIconClick")},s.prototype.updateInputValue=function(t){v.pd.setValue(t,this.inputElement,this.floatLabelType,this.showClearButton)},s.prototype.dateIconHandler=function(t){this.enabled&&(this.isIconClicked=!0,e.Pw.isDevice&&(this.inputElement.setAttribute("readonly",""),this.inputElement.blur()),t.preventDefault(),this.readonly||(this.isCalendar()?this.hide(t):(this.isDateIconClicked=!0,this.show(null,t),"datetimepicker"===this.getModuleName()&&this.inputElement.focus(),this.inputElement.focus(),(0,e.iQ)([this.inputWrapper.container],[_t]),(0,e.iQ)(this.inputWrapper.buttons,Xt))),this.isIconClicked=!1)},s.prototype.updateHtmlAttributeToWrapper=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];if(!(0,e.hX)(this.htmlAttributes[""+a])&&Oe.indexOf(a)>-1)if("class"===a){var n=this.htmlAttributes[""+a].replace(/\s+/g," ").trim();""!==n&&(0,e.iQ)([this.inputWrapper.container],n.split(" "))}else if("style"===a){var l=this.inputWrapper.container.getAttribute(a);(0,e.hX)(l)?l=this.htmlAttributes[""+a]:";"===l.charAt(l.length-1)?l+=this.htmlAttributes[""+a]:l=l+";"+this.htmlAttributes[""+a],this.inputWrapper.container.setAttribute(a,l)}else this.inputWrapper.container.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateHtmlAttributeToElement=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];Oe.indexOf(a)<0&&this.inputElement.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateCssClass=function(t,i){(0,e.hX)(i)||(i=i.replace(/\s+/g," ").trim()),(0,e.hX)(t)||(t=t.replace(/\s+/g," ").trim()),v.pd.setCssClass(t,[this.inputWrapper.container],i),this.popupWrapper&&v.pd.setCssClass(t,[this.popupWrapper],i)},s.prototype.calendarKeyActionHandle=function(t){switch(t.action){case"escape":this.isCalendar()?this.hide(t):this.inputWrapper.container.children[this.index].blur();break;case"enter":this.isCalendar()?+this.value!=+this.currentDate&&!this.isCalendar()&&this.inputWrapper.container.children[this.index].focus():this.show(null,t),"datetimepicker"===this.getModuleName()&&this.inputElement.focus()}},s.prototype.inputFocusHandler=function(){if(this.isFocused=!0,this.enabled){this.enableMask&&!this.inputElement.value&&this.placeholder&&this.maskedDateValue&&!this.value&&("Auto"===this.floatLabelType||"Never"===this.floatLabelType||this.placeholder)&&(this.updateInputValue(this.maskedDateValue),this.inputElement.selectionStart=0,this.inputElement.selectionEnd=this.inputElement.value.length);var t={model:this};this.isDateIconClicked=!1,this.trigger("focus",t),this.updateIconState(),this.openOnFocus&&!this.isIconClicked&&this.show()}},s.prototype.inputHandler=function(t){this.isPopupClicked=!1,this.enableMask&&((0,e.hX)(t)||(0,e.hX)(t.inputType)||"insertFromPaste"!==t.inputType?this.notify("inputHandler",{module:"MaskedDateTime"}):this.notify("maskPasteInputHandler",{module:"MaskedDateTime"}))},s.prototype.inputBlurHandler=function(t){this.enabled&&(this.strictModeUpdate(),""===this.inputElement.value&&(0,e.hX)(this.value)&&(this.invalidValueString=null,this.updateInputValue("")),this.isBlur=!0,this.updateInput(!1,!0),this.isBlur=!1,this.popupUpdate(),this.changeTrigger(t),this.enableMask&&this.maskedDateValue&&this.placeholder&&"Always"!==this.floatLabelType&&this.inputElement.value===this.maskedDateValue&&!this.value&&("Auto"===this.floatLabelType||"Never"===this.floatLabelType||this.placeholder)&&this.updateInputValue(""),this.errorClass(),this.isCalendar()&&document.activeElement===this.inputElement&&this.hide(t),"datepicker"===this.getModuleName()&&this.trigger("blur",{model:this}),this.isCalendar()&&(this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,this.keyConfigs),this.calendarKeyboardModules=new e.j9(this.calendarElement.children[1].firstElementChild,{eventName:"keydown",keyAction:this.calendarKeyActionHandle.bind(this),keyConfigs:this.defaultKeyConfigs})),this.isPopupClicked=!1)},s.prototype.documentHandler=function(t){!(0,e.hX)(this.popupObj)&&!(0,e.hX)(this.inputWrapper)&&(this.inputWrapper.container.contains(t.target)&&"mousedown"!==t.type||this.popupObj.element&&this.popupObj.element.contains(t.target))&&"touchstart"!==t.type&&t.preventDefault();var i=t.target;(0,e.kp)(i,".e-datepicker.e-popup-wrapper")||(0,e.hX)(this.inputWrapper)||(0,e.kp)(i,".e-input-group")===this.inputWrapper.container||i.classList.contains("e-day")||i.classList.contains("e-dlg-overlay")?(0,e.kp)(i,".e-datepicker.e-popup-wrapper")&&(i.classList.contains("e-day")&&!(0,e.hX)(t.target.parentElement)&&t.target.parentElement.classList.contains("e-selected")&&(0,e.kp)(i,".e-content")&&(0,e.kp)(i,".e-content").classList.contains("e-"+this.depth.toLowerCase())||(0,e.kp)(i,".e-footer-container")&&i.classList.contains("e-today")&&i.classList.contains("e-btn")&&+new Date(+this.value)==+u.prototype.generateTodayVal.call(this,this.value))&&this.hide(t):(this.hide(t),this.focusOut())},s.prototype.inputKeyActionHandle=function(t){var i=this.currentView();switch(t.action){case"altUpArrow":this.isAltKeyPressed=!1,this.hide(t),this.inputElement.focus();break;case"altDownArrow":this.isAltKeyPressed=!0,this.strictModeUpdate(),this.updateInput(),this.changeTrigger(t),"datepicker"===this.getModuleName()&&this.show(null,t);break;case"escape":this.hide(t);break;case"enter":this.strictModeUpdate(),this.updateInput(),this.popupUpdate(),this.changeTrigger(t),this.errorClass(),!this.isCalendar()&&document.activeElement===this.inputElement&&this.hide(t),this.isCalendar()&&(t.preventDefault(),t.stopPropagation());break;case"tab":case"shiftTab":var a=this.inputElement.selectionStart,n=this.inputElement.selectionEnd;if(this.enableMask&&!this.popupObj&&!this.readonly){var l=this.inputElement.value.length;(0===a&&n===l||n!==l&&"tab"===t.action||0!==a&&"shiftTab"===t.action)&&t.preventDefault(),this.notify("keyDownHandler",{module:"MaskedDateTime",e:t}),a=this.inputElement.selectionStart,n=this.inputElement.selectionEnd}this.strictModeUpdate(),this.updateInput(),this.popupUpdate(),this.changeTrigger(t),this.errorClass(),this.enableMask&&(this.inputElement.selectionStart=a,this.inputElement.selectionEnd=n),"tab"===t.action&&t.target===this.inputElement&&this.isCalendar()&&document.activeElement===this.inputElement&&(t.preventDefault(),this.headerTitleElement.focus()),"shiftTab"===t.action&&t.target===this.inputElement&&this.isCalendar()&&document.activeElement===this.inputElement&&this.hide(t);break;default:this.defaultAction(t),"select"===t.action&&i===this.depth&&this.hide(t)}},s.prototype.defaultAction=function(t){this.previousDate=!(0,e.hX)(this.value)&&new Date(+this.value)||null,this.isCalendar()&&(u.prototype.keyActionHandle.call(this,t),this.isCalendar()&&(0,e.uK)(this.inputElement,{"aria-activedescendant":""+this.setActiveDescendant()}))},s.prototype.popupUpdate=function(){if(((0,e.hX)(this.value)&&!(0,e.hX)(this.previousDate)||"datetimepicker"!==this.getModuleName()&&+this.value!=+this.previousDate||"datetimepicker"===this.getModuleName()&&+this.value!=+this.previousDateTime)&&(this.popupObj&&this.popupObj.element.querySelectorAll("."+Wt).length>0&&(0,e.vy)(this.popupObj.element.querySelectorAll("."+Wt),[Wt]),!(0,e.hX)(this.value)&&+this.value>=+this.min&&+this.value<=+this.max)){var t=new Date(this.checkValue(this.value));u.prototype.navigateTo.call(this,"Month",t)}},s.prototype.strictModeUpdate=function(){var t,n,r,h;"datetimepicker"===this.getModuleName()?t=(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString:(!/^y/.test(this.formatString)||/[^a-zA-Z]/.test(this.formatString))&&(t=(0,e.hX)(this.formatString)?this.formatString:this.formatString.replace("dd","d")),(0,e.hX)(t)?t=this.formatString:t.split("M").length-1<3&&(t=t.replace("MM","M")),r="datetimepicker"===this.getModuleName()?"Gregorian"===this.calendarMode?{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}:n="Gregorian"===this.calendarMode?{format:t,type:"dateTime",skeleton:"yMd"}:{format:t,type:"dateTime",skeleton:"yMd",calendar:"islamic"},"string"==typeof this.inputElement.value&&(this.inputElement.value=this.inputElement.value.trim()),"datetimepicker"===this.getModuleName()?this.checkDateValue(this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),r))?h=this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),r):(n="Gregorian"===this.calendarMode?{format:t,type:"dateTime",skeleton:"yMd"}:{format:t,type:"dateTime",skeleton:"yMd",calendar:"islamic"},h=this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),n)):(h=this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),r),h=!(0,e.hX)(h)&&isNaN(+h)?null:h,!(0,e.hX)(this.formatString)&&""!==this.inputElement.value&&this.strictMode&&(this.isPopupClicked||!this.isPopupClicked&&this.inputElement.value===this.previousElementValue)&&-1===this.formatString.indexOf("y")&&h.setFullYear(this.value.getFullYear())),"datepicker"===this.getModuleName()&&this.value&&!isNaN(+this.value)&&h&&h.setHours(this.value.getHours(),this.value.getMinutes(),this.value.getSeconds(),this.value.getMilliseconds()),this.strictMode&&h?(this.updateInputValue(this.globalize.formatDate(h,r)),this.inputElement.value!==this.previousElementValue&&this.setProperties({value:h},!0)):this.strictMode||this.inputElement.value!==this.previousElementValue&&this.setProperties({value:h},!0),this.strictMode&&!h&&this.inputElement.value===(this.enableMask?this.maskedDateValue:"")&&this.setProperties({value:null},!0),isNaN(+this.value)&&this.setProperties({value:null},!0),(0,e.hX)(this.value)&&(this.currentDate=new Date((new Date).setHours(0,0,0,0)))},s.prototype.createCalendar=function(){var t=this;this.popupWrapper=this.createElement("div",{className:Pt+" "+Ie,id:this.inputElement.id+"_options"}),this.popupWrapper.setAttribute("aria-label",this.element.id),this.popupWrapper.setAttribute("role","dialog"),(0,e.hX)(this.cssClass)||(this.popupWrapper.className+=" "+this.cssClass),e.Pw.isDevice&&(this.modelHeader(),this.modal=this.createElement("div"),this.modal.className=Pt+" e-date-modal",document.body.className+=" "+xe,this.modal.style.display="block",document.body.appendChild(this.modal)),this.calendarElement.querySelector("table tbody").className="",this.popupObj=new It.zD(this.popupWrapper,{content:this.calendarElement,relateTo:e.Pw.isDevice?document.body:this.inputWrapper.container,position:e.Pw.isDevice?{X:"center",Y:"center"}:this.enableRtl?{X:"right",Y:"bottom"}:{X:"left",Y:"bottom"},offsetY:4,targetType:"container",enableRtl:this.enableRtl,zIndex:this.zIndex,collision:e.Pw.isDevice?{X:"fit",Y:"fit"}:this.enableRtl?{X:"fit",Y:"flip"}:{X:"flip",Y:"flip"},open:function(){e.Pw.isDevice&&t.fullScreenMode&&(t.iconRight=parseInt(window.getComputedStyle(t.calendarElement.querySelector(".e-header.e-month .e-prev")).marginRight,10)>16,t.touchModule=new e.Up(t.calendarElement.querySelector(".e-content.e-month"),{swipe:t.CalendarSwipeHandler.bind(t)}),e.Jm.add(t.calendarElement.querySelector(".e-content.e-month"),"touchstart",t.TouchStartHandler,t)),"datetimepicker"!==t.getModuleName()&&document.activeElement!==t.inputElement&&(t.defaultKeyConfigs=(0,e.X$)(t.defaultKeyConfigs,t.keyConfigs),t.calendarElement.children[1].firstElementChild.focus(),t.calendarKeyboardModules=new e.j9(t.calendarElement.children[1].firstElementChild,{eventName:"keydown",keyAction:t.calendarKeyActionHandle.bind(t),keyConfigs:t.defaultKeyConfigs}),t.calendarKeyboardModules=new e.j9(t.inputWrapper.container.children[t.index],{eventName:"keydown",keyAction:t.calendarKeyActionHandle.bind(t),keyConfigs:t.defaultKeyConfigs}))},close:function(){t.isDateIconClicked&&t.inputWrapper.container.children[t.index].focus(),t.value&&t.disabledDates(),t.popupObj&&t.popupObj.destroy(),t.resetCalendar(),(0,e.Yo)(t.popupWrapper),t.popupObj=t.popupWrapper=null,t.preventArgs=null,t.calendarKeyboardModules=null,t.setAriaAttributes()},targetExitViewport:function(){e.Pw.isDevice||t.hide()}}),this.popupObj.element.className+=" "+this.cssClass,this.setAriaAttributes()},s.prototype.getAmPmValue=function(t){try{return"string"==typeof t&&""!==t.trim()?t.replace(/(am|pm|Am|aM|pM|Pm)/g,function(i){return i.toLocaleUpperCase()}):""}catch(i){return console.error("Error occurred while processing date:",i),""}},s.prototype.CalendarSwipeHandler=function(t){var i=0;if(this.iconRight)switch(t.swipeDirection){case"Left":i=1;break;case"Right":i=-1}else switch(t.swipeDirection){case"Up":i=1;break;case"Down":i=-1}this.touchStart&&(1===i?this.navigateNext(t):-1===i&&this.navigatePrevious(t),this.touchStart=!1)},s.prototype.TouchStartHandler=function(t){this.touchStart=!0},s.prototype.setAriaDisabled=function(){this.enabled?(this.inputElement.setAttribute("aria-disabled","false"),this.inputElement.setAttribute("tabindex",this.tabIndex)):(this.inputElement.setAttribute("aria-disabled","true"),this.inputElement.tabIndex=-1)},s.prototype.modelHeader=function(){var t,i=this.createElement("div",{className:"e-model-header"}),a=this.createElement("h1",{className:"e-model-year"}),n=this.createElement("div"),l=this.createElement("span",{className:"e-model-day"}),r=this.createElement("span",{className:"e-model-month"});if(t="Gregorian"===this.calendarMode?{format:"y",skeleton:"dateTime"}:{format:"y",skeleton:"dateTime",calendar:"islamic"},a.textContent=""+this.globalize.formatDate(this.value||new Date,t),t="Gregorian"===this.calendarMode?{format:"E",skeleton:"dateTime"}:{format:"E",skeleton:"dateTime",calendar:"islamic"},l.textContent=this.globalize.formatDate(this.value||new Date,t)+", ",t="Gregorian"===this.calendarMode?{format:"MMM d",skeleton:"dateTime"}:{format:"MMM d",skeleton:"dateTime",calendar:"islamic"},r.textContent=""+this.globalize.formatDate(this.value||new Date,t),this.fullScreenMode){var h=this.createElement("span",{className:"e-popup-close"});e.Jm.add(h,"mousedown touchstart",this.modelCloseHandler,this);var o=this.calendarElement.querySelector("button.e-today");n.classList.add("e-day-wrapper"),o.classList.add("e-outline"),i.appendChild(h),i.appendChild(o)}this.fullScreenMode||i.appendChild(a),n.appendChild(l),n.appendChild(r),i.appendChild(n),this.calendarElement.insertBefore(i,this.calendarElement.firstElementChild)},s.prototype.modelCloseHandler=function(t){this.hide()},s.prototype.changeTrigger=function(t){this.inputElement.value!==this.previousElementValue&&(this.previousDate&&this.previousDate.valueOf())!==(this.value&&this.value.valueOf())&&(this.isDynamicValueChanged&&this.isCalendar()&&this.popupUpdate(),this.changedArgs.value=this.value,this.changedArgs.event=t||null,this.changedArgs.element=this.element,this.changedArgs.isInteracted=!(0,e.hX)(t),this.isAngular&&this.preventChange?this.preventChange=!1:this.trigger("change",this.changedArgs),this.previousElementValue=this.inputElement.value,this.previousDate=isNaN(+new Date(this.checkValue(this.value)))?null:new Date(this.checkValue(this.value)),this.isInteracted=!0),this.isKeyAction=!1},s.prototype.navigatedEvent=function(){this.trigger("navigated",this.navigatedArgs)},s.prototype.keyupHandler=function(t){this.isKeyAction=this.inputElement.value!==this.previousElementValue},s.prototype.changeEvent=function(t){!this.isIconClicked&&!(this.isBlur||this.isKeyAction)&&this.selectCalendar(t),(this.previousDate&&this.previousDate.valueOf())!==(this.value&&this.value.valueOf())?(this.changedArgs.event=t||null,this.changedArgs.element=this.element,this.changedArgs.isInteracted=this.isInteracted,this.isDynamicValueChanged||this.trigger("change",this.changedArgs),this.previousDate=this.value&&new Date(+this.value),this.isDynamicValueChanged||this.hide(t),this.previousElementValue=this.inputElement.value,this.errorClass()):t&&this.hide(t),this.isKeyAction=!1},s.prototype.requiredModules=function(){var t=[];return"Islamic"===this.calendarMode&&t.push({args:[this],member:"islamic",name:"Islamic"}),this.enableMask&&t.push({args:[this],member:"MaskedDateTime"}),t},s.prototype.selectCalendar=function(t){var i,a;a="datetimepicker"===this.getModuleName()&&(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,this.value&&(i="datetimepicker"===this.getModuleName()?this.globalize.formatDate(this.changedArgs.value,"Gregorian"===this.calendarMode?{format:a,type:"dateTime",skeleton:"yMd"}:{format:a,type:"dateTime",skeleton:"yMd",calendar:"islamic"}):this.globalize.formatDate(this.changedArgs.value,"Gregorian"===this.calendarMode?{format:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"})),(0,e.hX)(i)||(this.updateInputValue(i),this.enableMask&&this.notify("setMaskSelection",{module:"MaskedDateTime"}))},s.prototype.isCalendar=function(){return!(!this.popupWrapper||!this.popupWrapper.classList.contains(""+Ie))},s.prototype.setWidth=function(t){this.inputWrapper.container.style.width="number"==typeof t?(0,e.IV)(this.width):"string"==typeof t?t.match(/px|%|em/)?this.width:(0,e.IV)(this.width):"100%"},s.prototype.show=function(t,i){var a=this;if(!(this.enabled&&this.readonly||!this.enabled||this.popupObj)){var n=!0,l=void 0;if((0,e.hX)(this.value)||+this.value>=+new Date(this.checkValue(this.min))&&+this.value<=+new Date(this.checkValue(this.max))?l=this.value||null:(l=new Date(this.checkValue(this.value)),this.setProperties({value:null},!0)),this.isCalendar()||(u.prototype.render.call(this),this.setProperties({value:l||null},!0),this.previousDate=l,this.createCalendar()),e.Pw.isDevice&&(this.mobilePopupWrapper=this.createElement("div",{className:"e-datepick-mob-popup-wrap"}),document.body.appendChild(this.mobilePopupWrapper)),this.preventArgs={preventDefault:function(){n=!1},popup:this.popupObj,event:i||null,cancel:!1,appendTo:e.Pw.isDevice?this.mobilePopupWrapper:document.body},this.trigger("open",this.preventArgs,function(o){(a.preventArgs=o,n&&!a.preventArgs.cancel)?((0,e.iQ)(a.inputWrapper.buttons,Xt),a.preventArgs.appendTo.appendChild(a.popupWrapper),a.popupObj.refreshPosition(a.inputElement),a.popupObj.show(new e.X5({name:"FadeIn",duration:e.Pw.isDevice?0:300}),1e3===a.zIndex?a.element:null),u.prototype.setOverlayIndex.call(a,a.mobilePopupWrapper,a.popupObj.element,a.modal,e.Pw.isDevice),a.setAriaAttributes()):(a.popupObj.destroy(),a.popupWrapper=a.popupObj=null);!(0,e.hX)(a.inputElement)&&""===a.inputElement.value&&!(0,e.hX)(a.tableBodyElement)&&a.tableBodyElement.querySelectorAll("td.e-selected").length>0&&((0,e.iQ)([a.tableBodyElement.querySelector("td.e-selected")],"e-focused-date"),(0,e.vy)(a.tableBodyElement.querySelectorAll("td.e-selected"),Wt)),e.Jm.add(document,"mousedown touchstart",a.documentHandler,a)}),e.Pw.isDevice){var h=this.createElement("div",{className:"e-dlg-overlay"});h.style.zIndex=(this.zIndex-1).toString(),this.mobilePopupWrapper.appendChild(h)}}},s.prototype.hide=function(t){var i=this;if((0,e.hX)(this.popupWrapper))e.Pw.isDevice&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly"),this.setAllowEdit();else{var a=!0;this.preventArgs={preventDefault:function(){a=!1},popup:this.popupObj,event:t||null,cancel:!1},(0,e.vy)(this.inputWrapper.buttons,Xt),(0,e.vy)([document.body],xe);var n=this.preventArgs;this.isCalendar()?this.trigger("close",n,function(l){i.closeEventCallback(a,l)}):this.closeEventCallback(a,n)}},s.prototype.closeEventCallback=function(t,i){this.preventArgs=i,this.isCalendar()&&t&&!this.preventArgs.cancel&&(this.popupObj.hide(),this.isAltKeyPressed=!1,this.keyboardModule.destroy(),(0,e.vy)(this.inputWrapper.buttons,Xt)),this.setAriaAttributes(),e.Pw.isDevice&&this.modal&&(this.modal.style.display="none",this.modal.outerHTML="",this.modal=null),e.Pw.isDevice&&!(0,e.hX)(this.mobilePopupWrapper)&&t&&((0,e.hX)(this.preventArgs)||!this.preventArgs.cancel)&&(this.mobilePopupWrapper.remove(),this.mobilePopupWrapper=null),e.Jm.remove(document,"mousedown touchstart",this.documentHandler),e.Pw.isDevice&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly"),this.setAllowEdit()},s.prototype.focusIn=function(t){document.activeElement!==this.inputElement&&this.enabled&&(this.inputElement.focus(),(0,e.iQ)([this.inputWrapper.container],[_t]))},s.prototype.focusOut=function(){document.activeElement===this.inputElement&&((0,e.vy)([this.inputWrapper.container],[_t]),this.inputElement.blur())},s.prototype.currentView=function(){var t;return this.calendarElement&&(t=u.prototype.currentView.call(this)),t},s.prototype.navigateTo=function(t,i){this.calendarElement&&u.prototype.navigateTo.call(this,t,i)},s.prototype.destroy=function(){this.unBindEvents(),this.showClearButton&&(this.clearButton=document.getElementsByClassName("e-clear-icon")[0]),u.prototype.destroy.call(this),v.pd.destroy({element:this.inputElement,floatLabelType:this.floatLabelType,properties:this.properties},this.clearButton),(0,e.hX)(this.keyboardModules)||this.keyboardModules.destroy(),this.popupObj&&this.popupObj.element.classList.contains("e-popup")&&u.prototype.destroy.call(this),this.inputElement.hasAttribute("aria-label")&&this.inputElement.removeAttribute("aria-label"),this.inputElement&&(v.pd.removeAttributes({"aria-atomic":"true","aria-disabled":"true","aria-expanded":"false",role:"combobox",autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false"},this.inputElement),(0,e.hX)(this.inputElementCopy.getAttribute("tabindex"))?this.inputElement.removeAttribute("tabindex"):this.inputElement.setAttribute("tabindex",this.tabIndex),e.Jm.remove(this.inputElement,"blur",this.inputBlurHandler),e.Jm.remove(this.inputElement,"focus",this.inputFocusHandler),this.ensureInputAttribute()),this.isCalendar()&&(this.popupWrapper&&(0,e.Yo)(this.popupWrapper),this.popupObj=this.popupWrapper=null,this.keyboardModule.destroy()),null===this.ngTag&&(this.inputElement&&((0,e.hX)(this.inputWrapper)||this.inputWrapper.container.insertAdjacentElement("afterend",this.inputElement),(0,e.vy)([this.inputElement],["e-input"])),(0,e.vy)([this.element],[Pt]),(0,e.hX)(this.inputWrapper)||(0,e.Yo)(this.inputWrapper.container)),this.formElement&&e.Jm.remove(this.formElement,"reset",this.resetFormHandler),this.inputWrapper=null,this.keyboardModules=null},s.prototype.ensureInputAttribute=function(){for(var t=[],i=0;i<this.inputElement.attributes.length;i++)t[i]=this.inputElement.attributes[i].name;for(i=0;i<t.length;i++)(0,e.hX)(this.inputElementCopy.getAttribute(t[i]))?("value"===t[i].toLowerCase()&&(this.inputElement.value=""),this.inputElement.removeAttribute(t[i])):("value"===t[i].toLowerCase()&&(this.inputElement.value=this.inputElementCopy.getAttribute(t[i])),this.inputElement.setAttribute(t[i],this.inputElementCopy.getAttribute(t[i])))},s.prototype.preRender=function(){this.inputElementCopy=this.element.cloneNode(!0),(0,e.vy)([this.inputElementCopy],[Pt,"e-control","e-lib"]),this.inputElement=this.element,this.formElement=(0,e.kp)(this.inputElement,"form"),this.index=this.showClearButton?2:1,this.ngTag=null,("EJS-DATEPICKER"===this.element.tagName||"EJS-DATETIMEPICKER"===this.element.tagName)&&(this.ngTag=this.element.tagName,this.inputElement=this.createElement("input"),this.element.appendChild(this.inputElement)),this.element.getAttribute("id")?null!==this.ngTag&&(this.inputElement.id=this.element.getAttribute("id")+"_input"):"datetimepicker"===this.getModuleName()?(this.element.id=(0,e.Lz)("ej2-datetimepicker"),null!==this.ngTag&&(0,e.uK)(this.inputElement,{id:this.element.id+"_input"})):(this.element.id=(0,e.Lz)("ej2-datepicker"),null!==this.ngTag&&(0,e.uK)(this.inputElement,{id:this.element.id+"_input"})),null!==this.ngTag&&this.validationAttribute(this.element,this.inputElement),this.updateHtmlAttributeToElement(),this.defaultKeyConfigs=this.getDefaultKeyConfig(),this.checkHtmlAttributes(!1),this.tabIndex=this.element.hasAttribute("tabindex")?this.element.getAttribute("tabindex"):"0",this.element.removeAttribute("tabindex"),u.prototype.preRender.call(this)},s.prototype.getDefaultKeyConfig=function(){return this.defaultKeyConfigs={altUpArrow:"alt+uparrow",altDownArrow:"alt+downarrow",escape:"escape",enter:"enter",controlUp:"ctrl+38",controlDown:"ctrl+40",moveDown:"downarrow",moveUp:"uparrow",moveLeft:"leftarrow",moveRight:"rightarrow",select:"enter",home:"home",end:"end",pageUp:"pageup",pageDown:"pagedown",shiftPageUp:"shift+pageup",shiftPageDown:"shift+pagedown",controlHome:"ctrl+home",controlEnd:"ctrl+end",shiftTab:"shift+tab",tab:"tab"},this.defaultKeyConfigs},s.prototype.validationAttribute=function(t,i){var a=t.getAttribute("name")?t.getAttribute("name"):t.getAttribute("id");i.setAttribute("name",a),t.removeAttribute("name");for(var n=["required","aria-required","form"],l=0;l<n.length;l++)if(!(0,e.hX)(t.getAttribute(n[l]))){var r=t.getAttribute(n[l]);i.setAttribute(n[l],r),t.removeAttribute(n[l])}},s.prototype.checkFormat=function(){var t=new e.DL(this.locale);if(this.format)if("string"==typeof this.format)this.formatString=this.format;else if(""===this.format.skeleton||(0,e.hX)(this.format.skeleton))this.formatString="datetimepicker"===this.getModuleName()?this.dateTimeFormat:null;else{var i=this.format.skeleton;this.formatString="datetimepicker"===this.getModuleName()?t.getDatePattern({skeleton:i,type:"dateTime"}):t.getDatePattern({skeleton:i,type:"date"})}else this.formatString=null},s.prototype.checkHtmlAttributes=function(t){this.globalize=new e.DL(this.locale),this.checkFormat(),this.checkView();var a,i=t?(0,e.hX)(this.htmlAttributes)?[]:Object.keys(this.htmlAttributes):["value","min","max","disabled","readonly","style","name","placeholder","type"];a="datetimepicker"===this.getModuleName()?"Gregorian"===this.calendarMode?{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}:"Gregorian"===this.calendarMode?{format:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"};for(var n=0,l=i;n<l.length;n++){var r=l[n];if(!(0,e.hX)(this.inputElement.getAttribute(r)))switch(r){case"disabled":if((0,e.hX)(this.datepickerOptions)||void 0===this.datepickerOptions.enabled||t){var h=!("disabled"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r));this.setProperties({enabled:h},!t)}break;case"readonly":if((0,e.hX)(this.datepickerOptions)||void 0===this.datepickerOptions.readonly||t){var o="readonly"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r);this.setProperties({readonly:o},!t)}break;case"placeholder":((0,e.hX)(this.datepickerOptions)||void 0===this.datepickerOptions.placeholder||t)&&this.setProperties({placeholder:this.inputElement.getAttribute(r)},!t);break;case"style":this.inputElement.setAttribute("style",""+this.inputElement.getAttribute(r));break;case"name":this.inputElement.setAttribute("name",""+this.inputElement.getAttribute(r));break;case"value":if((0,e.hX)(this.datepickerOptions)||void 0===this.datepickerOptions.value||t){var p=this.inputElement.getAttribute(r);this.setProperties((0,e.KY)(r,this.globalize.parseDate(this.getAmPmValue(p),a),{}),!t)}break;case"min":if(+this.min==+new Date(1900,0,1)||t){var c=this.inputElement.getAttribute(r);this.setProperties((0,e.KY)(r,this.globalize.parseDate(this.getAmPmValue(c),a),{}),!t)}break;case"max":if(+this.max==+new Date(2099,11,31)||t){var f=this.inputElement.getAttribute(r);this.setProperties((0,e.KY)(r,this.globalize.parseDate(this.getAmPmValue(f),a),{}),!t)}break;case"type":"text"!==this.inputElement.getAttribute(r)&&this.inputElement.setAttribute("type","text")}}},s.prototype.getModuleName=function(){return"datepicker"},s.prototype.disabledDates=function(t,i){void 0===t&&(t=!1),void 0===i&&(i=!1);var l=this.checkDateValue(this.value)?new Date(+this.value):new Date(this.checkValue(this.value)),r=this.previousDate;this.minMaxUpdates(),(!t||t&&!(0,e.hX)(this.renderDayCell))&&u.prototype.render.call(this),this.previousDate=r;var p,o='*[id^="/id"]'.replace("/id",""+(l&&+l));this.strictMode||("string"==typeof this.value||"object"==typeof this.value&&+this.value!=+l)&&this.setProperties({value:l},!0),!(0,e.hX)(this.calendarElement)&&!(0,e.hX)(this.calendarElement.querySelectorAll(o)[0])&&this.calendarElement.querySelectorAll(o)[0].classList.contains("e-disabled")&&(this.strictMode||(this.currentDate=new Date((new Date).setHours(0,0,0,0)))),p="datetimepicker"===this.getModuleName()?this.globalize.formatDate(l,"Gregorian"===this.calendarMode?{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:(0,e.hX)(this.formatString)?this.dateTimeFormat:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}):this.globalize.formatDate(l,"Gregorian"===this.calendarMode?{format:this.formatString,type:"dateTime",skeleton:"yMd"}:{format:this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}),this.popupObj||(this.updateInputValue(p),this.enableMask&&(this.updateInputValue(this.maskedDateValue),this.notify("createMask",{module:"MaskedDateTime",isBlur:i})))},s.prototype.setAriaAttributes=function(){this.isCalendar()?(v.pd.addAttributes({"aria-expanded":"true"},this.inputElement),(0,e.uK)(this.inputElement,{"aria-owns":this.inputElement.id+"_options"}),(0,e.uK)(this.inputElement,{"aria-controls":this.inputElement.id}),this.value&&(0,e.uK)(this.inputElement,{"aria-activedescendant":""+this.setActiveDescendant()})):(v.pd.addAttributes({"aria-expanded":"false"},this.inputElement),this.inputElement.removeAttribute("aria-owns"),this.inputElement.removeAttribute("aria-controls"),this.inputElement.removeAttribute("aria-activedescendant"))},s.prototype.errorClass=function(){var t='*[id^="/id"]'.replace("/id",""+ +this.value),i=this.calendarElement&&this.calendarElement.querySelectorAll(t)[0]&&this.calendarElement.querySelectorAll(t)[0].classList.contains("e-disabled");!((0,e.hX)(this.value)||(0,e.hX)(this.min)||(0,e.hX)(this.max)||new Date(this.value).setMilliseconds(0)>=new Date(this.min).setMilliseconds(0)&&new Date(this.value).setMilliseconds(0)<=new Date(this.max).setMilliseconds(0))||!this.strictMode&&""!==this.inputElement.value&&this.inputElement.value!==this.maskedDateValue&&(0,e.hX)(this.value)||i||!this.isValidTime(this.value)?((0,e.iQ)([this.inputWrapper.container],Se),(0,e.uK)(this.inputElement,{"aria-invalid":"true"})):(0,e.hX)(this.inputWrapper)||((0,e.vy)([this.inputWrapper.container],Se),(0,e.uK)(this.inputElement,{"aria-invalid":"false"}))},s.prototype.isValidTime=function(t){return!0},s.prototype.onPropertyChanged=function(t,i){for(var a=0,n=Object.keys(t);a<n.length;a++){var l=n[a];switch(["blur","change","cleared","close","created","destroyed","focus","navigated","open","renderDayCell"].indexOf(l)>0&&this.isReact&&(this.isDynamicValueChanged=!0),l){case"value":this.isDynamicValueChanged=!0,this.isInteracted=!1,this.invalidValueString=null,this.checkInvalidValue(t.value),t.value=this.value,this.previousElementValue=this.inputElement.value,(0,e.hX)(this.value)&&(this.updateInputValue(this.enableMask?this.maskedDateValue:""),this.currentDate=new Date((new Date).setHours(0,0,0,0))),this.updateInput(!0),+this.previousDate!=+this.value&&this.changeTrigger(null),this.isInteracted=!0,this.preventChange=this.isAngular&&this.preventChange?!this.preventChange:this.preventChange,this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"});break;case"format":this.checkFormat(),this.bindInputEvent(),this.updateInput(),this.enableMask&&(this.notify("createMask",{module:"MaskedDateTime"}),this.value||this.updateInputValue(this.maskedDateValue));break;case"allowEdit":this.setAllowEdit();break;case"placeholder":v.pd.setPlaceholder(this.placeholder,this.inputElement);break;case"readonly":v.pd.setReadonly(this.readonly,this.inputElement);break;case"enabled":v.pd.setEnabled(this.enabled,this.inputElement),this.setAriaDisabled();break;case"htmlAttributes":this.updateHtmlAttributeToElement(),this.updateHtmlAttributeToWrapper(),this.checkHtmlAttributes(!0);break;case"locale":this.globalize=new e.DL(this.locale),this.l10n.setLocale(this.locale),this.datepickerOptions&&null==this.datepickerOptions.placeholder&&(this.setProperties({placeholder:this.l10n.getConstant("placeholder")},!0),v.pd.setPlaceholder(this.placeholder,this.inputElement)),this.updateInput(),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"});break;case"enableRtl":v.pd.setEnableRtl(this.enableRtl,[this.inputWrapper.container]);break;case"start":case"depth":this.checkView(),this.calendarElement&&u.prototype.onPropertyChanged.call(this,t,i);break;case"zIndex":this.setProperties({zIndex:t.zIndex},!0);break;case"cssClass":this.updateCssClass(t.cssClass,i.cssClass);break;case"showClearButton":v.pd.setClearButton(this.showClearButton,this.inputElement,this.inputWrapper),this.bindClearEvent(),this.index=this.showClearButton?2:1;break;case"strictMode":this.invalidValueString=null,this.updateInput();break;case"width":this.setWidth(t.width),v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"floatLabelType":this.floatLabelType=t.floatLabelType,v.pd.removeFloating(this.inputWrapper),v.pd.addFloating(this.inputElement,this.floatLabelType,this.placeholder),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"enableMask":this.enableMask?(this.notify("createMask",{module:"MaskedDateTime"}),this.updateInputValue(this.maskedDateValue),this.bindInputEvent()):this.inputElement.value===this.maskedDateValue&&this.updateInputValue("");break;default:this.calendarElement&&this.isCalendar()&&u.prototype.onPropertyChanged.call(this,t,i)}this.isDynamicValueChanged||this.hide(null),this.isDynamicValueChanged=!1}},I([(0,e.mA)(null)],s.prototype,"width",void 0),I([(0,e.mA)(null)],s.prototype,"value",void 0),I([(0,e.mA)(null)],s.prototype,"cssClass",void 0),I([(0,e.mA)(!1)],s.prototype,"strictMode",void 0),I([(0,e.mA)(null)],s.prototype,"format",void 0),I([(0,e.mA)(!0)],s.prototype,"enabled",void 0),I([(0,e.mA)(!1)],s.prototype,"fullScreenMode",void 0),I([(0,e.mA)({})],s.prototype,"htmlAttributes",void 0),I([(0,e.mA)(null)],s.prototype,"values",void 0),I([(0,e.mA)(!1)],s.prototype,"isMultiSelection",void 0),I([(0,e.mA)(!0)],s.prototype,"showClearButton",void 0),I([(0,e.mA)(!0)],s.prototype,"allowEdit",void 0),I([(0,e.mA)(null)],s.prototype,"keyConfigs",void 0),I([(0,e.mA)(!1)],s.prototype,"enablePersistence",void 0),I([(0,e.mA)(1e3)],s.prototype,"zIndex",void 0),I([(0,e.mA)(!1)],s.prototype,"readonly",void 0),I([(0,e.mA)(null)],s.prototype,"placeholder",void 0),I([(0,e.mA)("Never")],s.prototype,"floatLabelType",void 0),I([(0,e.mA)(null)],s.prototype,"serverTimezoneOffset",void 0),I([(0,e.mA)(!1)],s.prototype,"openOnFocus",void 0),I([(0,e.mA)(!1)],s.prototype,"enableMask",void 0),I([(0,e.mA)({day:"day",month:"month",year:"year",hour:"hour",minute:"minute",second:"second",dayOfTheWeek:"day of the week"})],s.prototype,"maskPlaceholder",void 0),I([(0,e.Jh)()],s.prototype,"open",void 0),I([(0,e.Jh)()],s.prototype,"cleared",void 0),I([(0,e.Jh)()],s.prototype,"close",void 0),I([(0,e.Jh)()],s.prototype,"blur",void 0),I([(0,e.Jh)()],s.prototype,"focus",void 0),I([(0,e.Jh)()],s.prototype,"created",void 0),I([(0,e.Jh)()],s.prototype,"destroyed",void 0),I([e.kc],s)}(Ve),Pe=function(){var u=function(s,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var n in a)a.hasOwnProperty(n)&&(i[n]=a[n])})(s,t)};return function(s,t){function i(){this.constructor=s}u(s,t),s.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}}(),D=function(u,s,t,i){var l,a=arguments.length,n=a<3?s:null===i?i=Object.getOwnPropertyDescriptor(s,t):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(u,s,t,i);else for(var r=u.length-1;r>=0;r--)(l=u[r])&&(n=(a<3?l(n):a>3?l(s,t,n):l(s,t))||n);return a>3&&n&&Object.defineProperty(s,t,n),n},Xe="e-input-group",ee="e-popup",N="e-left-calendar",P="e-right-calendar",Ct="e-daterangepicker",We="e-error",T="e-active",L="e-start-date",K="e-end-date",He="e-start-btn",ot="e-input-focus",Z="e-range-hover",mt="e-other-month",Ht="e-start-label",Lt="e-end-label",k="e-disabled",Q="e-selected",O="e-calendar",Ft="e-next",Rt="e-prev",F="e-header",ut="e-title",ie="e-icon-container",se="e-date-range-container",kt="e-presets",Le="e-today",pt="e-focused-date",ft="e-content",Mt="e-day-span",Vt="e-week-number",st="e-date-disabled",R="e-icon-disabled",Fe="e-device",Y="e-overlay",_="e-list-item",B="e-hover",Re="e-range-overflow",ae="e-non-edit",je="e-daterange-hidden",ze=["title","class","style"],Cs=function(u){function s(){return null!==u&&u.apply(this,arguments)||this}return Pe(s,u),D([(0,e.mA)()],s.prototype,"label",void 0),D([(0,e.mA)()],s.prototype,"start",void 0),D([(0,e.mA)()],s.prototype,"end",void 0),s}(e.I1),ks=function(u){function s(t,i){var a=u.call(this,t,i)||this;return a.isCustomRange=!1,a.isCustomWindow=!1,a.presetsItem=[],a.liCollections=[],a.previousEleValue="",a.isKeyPopup=!1,a.dateDisabled=!1,a.isRangeIconClicked=!1,a.isMaxDaysClicked=!1,a.disabledDays=[],a.preventBlur=!1,a.preventFocus=!1,a.invalidValueString=null,a.preventChange=!1,a.dateRangeOptions=t,a}return Pe(s,u),s.prototype.render=function(){this.initialize(),this.setProperties({startDate:this.startValue},!0),this.setProperties({endDate:this.endValue},!0),this.setModelValue(),this.setDataAttribute(!1),this.element.hasAttribute("data-val")&&this.element.setAttribute("data-val","false"),"Never"!==this.floatLabelType&&v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon"),!(0,e.hX)((0,e.kp)(this.element,"fieldset"))&&(0,e.kp)(this.element,"fieldset").disabled&&(this.enabled=!1),this.renderComplete()},s.prototype.preRender=function(){if(this.keyInputConfigs={altDownArrow:"alt+downarrow",escape:"escape",enter:"enter",tab:"tab",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow",moveUp:"uparrow",moveDown:"downarrow",spacebar:"space"},this.defaultConstant={placeholder:this.placeholder,startLabel:"Start Date",endLabel:"End Date",customRange:"Custom Range",applyText:"Apply",cancelText:"Cancel",selectedDays:"Selected Days",days:"days"},this.isMobile=!!e.Pw.isDevice||window.matchMedia("(max-width:550px)").matches,this.inputElement=this.element,this.angularTag=null,"EJS-DATERANGEPICKER"===this.element.tagName&&(this.angularTag=this.element.tagName,this.inputElement=this.createElement("input"),this.element.appendChild(this.inputElement)),this.cloneElement=this.element.cloneNode(!0),(0,e.vy)([this.cloneElement],[Ct,"e-control","e-lib"]),this.updateHtmlAttributeToElement(),this.element.getAttribute("id")?null!==this.angularTag&&(this.inputElement.id=this.element.getAttribute("id")+"_input"):(this.element.id=(0,e.Lz)("ej2-datetimepicker"),null!==this.angularTag&&(0,e.uK)(this.inputElement,{id:this.element.id+"_input"})),this.checkInvalidRange(this.value),!this.invalidValueString&&"string"==typeof this.value){var t=this.value.split(" "+this.separator+" ");this.value=[new Date(t[0]),new Date(t[1])]}this.initProperty(),this.tabIndex=this.element.hasAttribute("tabindex")?this.element.getAttribute("tabindex"):"0",this.element.removeAttribute("tabindex"),u.prototype.preRender.call(this),this.navNextFunction=this.navNextMonth.bind(this),this.navPrevFunction=this.navPrevMonth.bind(this),this.deviceNavNextFunction=this.deviceNavNext.bind(this),this.deviceNavPrevFunction=this.deviceNavPrevious.bind(this),this.initStartDate=this.checkDateValue(this.startValue),this.initEndDate=this.checkDateValue(this.endValue),this.formElement=(0,e.kp)(this.element,"form")},s.prototype.updateValue=function(){this.value&&this.value.length>0?(this.value[0]instanceof Date&&!isNaN(+this.value[0])?(this.setProperties({startDate:this.value[0]},!0),this.startValue=this.value[0]):"string"==typeof this.value[0]?0==+this.value[0]||isNaN(+new Date(this.checkValue(this.value[0])))?(this.startValue=null,this.setValue()):(this.setProperties({startDate:new Date(this.checkValue(this.value[0]))},!0),this.startValue=new Date(this.checkValue(this.value[0]))):(this.startValue=null,this.setValue()),this.value[1]instanceof Date&&!isNaN(+this.value[1])?(this.setProperties({endDate:this.value[1]},!0),this.endValue=this.value[1]):"string"==typeof this.value[1]?0==+this.value[0]||isNaN(+new Date(this.checkValue(this.value[0])))?(this.setProperties({endDate:null},!0),this.endValue=null,this.setValue()):(this.setProperties({endDate:new Date(this.checkValue(this.value[1]))},!0),this.endValue=new Date(this.checkValue(this.value[1])),this.setValue()):(this.setProperties({endDate:null},!0),this.endValue=null,this.setValue())):this.value&&this.value.start?(this.value.start instanceof Date&&!isNaN(+this.value.start)?(this.setProperties({startDate:this.value.start},!0),this.startValue=this.value.start):"string"==typeof this.value.start?(this.setProperties({startDate:new Date(this.checkValue(this.value.start))},!0),this.startValue=new Date(this.checkValue(this.value.start))):(this.startValue=null,this.setValue()),this.value.end instanceof Date&&!isNaN(+this.value.end)?(this.setProperties({endDate:this.value.end},!0),this.endValue=this.value.end):"string"==typeof this.value.end?(this.setProperties({endDate:new Date(this.checkValue(this.value.end))},!0),this.endValue=new Date(this.checkValue(this.value.end)),this.setValue()):(this.setProperties({endDate:null},!0),this.endValue=null,this.setValue())):(0,e.hX)(this.value)&&(this.endValue=this.checkDateValue(new Date(this.checkValue(this.endDate))),this.startValue=this.checkDateValue(new Date(this.checkValue(this.startDate))),this.setValue())},s.prototype.initProperty=function(){this.globalize=new e.DL(this.locale),this.checkFormat(),this.checkView(),((0,e.hX)(this.firstDayOfWeek)||this.firstDayOfWeek>6||this.firstDayOfWeek<0)&&this.setProperties({firstDayOfWeek:this.globalize.getFirstDayOfWeek()},!0),this.updateValue()},s.prototype.checkFormat=function(){this.formatString=this.format?"string"==typeof this.format?this.format:""===this.format.skeleton||(0,e.hX)(this.format.skeleton)?null:this.globalize.getDatePattern({skeleton:this.format.skeleton,type:"date"}):null},s.prototype.initialize=function(){null!==this.angularTag&&this.validationAttribute(this.element,this.inputElement),this.checkHtmlAttributes(!1),(0,e.h1)(this.defaultKeyConfigs,{shiftTab:"shift+tab",tab:"tab"});var t=this.checkDateValue(new Date(this.checkValue(this.startValue)));this.setProperties({startDate:t},!0),this.setProperties({endValue:this.checkDateValue(new Date(this.checkValue(this.endValue)))},!0),this.setValue(),this.setProperties({min:this.checkDateValue(new Date(this.checkValue(this.min)))},!0),this.setProperties({max:this.checkDateValue(new Date(this.checkValue(this.max)))},!0),this.l10n=new e.Wo("daterangepicker",this.defaultConstant,this.locale),this.l10n.setLocale(this.locale),this.setProperties({placeholder:this.placeholder||this.l10n.getConstant("placeholder")},!0),this.processPresets(),this.createInput(),this.updateHtmlAttributeToWrapper(),this.setRangeAllowEdit(),this.bindEvents()},s.prototype.setDataAttribute=function(t){var i={};if(t)i=this.htmlAttributes;else for(var a=0;a<this.element.attributes.length;a++)i[this.element.attributes[a].name]=this.element.getAttribute(this.element.attributes[a].name);for(var n=0,l=Object.keys(i);n<l.length;n++){var r=l[n];0===r.indexOf("data")&&(this.firstHiddenChild.setAttribute(r,i[""+r]),this.secondHiddenChild.setAttribute(r,i[""+r]))}},s.prototype.setRangeAllowEdit=function(){this.allowEdit?this.readonly||this.inputElement.removeAttribute("readonly"):(0,e.uK)(this.inputElement,{readonly:""}),this.updateClearIconState()},s.prototype.updateClearIconState=function(){this.allowEdit||!this.inputWrapper||this.readonly?this.inputWrapper&&(0,e.vy)([this.inputWrapper.container],[ae]):""===this.inputElement.value?(0,e.vy)([this.inputWrapper.container],[ae]):(0,e.iQ)([this.inputWrapper.container],[ae])},s.prototype.validationAttribute=function(t,i){var a=t.getAttribute("name")?t.getAttribute("name"):t.getAttribute("id");i.setAttribute("name",a),t.removeAttribute("name");for(var n=["required","aria-required","form"],l=0;l<n.length;l++)if(!(0,e.hX)(t.getAttribute(n[l]))){var r=t.getAttribute(n[l]);i.setAttribute(n[l],r),t.removeAttribute(n[l])}},s.prototype.updateHtmlAttributeToWrapper=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];if(ze.indexOf(a)>-1)if("class"===a){var n=this.htmlAttributes[""+a].replace(/\s+/g," ").trim();""!==n&&(0,e.iQ)([this.inputWrapper.container],n.split(" "))}else if("style"===a){var l=this.inputWrapper.container.getAttribute(a);l=(0,e.hX)(l)?this.htmlAttributes[""+a]:l+this.htmlAttributes[""+a],this.inputWrapper.container.setAttribute(a,l)}else this.inputWrapper.container.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateHtmlAttributeToElement=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];ze.indexOf(a)<0&&this.inputElement.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateCssClass=function(t,i){(0,e.hX)(i)||(i=i.replace(/\s+/g," ").trim()),(0,e.hX)(t)||(t=t.replace(/\s+/g," ").trim()),v.pd.setCssClass(t,[this.inputWrapper.container],i),this.popupWrapper&&v.pd.setCssClass(t,[this.popupWrapper],i)},s.prototype.processPresets=function(){this.presetsItem=[];var t=0;if(!(0,e.hX)(this.presets[0])&&!(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)){for(var i=0,a=this.presets;i<a.length;i++){var n=a[i],l=n.label.replace(/\s+/g,"")+"_"+ ++t;this.presetsItem.push("string"==typeof n.end?{id:l,text:n.label,end:new Date(this.checkValue(n.end)),start:new Date(this.checkValue(n.start))}:{id:l,text:n.label,start:n.start,end:n.end})}var r=(0,e.hX)(this.startValue)?null:new Date(+this.startValue),h=(0,e.hX)(this.endValue)?null:new Date(+this.endValue);this.presetsItem.push({id:"custom_range",text:this.l10n.getConstant("customRange"),start:r,end:h}),!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&(this.isCustomRange=!0,this.activeIndex=this.presetsItem.length-1)}},s.prototype.bindEvents=function(){e.Jm.add(this.inputWrapper.buttons[0],"mousedown",this.rangeIconHandler,this),e.Jm.add(this.inputElement,"focus",this.inputFocusHandler,this),e.Jm.add(this.inputElement,"blur",this.inputBlurHandler,this),e.Jm.add(this.inputElement,"change",this.inputChangeHandler,this),this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown",this.resetHandler,this),this.isMobile||(this.keyInputConfigs=(0,e.X$)(this.keyInputConfigs,this.keyConfigs),this.inputKeyboardModule=new e.j9(this.inputElement,{eventName:"keydown",keyAction:this.inputHandler.bind(this),keyConfigs:this.keyInputConfigs})),this.formElement&&e.Jm.add(this.formElement,"reset",this.formResetHandler,this),this.enabled?this.inputElement.setAttribute("tabindex",this.tabIndex):this.inputElement.tabIndex=-1},s.prototype.unBindEvents=function(){e.Jm.remove(this.inputWrapper.buttons[0],"mousedown",this.rangeIconHandler),e.Jm.remove(this.inputElement,"blur",this.inputBlurHandler),e.Jm.remove(this.inputElement,"focus",this.inputFocusHandler),e.Jm.remove(this.inputElement,"change",this.inputChangeHandler),this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.remove(this.inputWrapper.clearButton,"mousedown touchstart",this.resetHandler),this.isMobile||(0,e.hX)(this.inputKeyboardModule)||this.inputKeyboardModule.destroy(),this.formElement&&e.Jm.remove(this.formElement,"reset",this.formResetHandler),this.inputElement.tabIndex=-1},s.prototype.updateHiddenInput=function(){if(this.firstHiddenChild&&this.secondHiddenChild){var t={format:this.formatString,type:"datetime",skeleton:"yMd"};"string"==typeof this.startDate&&(this.startDate=this.globalize.parseDate(this.getAmPmValue(this.startDate),t)),"string"==typeof this.endDate&&(this.endDate=this.globalize.parseDate(this.getAmPmValue(this.endDate),t)),this.firstHiddenChild.value=this.startDate&&this.globalize.formatDate(this.startDate,t)||this.inputElement.value,this.secondHiddenChild.value=this.endDate&&this.globalize.formatDate(this.endDate,t)||this.inputElement.value,this.dispatchEvent(this.firstHiddenChild,"focusout"),this.dispatchEvent(this.firstHiddenChild,"change")}},s.prototype.inputChangeHandler=function(t){this.enabled&&(t.stopPropagation(),this.updateHiddenInput())},s.prototype.bindClearEvent=function(){this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown",this.resetHandler,this)},s.prototype.resetHandler=function(t){if(this.enabled){this.valueType=this.value,t.preventDefault(),this.clear();var i={event:t};if(this.setProperties({endDate:this.checkDateValue(this.endValue)},!0),this.setProperties({startDate:this.checkDateValue(this.startValue)},!0),this.trigger("cleared",i),this.changeTrigger(t),this.clearRange(),this.hide(t),(0,e.kp)(this.element,"form")){var a=this.firstHiddenChild,n=document.createEvent("KeyboardEvent");n.initEvent("keyup",!1,!0),a.dispatchEvent(n)}}},s.prototype.restoreValue=function(){this.previousEleValue=this.inputElement.value,this.previousStartValue=this.startValue,this.previousEndValue=this.endValue,this.valueType=null,this.initStartDate=this.checkDateValue(this.startValue),this.initEndDate=this.checkDateValue(this.endValue),this.setValue(),this.setModelValue()},s.prototype.formResetHandler=function(t){if(this.enabled&&this.formElement&&t.target===this.formElement&&!this.inputElement.disabled){var i=this.inputElement.getAttribute("value");(0,e.hX)(this.startCopy)?(this.setProperties({value:null,startDate:null,endDate:null},!0),this.startValue=this.endValue=null):((0,e.hX)(this.value)||(0,e.hX)(this.value.start)?(this.setProperties({value:[this.startCopy,this.endCopy]},!0),this.startValue=this.value[0],this.endValue=this.value[1]):(this.setProperties({value:{start:this.startCopy,end:this.endCopy}},!0),this.startValue=this.value.start,this.endValue=this.value.end),this.setProperties({startDate:this.startValue,endDate:this.endValue},!0)),"EJS-DATERANGEPICKER"===this.element.tagName&&(this.setProperties({value:null,startDate:null,endDate:null},!0),i="",this.startValue=this.endValue=null,this.inputElement.setAttribute("value","")),this.restoreValue(),this.inputElement&&(v.pd.setValue(i,this.inputElement,this.floatLabelType,this.showClearButton),this.errorClass())}},s.prototype.clear=function(){null!==this.startValue&&(this.startValue=null),null!==this.endValue&&(this.endValue=null),this.value&&this.value.start&&this.setProperties({value:{start:null,end:null}},!0),null!==this.value&&this.value.length>0&&this.setProperties({value:null},!0),v.pd.setValue("",this.inputElement,this.floatLabelType,this.showClearButton),(0,e.hX)(this.applyButton)||(this.applyButton.disabled=this.applyButton.element.disabled=!0),this.removeSelection()},s.prototype.rangeIconHandler=function(t){this.enabled&&(this.isMobile&&this.inputElement.setAttribute("readonly",""),t.preventDefault(),this.targetElement=null,this.isPopupOpen()&&document.body.contains(this.popupObj.element)?this.applyFunction(t):(this.isRangeIconClicked=!0,this.inputWrapper.container.children[0].focus(),this.show(null,t),this.isMobile||(0,e.hX)(this.leftCalendar)||(this.isRangeIconClicked=!1,this.calendarFocus(),this.isRangeIconClicked=!0),(0,e.iQ)([this.inputWrapper.container],[ot])))},s.prototype.checkHtmlAttributes=function(t){this.globalize=new e.DL(this.locale);for(var i=t?(0,e.hX)(this.htmlAttributes)?[]:Object.keys(this.htmlAttributes):["startDate","endDate","minDays","maxDays","min","max","disabled","readonly","style","name","placeholder","type","value"],a={format:this.formatString,type:"date",skeleton:"yMd"},n=0,l=i;n<l.length;n++){var r=l[n];if(!(0,e.hX)(this.inputElement.getAttribute(r)))switch(r){case"disabled":if((0,e.hX)(this.dateRangeOptions)||void 0===this.dateRangeOptions.enabled||t){var h="disabled"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r);this.setProperties({enabled:!h},!t)}break;case"readonly":if((0,e.hX)(this.dateRangeOptions)||void 0===this.dateRangeOptions.readonly||t){var o="readonly"===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r);this.setProperties({readonly:o},!t)}break;case"placeholder":((0,e.hX)(this.dateRangeOptions)||void 0===this.dateRangeOptions.placeholder||t)&&this.setProperties({placeholder:this.inputElement.getAttribute(r)},!t);break;case"value":if((0,e.hX)(this.dateRangeOptions)||void 0===this.dateRangeOptions.value||t){var p=this.inputElement.getAttribute(r);this.setProperties((0,e.KY)(r,p,{}),!t)}break;case"style":this.inputElement.setAttribute("style",""+this.inputElement.getAttribute(r));break;case"min":if((0,e.hX)(this.min)||+this.min==+new Date(1900,0,1)||t){var c=this.globalize.parseDate(this.getAmPmValue(this.inputElement.getAttribute(r)),a);this.setProperties((0,e.KY)(r,c,{}),!t)}break;case"name":this.inputElement.setAttribute("name",""+this.inputElement.getAttribute(r));break;case"max":((0,e.hX)(this.max)||+this.max==+new Date(2099,11,31)||t)&&(c=this.globalize.parseDate(this.getAmPmValue(this.inputElement.getAttribute(r)),a),this.setProperties((0,e.KY)(r,c,{}),!t));break;case"startDate":(0,e.hX)(this.startDate)&&(c=this.globalize.parseDate(this.getAmPmValue(this.inputElement.getAttribute(r)),a),this.startValue=c,this.setValue());break;case"endDate":(0,e.hX)(this.endDate)&&(c=this.globalize.parseDate(this.getAmPmValue(this.inputElement.getAttribute(r)),a),this.endValue=c,this.setValue());break;case"minDays":(0,e.hX)(this.minDays)&&this.setProperties((0,e.KY)(r,parseInt(this.inputElement.getAttribute(r),10),{}),!0);break;case"maxDays":(0,e.hX)(this.maxDays)&&this.setProperties((0,e.KY)(r,parseInt(this.inputElement.getAttribute(r),10),{}),!0);break;case"type":"text"!==this.inputElement.getAttribute(r)&&this.inputElement.setAttribute("type","text")}}},s.prototype.createPopup=function(){for(var t=0;t<this.presetsItem.length;t++)t!==this.presetsItem.length-1&&"custom_range"===this.presetsItem[t].id&&this.presetsItem.splice(t,1);for(this.activeIndex=this.presetsItem.length-1,this.isCustomRange=!0,t=0;t<=this.presetsItem.length-2;t++){var i=this.presetsItem[t].start,a=this.presetsItem[t].end;this.startValue&&this.endValue&&i.getDate()===this.startValue.getDate()&&i.getMonth()===this.startValue.getMonth()&&i.getFullYear()===this.startValue.getFullYear()&&a.getDate()===this.endValue.getDate()&&a.getMonth()===this.endValue.getMonth()&&a.getFullYear()===this.endValue.getFullYear()&&(this.activeIndex=t,this.isCustomRange=!1)}this.popupWrapper=(0,e.n)("div",{id:this.element.id+"_popup",className:Ct+" "+ee}),this.popupWrapper.setAttribute("aria-label",this.element.id),this.popupWrapper.setAttribute("role","dialog"),this.adjustLongHeaderWidth();var n=!this.isCustomRange||this.isMobile;!(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)&&n?(this.isCustomWindow=!1,this.createPresets(),this.listRippleEffect(),this.renderPopup()):(this.isCustomWindow=!0,this.renderControl())},s.prototype.renderControl=function(){this.createControl(),this.bindCalendarEvents(),this.updateRange(this.isMobile?[this.calendarElement]:[this.leftCalendar,this.rightCalendar]),!(0,e.hX)(this.endValue)&&!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.renderDayCellArgs)&&this.renderDayCellArgs.isDisabled&&this.disabledDateRender(),this.updateHeader()},s.prototype.clearCalendarEvents=function(){this.leftCalPrevIcon&&this.leftCalNextIcon&&this.rightCalPrevIcon&&this.rightCalNextIcon&&(e.Jm.clearEvents(this.leftCalPrevIcon),e.Jm.clearEvents(this.leftCalNextIcon),e.Jm.clearEvents(this.rightCalPrevIcon),e.Jm.clearEvents(this.rightCalNextIcon))},s.prototype.updateNavIcons=function(){u.prototype.iconHandler.call(this)},s.prototype.calendarIconEvent=function(){this.clearCalendarEvents(),this.leftCalPrevIcon&&!this.leftCalPrevIcon.classList.contains(k)&&e.Jm.add(this.leftCalPrevIcon,"mousedown",this.navPrevFunction),this.leftCalNextIcon&&!this.leftCalNextIcon.classList.contains(k)&&e.Jm.add(this.leftCalNextIcon,"mousedown",this.navNextFunction),this.rightCalPrevIcon&&!this.rightCalPrevIcon.classList.contains(k)&&e.Jm.add(this.rightCalPrevIcon,"mousedown",this.navPrevFunction),this.rightCalNextIcon&&!this.rightCalNextIcon.classList.contains(k)&&e.Jm.add(this.rightCalNextIcon,"mousedown",this.navNextFunction)},s.prototype.bindCalendarEvents=function(){this.isMobile?(this.deviceCalendarEvent(),e.Jm.add(this.startButton.element,"click",this.deviceHeaderClick,this),e.Jm.add(this.endButton.element,"click",this.deviceHeaderClick,this)):(this.updateNavIcons(),this.calendarIconEvent(),this.calendarIconRipple(),this.headerTitleElement=this.popupObj.element.querySelector("."+P+" ."+F+" ."+ut),this.headerTitleElement=this.popupObj.element.querySelector("."+N+" ."+F+" ."+ut),this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,this.keyConfigs),this.leftKeyboardModule=new e.j9(this.leftCalendar,{eventName:"keydown",keyAction:this.keyInputHandler.bind(this),keyConfigs:this.defaultKeyConfigs}),this.rightKeyboardModule=new e.j9(this.rightCalendar,{eventName:"keydown",keyAction:this.keyInputHandler.bind(this),keyConfigs:this.defaultKeyConfigs})),this.start===this.depth&&this.bindCalendarCellEvents(),this.removeFocusedDate()},s.prototype.calendarIconRipple=function(){(0,e.CF)(this.leftCalPrevIcon,{selector:".e-prev",duration:400,isCenterRipple:!0}),(0,e.CF)(this.leftCalNextIcon,{selector:".e-next",duration:400,isCenterRipple:!0}),(0,e.CF)(this.rightCalPrevIcon,{selector:".e-prev",duration:400,isCenterRipple:!0}),(0,e.CF)(this.rightCalNextIcon,{selector:".e-next",duration:400,isCenterRipple:!0})},s.prototype.deviceCalendarEvent=function(){e.Jm.clearEvents(this.nextIcon),e.Jm.clearEvents(this.previousIcon),(0,e.CF)(this.nextIcon,{selector:".e-prev",duration:400,isCenterRipple:!0}),(0,e.CF)(this.previousIcon,{selector:".e-next",duration:400,isCenterRipple:!0}),this.nextIcon&&!this.nextIcon.classList.contains(k)&&e.Jm.add(this.nextIcon,"mousedown",this.deviceNavNextFunction),this.previousIcon&&!this.previousIcon.classList.contains(k)&&e.Jm.add(this.previousIcon,"mousedown",this.deviceNavPrevFunction)},s.prototype.deviceNavNext=function(t){var i=(0,e.kp)(t.target,"."+O);this.updateDeviceCalendar(i),this.navigateNext(t),this.deviceNavigation()},s.prototype.deviceNavPrevious=function(t){var i=(0,e.kp)(t.target,"."+O);this.updateDeviceCalendar(i),this.navigatePrevious(t),this.deviceNavigation()},s.prototype.updateDeviceCalendar=function(t){t&&(this.previousIcon=t.querySelector("."+Rt),this.nextIcon=t.querySelector("."+Ft),this.calendarElement=t,this.deviceCalendar=t,this.contentElement=t.querySelector("."+ft),this.tableBodyElement=(0,e.Lt)("."+ft+" tbody",t),this.table=t.querySelector("."+ft).getElementsByTagName("table")[0],this.headerTitleElement=t.querySelector("."+F+" ."+ut),this.headerElement=t.querySelector("."+F))},s.prototype.deviceHeaderClick=function(t){if(t.currentTarget.classList.contains(He)&&!(0,e.hX)(this.startValue)){this.endButton.element.classList.remove(T),this.startButton.element.classList.add(T);var a=this.popupObj.element.querySelector("."+O);this.updateDeviceCalendar(a),(0,e.hX)(this.calendarElement.querySelector("."+L+":not(.e-other-month)"))&&(this.currentDate=new Date(+this.startValue),(0,e.TF)(this.tableBodyElement),this.createContentBody(),this.deviceNavigation()),this.removeClassDisabled()}else(0,e.hX)(this.startValue)||(0,e.hX)(this.endValue)||(this.startButton.element.classList.remove(T),this.endButton.element.classList.add(T),a=this.popupObj.element.querySelector("."+O),this.updateDeviceCalendar(a),(0,e.hX)(this.calendarElement.querySelector("."+K+":not(.e-other-month)"))&&(this.currentDate=new Date(+this.endValue),(0,e.TF)(this.tableBodyElement),this.createContentBody(),this.deviceNavigation()),this.updateMinMaxDays(this.popupObj.element.querySelector("."+O)),this.selectableDates())},s.prototype.inputFocusHandler=function(){this.enabled&&(this.preventBlur=!1,this.preventFocus||this.trigger("focus",{model:this}),this.updateClearIconState(),this.openOnFocus&&!this.preventFocus?(this.preventFocus=!0,this.show()):this.preventFocus=!0)},s.prototype.inputBlurHandler=function(t){if(this.enabled){if(!this.preventBlur){var i=this.inputElement.value;if((0,e.hX)(this.presetsItem)||this.presetsItem.length>0&&this.previousEleValue!==this.inputElement.value&&(this.activeIndex=this.presetsItem.length-1,this.isCustomRange=!0),!(0,e.hX)(i)&&""!==i.trim()){var a=i.split(" "+this.separator+" ");if(a.length>1){this.invalidValueString=null;var n={format:this.formatString,type:"date",skeleton:"yMd"},l=this.globalize.parseDate(this.getAmPmValue(a[0]),n),r=this.globalize.parseDate(this.getAmPmValue(a[1]),n),h=this.getStartEndDate(l,!1,a,n),o=this.getStartEndDate(r,!0,a,n);if(!((0,e.hX)(h)||isNaN(+h)||(0,e.hX)(o)||isNaN(+o))){var p=this.startValue;this.startValue=h;var c=this.endValue;return this.endValue=o,this.setValue(),this.refreshControl(),i!==this.previousEleValue&&this.changeTrigger(t),this.preventBlur||document.activeElement===this.inputElement||(this.preventFocus=!1,this.trigger("blur",{model:this})),this.updateHiddenInput(),void(this.isMobile&&this.isPopupOpen()&&(this.startValue=p,this.endValue=c))}this.strictMode||(this.startValue=null,this.endValue=null,this.setValue())}else this.strictMode||(this.startValue=null,this.endValue=null,this.setValue())}this.strictMode?(!(0,e.hX)(i)&&""===i.trim()&&(this.startValue=null,this.endValue=null),v.pd.setValue("",this.inputElement,this.floatLabelType,this.showClearButton),this.updateInput()):((0,e.hX)(this.popupObj)&&(this.currentDate=null),this.previousStartValue=this.previousEndValue=null,this.startValue=null,this.endValue=null,this.setValue()),this.errorClass(),this.changeTrigger(t),this.preventBlur||document.activeElement===this.inputElement||(this.preventFocus=!1,this.trigger("blur",{model:this}))}this.updateHiddenInput()}},s.prototype.getStartEndDate=function(t,i,a,n){return"Month"===this.depth?this.globalize.parseDate(this.getAmPmValue(a[i?1:0]).trim(),n):"Year"!==this.depth||(0,e.hX)(t)?(0,e.hX)(t)?null:new Date(t.getFullYear(),i?11:0,i?31:1):new Date(t.getFullYear(),t.getMonth()+(i?1:0),i?0:1)},s.prototype.clearRange=function(){this.previousStartValue=this.previousEndValue=null,this.currentDate=null},s.prototype.errorClass=function(){var t=(0,e.hX)(this.inputElement.value)?null:this.inputElement.value.trim();((0,e.hX)(this.endValue)&&(0,e.hX)(this.startValue)&&""!==t||!(0,e.hX)(this.startValue)&&+this.startValue<+this.min||!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue||!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||this.startValue&&this.isDateDisabled(this.startValue)||this.endValue&&this.isDateDisabled(this.endValue))&&""!==t?((0,e.iQ)([this.inputWrapper.container],We),(0,e.uK)(this.inputElement,{"aria-invalid":"true"})):this.inputWrapper&&((0,e.vy)([this.inputWrapper.container],We),(0,e.uK)(this.inputElement,{"aria-invalid":"false"}))},s.prototype.keyCalendarUpdate=function(t,i,a){return void 0===a&&(a=!0),a&&this.removeFocusedDate(),t?(this.leftCalCurrentDate=new Date(+this.currentDate),i=this.leftCalendar):(this.rightCalCurrentDate=new Date(+this.currentDate),i=this.rightCalendar),this.updateCalendarElement(i),this.table.focus(),i},s.prototype.navInCalendar=function(t,i,a,n,l){var h,p,r=this.getViewNumber(this.currentView()),o=this.min;switch(p=(0,e.hX)(this.maxDays)||!this.isMaxDaysClicked||(0,e.hX)(this.startValue)?this.max:new Date(new Date(+this.startValue).setDate(this.startValue.getDate()+(this.maxDays-1))),t.action){case"moveRight":this.addDay(h=new Date(+this.currentDate),1,t,p,o),i&&+h==+n&&(l=this.keyCalendarUpdate(!1,l)),this.keyboardNavigate(1,r,t,p,o),this.keyNavigation(l,t);break;case"moveLeft":this.addDay(h=new Date(+this.currentDate),-1,t,p,o),i||+h==+a&&(l=this.keyCalendarUpdate(!0,l)),this.keyboardNavigate(-1,r,t,p,o),this.keyNavigation(l,t);break;case"moveUp":0===r?(this.addDay(h=new Date(+this.currentDate),-7,t,p,o),+h<=+a&&!i&&(l=this.keyCalendarUpdate(!0,l)),this.keyboardNavigate(-7,r,t,p,o)):this.keyboardNavigate(-4,r,t,this.max,this.min),this.keyNavigation(l,t);break;case"moveDown":0===r?(this.addDay(h=new Date(+this.currentDate),7,t,p,o),i&&+h>=+n&&(l=this.keyCalendarUpdate(!1,l)),this.keyboardNavigate(7,r,t,p,o)):this.keyboardNavigate(4,r,t,this.max,this.min),this.keyNavigation(l,t);break;case"home":this.currentDate=this.firstDay(this.currentDate),(0,e.TF)(this.tableBodyElement),0===r?this.renderMonths(t):1===r?this.renderYears(t):this.renderDecades(t),this.keyNavigation(l,t);break;case"end":this.currentDate=this.lastDay(this.currentDate,r),(0,e.TF)(this.tableBodyElement),0===r?this.renderMonths(t):1===r?this.renderYears(t):this.renderDecades(t),this.keyNavigation(l,t);break;case"tab":this.tabKeyValidation(l,N)&&(l=this.keyCalendarUpdate(!1,l,!1),this.currentDate=this.firstCellToFocus(this.rightCalendar),r=this.getViewNumber(this.currentView()),this.keyboardNavigate(0,r,t,p,o),this.keyNavigation(l,t));break;case"shiftTab":this.tabKeyValidation(l,P)&&(l=this.keyCalendarUpdate(!0,l,!1),this.currentDate=this.firstCellToFocus(this.leftCalendar),this.keyboardNavigate(0,r,t,p,o),this.keyNavigation(l,t))}},s.prototype.firstCellToFocus=function(t){var i=2===this.getViewNumber(this.currentView())?t.children[1].firstElementChild.querySelector("td.e-cell:not(.e-week-number):not(.e-disabled):not(.e-other-year)"):t.children[1].firstElementChild.querySelector("td.e-cell:not(.e-week-number):not(.e-disabled):not(.e-other-month)"),a=i&&i.id?i.id.split("_")[0]:null,n=new Date(this.currentDate.getFullYear(),this.currentDate.getMonth(),1);return a?new Date(+a):n},s.prototype.keyInputHandler=function(t,i){var a,n=this.getViewNumber(this.currentView()),l=new Date(this.rightCalCurrentDate.getFullYear(),this.rightCalCurrentDate.getMonth(),1),r=new Date(this.leftCalCurrentDate.getFullYear(),this.leftCalCurrentDate.getMonth()+1,0),h=(0,e.kp)(t.target,"."+P),o=(h=(0,e.hX)(h)?this.leftCalendar:h).classList.contains(N);this.updateCalendarElement(h);var p=this.tableBodyElement.querySelector("tr td.e-selected"),c=h.querySelector("tr td."+pt),f=h.querySelector("tr td."+L),g=h.querySelector("tr td."+K),d=this.getViewNumber(this.depth),y=n===d&&this.getViewNumber(this.start)>=d,b=(0,e.kp)(t.target,"."+N),m=(0,e.kp)(t.target,"."+P),A=(0,e.kp)(t.target,"."+kt);switch((0,e.hX)(c)?(0,e.hX)(g)||this.dateDisabled?(0,e.hX)(f)||this.dateDisabled?this.dateDisabled||this.currentDate.setDate(1):this.currentDate=new Date(+this.startValue):this.currentDate=new Date(+this.endValue):this.currentDate=this.currentDate,this.effect="",t.action){case"altUpArrow":this.isPopupOpen()&&(this.hide(t),this.preventFocus=!0,this.inputElement.focus(),(0,e.iQ)([this.inputWrapper.container],[ot]));break;case"select":if(y){var M=(0,e.hX)(c)?f:c;!(0,e.hX)(M)&&!M.classList.contains(k)&&this.selectRange(null,M)}else(!(0,e.hX)(p)&&!y||!(0,e.hX)(c))&&((0,e.hX)(this.value)||(i=this.calendarElement.classList.contains(N)?this.startDate:this.endDate),this.controlDown=t,this.contentClick(null,--n,c||p,i));t.preventDefault();break;case"controlHome":var V=new Date(this.currentDate.getFullYear(),0,1);!o&&+V<+r&&(h=this.keyCalendarUpdate(!0,h)),u.prototype.navigateTo.call(this,"Month",new Date(this.currentDate.getFullYear(),0,1)),this.keyNavigation(h,t);break;case"altRightArrow":(0,e.hX)(b)?(0,e.hX)(m)?(0,e.hX)(A)||this.cancelButton.element.focus():(0,e.hX)(this.presetElement)?this.cancelButton.element.focus():(this.presetElement.focus(),this.removeFocusedDate()):this.rightCalendar.children[1].firstElementChild.focus(),t.preventDefault();break;case"altLeftArrow":(0,e.hX)(b)?(0,e.hX)(m)||this.leftCalendar.children[1].firstElementChild.focus():!0!==this.applyButton.element.disabled?this.applyButton.element.focus():this.cancelButton.element.focus(),t.preventDefault();break;case"controlUp":this.calendarElement.classList.contains(N),this.calendarNavigation(t,this.calendarElement),t.preventDefault();break;case"controlDown":(!(0,e.hX)(p)||!(0,e.hX)(c))&&!y&&((0,e.hX)(this.value)||(i=this.calendarElement.classList.contains(N)?this.startDate:this.endDate),this.controlDown=t,this.contentClick(null,--n,p||c,i)),t.preventDefault();break;case"controlEnd":V=new Date(this.currentDate.getFullYear(),11,31),o&&+V>+l&&(h=this.keyCalendarUpdate(!1,h)),u.prototype.navigateTo.call(this,"Month",new Date(this.currentDate.getFullYear(),11,31)),this.keyNavigation(h,t);break;case"pageUp":this.addMonths(a=new Date(+this.currentDate),-1),!o&&+a<=+r&&(h=this.keyCalendarUpdate(!0,h)),this.addMonths(this.currentDate,-1),u.prototype.navigateTo.call(this,"Month",this.currentDate),this.keyNavigation(h,t);break;case"pageDown":this.addMonths(a=new Date(+this.currentDate),1),o&&+a>=+l&&(h=this.keyCalendarUpdate(!1,h)),this.addMonths(this.currentDate,1),u.prototype.navigateTo.call(this,"Month",this.currentDate),this.keyNavigation(h,t);break;case"shiftPageUp":this.addYears(a=new Date(+this.currentDate),-1),!o&&+a<=+r&&(h=this.keyCalendarUpdate(!0,h)),this.addYears(this.currentDate,-1),u.prototype.navigateTo.call(this,"Month",this.currentDate),this.keyNavigation(h,t);break;case"shiftPageDown":this.addYears(a=new Date(+this.currentDate),1),o&&+a>=+l&&(h=this.keyCalendarUpdate(!1,h)),this.addYears(this.currentDate,1),u.prototype.navigateTo.call(this,"Month",this.currentDate),this.keyNavigation(h,t);break;case"shiftTab":(0,e.hX)(this.presetElement)||(this.presetElement.setAttribute("tabindex","0"),this.presetElement.focus(),this.removeFocusedDate()),o&&t.preventDefault(),this.tabKeyValidation(h,P)&&(this.currentDate=new Date(+this.leftCalCurrentDate),this.navInCalendar(t,o,r,l,h));break;case"spacebar":this.applyButton&&!this.applyButton.disabled&&this.applyFunction(t);break;case"tab":this.tabKeyValidation(h,N)&&(this.currentDate=new Date(+this.rightCalCurrentDate),this.navInCalendar(t,o,r,l,h));break;default:this.navInCalendar(t,o,r,l,h),this.checkMinMaxDays()}this.presetHeight()},s.prototype.tabKeyValidation=function(t,i){var a=t.classList.contains(i),n=this.rightCalendar.querySelector(".e-header"),l=this.leftCalendar.querySelector(".e-header"),r=!!n&&n.classList.contains("e-month"),h=!!l&&l.classList.contains("e-month"),o=!!n&&n.classList.contains("e-year"),p=!!l&&l.classList.contains("e-year"),c=!!n&&n.classList.contains("e-decade"),f=!!l&&l.classList.contains("e-decade");return a&&(h||p||f)&&(r||o||c)&&!this.isMobile},s.prototype.keyNavigation=function(t,i){this.bindCalendarCellEvents(t),t.classList.contains(N)?this.leftCalCurrentDate=new Date(+this.currentDate):this.rightCalCurrentDate=new Date(+this.currentDate),this.updateNavIcons(),this.calendarIconEvent(),this.updateRange([t]),this.dateDisabled=this.isDateDisabled(this.currentDate),i.preventDefault()},s.prototype.inputHandler=function(t){switch(t.action){case"altDownArrow":this.isPopupOpen()||(""===this.inputElement.value&&(this.clear(),this.changeTrigger(t),this.clearRange()),this.show(null,t),this.isRangeIconClicked=!1,this.isMobile||(0,e.hX)(this.leftCalendar)||this.calendarFocus(),this.isKeyPopup=!0);break;case"escape":this.isPopupOpen()&&this.hide(t);break;case"enter":document.activeElement===this.inputElement&&(this.inputBlurHandler(t),this.hide(t));break;case"tab":document.activeElement===this.inputElement&&this.isPopupOpen()&&(this.hide(t),t.preventDefault())}},s.prototype.bindCalendarCellEvents=function(t){for(var a=0,n=t?t.querySelectorAll("."+O+" td"):this.popupObj.element.querySelectorAll("."+O+" td");a<n.length;a++){var l=n[a];e.Jm.clearEvents(l),!l.classList.contains(k)&&!l.classList.contains(st)&&!l.classList.contains(Vt)&&(this.isMobile||e.Jm.add(l,"mouseover",this.hoverSelection,this),e.Jm.add(l,"mousedown",this.selectRange,this))}},s.prototype.removeFocusedDate=function(){for(var t=!(0,e.hX)(this.startValue)||!(0,e.hX)(this.endValue),a=0,n=this.popupObj.element.querySelectorAll("."+O+" ."+pt);a<n.length;a++){var l=n[a],r=new Date,h=this.getIdValue(null,l);("Month"===this.depth&&"Month"===this.currentView()&&(!l.classList.contains(Le)||l.classList.contains(Le)&&t)||"Year"===this.depth&&"Year"===this.currentView()&&(!this.isSameMonth(r,h)&&!this.isSameYear(r,h)||t)||"Decade"===this.depth&&"Decade"===this.currentView()&&(!this.isSameYear(r,h)||t))&&(l.classList.remove(pt),!l.classList.contains(L)&&!l.classList.contains(K)&&l.removeAttribute("aria-label"))}},s.prototype.hoverSelection=function(t,i){var n=this.getIdValue(null,i||t.currentTarget);if(!(0,e.hX)(this.startValue)&&+this.startValue>=+this.min&&+this.startValue<=+this.max&&(!this.isDateDisabled(this.endValue)&&!this.isDateDisabled(this.startValue)&&(0,e.hX)(this.endValue)&&(0,e.hX)(this.startValue)||!(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)))for(var r=0,h=this.popupObj.element.querySelectorAll("."+O+" td");r<h.length;r++){var o=h[r],p=!o.classList.contains(k)||o.classList.contains(st);if(!o.classList.contains(Vt)&&p){var c=this.getIdValue(null,o),f=new Date(+this.startValue);new Date(+c).setHours(0,0,0,0)>=f.setHours(0,0,0,0)&&+c<=+n?(0,e.iQ)([o],Z):(0,e.vy)([o],[Z])}}},s.prototype.isSameStartEnd=function(t,i){var a=!1;return"Month"===this.depth?t.setHours(0,0,0,0)===i.setHours(0,0,0,0)&&(a=!0):"Year"===this.depth?t.getFullYear()===i.getFullYear()&&t.getMonth()===i.getMonth()&&(a=!0):"Decade"===this.depth&&t.getFullYear()===i.getFullYear()&&(a=!0),a},s.prototype.updateRange=function(t){if(!(0,e.hX)(this.startValue))for(var i=0,a=t;i<a.length;i++)for(var r=0,h=a[i].querySelectorAll("."+O+" td");r<h.length;r++){var o=h[r];if(!o.classList.contains(Vt)&&!o.classList.contains(k)){var p=this.getIdValue(null,o),c=this.getIdValue(null,o);if((0,e.hX)(this.endValue)?(0,e.vy)([o],[Z]):this.currentView()===this.depth&&+c.setHours(0,0,0,0)>=+new Date(+this.startValue).setHours(0,0,0,0)&&+c.setHours(0,0,0,0)<=+new Date(+this.endValue).setHours(0,0,0,0)&&!this.isSameStartEnd(new Date(+this.startValue),new Date(+this.endValue))&&+new Date(+this.startValue).setHours(0,0,0,0)>=+this.min&&+new Date(+this.endValue).setHours(0,0,0,0)<=+this.max&&!(this.isDateDisabled(this.startValue)||this.isDateDisabled(this.endValue))&&(0,e.iQ)([o],Z),o.classList.contains(Q)&&o.classList.contains(K)&&+c!=+this.endValue&&((0,e.vy)([o],[Q]),(0,e.vy)([o],[K])),o.classList.contains(Z)&&+c>+this.endValue&&(0,e.vy)([o],[Z]),!o.classList.contains(mt)){var f=new Date(+this.startValue),g=new Date(+p);this.currentView()===this.depth&&+g.setHours(0,0,0,0)==+f.setHours(0,0,0,0)&&+g.setHours(0,0,0,0)>=+f.setHours(0,0,0,0)&&+this.startValue>=+this.min&&!this.inputWrapper.container.classList.contains("e-error")&&!this.isDateDisabled(this.startValue)&&!this.isDateDisabled(this.endValue)&&((0,e.iQ)([o],[L,Q]),this.addSelectedAttributes(o,this.startValue,!0));var d=new Date(+this.endValue);"Year"===this.currentView()?g=new Date(g.getFullYear(),g.getMonth()+1,0):"Decade"===this.currentView()&&(g=new Date(g.getFullYear(),11,31)),this.currentView()===this.depth&&!(0,e.hX)(this.endValue)&&+g.setHours(0,0,0,0)==+d.setHours(0,0,0,0)&&+g.setHours(0,0,0,0)<=+d.setHours(0,0,0,0)&&+this.startValue>=+this.min&&!this.inputWrapper.container.classList.contains("e-error")&&!(this.isDateDisabled(this.startValue)||this.isDateDisabled(this.endValue))&&((0,e.iQ)([o],[K,Q]),this.addSelectedAttributes(o,this.startValue,!1)),+p==+this.startValue&&!(0,e.hX)(this.endValue)&&+p==+this.endValue&&this.addSelectedAttributes(o,this.endValue,!1,!0)}}}},s.prototype.checkMinMaxDays=function(){(!(0,e.hX)(this.minDays)&&this.minDays>0||!(0,e.hX)(this.maxDays)&&this.maxDays>0)&&(this.isMobile?this.updateMinMaxDays(this.popupObj.element.querySelector("."+O)):(this.updateMinMaxDays(this.popupObj.element.querySelector("."+N)),this.updateMinMaxDays(this.popupObj.element.querySelector("."+P))))},s.prototype.rangeArgs=function(t){var i,a,n=(0,e.hX)(this.startValue)?null:this.globalize.formatDate(this.startValue,{format:this.formatString,type:"date",skeleton:"yMd"}),l=(0,e.hX)(this.endValue)?null:this.globalize.formatDate(this.endValue,{format:this.formatString,type:"date",skeleton:"yMd"});return(0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)?(i="",a=0):(i=n+" "+this.separator+" "+l,a=Math.round(Math.abs((this.removeTimeValueFromDate(this.startValue).getTime()-this.removeTimeValueFromDate(this.endValue).getTime())/864e5))+1),{value:this.value,startDate:this.startValue,endDate:this.endValue,daySpan:a,event:t||null,element:this.element,isInteracted:!(0,e.hX)(t),text:i}},s.prototype.otherMonthSelect=function(t,i,a){var n=+this.getIdValue(null,t),l='*[id^="/id"]:not(.e-other-month)'.replace("/id",""+n),r=this.popupObj&&this.popupObj.element.querySelector(l);(0,e.hX)(r)||(i?((0,e.iQ)([r],[L,Q]),this.addSelectedAttributes(r,this.startValue,!0)):((0,e.iQ)([r],[K,Q]),this.addSelectedAttributes(r,this.endValue,!0)),a&&this.addSelectedAttributes(t,this.endValue,!1,!0))},s.prototype.selectRange=function(t,i){var a,n;t&&t.preventDefault();var l=(0,e.hX)(t)?this.getIdValue(null,i):this.getIdValue(t,null),r=l.getFullYear(),h=l.getMonth(),o=new Date(r,h,1),p=new Date(r,h+1,0),c=new Date(r,0,1),f=new Date(r,11,31);(0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)?this.isMobile&&this.startButton.element.classList.contains(T)&&this.removeSelection():(!this.isMobile||this.isMobile&&!this.endButton.element.classList.contains(T))&&this.removeSelection();var g=i||t.currentTarget;if((0,e.hX)(this.startValue))(0,e.hX)(this.previousStartValue)||(l.setHours(this.previousStartValue.getHours()),l.setMinutes(this.previousStartValue.getMinutes()),l.setSeconds(this.previousStartValue.getSeconds())),this.startValue="Month"===this.depth?new Date(this.checkValue(l)):"Year"===this.depth?o:c,this.endValue=null,this.setValue(),(0,e.iQ)([g],L),this.addSelectedAttributes(g,this.startValue,!0),g.classList.contains(mt)&&this.otherMonthSelect(g,!0),this.checkMinMaxDays(),this.applyButton.disabled=!0,this.applyButton.element.disabled=!0,this.isMobile&&(this.endButton.element.classList.add(T),this.startButton.element.classList.remove(T),this.endButton.element.removeAttribute("disabled"),this.selectableDates()),this.trigger("select",this.rangeArgs(t));else if(+l==+this.startValue||+l>+this.startValue){if(+l==+this.startValue&&!(0,e.hX)(this.minDays)&&this.minDays>1)return;this.endValue=null,this.setValue(),(this.isMobile||i)&&this.hoverSelection(t,i),(0,e.hX)(this.previousEndValue)||(l.setHours(this.previousEndValue.getHours()),l.setMinutes(this.previousEndValue.getMinutes()),l.setSeconds(this.previousEndValue.getSeconds())),this.endValue="Month"===this.depth?new Date(this.checkValue(l)):"Year"===this.depth?p:f,this.setValue();var d=this.popupObj.element.querySelectorAll("."+K);if(this.isMobile){this.startButton.element.classList.remove(T),this.endButton.element.classList.add(T);for(var y=0,b=d;y<b.length;y++){var m=b[y];m.removeAttribute("aria-label"),m.classList.contains(L)?(this.addSelectedAttributes(m,this.startValue,!0),(0,e.vy)([m],[K])):(m.setAttribute("aria-selected","false"),(0,e.vy)([m],[K,Q]))}}(0,e.iQ)([g],K),+this.endValue==+this.startValue?this.addSelectedAttributes(g,this.endValue,!1,!0):this.addSelectedAttributes(g,this.endValue,!1),g.classList.contains(mt)&&(+this.endValue==+this.startValue?this.otherMonthSelect(g,!1,!0):this.otherMonthSelect(g,!1));for(var A=0,M=d=this.popupObj.element.querySelectorAll("."+K);A<M.length;A++){var V=M[A];V.classList.contains(L)&&(0,e.vy)([V],[Z])}this.applyButton.disabled=!1,this.applyButton.element.disabled=!1,this.isMobile||this.removeClassDisabled(),!(0,e.hX)(this.renderDayCellArgs)&&this.renderDayCellArgs.isDisabled&&this.disabledDateRender(),this.trigger("select",this.rangeArgs(t))}else+l<+this.startValue&&(this.removeClassDisabled(),this.startValue="Month"===this.depth?new Date(this.checkValue(l)):"Year"===this.depth?o:c,this.setValue(),this.removeSelectedAttributes(),(0,e.vy)(this.popupObj.element.querySelectorAll("."+L),[L,Q]),(0,e.iQ)([g],L),this.addSelectedAttributes(g,this.startValue,!0),g.classList.contains(mt)&&this.otherMonthSelect(g,!0),this.checkMinMaxDays());t&&(a=(0,e.kp)(t.target,"."+N)),(0,e.hX)(a)?(t&&(n=t&&(0,e.kp)(t.target,"."+P)),(0,e.hX)(n)||this.rightCalendar.children[1].firstElementChild.focus()):this.leftCalendar.children[1].firstElementChild.focus(),(0,e.iQ)([g],Q),this.calendarIconEvent(),this.updateHeader(),this.removeFocusedDate()},s.prototype.selectableDates=function(){if(!(0,e.hX)(this.startValue)){var t=this.calendarElement.querySelectorAll("."+O+" td"),i=!1;if(this.currentView()===this.depth){for(var a=0,n=t;a<n.length;a++){if(!(l=n[a]).classList.contains(L)&&!l.classList.contains(Vt)&&!l.classList.contains(k)){if(+this.getIdValue(null,l)<+this.startValue){(0,e.iQ)([l],[st,k,Y]),e.Jm.clearEvents(l);continue}break}if(l.classList.contains(L)&&!l.classList.contains(mt)){i=!0;break}}i&&(this.previousIcon.classList.contains(k)||(0,e.iQ)([this.previousIcon],[R,k,Y]))}else{for(var h=0,o=t;h<o.length;h++){var l=o[h],p=this.startValue.getMonth(),c=this.startValue.getFullYear(),f=this.getIdValue(null,l);if(this.startButton.element.classList.contains(T)||!("Year"===this.currentView()&&f.getMonth()<p&&f.getFullYear()<=c||"Decade"===this.currentView()&&f.getMonth()<=p&&f.getFullYear()<c))break;(0,e.iQ)([l],[k])}t[0].classList.contains(k)?this.previousIconHandler(!0):t[t.length-1].classList.contains(k)&&this.nextIconHandler(!0)}}},s.prototype.updateMinMaxDays=function(t){if(!(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)||this.isMobile&&this.endButton&&this.endButton.element.classList.contains(T)){if(!(0,e.hX)(this.minDays)&&this.minDays>0||!(0,e.hX)(this.maxDays)&&this.maxDays>0){var i=this.removeTimeValueFromDate(this.startValue),a=new Date(new Date(+i).setDate(i.getDate()+(this.minDays-1))),n=new Date(new Date(+i).setDate(i.getDate()+(this.maxDays-1)));a=!(0,e.hX)(this.minDays)&&this.minDays>0?a:null,n=!(0,e.hX)(this.maxDays)&&this.maxDays>0?n:null,"Year"===this.currentView()?(a=(0,e.hX)(a)?null:new Date(a.getFullYear(),a.getMonth(),0),n=(0,e.hX)(n)?null:new Date(n.getFullYear(),n.getMonth(),1)):"Decade"===this.currentView()&&(a=(0,e.hX)(a)?null:new Date(a.getFullYear()-1,11,1),n=(0,e.hX)(n)?null:new Date(n.getFullYear(),0,1));for(var r=void 0,h=0,o=t.querySelectorAll("."+O+" td");h<o.length;h++){var p=o[h];if(!p.classList.contains(L)&&!p.classList.contains(Vt)){var c=this.getIdValue(null,p);if(c=this.removeTimeValueFromDate(c),!(0,e.hX)(a)&&+c==+a&&p.classList.contains(k)&&a.setDate(a.getDate()+1),!p.classList.contains(k)){if(+c<=+i)continue;!(0,e.hX)(a)&&+c<+a&&((0,e.iQ)([p],[st,k,Y]),e.Jm.clearEvents(p)),!(0,e.hX)(n)&&+c>+n&&((0,e.iQ)([p],[st,k,Y]),this.isMaxDaysClicked=!0,e.Jm.clearEvents(p),(0,e.hX)(r)&&!p.classList.contains(mt)&&(r=p))}}}if(!(0,e.hX)(r))if(this.isMobile)this.nextIcon.classList.contains(k)||(0,e.iQ)([this.nextIcon],[R,k,Y]);else{var f=(0,e.kp)(r,"."+P);(f=(0,e.hX)(f)?this.leftCalendar:f).classList.contains(N)?(this.rightCalNextIcon.classList.contains(k)||(0,e.iQ)([this.rightCalNextIcon],[R,k,Y]),this.leftCalNextIcon.classList.contains(k)||(0,e.iQ)([this.leftCalNextIcon],[R,k,Y]),this.rightCalPrevIcon.classList.contains(k)||(0,e.iQ)([this.rightCalPrevIcon],[R,k,Y])):this.rightCalNextIcon.classList.contains(k)||(0,e.iQ)([this.rightCalNextIcon],[R,k,Y])}}}else this.isMaxDaysClicked=!1},s.prototype.removeTimeValueFromDate=function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate())},s.prototype.removeClassDisabled=function(){for(var i=0,a=this.popupObj.element.querySelectorAll("."+O+" td."+st);i<a.length;i++){var n=a[i];n.classList.contains(st)&&((0,e.vy)([n],[st,k,Y]),e.Jm.add(n,"click",this.selectRange,this),this.isMobile||e.Jm.add(n,"mouseover",this.hoverSelection,this))}this.isMobile?(this.nextIcon.classList.contains(R)&&(0,e.vy)([this.nextIcon],[R,k,Y]),this.previousIcon.classList.contains(R)&&(0,e.vy)([this.previousIcon],[R,k,Y])):(this.rightCalNextIcon.classList.contains(R)&&(0,e.vy)([this.rightCalNextIcon],[R,k,Y]),this.rightCalPrevIcon.classList.contains(R)&&(0,e.vy)([this.rightCalPrevIcon],[R,k,Y]),this.leftCalNextIcon.classList.contains(R)&&(0,e.vy)([this.leftCalNextIcon],[R,k,Y]))},s.prototype.updateHeader=function(){var t={type:"date",skeleton:"yMMMd"};if((0,e.hX)(this.endValue)||(0,e.hX)(this.startValue))this.popupObj.element.querySelector("."+Mt).textContent=this.l10n.getConstant("selectedDays");else{var i=Math.round(Math.abs((this.removeTimeValueFromDate(this.startValue).getTime()-this.removeTimeValueFromDate(this.endValue).getTime())/864e5))+1;(0,e.hX)(this.disabledDayCnt)||(i-=this.disabledDayCnt,this.disabledDayCnt=null),this.popupObj.element.querySelector("."+Mt).textContent=i.toString()+" "+this.l10n.getConstant("days")}this.isMobile?(this.startButton.element.textContent=(0,e.hX)(this.startValue)?this.l10n.getConstant("startLabel"):this.globalize.formatDate(this.startValue,t),this.endButton.element.textContent=(0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)?this.l10n.getConstant("endLabel"):this.globalize.formatDate(this.endValue,t)):((0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)?this.popupObj.element.querySelector("."+Lt).textContent=this.l10n.getConstant("endLabel"):this.popupObj.element.querySelector("."+Lt).textContent=this.globalize.formatDate(this.endValue,t),(0,e.hX)(this.startValue)?this.popupObj.element.querySelector("."+Ht).textContent=this.l10n.getConstant("startLabel"):this.popupObj.element.querySelector("."+Ht).textContent=this.globalize.formatDate(this.startValue,t)),(this.isDateDisabled(this.startValue)||this.isDateDisabled(this.endValue)||!(0,e.hX)(this.startValue)&&+this.startValue<+this.min||!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue)&&(this.isMobile?(this.startButton.element.textContent=this.l10n.getConstant("startLabel"),this.endButton.element.textContent=this.l10n.getConstant("endLabel"),this.popupObj.element.querySelector("."+Mt).textContent=this.l10n.getConstant("selectedDays")):(this.popupObj.element.querySelector("."+Mt).textContent=this.l10n.getConstant("selectedDays"),this.popupObj.element.querySelector("."+Ht).textContent=this.l10n.getConstant("startLabel"),this.popupObj.element.querySelector("."+Lt).textContent=this.l10n.getConstant("endLabel"))),this.popupObj.element.querySelector("#custom_range")&&(this.popupObj.element.querySelector("#custom_range").textContent=""!==this.l10n.getConstant("customRange")?this.l10n.getConstant("customRange"):"Custom Range")},s.prototype.removeSelection=function(){this.startValue=null,this.endValue=null,this.setValue(),this.removeSelectedAttributes(),this.popupObj&&(this.popupObj.element.querySelectorAll("."+Q).length>0&&(0,e.vy)(this.popupObj.element.querySelectorAll("."+Q),[L,K,Q]),this.popupObj.element.querySelectorAll("."+pt).length>0&&(0,e.vy)(this.popupObj.element.querySelectorAll("."+pt),pt),this.popupObj.element.querySelectorAll("."+Z).length>0&&(0,e.vy)(this.popupObj.element.querySelectorAll("."+Z),[Z]))},s.prototype.addSelectedAttributes=function(t,i,a,n){if(t){var l=this.globalize.formatDate(i,{type:"date",skeleton:"full"});!(0,e.hX)(n)&&n?t.setAttribute("aria-label","The current start and end date is "+l):t.setAttribute("aria-label","The current "+(a?"start":"end")+" date is "+l),t.setAttribute("aria-selected","true")}},s.prototype.removeSelectedAttributes=function(){if(this.popupObj){for(var i=0,a=this.popupObj.element.querySelectorAll("."+L);i<a.length;i++)(n=a[i]).setAttribute("aria-selected","false"),n.removeAttribute("aria-label");for(var r=0,h=this.popupObj.element.querySelectorAll("."+K);r<h.length;r++){var n;(n=h[r]).setAttribute("aria-selected","false"),n.removeAttribute("aria-label")}}},s.prototype.updateCalendarElement=function(t){t.classList.contains(N)?(this.calendarElement=this.leftCalendar,this.currentDate=this.leftCalCurrentDate,this.previousIcon=this.leftCalPrevIcon,this.nextIcon=this.leftCalNextIcon):(this.calendarElement=this.rightCalendar,this.currentDate=this.rightCalCurrentDate,this.previousIcon=this.rightCalPrevIcon,this.nextIcon=this.rightCalNextIcon),this.contentElement=t.querySelector("."+ft),this.tableBodyElement=(0,e.Lt)("."+ft+" tbody",t),this.table=t.querySelector("."+ft).getElementsByTagName("table")[0],this.headerTitleElement=t.querySelector("."+F+" ."+ut),this.headerElement=t.querySelector("."+F)},s.prototype.navPrevMonth=function(t){t.preventDefault();var i=(0,e.kp)(t.target,"."+N);i=(0,e.hX)(i)?(0,e.kp)(t.target,"."+P):i,this.updateCalendarElement(i),this.navigatePrevious(t),!(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)&&this.updateMinMaxDays(i),this.updateControl(i)},s.prototype.deviceNavigation=function(t){this.deviceCalendarEvent(),this.updateRange([this.popupObj.element.querySelector("."+O)]),this.endButton.element.classList.contains(T)&&this.updateMinMaxDays(this.popupObj.element.querySelector("."+O)),this.endButton.element.classList.contains(T)&&this.selectableDates(),this.currentView()===this.depth&&this.bindCalendarCellEvents(),this.removeFocusedDate()},s.prototype.updateControl=function(t,i){void 0===i&&(i=null),t.classList.contains(P)?this.rightCalCurrentDate=new Date(+(i||this.currentDate)):this.leftCalCurrentDate=new Date(+this.currentDate),this.calendarIconEvent(),("Month"===this.depth&&this.leftCalendar.querySelector(".e-content").classList.contains("e-month")&&this.rightCalendar.querySelector(".e-content").classList.contains("e-month")||"Year"===this.depth&&this.leftCalendar.querySelector(".e-content").classList.contains("e-year")&&this.rightCalendar.querySelector(".e-content").classList.contains("e-year")||"Decade"===this.depth&&this.leftCalendar.querySelector(".e-content").classList.contains("e-decade")&&this.rightCalendar.querySelector(".e-content").classList.contains("e-decade")||this.isMobile)&&this.bindCalendarCellEvents(),this.removeFocusedDate(),this.updateRange([t])},s.prototype.navNextMonth=function(t){t.preventDefault();var i=(0,e.kp)(t.target,"."+N);i=(0,e.hX)(i)?(0,e.kp)(t.target,"."+P):i,this.updateCalendarElement(i),this.navigateNext(t),!(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)&&this.updateMinMaxDays(i),this.updateControl(i)},s.prototype.isPopupOpen=function(){return!((0,e.hX)(this.popupObj)||!this.popupObj.element.classList.contains(ee))},s.prototype.createRangeHeader=function(){var t=this.createElement("div",{className:"e-start-end"});if(this.isMobile){var l=this.createElement("button",{className:"e-end-btn"}),r=this.createElement("button",{className:He});this.startButton=new St.$n({content:this.l10n.getConstant("startLabel")},r),this.endButton=new St.$n({content:this.l10n.getConstant("endLabel")},l),t.appendChild(r),t.appendChild(l)}else{var i=this.createElement("a",{className:Ht}),a=this.createElement("a",{className:Lt}),n=this.createElement("span",{className:"e-change-icon e-icons"});(0,e.uK)(i,{"aria-atomic":"true","aria-live":"assertive","aria-label":"Start Date",role:"button"}),(0,e.uK)(a,{"aria-atomic":"true","aria-live":"assertive","aria-label":"End Date",role:"button"}),t.appendChild(i),t.appendChild(n),t.appendChild(a),i.textContent=this.l10n.getConstant("startLabel"),a.textContent=this.l10n.getConstant("endLabel")}return t},s.prototype.disableInput=function(){this.strictMode?!(0,e.hX)(this.previousStartValue)&&!(0,e.hX)(this.previousEndValue)&&(this.startValue=this.previousStartValue,this.endValue=this.previousEndValue,this.setValue(),this.updateInput()):(this.updateInput(),this.clearRange(),this.setProperties({startDate:null},!0),this.setProperties({endDate:null},!0),this.startValue=null,this.endValue=null,this.setValue(),this.errorClass()),this.setProperties({enabled:!1},!0),v.pd.setEnabled(this.enabled,this.inputElement),this.bindEvents()},s.prototype.validateMinMax=function(){this.min=(0,e.hX)(this.min)||!+this.min?this.min=new Date(1900,0,1):this.min,this.max=(0,e.hX)(this.max)||!+this.max?this.max=new Date(2099,11,31):this.max,this.min<=this.max?(!(0,e.hX)(this.minDays)&&!(0,e.hX)(this.maxDays)&&this.maxDays>0&&this.minDays>0&&this.minDays>this.maxDays&&(this.maxDays=null),!(0,e.hX)(this.minDays)&&this.minDays<0&&(this.minDays=null),!(0,e.hX)(this.maxDays)&&this.maxDays<0&&(this.maxDays=null)):this.disableInput()},s.prototype.validateRangeStrict=function(){(0,e.hX)(this.startValue)||(+this.startValue<=+this.min?(this.startValue=this.min,this.setValue()):+this.startValue>=+this.min&&+this.startValue>=+this.max&&(this.startValue=this.max)),(0,e.hX)(this.endValue)||(+this.endValue>+this.max?(this.endValue=this.max,this.setValue()):+this.endValue<+this.min&&(this.endValue=this.min,this.setValue())),this.validateMinMaxDays()},s.prototype.validateRange=function(){this.validateMinMaxDays()},s.prototype.validateMinMaxDays=function(){if(!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)){var t=Math.round(Math.abs((this.removeTimeValueFromDate(this.startValue).getTime()-this.removeTimeValueFromDate(this.endValue).getTime())/864e5))+1;if(!(0,e.hX)(this.minDays)&&this.minDays>0&&!(t>=this.minDays))if(this.strictMode){var i=new Date(+this.startValue);i.setDate(i.getDate()+(this.minDays-1)),+i>+this.max?(this.endValue=this.max,this.setValue()):(this.endValue=i,this.setValue())}else this.startValue=null,this.endValue=null,this.setValue();!(0,e.hX)(this.maxDays)&&this.maxDays>0&&!(t<=this.maxDays)&&(this.strictMode?(this.endValue=new Date(+this.startValue),this.endValue.setDate(this.endValue.getDate()+(this.maxDays-1)),this.setValue()):(this.startValue=null,this.endValue=null,this.setValue()))}},s.prototype.renderCalendar=function(){this.calendarElement=this.createElement("div"),this.calendarElement.classList.add(O),this.enableRtl&&this.calendarElement.classList.add("e-rtl"),(0,e.uK)(this.calendarElement,{"data-role":"calendar"}),u.prototype.createHeader.call(this),u.prototype.createContent.call(this)},s.prototype.isSameMonth=function(t,i){return t.getMonth()===i.getMonth()&&t.getFullYear()===i.getFullYear()},s.prototype.isSameYear=function(t,i){return t.getFullYear()===i.getFullYear()},s.prototype.isSameDecade=function(t,i){var a=t.getFullYear(),n=i.getFullYear();return a-a%10==n-n%10},s.prototype.startMonthCurrentDate=function(){this.isSameMonth(this.min,this.max)||+this.currentDate>+this.max||this.isSameMonth(this.currentDate,this.max)?(this.currentDate=new Date(+this.max),this.currentDate.setDate(1),this.currentDate.setMonth(this.currentDate.getMonth()-1)):this.currentDate<this.min&&(this.currentDate=new Date(this.checkValue(this.min)))},s.prototype.selectNextMonth=function(){if((0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)||this.isSameMonth(this.endValue,this.currentDate)||this.isDateDisabled(this.endValue)||this.isDateDisabled(this.startValue))return this.currentDate.setDate(1),void this.currentDate.setMonth(this.currentDate.getMonth()+1);if(this.currentDate=new Date(+this.endValue),!(0,e.hX)(this.startValue)&&+this.startValue<+this.min||!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue){this.currentDate=new Date((new Date).setHours(0,0,0,0)),this.currentDate.setDate(1);var t=this.currentDate.getMonth()+1;this.currentDate.setMonth(t)}},s.prototype.selectNextYear=function(){if((0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)||this.isSameYear(this.endValue,this.currentDate)||this.isDateDisabled(this.endValue)||this.isDateDisabled(this.startValue)){this.currentDate.setMonth(0);var t=this.currentDate.getFullYear()+1;this.currentDate.setFullYear(t)}else this.currentDate=new Date(+this.endValue),(!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue||!(0,e.hX)(this.startValue)&&+this.startValue<+this.min)&&(this.currentDate=new Date((new Date).setHours(0,0,0,0)),this.currentDate.setMonth(0),this.currentDate.setFullYear(this.currentDate.getFullYear()+1))},s.prototype.selectNextDecade=function(){if((0,e.hX)(this.endValue)||(0,e.hX)(this.startValue)||this.isSameDecade(this.endValue,this.currentDate)||this.isDateDisabled(this.endValue)||this.isDateDisabled(this.startValue)){var t=this.currentDate.getFullYear()+10;this.currentDate.setFullYear(t)}else this.currentDate=new Date(+this.endValue),(!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue||!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||!(0,e.hX)(this.startValue)&&+this.startValue<+this.min)&&(this.currentDate=new Date((new Date).setHours(0,0,0,0)),this.currentDate.setFullYear(this.currentDate.getFullYear()+10))},s.prototype.selectStartMonth=function(){(0,e.hX)(this.startValue)?(this.currentDate=new Date((new Date).setHours(0,0,0,0)),this.startMonthCurrentDate()):!(0,e.hX)(this.max)&&this.isSameMonth(this.startValue,this.max)?(this.currentDate=new Date(+this.max),this.currentDate.setDate(1),this.currentDate.setMonth(this.currentDate.getMonth()-1)):this.currentDate=this.startValue>=this.min&&this.startValue<=this.max&&!this.isDateDisabled(this.startValue)?new Date(+this.startValue):new Date((new Date).setHours(0,0,0,0)),(!(0,e.hX)(this.endValue)&&+this.endValue>+this.max||!(0,e.hX)(this.startValue)&&+this.startValue<+this.min||!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue)&&+this.startValue>+this.endValue)&&(this.currentDate=new Date((new Date).setHours(0,0,0,0))),this.startMonthCurrentDate()},s.prototype.createCalendar=function(){var t=this.createElement("div",{className:"e-calendar-container"});if(this.isMobile){(0,e.hX)(this.startValue)||(this.currentDate=new Date(+this.startValue)),u.prototype.validateDate.call(this),u.prototype.minMaxUpdate.call(this),u.prototype.render.call(this);var n=this.calendarElement.querySelector("."+O+" ."+Rt),l=this.calendarElement.querySelector("."+O+" ."+Ft);(0,e.TF)(this.calendarElement.querySelector("."+O+" ."+ie)),this.calendarElement.querySelector("."+O+" ."+F).appendChild(l),this.calendarElement.querySelector("."+O+" ."+F).appendChild(n),(0,e.Hs)([n],this.calendarElement.querySelector("."+O+" ."+F)),this.deviceCalendar=this.calendarElement,t.appendChild(this.calendarElement),this.headerTitleElement=this.calendarElement.querySelector("."+O+" ."+F+" ."+ut)}else{this.selectStartMonth(),this.renderCalendar(),this.leftCalCurrentDate=new Date(+this.currentDate),this.calendarElement.classList.add(N),this.leftCalPrevIcon=this.calendarElement.querySelector("."+N+" ."+Rt),this.leftCalNextIcon=this.calendarElement.querySelector("."+N+" ."+Ft),this.leftTitle=this.calendarElement.querySelector("."+N+" ."+ut),(0,e.TF)(this.calendarElement.querySelector("."+N+" ."+ie)),this.calendarElement.querySelector("."+N+" ."+F).appendChild(this.leftCalNextIcon),this.calendarElement.querySelector("."+N+" ."+F).appendChild(this.leftCalPrevIcon),(0,e.Hs)([this.leftCalPrevIcon],this.calendarElement.querySelector("."+N+" ."+F)),this.leftCalendar=this.calendarElement;var i=this.createElement("div",{className:"e-left-container"}),a=this.createElement("div",{className:"e-right-container"});i.appendChild(this.leftCalendar),t.appendChild(i),this.isMobile||e.Jm.add(this.leftTitle,"click",this.leftNavTitle,this),"Month"===this.start&&this.selectNextMonth(),"Year"===this.start&&this.selectNextYear(),"Decade"===this.start&&this.selectNextDecade(),this.renderCalendar(),this.rightCalCurrentDate=new Date(+this.currentDate),(0,e.iQ)([this.calendarElement],P),this.rightCalendar=this.calendarElement,(0,e.vy)([this.leftCalendar&&this.leftCalendar.querySelector(".e-content tbody")],"e-zoomin"),(0,e.vy)([this.rightCalendar&&this.rightCalendar.querySelector(".e-content tbody")],"e-zoomin"),this.rightCalPrevIcon=this.calendarElement.querySelector("."+P+" ."+Rt),this.rightCalNextIcon=this.calendarElement.querySelector("."+P+" ."+Ft),this.rightTitle=this.calendarElement.querySelector("."+P+" ."+ut),(0,e.TF)(this.calendarElement.querySelector("."+P+" ."+ie)),this.calendarElement.querySelector("table").setAttribute("tabindex","0"),this.calendarElement.querySelector("."+P+" ."+F).appendChild(this.rightCalNextIcon),this.calendarElement.querySelector("."+P+" ."+F).appendChild(this.rightCalPrevIcon),(0,e.Hs)([this.rightCalPrevIcon],this.calendarElement.querySelector("."+P+" ."+F)),a.appendChild(this.rightCalendar),t.appendChild(a),this.isMobile||e.Jm.add(this.rightTitle,"click",this.rightNavTitle,this)}return t},s.prototype.leftNavTitle=function(t){this.isPopupOpen()&&(this.calendarElement=this.leftCalendar,this.calendarNavigation(t,this.calendarElement))},s.prototype.calendarNavigation=function(t,i){this.table=i.querySelector("table"),this.headerTitleElement=i.querySelector(".e-title"),this.tableBodyElement=i.querySelector("tbody"),this.tableHeadElement=i.querySelector("thead"),this.contentElement=i.querySelector(".e-content"),this.updateCalendarElement(i),u.prototype.navigateTitle.call(this,t),this.updateNavIcons()},s.prototype.rightNavTitle=function(t){this.isPopupOpen()&&(this.calendarElement=this.rightCalendar,this.calendarNavigation(t,this.calendarElement))},s.prototype.clickEventEmitter=function(t){this.isMobile||((0,e.kp)(t.target,".e-calendar.e-left-calendar")?(this.calendarElement=this.leftCalendar,this.updateCalendarElement(this.leftCalendar)):(this.calendarElement=this.rightCalendar,this.updateCalendarElement(this.rightCalendar)))},s.prototype.currentView=function(){return u.prototype.currentView.call(this)},s.prototype.getCalendarView=function(t){return"Year"===t?"Year":"Decade"===t?"Decade":"Month"},s.prototype.navigatedEvent=function(t){if(this.trigger("navigated",this.navigatedArgs),!(0,e.hX)(this.popupObj)){var a=this.getCalendarView(this.currentView());this.isMobile?a===this.depth?(this.bindCalendarCellEvents(),this.deviceNavigation(),this.removeFocusedDate(),this.checkMinMaxDays()):this.selectableDates():this.isMobile||a!==this.depth?(this.updateNavIcons(),this.calendarIconEvent()):((this.calendarElement.classList.contains("e-left-calendar")?this.leftCalendar:this.rightCalendar)!==this.leftCalendar||(!t||t.currentTarget.children[0].classList.contains("e-icons"))&&(0,e.hX)(this.controlDown)?(t&&!t.currentTarget.children[0].classList.contains("e-icons")||!(0,e.hX)(this.controlDown))&&(this.rightCalCurrentDate=new Date(+this.currentDate),this.effect="",this.currentDate=this.rightCalCurrentDate,this.updateCalendarElement(this.rightCalendar),this.updateControl(this.rightCalendar),this.updateCalendarElement(this.leftCalendar),this.startValue&&(0,e.hX)(this.endValue)&&("Month"===a&&this.startValue.getMonth()<this.rightCalCurrentDate.getMonth()&&this.startValue.getFullYear()<=this.rightCalCurrentDate.getFullYear()||"Year"===a&&this.startValue.getFullYear()<this.rightCalCurrentDate.getFullYear())?u.prototype.navigateTo.call(this,a,new Date(+this.startValue)):u.prototype.navigateTo.call(this,a,this.leftCalCurrentDate),this.updateControl(this.leftCalendar),this.updateNavIcons(),this.calendarIconEvent(),this.calendarIconRipple(),this.controlDown=null):(this.leftCalCurrentDate=new Date(+this.currentDate),this.effect="",this.currentDate=this.leftCalCurrentDate,this.updateCalendarElement(this.leftCalendar),this.updateControl(this.leftCalendar),this.updateCalendarElement(this.rightCalendar),u.prototype.navigateTo.call(this,a,this.rightCalCurrentDate),this.updateControl(this.rightCalendar,this.rightCalCurrentDate?this.rightCalCurrentDate:this.currentDate),this.updateNavIcons(),this.calendarIconEvent(),this.calendarIconRipple(),this.controlDown=null),this.checkMinMaxDays())}},s.prototype.createControl=function(){var t=this.createElement("div",{className:se}),i=this.createElement("div",{className:"e-range-header"});if(this.isMobile&&this.fullScreenMode){var a=this.createElement("div",{className:"e-model-header-wrapper"}),n=this.createElement("span",{className:"e-popup-close"});e.Jm.add(n,"mousedown touchstart",this.modelRangeCloseHandler,this);var l=this.createElement("span",{className:"e-apply"});e.Jm.add(l,"mousedown touchstart",this.applyFunction,this),a.appendChild(n),a.appendChild(l),i.appendChild(a)}var r=this.createRangeHeader();i.appendChild(r);var h=this.createElement("div",{className:Mt});(0,e.uK)(h,{"aria-label":"Selected Days"}),h.textContent=this.l10n.getConstant("selectedDays"),i.appendChild(h);var o=this.createElement("div",{className:"e-separator"}),p=this.createCalendar();t.appendChild(i),t.appendChild(o),t.appendChild(p);var c=this.createElement("div",{className:"e-footer"}),f=this.createElement("button",{className:"e-cancel e-flat e-css"}),g=this.createElement("button");(0,e.iQ)([g],["e-apply","e-flat","e-primary","e-css"]),c.appendChild(g),c.appendChild(f);var d=!(0,e.hX)(this.startValue)&&!(0,e.hX)(this.endValue);this.cancelButton=new St.$n({content:this.l10n.getConstant("cancelText")},f),this.applyButton=new St.$n({content:this.l10n.getConstant("applyText"),disabled:!d},g),e.Jm.add(g,"click",this.applyFunction,this),e.Jm.add(f,"click",this.cancelFunction,this),this.popupWrapper.appendChild(t),this.isMobile||(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)||(this.createPresets(),this.listRippleEffect(),(0,e.iQ)([t],"e-range-border"),(0,e.iQ)([this.popupWrapper],"e-preset-wrapper"),this.popupWrapper.querySelector("."+kt).style.height=this.popupWrapper.querySelector("."+se).getBoundingClientRect().height+"px"),this.popupWrapper.appendChild(c),this.isMobile&&this.deviceHeaderUpdate(),this.renderPopup()},s.prototype.modelRangeCloseHandler=function(t){this.hide()},s.prototype.cancelFunction=function(t){document.activeElement!==this.inputElement&&(this.preventFocus=!0,this.inputElement.focus(),(0,e.iQ)([this.inputWrapper.container],[ot])),t.preventDefault(),this.isKeyPopup&&(this.inputElement.focus(),this.isKeyPopup=!1),this.startValue=null,this.endValue=null,this.removeSelection(),this.hide(t)},s.prototype.deviceHeaderUpdate=function(){(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)?(this.endButton.element.setAttribute("disabled",""),this.startButton.element.classList.add(T)):(0,e.hX)(this.startValue)||this.startButton.element.classList.add(T)},s.prototype.applyFunction=function(t){var i=!1;"touchstart"!==t.type&&t.preventDefault(),this.closeEventArgs&&this.closeEventArgs.cancel&&(this.startValue=this.popupWrapper.querySelector(".e-start-date")&&this.getIdValue(null,this.popupWrapper.querySelector(".e-start-date")),this.endValue=this.popupWrapper.querySelector(".e-end-date")&&this.getIdValue(null,this.popupWrapper.querySelector(".e-end-date")),this.setValue()),document.activeElement!==this.inputElement&&(this.preventFocus=!0,this.inputElement.focus(),(0,e.iQ)([this.inputWrapper.container],[ot])),"touchstart"!==t.type&&this.closeEventArgs&&!this.closeEventArgs.cancel&&t.preventDefault(),(0,e.hX)(this.startValue)||(0,e.hX)(this.endValue)?this.hide(t||null):(this.previousStartValue&&this.previousEndValue&&this.startValue.getDate()===this.previousStartValue.getDate()&&this.startValue.getMonth()===this.previousStartValue.getMonth()&&this.startValue.getFullYear()===this.previousStartValue.getFullYear()&&this.endValue.getDate()===this.previousEndValue.getDate()&&this.endValue.getMonth()===this.previousEndValue.getMonth()&&this.endValue.getFullYear()===this.previousEndValue.getFullYear()||v.pd.setValue(this.rangeArgs(t).text,this.inputElement,this.floatLabelType,this.showClearButton),this.previousStartValue=new Date(+this.startValue),this.previousEndValue=new Date(+this.endValue),this.previousEleValue=this.inputElement.value,(+this.initStartDate!=+this.startValue||+this.initEndDate!=+this.endValue)&&(i=!0),this.changeTrigger(t),this.hide(t||null),this.errorClass(),i=!0),!(0,e.kp)(t.target,"."+Xe)&&!i&&this.focusOut(),this.isMobile||(this.isKeyPopup=!1,this.isRangeIconClicked&&(this.inputWrapper.container.children[1].focus(),this.keyInputConfigs=(0,e.X$)(this.keyInputConfigs,this.keyConfigs),this.popupKeyboardModule=new e.j9(this.inputWrapper.container.children[1],{eventName:"keydown",keyConfigs:this.keyInputConfigs,keyAction:this.popupKeyActionHandle.bind(this)})))},s.prototype.onMouseClick=function(t,i){if("touchstart"!==t.type){var n=(0,e.kp)(i||t.target,"."+_),l=n&&n.classList.contains(T);n&&n.classList.contains(_)&&this.setListSelection(n,t),this.preventFocus=!0,this.inputElement.focus(),this.isMobile||(this.preventFocus=!0,n&&n.classList.contains(_)&&"custom_range"===n.getAttribute("id")?this.leftCalendar.children[1].firstElementChild.focus():!l&&"keydown"===t.type&&this.inputElement.focus())}},s.prototype.onMouseOver=function(t){var i=(0,e.kp)(t.target,"."+_);i&&i.classList.contains(_)&&!i.classList.contains(B)&&(0,e.iQ)([i],B)},s.prototype.onMouseLeave=function(t){var i=(0,e.kp)(t.target,"."+B);(0,e.hX)(i)||(0,e.vy)([i],B)},s.prototype.setListSelection=function(t,i){if(t&&(!t.classList.contains(T)||this.isMobile&&t.classList.contains(T))){if(this.isMobile&&t.classList.contains(T))return this.activeIndex=Array.prototype.slice.call(this.liCollections).indexOf(t),"custom_range"===this.presetsItem[this.activeIndex].id?void this.renderCustomPopup():void 0;this.removeListSelection(),this.activeIndex=Array.prototype.slice.call(this.liCollections).indexOf(t),(0,e.iQ)([t],T),t.setAttribute("aria-selected","true");var n=this.presetsItem[this.activeIndex];"custom_range"===n.id?this.renderCustomPopup():this.applyPresetRange(n,i)}},s.prototype.removeListSelection=function(){var t=this.presetElement.querySelector("."+T);(0,e.hX)(t)||((0,e.vy)([t],T),t.removeAttribute("aria-selected"))},s.prototype.setValue=function(){this.modelValue=[this.startValue,this.endValue]},s.prototype.applyPresetRange=function(t,i){this.hide(null),this.presetsItem[this.presetsItem.length-1].start=null,this.presetsItem[this.presetsItem.length-1].end=null,this.startValue=t.start,this.endValue=t.end,this.setValue(),this.refreshControl(),this.trigger("select",this.rangeArgs(i)),this.changeTrigger(i),this.previousEleValue=this.inputElement.value,this.isCustomRange=!1,this.leftCalendar=this.rightCalendar=null,this.isKeyPopup&&(this.isRangeIconClicked=!1,this.inputElement.focus())},s.prototype.showPopup=function(t,i){this.presetHeight(),this.popupObj.show(null,1e3===this.zIndex?this.element:null),this.isMobile&&this.popupObj.refreshPosition()},s.prototype.renderCustomPopup=function(){this.isCustomWindow=!0,this.popupObj.hide(),this.popupWrapper=this.createElement("div",{id:this.element.id+"_popup",className:Ct+" "+ee}),this.renderControl(),this.openEventArgs.appendTo.appendChild(this.popupWrapper),this.showPopup(),this.isCustomRange=!0,this.isMobile||this.calendarFocus()},s.prototype.listRippleEffect=function(){for(var t=0,i=this.liCollections;t<i.length;t++)(0,e.CF)(i[t])},s.prototype.createPresets=function(){if(!(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)){this.presetElement=this.createElement("div",{className:kt,attrs:{tabindex:"0"}});var t=qt.iY.createList(this.createElement,this.presetsItem,null,!0);(0,e.uK)(t,{role:"listbox","aria-hidden":"false",id:this.element.id+"_options",tabindex:"0","aria-label":"daterangepicker-preset"}),this.presetElement.appendChild(t),this.popupWrapper.appendChild(this.presetElement);var i=this.presetElement.querySelector("#custom_range");if((0,e.hX)(i)||(i.textContent=""!==this.l10n.getConstant("customRange")?this.l10n.getConstant("customRange"):"Custom Range"),this.liCollections=this.presetElement.querySelectorAll("."+_),this.wireListEvents(),this.isMobile)if(this.fullScreenMode){var a=(0,e.n)("div",{className:"e-range-mob-popup-wrap"}),n=this.createElement("div",{className:"e-model-header"}),l=this.createElement("span",{className:"e-model-title"});l.textContent="Select Preset";var r=this.createElement("span",{className:"e-popup-close"});e.Jm.add(r,"mousedown touchstart",this.modelRangeCloseHandler,this);var h=this.presetElement;n.appendChild(r),n.appendChild(l),a.appendChild(n),a.appendChild(h),this.popupWrapper.insertBefore(a,this.popupWrapper.firstElementChild),this.presetElement.style.width="100%"}else this.presetElement.style.width=this.inputWrapper.container.getBoundingClientRect().width+"px";!(0,e.hX)(this.activeIndex)&&this.activeIndex>-1&&(0,e.iQ)([this.liCollections[this.activeIndex]],T)}},s.prototype.wireListEvents=function(){e.Jm.add(this.presetElement,"click",this.onMouseClick,this),this.isMobile||(e.Jm.add(this.presetElement,"mouseover",this.onMouseOver,this),e.Jm.add(this.presetElement,"mouseout",this.onMouseLeave,this))},s.prototype.unWireListEvents=function(){(0,e.hX)(this.presetElement)||(e.Jm.remove(this.presetElement,"click touchstart",this.onMouseClick),this.isMobile||(e.Jm.remove(this.presetElement,"mouseover",this.onMouseOver),e.Jm.remove(this.presetElement,"mouseout",this.onMouseLeave)))},s.prototype.renderPopup=function(){var t=this;this.popupWrapper.classList.add("e-control");var i=this.popupWrapper.getBoundingClientRect().width;!(0,e.hX)(this.cssClass)&&""!==this.cssClass.trim()&&(this.popupWrapper.className+=" "+this.cssClass),this.isMobile&&this.isCustomWindow&&(this.modal=this.createElement("div"),document.body.appendChild(this.modal)),this.popupObj=new It.zD(this.popupWrapper,{relateTo:this.isMobile&&this.isCustomWindow?document.body:(0,e.hX)(this.targetElement)?this.inputWrapper.container:this.targetElement,position:this.isMobile?(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)||this.isCustomWindow?{X:"center",Y:"center"}:{X:"left",Y:"bottom"}:this.enableRtl?{X:"left",Y:"bottom"}:{X:"right",Y:"bottom"},offsetX:this.isMobile||this.enableRtl?0:-i,offsetY:4,collision:this.isMobile?(0,e.b0)(this.presets[0].start&&this.presets[0].end&&this.presets[0].label)||this.isCustomWindow?{X:"fit",Y:"fit"}:{X:"fit"}:{X:"fit",Y:"flip"},targetType:this.isMobile&&this.isCustomWindow?"container":"relative",enableRtl:this.enableRtl,zIndex:this.zIndex,open:function(){t.isMobile&&t.fullScreenMode&&(t.iconRangeRight=t.calendarElement&&window.getComputedStyle(t.calendarElement.querySelector(".e-header.e-month .e-prev")).cssFloat,t.iconRangeRight&&(t.touchRangeModule=new e.Up(t.calendarElement.querySelector(".e-content.e-month"),{swipe:t.dateRangeSwipeHandler.bind(t)}),e.Jm.add(t.calendarElement.querySelector(".e-content.e-month"),"touchstart",t.touchStartRangeHandler,t))),(0,e.uK)(t.inputElement,{"aria-expanded":"true","aria-owns":t.element.id,"aria-controls":t.inputElement.id}),t.value?(0,e.uK)(t.inputElement,{"aria-activedescendant":t.inputElement.id}):t.inputElement.removeAttribute("aria-activedescendant"),(0,e.iQ)([t.inputWrapper.buttons[0]],T),t.isMobile||(t.cancelButton&&(t.btnKeyboardModule=new e.j9(t.cancelButton.element,{eventName:"keydown",keyAction:t.popupKeyActionHandle.bind(t),keyConfigs:{tab:"tab",altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"}}),t.btnKeyboardModule=new e.j9(t.applyButton.element,{eventName:"keydown",keyAction:t.popupKeyActionHandle.bind(t),keyConfigs:{altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"}})),(0,e.hX)(t.leftCalendar)||t.isRangeIconClicked||t.calendarFocus(),(0,e.hX)(t.presetElement)||(t.keyInputConfigs=(0,e.X$)(t.keyInputConfigs,t.keyConfigs),t.presetKeyboardModule=new e.j9(t.presetElement,{eventName:"keydown",keyAction:t.presetKeyActionHandler.bind(t),keyConfigs:t.keyInputConfigs}),t.presetKeyboardModule=new e.j9(t.presetElement,{eventName:"keydown",keyAction:t.popupKeyActionHandle.bind(t),keyConfigs:{altRightArrow:"alt+rightarrow",altLeftArrow:"alt+leftarrow"}}),(0,e.hX)(t.leftCalendar)?(t.preventBlur=!0,t.presetElement.focus()):t.presetElement.setAttribute("tabindex","-1")),t.popupKeyBoardHandler()),t.isMobile&&!e.Pw.isDevice&&e.Jm.add(document,"keydown",t.popupCloseHandler,t)},close:function(){(0,e.uK)(t.inputElement,{"aria-expanded":"false"}),t.inputElement.removeAttribute("aria-owns"),t.inputElement.removeAttribute("aria-controls"),t.inputElement.removeAttribute("aria-activedescendant"),(0,e.vy)([t.inputWrapper.buttons[0]],T),t.isRangeIconClicked&&t.inputWrapper.container.children[1].focus(),(0,e.b0)(t.presets[0].start&&t.presets[0].end&&t.presets[0].label)||t.unWireListEvents(),t.applyButton&&t.applyButton.destroy(),t.cancelButton&&t.cancelButton.destroy(),t.isMobile&&t.endButton&&t.endButton.destroy(),t.isMobile&&t.startButton&&t.startButton.destroy(),(0,e.hX)(t.popupObj)||((0,e.hX)(t.popupObj.element.parentElement)||(0,e.Yo)(t.popupObj.element),t.popupObj.destroy(),t.popupObj=null),t.isMobile&&!e.Pw.isDevice&&e.Jm.remove(document,"keydown",t.popupCloseHandler)},targetExitViewport:function(){var a=t.popupObj&&t.popupObj.element;!e.Pw.isDevice&&a&&a.getBoundingClientRect().height<window.innerHeight&&t.hide()}}),this.isMobile&&(this.popupObj.element.classList.add(Fe),this.isMobile||this.popupObj.element.classList.add("e-bigger")),this.isMobile&&this.isCustomWindow&&((0,e.iQ)([this.modal],[Fe,Ct,"e-range-modal"]),document.body.className+=" "+Re,this.modal.style.display="block"),e.Jm.add(document,"mousedown touchstart",this.documentHandler,this)},s.prototype.dateRangeSwipeHandler=function(t){var i=0;if("left"===this.iconRangeRight)switch(t.swipeDirection){case"Left":i=1;break;case"Right":i=-1}else switch(t.swipeDirection){case"Up":i=1;break;case"Down":i=-1}this.touchRangeStart&&(1===i?this.navigateNext(t):-1===i&&this.navigatePrevious(t),this.touchRangeStart=!1)},s.prototype.touchStartRangeHandler=function(t){this.touchRangeStart=!0},s.prototype.popupCloseHandler=function(t){27===t.keyCode&&this.hide(t)},s.prototype.calendarFocus=function(){var t=this.popupObj&&this.popupObj.element.querySelector("."+L);if(t){var i=(0,e.kp)(t,"."+P);i=(0,e.hX)(i)?this.leftCalendar:i,this.isRangeIconClicked?this.inputWrapper.container.focus():(this.preventBlur=!0,i.children[1].firstElementChild.focus()),(0,e.iQ)([t],pt)}else this.isRangeIconClicked?this.inputWrapper.container.focus():(this.preventBlur=!0,this.leftCalendar.children[1].firstElementChild.focus())},s.prototype.presetHeight=function(){var t=this.popupObj&&this.popupObj.element.querySelector("."+kt),i=this.popupObj&&this.popupObj.element.querySelector("."+se);!(0,e.hX)(t)&&!(0,e.hX)(i)&&(t.style.height=i.getBoundingClientRect().height+"px")},s.prototype.presetKeyActionHandler=function(t){switch(t.action){case"moveDown":this.listMoveDown(t),this.setScrollPosition(),t.preventDefault();break;case"moveUp":this.listMoveUp(t),this.setScrollPosition(),t.preventDefault();break;case"enter":var i=this.getHoverLI(),a=this.getActiveLI();if(!(0,e.hX)(this.leftCalendar)&&!(0,e.hX)(a)&&((0,e.hX)(i)||!(0,e.hX)(a)&&a===i)&&(this.activeIndex=Array.prototype.slice.call(this.liCollections).indexOf(a),"custom_range"===this.presetsItem[this.activeIndex].id))return this.calendarFocus(),a.classList.remove(B),void t.preventDefault();(!(0,e.hX)(i)||!(0,e.hX)(a))&&this.onMouseClick(t,i||a),t.preventDefault();break;case"tab":if(this.leftCalendar){var l=this.getHoverLI();(0,e.hX)(l)||l.classList.remove(B)}else this.hide(t),t.preventDefault()}},s.prototype.listMoveDown=function(t){var i=this.getHoverLI(),a=this.getActiveLI();if((0,e.hX)(i))if((0,e.hX)(a))(0,e.iQ)([this.liCollections[0]],B);else{var n;!(0,e.hX)(n=a.nextElementSibling)&&n.classList.contains(_)&&(0,e.iQ)([n],B)}else!(0,e.hX)(n=i.nextElementSibling)&&n.classList.contains(_)&&((0,e.vy)([i],B),(0,e.iQ)([n],B))},s.prototype.listMoveUp=function(t){var n,i=this.getHoverLI(),a=this.getActiveLI();(0,e.hX)(i)?(0,e.hX)(a)||!(0,e.hX)(n=a.previousElementSibling)&&n.classList.contains(_)&&(0,e.iQ)([n],B):!(0,e.hX)(n=i.previousElementSibling)&&n.classList.contains(_)&&((0,e.vy)([i],B),(0,e.iQ)([n],B))},s.prototype.getHoverLI=function(){return this.presetElement.querySelector("."+B)},s.prototype.getActiveLI=function(){return this.presetElement.querySelector("."+T)},s.prototype.popupKeyBoardHandler=function(){this.popupKeyboardModule=new e.j9(this.popupWrapper,{eventName:"keydown",keyAction:this.popupKeyActionHandle.bind(this),keyConfigs:{escape:"escape"}}),this.keyInputConfigs=(0,e.X$)(this.keyInputConfigs,this.keyConfigs),this.popupKeyboardModule=new e.j9(this.inputWrapper.container.children[1],{eventName:"keydown",keyAction:this.popupKeyActionHandle.bind(this),keyConfigs:this.keyInputConfigs})},s.prototype.setScrollPosition=function(){var t=this.presetElement.getBoundingClientRect().height,i=this.presetElement.querySelector("."+B),a=this.presetElement.querySelector("."+T),n=(0,e.hX)(i)?a:i;if(!(0,e.hX)(n)){var l=n.nextElementSibling,r=l?l.offsetTop:n.offsetTop,h=n.getBoundingClientRect().height;this.presetElement.scrollTop=r+n.offsetTop>t?l?r-(t/2+h/2):r:0}},s.prototype.popupKeyActionHandle=function(t){var i=(0,e.kp)(t.target,"."+kt);switch(t.action){case"escape":this.isPopupOpen()?(this.isKeyPopup&&(this.inputElement.focus(),this.isKeyPopup=!1),this.hide(t)):this.inputWrapper.container.children[1].blur();break;case"enter":this.isPopupOpen()?this.inputWrapper.container.children[1].focus():this.show(null,t);break;case"tab":this.hide(t);break;case"altRightArrow":(0,e.hX)(i)?document.activeElement===this.cancelButton.element&&!0!==this.applyButton.element.disabled?this.applyButton.element.focus():this.leftCalendar.children[1].firstElementChild.focus():this.cancelButton.element.focus(),t.preventDefault();break;case"altLeftArrow":(0,e.hX)(i)?document.activeElement===this.applyButton.element&&!0!==this.applyButton.element.disabled?this.cancelButton.element.focus():(0,e.hX)(this.presetElement)||document.activeElement!==this.cancelButton.element?this.rightCalendar.children[1].firstElementChild.focus():this.presetElement.focus():this.rightCalendar.children[1].firstElementChild.focus(),t.preventDefault()}},s.prototype.documentHandler=function(t){if(!(0,e.hX)(this.popupObj)){var i=t.target;(!this.inputWrapper.container.contains(i)||!(0,e.hX)(this.popupObj)&&!(0,e.kp)(i,'[id="'+this.popupWrapper.id+'"]')&&"mousedown"!==t.type)&&"touchstart"!==t.type&&("mousedown"===t.type||this.closeEventArgs&&!this.closeEventArgs.cancel)&&t.preventDefault(),((0,e.hX)(this.targetElement)||!(0,e.hX)(this.targetElement)&&i!==this.targetElement)&&!(0,e.kp)(i,'[id="'+this.popupWrapper.id+'"]')&&(0,e.kp)(i,"."+Xe)!==this.inputWrapper.container&&(!(0,e.kp)(i,".e-daterangepicker.e-popup")||i.classList.contains("e-day"))&&!i.classList.contains("e-dlg-overlay")&&(this.preventBlur=!1,this.isPopupOpen()&&document.body.contains(this.popupObj.element)&&(this.applyFunction(t),this.isMobile||(this.isRangeIconClicked=!1)))}},s.prototype.createInput=function(){this.fullScreenMode&&this.isMobile&&(this.cssClass+=" e-popup-expand");var t=this.cssClass;!(0,e.hX)(this.cssClass)&&""!==this.cssClass&&(t=this.cssClass.replace(/\s+/g," ").trim()),this.inputWrapper=v.pd.createInput({floatLabelType:this.floatLabelType,element:this.inputElement,properties:{readonly:this.readonly,placeholder:this.placeholder,cssClass:t,enabled:this.enabled,enableRtl:this.enableRtl,showClearButton:this.showClearButton},buttons:["e-input-group-icon e-range-icon e-icons"]},this.createElement),(0,e.uK)(this.inputElement,{tabindex:"0","aria-expanded":"false",role:"combobox",autocomplete:"off","aria-disabled":this.enabled?"false":"true",autocorrect:"off",autocapitalize:"off",spellcheck:"false"}),v.pd.addAttributes({"aria-label":"select",role:"button"},this.inputWrapper.buttons[0]),this.setEleWidth(this.width),(0,e.iQ)([this.inputWrapper.container],"e-date-range-wrapper"),(0,e.hX)(this.inputElement.getAttribute("name"))&&(0,e.uK)(this.inputElement,{name:this.element.id}),"hidden"===this.inputElement.type&&(this.inputWrapper.container.style.display="none"),this.refreshControl(),this.previousEleValue=this.inputElement.value,this.inputElement.setAttribute("value",this.inputElement.value),this.startCopy=this.startDate,this.endCopy=this.endDate},s.prototype.setEleWidth=function(t){this.inputWrapper.container.style.width="string"==typeof t?this.width:"number"==typeof t?(0,e.IV)(this.width):"100%"},s.prototype.adjustLongHeaderWidth=function(){"Wide"===this.dayHeaderFormat&&(0,e.iQ)([this.popupWrapper],"e-daterange-day-header-lg")},s.prototype.refreshControl=function(){this.validateMinMax(),this.strictMode&&this.validateRangeStrict();var t=this.disabledDates();this.strictMode&&t&&(this.startValue=this.previousStartValue,this.setProperties({startDate:this.startValue},!0),this.endValue=this.previousEndValue,this.setProperties({endDate:this.endValue},!0),this.setValue()),this.updateInput(),this.strictMode||this.validateRange(),!this.strictMode&&t&&this.clearRange(),!(0,e.hX)(this.endValue)&&!(0,e.hX)(this.startValue)&&!t&&!(0,e.hX)(this.renderDayCellArgs)&&this.renderDayCellArgs.isDisabled&&this.disabledDateRender(),this.errorClass(),this.previousStartValue=(0,e.hX)(this.startValue)||isNaN(+this.startValue)?null:new Date(+this.startValue),this.previousEndValue=(0,e.hX)(this.endValue)||isNaN(+this.endValue)?null:new Date(+this.endValue)},s.prototype.updateInput=function(){if(!(0,e.hX)(this.endValue)&&!(0,e.hX)(this.startValue)){var t={format:this.formatString,type:"date",skeleton:"yMd"},i=this.globalize.formatDate(this.startValue,t),a=this.globalize.formatDate(this.endValue,t);v.pd.setValue(i+" "+this.separator+" "+a,this.inputElement,this.floatLabelType,this.showClearButton),this.previousStartValue=new Date(+this.startValue),this.previousEndValue=new Date(+this.endValue)}!this.strictMode&&(0,e.hX)(this.value)&&this.invalidValueString&&v.pd.setValue(this.invalidValueString,this.inputElement,this.floatLabelType,this.showClearButton)},s.prototype.checkInvalidRange=function(t){if(!(0,e.hX)(t)){var i=!1,a=void 0,n=void 0,l=null,r=null,h=null,o=!1,p=!1,c=!1;if("string"==typeof t){var f=t.split(" "+this.separator+" ");2===f.length?(l=f[0],r=f[1]):(i=!0,h=t)}else t.length>0?(a=t[0],n=t[1]):(a=t.start,n=t.end),a instanceof Date||"object"==typeof a?a instanceof Date?o=!0:(0,e.hX)(a)||(c=!0):l=this.getstringvalue(a),n instanceof Date||"object"==typeof n?n instanceof Date?p=!0:(0,e.hX)(n)||(c=!0):r=this.getstringvalue(n);((0,e.hX)(l)&&!o&&!(0,e.hX)(r)||!(0,e.hX)(l)&&!p&&(0,e.hX)(r))&&(i=!0),c&&(l=r=h=null,i=!0),l&&(i=i||this.checkInvalidValue(l)),r&&(i=i||this.checkInvalidValue(r)),i&&(o&&!c&&(l=a.toLocaleDateString()),p&&!c&&(r=n.toLocaleDateString()),(0,e.hX)(l)||(0,e.hX)(r)?(0,e.hX)(l)?(0,e.hX)(r)||(h=r):h=l:h=l+" "+this.separator+" "+r,this.invalidValueString=h,this.setProperties({value:null},!0),this.setProperties({startValue:null},!0),this.setProperties({endValue:null},!0),this.startDate=null,this.endDate=null)}},s.prototype.getstringvalue=function(t){var i=null;return(0,e.hX)(t)||"number"!=typeof t?!(0,e.hX)(t)&&"string"==typeof t&&(i=""+t):i=t.toString(),i},s.prototype.checkInvalidValue=function(t){var n,i=t,a=!1;if(n={format:this.formatString,type:"date",skeleton:"yMd"},"string"!=typeof i)a=!0;else{var l=new e.DL(this.locale);if(!this.checkDateValue(l.parseDate(i,n))){var h=null;h=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,(!/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/.test(i)&&!h.test(i)||/^[a-zA-Z0-9- ]*$/.test(i)||isNaN(+new Date(this.checkValue(i))))&&(a=!0)}}return a},s.prototype.isDateDisabled=function(t){if((0,e.hX)(t))return!1;var i=new Date(+t);if(+i<+this.min||+i>+this.max)return!0;this.virtualRenderCellArgs={date:i,isDisabled:!1};var a=this.virtualRenderCellArgs;return this.virtualRenderCellEvent(a),!!a.isDisabled},s.prototype.disabledDateRender=function(){this.disabledDays=[],this.disabledDayCnt=null;for(var t=new Date(+this.startValue),i=0;+t<=+this.endValue&&+this.endValue<=+this.max;){this.virtualRenderCellArgs={date:t,isDisabled:!1};var a=this.virtualRenderCellArgs;this.virtualRenderCellEvent(a),a.isDisabled&&(this.disabledDays.push(new Date(+a.date)),+t>+this.startValue&&+t<+this.endValue&&i++),this.addDay(t,1,null,this.max,this.min)}this.disabledDayCnt=i},s.prototype.virtualRenderCellEvent=function(t){(0,e.X$)(this.virtualRenderCellArgs,{name:"renderDayCell"}),this.trigger("renderDayCell",t)},s.prototype.disabledDates=function(){var t=!1,i=!1;return!(0,e.hX)(this.endValue)&&!(0,e.hX)(this.startValue)&&(t=this.isDateDisabled(this.startValue),i=this.isDateDisabled(this.endValue),this.isPopupOpen()||(this.currentDate=null),this.setValue()),t||i},s.prototype.setModelValue=function(){this.value||null!==this.startDate||null!==this.endDate?null===this.value||null===this.value.start?null===this.value?this.setProperties({value:[this.startDate,this.endDate]},!0):null===this.value.start&&this.setProperties({value:{start:this.startDate,end:this.endDate}},!0):this.value&&this.value.length>0||this.valueType&&this.valueType.length>0?((+this.startDate!=+this.value[0]||+this.endDate!=+this.value[1])&&this.setProperties({value:[this.startDate,this.endDate]},!0),this.value&&null==this.value[0]&&null==this.value[1]&&this.setProperties({value:null},!0)):this.value&&this.value.start&&this.setProperties({value:{start:this.startDate,end:this.endDate}},!0):this.setProperties({value:null},!0),this.createHiddenInput()},s.prototype.dispatchEvent=function(t,i){var a=document.createEvent("HTMLEvents");a.initEvent(i,!1,!0),t.dispatchEvent(a),this.firstHiddenChild.dispatchEvent(a)},s.prototype.changeTrigger=function(t){(+this.initStartDate!=+this.startValue||+this.initEndDate!=+this.endValue)&&(this.setProperties({endDate:this.checkDateValue(this.endValue)},!0),this.setProperties({startDate:this.checkDateValue(this.startValue)},!0),this.setModelValue(),this.isAngular&&this.preventChange?this.preventChange=!1:this.trigger("change",this.rangeArgs(t))),this.previousEleValue=this.inputElement.value,this.initStartDate=this.checkDateValue(this.startValue),this.initEndDate=this.checkDateValue(this.endValue)},s.prototype.navigateTo=function(t,i){if(this.isPopupOpen()){if("month"===t.toLowerCase())t="Month";else if("year"===t.toLowerCase())t="Year";else{if("decade"!==t.toLowerCase())return;t="Decade"}this.getViewNumber(t)<this.getViewNumber(this.depth)&&(t=this.depth),this.isMobile?u.prototype.navigateTo.call(this,t,i):(i<this.min?i=new Date(+this.min):i>=this.max&&(i=new Date(+this.max)),"Month"===t&&this.isSameMonth(i,this.max)?i=new Date(this.max.getFullYear(),this.max.getMonth()-1,this.min.getDate()):"Year"===t&&this.isSameYear(i,this.max)?i=new Date(this.max.getFullYear()-1,this.max.getMonth(),this.max.getDate()):"Decade"===t&&this.isSameDecade(i,this.max)&&(i=new Date(this.max.getFullYear()-10,this.max.getMonth(),this.max.getDate())),this.leftCalCurrentDate=i,this.navigate(this.leftCalendar,this.leftCalCurrentDate,t),i="Month"===t?new Date(this.currentDate.setMonth(this.currentDate.getMonth()+1)):"Year"===t?new Date(this.currentDate.setFullYear(this.currentDate.getFullYear()+1)):new Date(this.currentDate.setFullYear(this.currentDate.getFullYear()+10)),this.rightCalCurrentDate=i,this.navigate(this.rightCalendar,this.rightCalCurrentDate,t),this.leftKeyboardModule=this.rightKeyboardModule=null,this.updateNavIcons()),this.currentView()===this.depth&&this.bindCalendarCellEvents(),this.removeFocusedDate(),this.updateRange(this.isMobile?[this.calendarElement]:[this.leftCalendar,this.rightCalendar])}},s.prototype.navigate=function(t,i,a){this.calendarElement=t,this.table=t.querySelector("table"),this.tableBodyElement=t.querySelector("tbody"),this.headerTitleElement=t.querySelector(".e-title"),this.tableHeadElement=t.querySelector("thead"),this.contentElement=t.querySelector(".e-content"),this.previousIcon=t.querySelector(".e-prev"),this.nextIcon=t.querySelector(".e-next"),this.effect="e-zoomin",u.prototype.navigateTo.call(this,a,i)},s.prototype.focusIn=function(){document.activeElement!==this.inputElement&&this.enabled&&((0,e.iQ)([this.inputWrapper.container],[ot]),this.inputElement.focus())},s.prototype.focusOut=function(){var t=this.preventBlur;document.activeElement===this.inputElement&&((0,e.vy)([this.inputWrapper.container],[ot]),this.preventBlur=!1,this.inputElement.blur(),this.preventBlur=t)},s.prototype.destroy=function(){this.unBindEvents(),this.showClearButton&&(this.clearButton=document.getElementsByClassName("e-clear-icon")[0]),this.hide(null);var t={tabindex:"0","aria-expanded":"false",role:"combobox",autocomplete:"off","aria-disabled":this.enabled?"false":"true",autocorrect:"off",autocapitalize:"off","aria-invalid":"false",spellcheck:"false"};this.inputElement&&((0,e.vy)([this.inputElement],[Ct]),e.Jm.remove(this.inputElement,"blur",this.inputBlurHandler),v.pd.removeAttributes(t,this.inputElement),(0,e.hX)(this.cloneElement.getAttribute("tabindex"))?this.inputElement.removeAttribute("tabindex"):this.inputElement.setAttribute("tabindex",this.tabIndex),this.ensureInputAttribute(),this.inputElement.classList.remove("e-input"),(0,e.hX)(this.inputWrapper)||(e.Jm.remove(this.inputWrapper.buttons[0],"mousedown",this.rangeIconHandler),null===this.angularTag&&this.inputWrapper.container.parentElement.appendChild(this.inputElement),(0,e.Yo)(this.inputWrapper.container))),!(0,e.hX)(this.inputKeyboardModule)&&!this.isMobile&&this.inputKeyboardModule.destroy(),this.popupObj&&(this.isMobile||this.clearCalendarEvents()),v.pd.destroy({element:this.inputElement,floatLabelType:this.floatLabelType,properties:this.properties},this.clearButton),u.prototype.destroy.call(this),this.inputWrapper=this.popupWrapper=this.popupObj=this.cloneElement=this.presetElement=null,this.formElement&&e.Jm.remove(this.formElement,"reset",this.formResetHandler),!(0,e.hX)(this.firstHiddenChild)&&!(0,e.hX)(this.secondHiddenChild)&&((0,e.Yo)(this.firstHiddenChild),(0,e.Yo)(this.secondHiddenChild),this.firstHiddenChild=this.secondHiddenChild=null,this.inputElement.setAttribute("name",this.element.getAttribute("data-name")),this.inputElement.removeAttribute("data-name")),this.closeEventArgs=null,this.leftCalendar=null,this.rightTitle=null,this.leftTitle=null,this.openEventArgs=null,this.leftCalNextIcon=null,this.rightCalendar=null,this.closeEventArgs=null,this.rightCalPrevIcon=null,this.leftCalPrevIcon=null,this.popupKeyboardModule=null,this.cancelButton=null,this.applyButton=null,this.calendarElement=null,this.leftKeyboardModule=null,this.rightCalNextIcon=null,this.leftCalNextIcon=null,this.btnKeyboardModule=null,this.rightKeyboardModule=null,this.leftKeyboardModule=null,this.presetKeyboardModule=null,this.liCollections=null,this.popupObj=null,this.popupWrapper=null},s.prototype.ensureInputAttribute=function(){for(var t=[],i=0;i<this.inputElement.attributes.length;i++)t[i]=this.inputElement.attributes[i].name;for(i=0;i<t.length;i++)(0,e.hX)(this.cloneElement.getAttribute(t[i]))?("value"===t[i].toLowerCase()&&(this.inputElement.value=""),this.inputElement.removeAttribute(t[i])):("value"===t[i].toLowerCase()&&(this.inputElement.value=this.cloneElement.getAttribute(t[i])),this.inputElement.setAttribute(t[i],this.cloneElement.getAttribute(t[i])))},s.prototype.getModuleName=function(){return"daterangepicker"},s.prototype.getPersistData=function(){return this.addOnPersist(["startDate","endDate","value"])},s.prototype.getSelectedRange=function(){var t;return(0,e.hX)(this.startValue)||(0,e.hX)(this.endValue)?t=0:(t=Math.round(Math.abs((this.removeTimeValueFromDate(this.startValue).getTime()-this.removeTimeValueFromDate(this.endValue).getTime())/864e5))+1,!(0,e.hX)(this.renderDayCellArgs)&&this.renderDayCellArgs.isDisabled&&this.disabledDateRender(),(0,e.hX)(this.disabledDayCnt)||(t-=this.disabledDayCnt,this.disabledDayCnt=null)),{startDate:this.startValue,endDate:this.endValue,daySpan:t}},s.prototype.show=function(t,i){var a=this;if(this.isMobile&&this.popupObj&&this.popupObj.refreshPosition(),!(this.enabled&&this.readonly||!this.enabled||this.popupObj||this.isPopupOpen())&&(t&&(this.targetElement=t),this.createPopup(),(this.isMobile||e.Pw.isDevice)&&(this.mobileRangePopupWrap=this.createElement("div",{className:"e-daterangepick-mob-popup-wrap"}),document.body.appendChild(this.mobileRangePopupWrap)),this.openEventArgs={popup:this.popupObj||null,cancel:!1,date:this.inputElement.value,model:this,event:i||null,appendTo:this.isMobile||e.Pw.isDevice?this.mobileRangePopupWrap:document.body},this.trigger("open",this.openEventArgs,function(r){if(a.openEventArgs=r,!a.openEventArgs.cancel){a.openEventArgs.appendTo.appendChild(a.popupWrapper),a.showPopup(t,i);var h=!a.isCustomRange||a.isMobile&&a.isCustomRange;!(0,e.b0)(a.presets[0].start&&a.presets[0].end&&a.presets[0].label)&&h&&a.setScrollPosition(),a.checkMinMaxDays(),a.isMobile&&!(0,e.hX)(a.startDate)&&(0,e.hX)(a.endDate)&&(a.endButton.element.classList.add(T),a.startButton.element.classList.remove(T),a.endButton.element.removeAttribute("disabled"),a.selectableDates()),u.prototype.setOverlayIndex.call(a,a.mobileRangePopupWrap,a.popupObj.element,a.modal,a.isMobile||e.Pw.isDevice)}}),e.Pw.isDevice)){var l=this.createElement("div",{className:"e-dlg-overlay"});l.style.zIndex=(this.zIndex-1).toString(),this.mobileRangePopupWrap.appendChild(l)}},s.prototype.hide=function(t){var i=this;this.popupObj?((0,e.hX)(this.previousEndValue)&&(0,e.hX)(this.previousStartValue)?this.clearRange():((0,e.hX)(this.previousStartValue)?(this.startValue=null,this.setValue()):(this.startValue=new Date(this.checkValue(this.previousStartValue)),this.setValue(),this.currentDate=new Date(this.checkValue(this.startValue))),(0,e.hX)(this.previousEndValue)?(this.endValue=null,this.setValue()):(this.endValue=new Date(this.checkValue(this.previousEndValue)),this.setValue())),this.isPopupOpen()&&(this.closeEventArgs={cancel:!1,popup:this.popupObj,date:this.inputElement.value,model:this,event:t||null},this.trigger("close",this.closeEventArgs,function(n){i.closeEventArgs=n,i.closeEventArgs.cancel?(0,e.vy)([i.inputWrapper.buttons[0]],T):(i.isMobile&&!(0,e.hX)(i.startButton)&&!(0,e.hX)(i.endButton)&&(e.Jm.remove(i.startButton.element,"click touchstart",i.deviceHeaderClick),e.Jm.remove(i.endButton.element,"click touchstart",i.deviceHeaderClick)),i.popupObj&&(i.popupObj.hide(),i.preventBlur&&(i.inputElement.focus(),(0,e.iQ)([i.inputWrapper.container],[ot]))),i.isMobile||(!(0,e.hX)(i.leftKeyboardModule)&&!(0,e.hX)(i.rightKeyboardModule)&&(i.leftKeyboardModule.destroy(),i.rightKeyboardModule.destroy()),(0,e.hX)(i.presetElement)||i.presetKeyboardModule.destroy(),(0,e.hX)(i.cancelButton)||i.btnKeyboardModule.destroy()),i.targetElement=null,(0,e.vy)([document.body],Re),e.Jm.remove(document,"mousedown touchstart",i.documentHandler),i.isMobile&&i.modal&&(i.modal.style.display="none",i.modal.outerHTML="",i.modal=null),(i.isMobile||e.Pw.isDevice)&&((0,e.hX)(i.mobileRangePopupWrap)||(i.mobileRangePopupWrap.remove(),i.mobileRangePopupWrap=null)),i.isKeyPopup=i.dateDisabled=!1),i.updateClearIconState(),i.updateHiddenInput(),i.isMobile&&i.allowEdit&&!i.readonly&&i.inputElement.removeAttribute("readonly")}))):(this.updateClearIconState(),this.updateHiddenInput(),this.isMobile&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly"))},s.prototype.setLocale=function(){this.globalize=new e.DL(this.locale),this.l10n.setLocale(this.locale),this.dateRangeOptions&&null==this.dateRangeOptions.placeholder&&(this.setProperties({placeholder:this.l10n.getConstant("placeholder")},!0),v.pd.setPlaceholder(this.placeholder,this.inputElement)),this.updateInput(),this.updateHiddenInput(),this.changeTrigger()},s.prototype.refreshChange=function(){this.checkView(),this.refreshControl(),this.changeTrigger()},s.prototype.setDate=function(){v.pd.setValue("",this.inputElement,this.floatLabelType,this.showClearButton),this.refreshChange()},s.prototype.enableInput=function(){+this.min<=+this.max&&(this.setProperties({enabled:!0},!0),v.pd.setEnabled(this.enabled,this.inputElement),this.element.hasAttribute("disabled")&&this.bindEvents())},s.prototype.clearModelvalue=function(t,i){this.setProperties({startDate:null},!0),this.setProperties({endDate:null},!0),i.value&&i.value.length>0?this.setProperties({value:null},!0):(i.value&&i.value.start||i.value&&!i.value.start)&&this.setProperties({value:{start:null,end:null}},!0),this.updateValue(),this.setDate()},s.prototype.createHiddenInput=function(){(0,e.hX)(this.firstHiddenChild)&&(0,e.hX)(this.secondHiddenChild)&&(this.firstHiddenChild=this.createElement("input"),this.secondHiddenChild=this.createElement("input")),(0,e.hX)(this.inputElement.getAttribute("name"))||(this.inputElement.setAttribute("data-name",this.inputElement.getAttribute("name")),this.inputElement.removeAttribute("name")),(0,e.uK)(this.firstHiddenChild,{type:"text",name:this.inputElement.getAttribute("data-name"),class:je}),(0,e.uK)(this.secondHiddenChild,{type:"text",name:this.inputElement.getAttribute("data-name"),class:je});var t={format:this.formatString,type:"datetime",skeleton:"yMd"};this.firstHiddenChild.value=this.startDate&&this.globalize.formatDate(this.startDate,t),this.secondHiddenChild.value=this.endDate&&this.globalize.formatDate(this.endDate,t),this.inputElement.parentElement.appendChild(this.firstHiddenChild),this.inputElement.parentElement.appendChild(this.secondHiddenChild)},s.prototype.setMinMaxDays=function(){this.isPopupOpen()&&(this.removeClassDisabled(),this.checkMinMaxDays(),this.isMobile&&this.selectableDates(),!this.strictMode&&(0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue)?this.removeSelection():this.updateRange(this.isMobile?[this.calendarElement]:[this.leftCalendar,this.rightCalendar]),this.updateHeader())},s.prototype.getAmPmValue=function(t){try{return"string"==typeof t&&""!==t.trim()?t.replace(/(am|pm|Am|aM|pM|Pm)/g,function(i){return i.toLocaleUpperCase()}):""}catch(i){return console.error("Error occurred while processing date:",i),""}},s.prototype.getStartEndValue=function(t,i){return"Month"===this.depth?this.checkDateValue(new Date(this.checkValue(t))):"Year"===this.depth?new Date(t.getFullYear(),t.getMonth()+(i?1:0),i?0:1):new Date(t.getFullYear(),i?11:0,i?31:1)},s.prototype.onPropertyChanged=function(t,i){for(var a={format:this.formatString,type:"date",skeleton:"yMd"},n=!1,l=0,r=Object.keys(t);l<r.length;l++){var h=r[l];switch(["blur","change","cleared","close","created","destroyed","focus","navigated","open","renderDayCell","select"].indexOf(h)>0&&this.isReact&&(n=!0),h){case"width":this.setEleWidth(this.width),v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"separator":this.previousEleValue=this.inputElement.value,this.setProperties({separator:t.separator},!0),this.updateInput(),this.changeTrigger();break;case"placeholder":v.pd.setPlaceholder(t.placeholder,this.inputElement),this.setProperties({placeholder:t.placeholder},!0);break;case"readonly":v.pd.setReadonly(this.readonly,this.inputElement),this.setRangeAllowEdit();break;case"cssClass":this.updateCssClass(t.cssClass,i.cssClass);break;case"enabled":this.setProperties({enabled:t.enabled},!0),v.pd.setEnabled(this.enabled,this.inputElement),this.enabled?this.inputElement.setAttribute("tabindex",this.tabIndex):this.inputElement.tabIndex=-1;break;case"allowEdit":this.setRangeAllowEdit();break;case"enableRtl":this.setProperties({enableRtl:t.enableRtl},!0),v.pd.setEnableRtl(this.enableRtl,[this.inputWrapper.container]);break;case"zIndex":this.setProperties({zIndex:t.zIndex},!0);break;case"format":this.setProperties({format:t.format},!0),this.checkFormat(),this.updateInput(),this.changeTrigger();break;case"locale":this.globalize=new e.DL(this.locale),this.l10n.setLocale(this.locale),this.dateRangeOptions&&null==this.dateRangeOptions.placeholder&&(this.setProperties({placeholder:this.l10n.getConstant("placeholder")},!0),v.pd.setPlaceholder(this.placeholder,this.inputElement)),this.setLocale();break;case"htmlAttributes":this.updateHtmlAttributeToElement(),this.updateHtmlAttributeToWrapper(),this.setDataAttribute(!0),this.checkHtmlAttributes(!0);break;case"showClearButton":v.pd.setClearButton(this.showClearButton,this.inputElement,this.inputWrapper),this.bindClearEvent();break;case"startDate":"string"==typeof t.startDate&&(t.startDate=this.globalize.parseDate(this.getAmPmValue(t.startDate),a)),+this.initStartDate!=+t.startDate&&(this.startValue=this.getStartEndValue(t.startDate,!1),this.setDate(),this.setValue());break;case"endDate":"string"==typeof t.endDate&&(t.endDate=this.globalize.parseDate(this.getAmPmValue(t.endDate),a)),+this.initEndDate!=+t.endDate&&(this.endValue=this.getStartEndValue(t.endDate,!0),this.setDate(),this.setValue());break;case"value":if(n=!0,this.invalidValueString=null,this.checkInvalidRange(t.value),"string"==typeof t.value)if(this.invalidValueString)this.clearModelvalue(t,i);else{var p=t.value.split(" "+this.separator+" ");this.value=[new Date(p[0]),new Date(p[1])],this.updateValue(),this.setDate()}else!(0,e.hX)(t.value)&&t.value.length>0||!(0,e.hX)(t.value)&&t.value.start?(this.valueType=t.value,null===t.value[0]||null===t.value.start?(1===t.value.length||t.value.start||null===t.value[1]||null===t.value.start)&&this.clearModelvalue(t,i):(+this.initStartDate!=+t.value[0]||+this.initEndDate!=+t.value[1]||+this.initStartDate!=+(t.value.start||+this.initEndDate!=+t.value.start))&&((1===t.value.length||t.value.start)&&(this.modelValue=t.value),this.updateValue(),this.setDate())):((0,e.hX)(this.value)||null==t.value.start)&&(this.valueType=t.value,this.startValue=null,this.endValue=null,this.clearModelvalue(t,i));if(this.isPopupOpen()){if((0,e.hX)(this.startValue)&&(0,e.hX)(this.endValue))return this.removeSelection(),void(this.isMobile&&this.deviceHeaderUpdate());this.isMobile?(this.navigate(this.deviceCalendar,this.startValue,this.currentView()),this.deviceHeaderUpdate()):(this.navigate(this.leftCalendar,this.startValue,this.currentView()),this.updateControl(this.leftCalendar),this.navigate(this.rightCalendar,this.endValue,this.currentView()),this.updateControl(this.rightCalendar)),this.updateRange(this.isMobile?[this.calendarElement]:[this.leftCalendar,this.rightCalendar]),this.updateHeader(),this.applyButton.disabled=this.applyButton.element.disabled=!1}this.preventChange=this.isAngular&&this.preventChange?!this.preventChange:this.preventChange;break;case"minDays":n=!0,this.setProperties({minDays:t.minDays},!0),this.refreshChange(),this.setMinMaxDays();break;case"maxDays":n=!0,this.setProperties({maxDays:t.maxDays},!0),this.refreshChange(),this.setMinMaxDays();break;case"min":this.setProperties({min:this.checkDateValue(new Date(this.checkValue(t.min)))},!0),this.previousEleValue=this.inputElement.value,this.enableInput(),this.refreshChange();break;case"max":this.setProperties({max:this.checkDateValue(new Date(this.checkValue(t.max)))},!0),this.enableInput(),this.refreshChange();break;case"strictMode":this.invalidValueString=null,this.setProperties({strictMode:t.strictMode},!0),this.refreshChange();break;case"presets":this.setProperties({presets:t.presets},!0),this.processPresets();break;case"floatLabelType":this.floatLabelType=t.floatLabelType,v.pd.removeFloating(this.inputWrapper),v.pd.addFloating(this.inputElement,this.floatLabelType,this.placeholder),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"start":this.setProperties({start:t.start},!0),this.refreshChange();break;case"depth":this.setProperties({depth:t.depth},!0),this.refreshChange()}n||this.hide(null)}},D([(0,e.mA)(null)],s.prototype,"value",void 0),D([(0,e.mA)(!1)],s.prototype,"enablePersistence",void 0),D([(0,e.mA)(new Date(1900,0,1))],s.prototype,"min",void 0),D([(0,e.mA)(new Date(2099,11,31))],s.prototype,"max",void 0),D([(0,e.mA)(null)],s.prototype,"locale",void 0),D([(0,e.mA)(null)],s.prototype,"firstDayOfWeek",void 0),D([(0,e.mA)(!1)],s.prototype,"weekNumber",void 0),D([(0,e.mA)("Gregorian")],s.prototype,"calendarMode",void 0),D([(0,e.mA)(!1)],s.prototype,"openOnFocus",void 0),D([(0,e.mA)(!1)],s.prototype,"fullScreenMode",void 0),D([(0,e.Jh)()],s.prototype,"created",void 0),D([(0,e.Jh)()],s.prototype,"destroyed",void 0),D([(0,e.Jh)()],s.prototype,"change",void 0),D([(0,e.Jh)()],s.prototype,"cleared",void 0),D([(0,e.Jh)()],s.prototype,"navigated",void 0),D([(0,e.Jh)()],s.prototype,"renderDayCell",void 0),D([(0,e.mA)(null)],s.prototype,"startDate",void 0),D([(0,e.mA)(null)],s.prototype,"endDate",void 0),D([(0,e.pM)([{}],Cs)],s.prototype,"presets",void 0),D([(0,e.mA)("")],s.prototype,"width",void 0),D([(0,e.mA)(1e3)],s.prototype,"zIndex",void 0),D([(0,e.mA)(!0)],s.prototype,"showClearButton",void 0),D([(0,e.mA)(!0)],s.prototype,"showTodayButton",void 0),D([(0,e.mA)("Month")],s.prototype,"start",void 0),D([(0,e.mA)("Month")],s.prototype,"depth",void 0),D([(0,e.mA)("")],s.prototype,"cssClass",void 0),D([(0,e.mA)("-")],s.prototype,"separator",void 0),D([(0,e.mA)(null)],s.prototype,"minDays",void 0),D([(0,e.mA)(null)],s.prototype,"maxDays",void 0),D([(0,e.mA)(!1)],s.prototype,"strictMode",void 0),D([(0,e.mA)(null)],s.prototype,"keyConfigs",void 0),D([(0,e.mA)(null)],s.prototype,"format",void 0),D([(0,e.mA)(!0)],s.prototype,"enabled",void 0),D([(0,e.mA)(!1)],s.prototype,"readonly",void 0),D([(0,e.mA)(!0)],s.prototype,"allowEdit",void 0),D([(0,e.mA)("Never")],s.prototype,"floatLabelType",void 0),D([(0,e.mA)(null)],s.prototype,"placeholder",void 0),D([(0,e.mA)({})],s.prototype,"htmlAttributes",void 0),D([(0,e.Jh)()],s.prototype,"open",void 0),D([(0,e.Jh)()],s.prototype,"close",void 0),D([(0,e.Jh)()],s.prototype,"select",void 0),D([(0,e.Jh)()],s.prototype,"focus",void 0),D([(0,e.Jh)()],s.prototype,"blur",void 0),D([e.kc],s)}(Me),Ke=function(){var u=function(s,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var n in a)a.hasOwnProperty(n)&&(i[n]=a[n])})(s,t)};return function(s,t){function i(){this.constructor=s}u(s,t),s.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}}(),E=function(u,s,t,i){var l,a=arguments.length,n=a<3?s:null===i?i=Object.getOwnPropertyDescriptor(s,t):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(u,s,t,i);else for(var r=u.length-1;r>=0;r--)(l=u[r])&&(n=(a<3?l(n):a>3?l(s,t,n):l(s,t))||n);return a>3&&n&&Object.defineProperty(s,t,n),n},Je="e-error",Ue=(new Date).getDate(),Qe=(new Date).getMonth(),$e=(new Date).getFullYear(),Yt="e-timepicker",ct="e-active",ne="e-hover",re="e-navigation",$="e-disabled",le="e-input-focus",tt="e-list-item",_e="e-time-overflow",he="e-non-edit",ti=["title","class","style"];(oe||(oe={})).createListItems=function s(t,i,a,n,l,r){var h,o=6e4*r,p=[],c=[];h=+i.setMilliseconds(0);for(var f=+a.setMilliseconds(0);f>=h;)c.push(h),p.push(n.formatDate(new Date(h),{format:l,type:"time"})),h+=o;return{collection:c,list:qt.iY.createList(t,p,null,!0)}};var xs=function(u){function s(t,i){var a=u.call(this,t,i)||this;return a.liCollections=[],a.timeCollections=[],a.disableItemCollection=[],a.invalidValueString=null,a.preventChange=!1,a.maskedDateValue="",a.moduleName=a.getModuleName(),a.timeOptions=t,a}return Ke(s,u),s.prototype.preRender=function(){this.keyConfigure={enter:"enter",escape:"escape",end:"end",tab:"tab",home:"home",down:"downarrow",up:"uparrow",left:"leftarrow",right:"rightarrow",open:"alt+downarrow",close:"alt+uparrow"},this.cloneElement=this.element.cloneNode(!0),(0,e.vy)([this.cloneElement],[Yt,"e-control","e-lib"]),this.inputElement=this.element,this.angularTag=null,this.formElement=(0,e.kp)(this.element,"form"),"EJS-TIMEPICKER"===this.element.tagName&&(this.angularTag=this.element.tagName,this.inputElement=this.createElement("input"),this.element.appendChild(this.inputElement)),this.tabIndex=this.element.hasAttribute("tabindex")?this.element.getAttribute("tabindex"):"0",this.element.removeAttribute("tabindex"),this.openPopupEventArgs={appendTo:document.body}},s.prototype.render=function(){this.initialize(),this.createInputElement(),this.updateHtmlAttributeToWrapper(),this.setTimeAllowEdit(),this.setEnable(),this.validateInterval(),this.bindEvents(),this.validateDisable(),this.setTimeZone(),this.setValue(this.getFormattedValue(this.value)),this.enableMask&&!this.value&&this.maskedDateValue&&("Always"===this.floatLabelType||!this.floatLabelType||!this.placeholder)&&(this.updateInputValue(this.maskedDateValue),this.checkErrorState(this.maskedDateValue)),this.anchor=this.inputElement,this.inputElement.setAttribute("value",this.inputElement.value),this.inputEleValue=this.getDateObject(this.inputElement.value),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon"),!(0,e.hX)((0,e.kp)(this.element,"fieldset"))&&(0,e.kp)(this.element,"fieldset").disabled&&(this.enabled=!1),this.renderComplete()},s.prototype.setTimeZone=function(){if(!(0,e.hX)(this.serverTimezoneOffset)&&this.value){var t=(new Date).getTimezoneOffset()/60,a=this.serverTimezoneOffset+t;a=this.isDayLightSaving()?a--:a,this.value=new Date(this.value.getTime()+60*a*60*1e3)}},s.prototype.isDayLightSaving=function(){var t=new Date(this.value.getFullYear(),0,1).getTimezoneOffset(),i=new Date(this.value.getFullYear(),6,1).getTimezoneOffset();return this.value.getTimezoneOffset()<Math.max(t,i)},s.prototype.setTimeAllowEdit=function(){this.allowEdit?this.readonly||this.inputElement.removeAttribute("readonly"):(0,e.uK)(this.inputElement,{readonly:""}),this.clearIconState()},s.prototype.clearIconState=function(){this.allowEdit||!this.inputWrapper||this.readonly?this.inputWrapper&&(0,e.vy)([this.inputWrapper.container],[he]):""===this.inputElement.value?(0,e.vy)([this.inputWrapper.container],[he]):(0,e.iQ)([this.inputWrapper.container],[he])},s.prototype.validateDisable=function(){this.setMinMax(this.initMin,this.initMax),(0,e.hX)(this.value)||(this.popupCreation(),this.popupObj.destroy(),this.popupWrapper=this.popupObj=null),!isNaN(+this.value)&&null!==this.value&&(this.valueIsDisable(this.value)||(this.strictMode&&this.resetState(),this.initValue=null,this.initMax=this.getDateObject(this.initMax),this.initMin=this.getDateObject(this.initMin),this.timeCollections=this.liCollections=[],this.setMinMax(this.initMin,this.initMax)))},s.prototype.validationAttribute=function(t,i){var a=t.getAttribute("name")?t.getAttribute("name"):t.getAttribute("id");i.setAttribute("name",a),t.removeAttribute("name");for(var n=["required","aria-required","form"],l=0;l<n.length;l++)if(!(0,e.hX)(t.getAttribute(n[l]))){var r=t.getAttribute(n[l]);i.setAttribute(n[l],r),t.removeAttribute(n[l])}},s.prototype.initialize=function(){this.globalize=new e.DL(this.locale),this.defaultCulture=new e.DL("en"),this.checkTimeFormat(),this.checkInvalidValue(this.value),this.setProperties({value:this.checkDateValue(new Date(this.checkInValue(this.value)))},!0),this.setProperties({min:this.checkDateValue(new Date(this.checkInValue(this.min)))},!0),this.setProperties({max:this.checkDateValue(new Date(this.checkInValue(this.max)))},!0),this.setProperties({scrollTo:this.checkDateValue(new Date(this.checkInValue(this.scrollTo)))},!0),null!==this.angularTag&&this.validationAttribute(this.element,this.inputElement),this.updateHtmlAttributeToElement(),this.checkAttributes(!1),this.l10n=new e.Wo("timepicker",{placeholder:this.placeholder},this.locale),this.setProperties({placeholder:this.placeholder||this.l10n.getConstant("placeholder")},!0),this.initValue=this.checkDateValue(this.value),this.initMin=this.checkDateValue(this.min),this.initMax=this.checkDateValue(this.max),this.isNavigate=this.isPreventBlur=this.isTextSelected=!1,this.activeIndex=this.valueWithMinutes=this.prevDate=null,(0,e.hX)(this.element.getAttribute("id"))?(this.element.id=(0,e.Lz)("ej2_timepicker"),null!==this.angularTag&&(0,e.uK)(this.inputElement,{id:this.element.id+"_input"})):null!==this.angularTag&&(this.inputElement.id=this.element.getAttribute("id")+"_input"),(0,e.hX)(this.inputElement.getAttribute("name"))&&(0,e.uK)(this.inputElement,{name:this.element.id}),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"})},s.prototype.checkTimeFormat=function(){this.formatString=this.format?"string"==typeof this.format?this.format:(0,e.hX)(this.format.skeleton)||""===this.format.skeleton?this.globalize.getDatePattern({type:"time",skeleton:"short"}):this.globalize.getDatePattern({type:"time",skeleton:this.format.skeleton}):null},s.prototype.checkDateValue=function(t){return!(0,e.hX)(t)&&t instanceof Date&&!isNaN(+t)?t:null},s.prototype.createInputElement=function(){this.fullScreenMode&&e.Pw.isDevice&&(this.cssClass+=" e-popup-expand");var t=this.cssClass,i=!this.enableMask;!(0,e.hX)(this.cssClass)&&""!==this.cssClass&&(t=this.cssClass.replace(/\s+/g," ").trim()),this.inputWrapper=v.pd.createInput({element:this.inputElement,bindClearAction:i,floatLabelType:this.floatLabelType,properties:{readonly:this.readonly,placeholder:this.placeholder,cssClass:t,enabled:this.enabled,enableRtl:this.enableRtl,showClearButton:this.showClearButton},buttons:[" e-input-group-icon e-time-icon e-icons"]},this.createElement),this.inputWrapper.container.style.width=this.setWidth(this.width),(0,e.uK)(this.inputElement,{"aria-autocomplete":"list",tabindex:"0","aria-expanded":"false",role:"combobox",autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false","aria-disabled":"false","aria-invalid":"false"}),this.isNullOrEmpty(this.inputStyle)||v.pd.addAttributes({style:this.inputStyle},this.inputElement),(0,e.iQ)([this.inputWrapper.container],"e-time-wrapper")},s.prototype.getCldrDateTimeFormat=function(){var a=new e.DL(this.locale).getDatePattern({skeleton:"yMd"});return this.isNullOrEmpty(this.formatString)?a+" "+this.cldrFormat("time"):this.formatString},s.prototype.checkInvalidValue=function(t){var i=!1;if("object"!=typeof t&&!(0,e.hX)(t)){var a=t;"string"==typeof a&&(a=a.trim());var l,n=null;if("number"==typeof t?a=t.toString():"string"==typeof t&&(/^[a-zA-Z0-9- ]*$/.test(t)||(n=this.setCurrentDate(this.getDateObject(t)),(0,e.hX)(n)&&(n=this.checkDateValue(this.globalize.parseDate(this.getAmPmValue(a),{format:this.getCldrDateTimeFormat(),type:"datetime"})),(0,e.hX)(n)&&(n=this.checkDateValue(this.globalize.parseDate(this.getAmPmValue(a),{format:this.formatString,type:"dateTime",skeleton:"yMd"})))))),l=this.globalize.parseDate(this.getAmPmValue(a),{format:this.getCldrDateTimeFormat(),type:"datetime"}),n=!(0,e.hX)(l)&&l instanceof Date&&!isNaN(+l)?l:null,(0,e.hX)(n)&&a.replace(/\s/g,"").length){var h=null;h=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/,!/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?/.test(a)&&!h.test(a)||/^[a-zA-Z0-9- ]*$/.test(t)||isNaN(+new Date(""+a))?i=!0:n=new Date(""+a)}i?(this.strictMode||(this.invalidValueString=a),this.setProperties({value:null},!0),this.initValue=null):(this.setProperties({value:n},!0),this.initValue=this.value)}},s.prototype.requiredModules=function(){var t=[];return this.enableMask&&t.push({args:[this],member:"MaskedDateTime"}),t},s.prototype.getAmPmValue=function(t){try{return"string"==typeof t&&""!==t.trim()?t.replace(/(am|pm|Am|aM|pM|Pm)/g,function(i){return i.toLocaleUpperCase()}):""}catch(i){return console.error("Error occurred while processing date:",i),""}},s.prototype.cldrFormat=function(t){return"en"===this.locale||"en-US"===this.locale?(0,e._W)("timeFormats.short",(0,e.P)()):this.getCultureTimeObject(e.pE,""+this.locale)},s.prototype.destroy=function(){this.hide(),this.showClearButton&&(this.clearButton=document.getElementsByClassName("e-clear-icon")[0]),this.unBindEvents(),this.inputElement&&(v.pd.removeAttributes({"aria-autocomplete":"list",tabindex:"0","aria-expanded":"false",role:"combobox",autocomplete:"off",autocorrect:"off",autocapitalize:"off",spellcheck:"false","aria-disabled":"true","aria-invalid":"false"},this.inputElement),null===this.angularTag&&this.inputWrapper.container.parentElement.appendChild(this.inputElement),(0,e.hX)(this.cloneElement.getAttribute("tabindex"))?this.inputElement.removeAttribute("tabindex"):this.inputElement.setAttribute("tabindex",this.tabIndex),this.ensureInputAttribute(),this.enableElement([this.inputElement]),this.inputElement.classList.remove("e-input"),(0,e.hX)(this.cloneElement.getAttribute("disabled"))&&v.pd.setEnabled(!0,this.inputElement,this.floatLabelType)),this.inputWrapper.container&&(0,e.Yo)(this.inputWrapper.container),this.inputWrapper=this.popupWrapper=this.cloneElement=void 0,this.liCollections=this.timeCollections=this.disableItemCollection=[],(0,e.hX)(this.rippleFn)||this.rippleFn(),v.pd.destroy({element:this.inputElement,floatLabelType:this.floatLabelType,properties:this.properties},this.clearButton),u.prototype.destroy.call(this),this.formElement&&e.Jm.remove(this.formElement,"reset",this.formResetHandler),this.rippleFn=null,this.openPopupEventArgs=null,this.selectedElement=null,this.listTag=null,this.liCollections=null},s.prototype.ensureInputAttribute=function(){for(var t=[],i=0;i<this.inputElement.attributes.length;i++)t[i]=this.inputElement.attributes[i].name;for(i=0;i<t.length;i++)(0,e.hX)(this.cloneElement.getAttribute(t[i]))?(this.inputElement.removeAttribute(t[i]),"value"===t[i].toLowerCase()&&(this.inputElement.value="")):(this.inputElement.setAttribute(t[i],this.cloneElement.getAttribute(t[i])),"value"===t[i].toLowerCase()&&(this.inputElement.value=this.cloneElement.getAttribute(t[i])))},s.prototype.popupCreation=function(){this.popupWrapper=this.createElement("div",{className:Yt+" e-popup",attrs:{id:this.element.id+"_popup",style:"visibility:hidden"}}),this.popupWrapper.setAttribute("aria-label",this.element.id),this.popupWrapper.setAttribute("role","dialog"),(0,e.hX)(this.cssClass)||(this.popupWrapper.className+=" "+this.cssClass),!(0,e.hX)(this.step)&&this.step>0&&(this.generateList(),(0,e.BC)([this.listWrapper],this.popupWrapper)),this.addSelection(),this.renderPopup(),(0,e.Yo)(this.popupWrapper)},s.prototype.getPopupHeight=function(){var t=parseInt("240px",10),i=this.popupWrapper.getBoundingClientRect().height;return i>t?t:i},s.prototype.generateList=function(){this.createListItems(),this.wireListEvents(),this.rippleFn=(0,e.CF)(this.listWrapper,{duration:300,selector:"."+tt}),this.liCollections=this.listWrapper.querySelectorAll("."+tt)},s.prototype.renderPopup=function(){var t=this;if(this.containerStyle=this.inputWrapper.container.getBoundingClientRect(),this.popupObj=new It.zD(this.popupWrapper,{width:this.setPopupWidth(this.width),zIndex:this.zIndex,targetType:"relative",position:e.Pw.isDevice?{X:"center",Y:"center"}:{X:"left",Y:"bottom"},collision:e.Pw.isDevice?{X:"fit",Y:"fit"}:{X:"flip",Y:"flip"},enableRtl:this.enableRtl,relateTo:e.Pw.isDevice?document.body:this.inputWrapper.container,offsetY:4,open:function(){t.popupWrapper.style.visibility="visible",(0,e.iQ)([t.inputWrapper.buttons[0]],ct)},close:function(){(0,e.vy)([t.inputWrapper.buttons[0]],ct),t.unWireListEvents(),t.inputElement.removeAttribute("aria-activedescendant"),(0,e.TF)(t.popupObj.element),t.popupObj.destroy(),t.popupWrapper.innerHTML="",t.listWrapper=t.popupWrapper=t.listTag=void 0},targetExitViewport:function(){e.Pw.isDevice||t.hide()}}),e.Pw.isDevice||(this.popupObj.collision={X:"none",Y:"flip"}),e.Pw.isDevice&&this.fullScreenMode?(this.popupObj.element.style.maxHeight="100%",this.popupObj.element.style.width="100%"):this.popupObj.element.style.maxHeight="240px",e.Pw.isDevice&&this.fullScreenMode){var i=this.createElement("div",{className:"e-model-header"}),a=this.createElement("span",{className:"e-model-title"});a.textContent="Select time";var n=this.createElement("span",{className:"e-popup-close"});e.Jm.add(n,"mousedown touchstart",this.timePopupCloseHandler,this),i.appendChild(n),i.appendChild(a),this.popupWrapper.insertBefore(i,this.popupWrapper.firstElementChild)}},s.prototype.timePopupCloseHandler=function(t){this.hide()},s.prototype.getFormattedValue=function(t){return(0,e.hX)(this.checkDateValue(t))?null:this.globalize.formatDate(t,{skeleton:"medium",type:"time"})},s.prototype.getDateObject=function(t){if(!this.isNullOrEmpty(t)){var i=this.createDateObj(t),a=!this.isNullOrEmpty(this.initValue);if(this.checkDateValue(i)){var n=a?this.initValue.getDate():Ue,l=a?this.initValue.getMonth():Qe,r=a?this.initValue.getFullYear():$e;return new Date(r,l,n,i.getHours(),i.getMinutes(),i.getSeconds())}}return null},s.prototype.updateHtmlAttributeToWrapper=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];if(ti.indexOf(a)>-1)if("class"===a){var n=this.htmlAttributes[""+a].replace(/\s+/g," ").trim();""!==n&&(0,e.iQ)([this.inputWrapper.container],n.split(" "))}else if("style"===a){var l=this.inputWrapper.container.getAttribute(a);l=(0,e.hX)(l)?this.htmlAttributes[""+a]:l+this.htmlAttributes[""+a],this.inputWrapper.container.setAttribute(a,l)}else this.inputWrapper.container.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateHtmlAttributeToElement=function(){if(!(0,e.hX)(this.htmlAttributes))for(var t=0,i=Object.keys(this.htmlAttributes);t<i.length;t++){var a=i[t];ti.indexOf(a)<0&&this.inputElement.setAttribute(a,this.htmlAttributes[""+a])}},s.prototype.updateCssClass=function(t,i){(0,e.hX)(i)||(i=i.replace(/\s+/g," ").trim()),(0,e.hX)(t)||(t=t.replace(/\s+/g," ").trim()),v.pd.setCssClass(t,[this.inputWrapper.container],i),this.popupWrapper&&v.pd.setCssClass(t,[this.popupWrapper],i)},s.prototype.removeErrorClass=function(){(0,e.vy)([this.inputWrapper.container],Je),(0,e.uK)(this.inputElement,{"aria-invalid":"false"})},s.prototype.checkErrorState=function(t){var i=this.getDateObject(t);this.validateState(i)&&!this.invalidValueString||this.enableMask&&this.inputElement.value===this.maskedDateValue?this.removeErrorClass():((0,e.iQ)([this.inputWrapper.container],Je),(0,e.uK)(this.inputElement,{"aria-invalid":"true"}))},s.prototype.validateInterval=function(){!(0,e.hX)(this.step)&&this.step>0?this.enableElement([this.inputWrapper.buttons[0]]):this.disableTimeIcon()},s.prototype.disableTimeIcon=function(){this.disableElement([this.inputWrapper.buttons[0]]),this.hide()},s.prototype.disableElement=function(t){(0,e.iQ)(t,$)},s.prototype.enableElement=function(t){(0,e.vy)(t,$)},s.prototype.selectInputText=function(){this.inputElement.setSelectionRange(0,this.inputElement.value.length)},s.prototype.setCursorToEnd=function(){this.inputElement.setSelectionRange(this.inputElement.value.length,this.inputElement.value.length)},s.prototype.getMeridianText=function(){return"en"===this.locale||"en-US"===this.locale?(0,e._W)("dayPeriods.format.wide",(0,e.P)()):(0,e._W)("main."+this.locale+".dates.calendars.gregorian.dayPeriods.format.abbreviated",e.pE)},s.prototype.getCursorSelection=function(){var t=this.inputElement,i=0,a=0;return isNaN(t.selectionStart)||(i=t.selectionStart,a=t.selectionEnd),{start:Math.abs(i),end:Math.abs(a)}},s.prototype.getActiveElement=function(){return(0,e.hX)(this.popupWrapper)?null:this.popupWrapper.querySelectorAll("."+ct)},s.prototype.isNullOrEmpty=function(t){return!!((0,e.hX)(t)||"string"==typeof t&&""===t.trim())},s.prototype.setWidth=function(t){return"number"==typeof t?(0,e.IV)(t):"string"==typeof t?t.match(/px|%|em/)?t:(0,e.IV)(t):"100%"},s.prototype.setPopupWidth=function(t){return(t=this.setWidth(t)).indexOf("%")>-1&&(t=(this.containerStyle.width*parseFloat(t)/100).toString()+"px"),t},s.prototype.setScrollPosition=function(){var t=this.selectedElement;(0,e.hX)(t)?this.popupWrapper&&this.checkDateValue(this.scrollTo)&&this.setScrollTo():this.findScrollTop(t)},s.prototype.findScrollTop=function(t){var i=this.getPopupHeight(),a=t.nextElementSibling,n=a?a.offsetTop:t.offsetTop,l=t.getBoundingClientRect().height;this.popupWrapper.scrollTop=n+t.offsetTop>i?a?n-(i/2+l/2):n:0},s.prototype.setScrollTo=function(){var t;if((0,e.hX)(this.popupWrapper))this.popupWrapper.scrollTop=0;else{var i=this.popupWrapper.querySelectorAll("."+tt);if(i.length){var a=this.timeCollections[0],n=this.getDateObject(this.checkDateValue(this.scrollTo)).getTime();t=i[Math.round((n-a)/(6e4*this.step))]}}(0,e.hX)(t)?this.popupWrapper.scrollTop=0:this.findScrollTop(t)},s.prototype.getText=function(){return(0,e.hX)(this.checkDateValue(this.value))?"":this.getValue(this.value)},s.prototype.getValue=function(t){return(0,e.hX)(this.checkDateValue(t))?null:this.globalize.formatDate(t,{format:this.cldrTimeFormat(),type:"time"})},s.prototype.cldrDateFormat=function(){return"en"===this.locale||"en-US"===this.locale?(0,e._W)("dateFormats.short",(0,e.P)()):this.getCultureDateObject(e.pE,""+this.locale)},s.prototype.cldrTimeFormat=function(){return this.isNullOrEmpty(this.formatString)?"en"===this.locale||"en-US"===this.locale?(0,e._W)("timeFormats.short",(0,e.P)()):this.getCultureTimeObject(e.pE,""+this.locale):this.formatString},s.prototype.dateToNumeric=function(){return"en"===this.locale||"en-US"===this.locale?(0,e._W)("timeFormats.medium",(0,e.P)()):(0,e._W)("main."+this.locale+".dates.calendars.gregorian.timeFormats.medium",e.pE)},s.prototype.getExactDateTime=function(t){return(0,e.hX)(this.checkDateValue(t))?null:this.globalize.formatDate(t,{format:this.dateToNumeric(),type:"time"})},s.prototype.setValue=function(t){var i=this.checkValue(t);this.strictMode||this.validateState(i)?this.isNullOrEmpty(i)?(this.initValue=null,this.validateMinMax(this.value,this.min,this.max)):this.initValue=this.compareFormatChange(i):(null===this.checkDateValue(this.valueWithMinutes)&&(this.initValue=this.valueWithMinutes=null),this.validateMinMax(this.value,this.min,this.max)),this.updateInput(!0,this.initValue)},s.prototype.compareFormatChange=function(t){return(0,e.hX)(t)?null:t!==this.getText()?this.getDateObject(t):this.getDateObject(this.value)},s.prototype.updatePlaceHolder=function(){v.pd.setPlaceholder(this.l10n.getConstant("placeholder"),this.inputElement)},s.prototype.updateInputValue=function(t){v.pd.setValue(t,this.inputElement,this.floatLabelType,this.showClearButton)},s.prototype.preventEventBubbling=function(t){t.preventDefault(),this.interopAdaptor.invokeMethodAsync("OnTimeIconClick")},s.prototype.popupHandler=function(t){this.enabled&&(e.Pw.isDevice&&this.inputElement.setAttribute("readonly",""),t.preventDefault(),this.isPopupOpen()?this.closePopup(0,t):(this.inputElement.focus(),this.show(t)))},s.prototype.mouseDownHandler=function(){this.enabled&&(this.readonly||(this.inputElement.setSelectionRange(0,0),e.Jm.add(this.inputElement,"mouseup",this.mouseUpHandler,this)))},s.prototype.mouseUpHandler=function(t){if(!this.readonly){if(t.preventDefault(),this.enableMask)return t.preventDefault(),void this.notify("setMaskSelection",{module:"MaskedDateTime"});e.Jm.remove(this.inputElement,"mouseup",this.mouseUpHandler);var i=this.getCursorSelection();0===i.start&&i.end===this.inputElement.value.length||(this.inputElement.value.length>0&&(this.cursorDetails=this.focusSelection()),this.inputElement.setSelectionRange(this.cursorDetails.start,this.cursorDetails.end))}},s.prototype.focusSelection=function(){var t=new RegExp("^[a-zA-Z0-9]+$"),i=this.inputElement.value.split("");i.push(" ");var a=this.getCursorSelection(),n=0,l=0,r=!1;if(this.isTextSelected)n=a.start,l=a.end,this.isTextSelected=!1;else for(var h=0;h<i.length;h++)if(t.test(i[h])||(l=h,r=!0),r){if(a.start>=n&&a.end<=l){this.isTextSelected=!0;break}n=h+1,r=!1}return{start:n,end:l}},s.prototype.inputHandler=function(t){if(!this.readonly&&this.enabled)switch("right"===t.action||"left"===t.action||"tab"===t.action||("home"===t.action||"end"===t.action||"up"===t.action||"down"===t.action)&&!this.isPopupOpen()&&!this.enableMask||t.preventDefault(),t.action){case"home":case"end":case"up":case"down":this.isPopupOpen()||(this.popupCreation(),this.popupObj.destroy(),this.popupObj=this.popupWrapper=null),this.enableMask&&!this.readonly&&!this.isPopupOpen()&&(t.preventDefault(),this.notify("keyDownHandler",{module:"MaskedDateTime",e:t})),this.isPopupOpen()&&this.keyHandler(t);break;case"enter":this.isNavigate?(this.selectedElement=this.liCollections[this.activeIndex],this.valueWithMinutes=new Date(this.timeCollections[this.activeIndex]),this.updateValue(this.valueWithMinutes,t)):this.updateValue(this.inputElement.value,t),this.hide(),this.isNavigate=!1,this.isPopupOpen()&&t.stopPropagation();break;case"open":this.show(t);break;case"escape":this.updateInputValue(this.objToString(this.value)),this.enableMask&&(this.value||this.updateInputValue(this.maskedDateValue),this.createMask()),this.previousState(this.value),this.hide();break;case"close":this.hide();break;case"right":case"left":case"tab":case"shiftTab":!this.isPopupOpen()&&this.enableMask&&!this.readonly&&((0===this.inputElement.selectionStart&&this.inputElement.selectionEnd===this.inputElement.value.length||this.inputElement.selectionEnd!==this.inputElement.value.length&&"tab"===t.action||0!==this.inputElement.selectionStart&&"shiftTab"===t.action||"left"===t.action||"right"===t.action)&&t.preventDefault(),this.notify("keyDownHandler",{module:"MaskedDateTime",e:t}));break;default:this.isNavigate=!1}},s.prototype.onMouseClick=function(t){var a=this.selectedElement=(0,e.kp)(t.target,"."+tt);this.setSelection(a,t),a&&a.classList.contains(tt)&&this.hide()},s.prototype.closePopup=function(t,i){var a=this;if(this.isPopupOpen()&&this.popupWrapper){var n={popup:this.popupObj,event:i||null,cancel:!1,name:"open"};(0,e.vy)([document.body],_e),this.trigger("close",n,function(l){l.cancel||(a.popupObj.hide(new e.X5({name:"FadeOut",duration:50,delay:t||0})),(0,e.vy)([a.inputWrapper.container],["e-icon-anim"]),(0,e.uK)(a.inputElement,{"aria-expanded":"false"}),a.inputElement.removeAttribute("aria-owns"),a.inputElement.removeAttribute("aria-controls"),a.inputElement.removeAttribute("aria-activedescendant"),e.Jm.remove(document,"mousedown touchstart",a.documentClickHandler)),e.Pw.isDevice&&a.modal&&(a.modal.style.display="none",a.modal.outerHTML="",a.modal=null),e.Pw.isDevice&&((0,e.hX)(a.mobileTimePopupWrap)||(a.mobileTimePopupWrap.remove(),a.mobileTimePopupWrap=null)),e.Pw.isDevice&&a.allowEdit&&!a.readonly&&a.inputElement.removeAttribute("readonly")})}else e.Pw.isDevice&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly")},s.prototype.disposeServerPopup=function(){this.popupWrapper&&(this.popupWrapper.style.visibility="hidden",this.popupWrapper.style.top="-9999px",this.popupWrapper.style.left="-9999px",this.popupWrapper.style.width="0px",this.popupWrapper.style.height="0px")},s.prototype.checkValueChange=function(t,i){if(this.strictMode||this.validateState(this.valueWithMinutes))if(i){var a=this.getDateObject(new Date(this.timeCollections[this.activeIndex]));+this.prevDate!=+a&&this.valueProcess(t,a)}else(this.prevValue!==this.inputElement.value||(0,e.hX)(this.checkDateValue(this.value)))&&this.valueProcess(t,this.compareFormatChange(this.inputElement.value));else null===this.checkDateValue(this.valueWithMinutes)&&(this.initValue=this.valueWithMinutes=null),this.setProperties({value:this.compareFormatChange(this.inputElement.value)},!0),this.initValue=this.valueWithMinutes=this.compareFormatChange(this.inputElement.value),this.prevValue=this.inputElement.value,+this.prevDate!=+this.value&&this.changeEvent(t)},s.prototype.onMouseOver=function(t){var i=(0,e.kp)(t.target,"."+tt);this.setHover(i,ne)},s.prototype.setHover=function(t,i){this.enabled&&this.isValidLI(t)&&!t.classList.contains(i)&&(this.removeHover(i),(0,e.iQ)([t],i),i===re&&t.setAttribute("aria-selected","true"))},s.prototype.setSelection=function(t,i){this.isValidLI(t)&&(this.checkValue(t.getAttribute("data-value")),this.enableMask&&this.createMask(),this.selectedElement=t,this.activeIndex=Array.prototype.slice.call(this.liCollections).indexOf(t),this.valueWithMinutes=new Date(this.timeCollections[this.activeIndex]),(0,e.iQ)([this.selectedElement],ct),this.selectedElement.setAttribute("aria-selected","true"),this.checkValueChange(i,!0))},s.prototype.onMouseLeave=function(){this.removeHover(ne)},s.prototype.scrollHandler=function(){"timepicker"===this.getModuleName()&&e.Pw.isDevice||this.hide()},s.prototype.setMinMax=function(t,i){(0,e.hX)(this.checkDateValue(t))&&(this.initMin=this.getDateObject("12:00:00 AM")),(0,e.hX)(this.checkDateValue(i))&&(this.initMax=this.getDateObject("11:59:59 PM"))},s.prototype.validateMinMax=function(t,i,a){var n=t instanceof Date?t:this.getDateObject(t);return(0,e.hX)(this.checkDateValue(n))?+this.createDateObj(this.getFormattedValue(this.initMin))>+this.createDateObj(this.getFormattedValue(this.initMax))&&this.disableTimeIcon():t=this.strictOperation(this.initMin,this.initMax,t,n),this.strictMode&&(t=this.valueIsDisable(t)?t:null),this.checkErrorState(t),t},s.prototype.valueIsDisable=function(t){if(!(0,e.hX)(this.disableItemCollection)&&this.disableItemCollection.length>0){if(this.disableItemCollection.length===this.timeCollections.length)return!1;for(var i=t instanceof Date?this.objToString(t):t,a=0;a<this.disableItemCollection.length;a++)if(i===this.disableItemCollection[a])return!1}return!0},s.prototype.validateState=function(t){if(!this.strictMode){if(!this.valueIsDisable(t))return!1;var i=this.setCurrentDate(this.getDateObject(t)),a=this.setCurrentDate(this.getDateObject(this.initMax)),n=this.setCurrentDate(this.getDateObject(this.initMin));if((0,e.hX)(this.checkDateValue(i))){if(+a<+n||""!==this.inputElement.value)return!1}else if(+i>+a||+i<+n)return!1}return!0},s.prototype.strictOperation=function(t,i,a,n){var l=this.createDateObj(this.getFormattedValue(i)),r=this.createDateObj(this.getFormattedValue(t)),h=this.createDateObj(this.getFormattedValue(n));if(this.strictMode){if(+r>+l)return this.disableTimeIcon(),this.initValue=this.getDateObject(l),this.updateInputValue(this.getValue(this.initValue)),this.enableMask&&this.createMask(),this.inputElement.value;if(+r>=+h)return this.getDateObject(r);if(+h>=+l||+r==+l)return this.getDateObject(l)}else if(+r>+l&&(this.disableTimeIcon(),!isNaN(+this.createDateObj(a))))return a;return a},s.prototype.bindEvents=function(){e.Jm.add(this.inputWrapper.buttons[0],"mousedown",this.popupHandler,this),e.Jm.add(this.inputElement,"blur",this.inputBlurHandler,this),e.Jm.add(this.inputElement,"focus",this.inputFocusHandler,this),e.Jm.add(this.inputElement,"change",this.inputChangeHandler,this),e.Jm.add(this.inputElement,"input",this.inputEventHandler,this),this.enableMask&&e.Jm.add(this.inputElement,"keydown",this.keydownHandler,this),this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown",this.clearHandler,this),this.formElement&&e.Jm.add(this.formElement,"reset",this.formResetHandler,this),e.Pw.isDevice||(this.keyConfigure=(0,e.X$)(this.keyConfigure,this.keyConfigs),this.inputEvent=new e.j9(this.inputWrapper.container,{keyAction:this.inputHandler.bind(this),keyConfigs:this.keyConfigure,eventName:"keydown"}),this.showClearButton&&this.inputElement&&e.Jm.add(this.inputElement,"mousedown",this.mouseDownHandler,this))},s.prototype.keydownHandler=function(t){"Delete"===t.code&&this.enableMask&&!this.popupObj&&!this.readonly&&this.notify("keyDownHandler",{module:"MaskedDateTime",e:t})},s.prototype.formResetHandler=function(){if(this.enabled&&!this.inputElement.disabled){var t=this.inputElement.getAttribute("value"),i=this.checkDateValue(this.inputEleValue);"EJS-TIMEPICKER"===this.element.tagName&&(i=null,t="",this.inputElement.setAttribute("value","")),this.setProperties({value:i},!0),this.prevDate=this.value,this.valueWithMinutes=this.value,this.initValue=this.value,this.inputElement&&(this.updateInputValue(t),this.enableMask&&(t||this.updateInputValue(this.maskedDateValue),this.createMask()),this.checkErrorState(t),this.prevValue=this.inputElement.value)}},s.prototype.inputChangeHandler=function(t){this.enabled&&t.stopPropagation()},s.prototype.inputEventHandler=function(){this.enableMask&&this.notify("inputHandler",{module:"MaskedDateTime"})},s.prototype.unBindEvents=function(){this.inputWrapper&&e.Jm.remove(this.inputWrapper.buttons[0],"mousedown touchstart",this.popupHandler),e.Jm.remove(this.inputElement,"blur",this.inputBlurHandler),e.Jm.remove(this.inputElement,"focus",this.inputFocusHandler),e.Jm.remove(this.inputElement,"change",this.inputChangeHandler),e.Jm.remove(this.inputElement,"input",this.inputEventHandler),this.inputEvent&&this.inputEvent.destroy(),e.Jm.remove(this.inputElement,"mousedown touchstart",this.mouseDownHandler),this.showClearButton&&!(0,e.hX)(this.inputWrapper)&&!(0,e.hX)(this.inputWrapper.clearButton)&&e.Jm.remove(this.inputWrapper.clearButton,"mousedown touchstart",this.clearHandler),this.formElement&&e.Jm.remove(this.formElement,"reset",this.formResetHandler)},s.prototype.bindClearEvent=function(){this.showClearButton&&this.inputWrapper.clearButton&&e.Jm.add(this.inputWrapper.clearButton,"mousedown",this.clearHandler,this)},s.prototype.raiseClearedEvent=function(t){this.trigger("cleared",{event:t})},s.prototype.clearHandler=function(t){if(this.enabled&&(t.preventDefault(),(0,e.hX)(this.value)?(this.resetState(),this.raiseClearedEvent(t)):this.clear(t),this.popupWrapper&&(this.popupWrapper.scrollTop=0),this.enableMask&&this.notify("clearHandler",{module:"MaskedDateTime"}),(0,e.kp)(this.element,"form"))){var i=this.element,a=document.createEvent("KeyboardEvent");a.initEvent("keyup",!1,!0),i.dispatchEvent(a)}},s.prototype.clear=function(t){this.setProperties({value:null},!0),this.initValue=null,this.resetState(),this.raiseClearedEvent(t),this.changeEvent(t)},s.prototype.setZIndex=function(){this.popupObj&&(this.popupObj.zIndex=this.zIndex,this.popupObj.dataBind())},s.prototype.checkAttributes=function(t){for(var a,n=0,l=t?(0,e.hX)(this.htmlAttributes)?[]:Object.keys(this.htmlAttributes):["step","disabled","readonly","style","name","value","min","max","placeholder"];n<l.length;n++){var r=l[n];if(!(0,e.hX)(this.inputElement.getAttribute(r)))switch(r){case"disabled":if((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.enabled||t){var h=!("disabled"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r));this.setProperties({enabled:h},!t)}break;case"style":this.inputStyle=this.inputElement.getAttribute(r);break;case"readonly":if((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.readonly||t){var o="readonly"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r);this.setProperties({readonly:o},!t)}break;case"name":this.inputElement.setAttribute("name",this.inputElement.getAttribute(r));break;case"step":this.step=parseInt(this.inputElement.getAttribute(r),10);break;case"placeholder":((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.placeholder||t)&&this.setProperties({placeholder:this.inputElement.getAttribute(r)},!t);break;case"min":((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.min||t)&&(a=new Date(this.inputElement.getAttribute(r)),(0,e.hX)(this.checkDateValue(a))||this.setProperties({min:a},!t));break;case"max":((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.max||t)&&(a=new Date(this.inputElement.getAttribute(r)),(0,e.hX)(this.checkDateValue(a))||this.setProperties({max:a},!t));break;case"value":((0,e.hX)(this.timeOptions)||void 0===this.timeOptions.value||t)&&(a=new Date(this.inputElement.getAttribute(r)),(0,e.hX)(this.checkDateValue(a))||(this.initValue=a,this.updateInput(!1,this.initValue),this.setProperties({value:a},!t)))}}},s.prototype.setCurrentDate=function(t){return(0,e.hX)(this.checkDateValue(t))?null:new Date($e,Qe,Ue,t.getHours(),t.getMinutes(),t.getSeconds())},s.prototype.getTextFormat=function(){var t=0;if("a"===this.cldrTimeFormat().split(" ")[0]||0===this.cldrTimeFormat().indexOf("a"))t=1;else if(this.cldrTimeFormat().indexOf("a")<0)for(var i=this.cldrTimeFormat().split(" "),a=0;a<i.length;a++)if(i[a].toLowerCase().indexOf("h")>=0){t=a;break}return t},s.prototype.updateValue=function(t,i){var a;if(this.isNullOrEmpty(t))this.resetState();else if(a=this.checkValue(t),this.strictMode){var n=null===a&&t.trim().length>0?this.previousState(this.prevDate):this.inputElement.value;this.updateInputValue(n),this.enableMask&&(n||this.updateInputValue(this.maskedDateValue),(0,e.hX)(a)&&t!==this.maskedDateValue&&this.createMask(),(0,e.hX)(a)&&t===this.maskedDateValue&&this.updateInputValue(this.maskedDateValue))}this.checkValueChange(i,"string"!=typeof t)},s.prototype.previousState=function(t){for(var i=this.getDateObject(t),a=0;a<this.timeCollections.length;a++)if(+i===this.timeCollections[a]){this.activeIndex=a,this.selectedElement=this.liCollections[a],this.valueWithMinutes=new Date(this.timeCollections[a]);break}return this.getValue(t)},s.prototype.resetState=function(){this.removeSelection(),v.pd.setValue("",this.inputElement,this.floatLabelType,!1),this.valueWithMinutes=this.activeIndex=null,this.strictMode||this.checkErrorState(null)},s.prototype.objToString=function(t){return(0,e.hX)(this.checkDateValue(t))?null:this.globalize.formatDate(t,{format:this.cldrTimeFormat(),type:"time"})},s.prototype.checkValue=function(t){if(!this.isNullOrEmpty(t)){var i=t instanceof Date?t:this.getDateObject(t);return this.validateValue(i,t)}return this.resetState(),this.valueWithMinutes=null},s.prototype.validateValue=function(t,i){var a,n=this.validateMinMax(i,this.min,this.max),l=this.getDateObject(n);if(this.getFormattedValue(l)!==this.getFormattedValue(this.value)?(this.valueWithMinutes=(0,e.hX)(l)?null:l,a=this.objToString(this.valueWithMinutes)):(this.strictMode&&(t=l),this.valueWithMinutes=this.checkDateValue(t),a=this.objToString(this.valueWithMinutes)),!this.strictMode&&(0,e.hX)(a)){var r=n.trim().length>0?n:"";this.updateInputValue(r),this.enableMask&&(r||this.updateInputValue(this.maskedDateValue))}else this.updateInputValue(a),this.enableMask&&(""===a&&this.updateInputValue(this.maskedDateValue),(0,e.hX)(a)&&i!==this.maskedDateValue&&this.createMask(),(0,e.hX)(a)&&i===this.maskedDateValue&&this.updateInputValue(this.maskedDateValue));return a},s.prototype.createMask=function(){this.notify("createMask",{module:"MaskedDateTime"})},s.prototype.findNextElement=function(t){var i=this.inputElement.value,a=(0,e.hX)(this.valueWithMinutes)?this.createDateObj(i):this.getDateObject(this.valueWithMinutes),n=null,l=this.liCollections.length,r=this.timeCollections;if((0,e.hX)(this.checkDateValue(a))&&(0,e.hX)(this.activeIndex))this.selectNextItem(t);else{if("home"===t.action){var h=this.validLiElement(0);n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h}else if("end"===t.action)h=this.validLiElement(r.length-1,!0),n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h;else if("down"===t.action)for(var o=0;o<l;o++){if(+a<this.timeCollections[o]){h=this.validLiElement(o),n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h;break}if(o===l-1){h=this.validLiElement(0),n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h;break}}else for(o=l-1;o>=0;o--){if(+a>this.timeCollections[o]){h=this.validLiElement(o,!0),n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h;break}if(0===o){h=this.validLiElement(l-1),n=+this.createDateObj(new Date(this.timeCollections[h])),this.activeIndex=h;break}}this.selectedElement=this.liCollections[this.activeIndex],this.elementValue((0,e.hX)(n)?null:new Date(n))}},s.prototype.selectNextItem=function(t){var i=this.validLiElement(0,"down"!==t.action);this.activeIndex=i,this.selectedElement=this.liCollections[i],this.elementValue(new Date(this.timeCollections[i]))},s.prototype.elementValue=function(t){(0,e.hX)(this.checkDateValue(t))||this.checkValue(t)},s.prototype.validLiElement=function(t,i){var a=null,n=(0,e.hX)(this.popupWrapper)?this.liCollections:this.popupWrapper.querySelectorAll("."+tt),l=!0;if(n.length)if(i)for(var r=t;r>=0;r--){if(!n[r].classList.contains($)){a=r;break}0===r&&l&&(t=r=n.length,l=!1)}else for(r=t;r<=n.length-1;r++){if(!n[r].classList.contains($)){a=r;break}r===n.length-1&&l&&(t=r=-1,l=!1)}return a},s.prototype.keyHandler=function(t){if(!((0,e.hX)(this.step)||this.step<=0||!(0,e.hX)(this.inputWrapper)&&this.inputWrapper.buttons[0].classList.contains($))){var i=this.timeCollections.length;if((0,e.hX)(this.getActiveElement())||0===this.getActiveElement().length)if(this.liCollections.length>0)if((0,e.hX)(this.value)&&(0,e.hX)(this.activeIndex)){var a=this.validLiElement(0,"down"!==t.action);this.activeIndex=a,this.selectedElement=this.liCollections[a],this.elementValue(new Date(this.timeCollections[a]))}else this.findNextElement(t);else this.findNextElement(t);else{var n=void 0;t.keyCode>=37&&t.keyCode<=40?(a=40===t.keyCode||39===t.keyCode?++this.activeIndex:--this.activeIndex,this.activeIndex=a=this.activeIndex===i?0:this.activeIndex,this.activeIndex=a=this.activeIndex<0?i-1:this.activeIndex,this.activeIndex=a=this.validLiElement(this.activeIndex,!(40===t.keyCode||39===t.keyCode)),n=(0,e.hX)(this.timeCollections[a])?this.timeCollections[0]:this.timeCollections[a]):"home"===t.action?(a=this.validLiElement(0),this.activeIndex=a,n=this.timeCollections[a]):"end"===t.action&&(a=this.validLiElement(i-1,!0),this.activeIndex=a,n=this.timeCollections[a]),this.selectedElement=this.liCollections[this.activeIndex],this.elementValue(new Date(n))}this.isNavigate=!0,this.setHover(this.selectedElement,re),this.setActiveDescendant(),this.enableMask&&this.selectInputText(),this.isPopupOpen()&&null!==this.selectedElement&&(!t||"click"!==t.type)&&this.setScrollPosition()}},s.prototype.getCultureTimeObject=function(t,i){return(0,e._W)("main."+i+".dates.calendars.gregorian.timeFormats.short",t)},s.prototype.getCultureDateObject=function(t,i){return(0,e._W)("main."+i+".dates.calendars.gregorian.dateFormats.short",t)},s.prototype.wireListEvents=function(){e.Jm.add(this.listWrapper,"click",this.onMouseClick,this),e.Pw.isDevice||(e.Jm.add(this.listWrapper,"mouseover",this.onMouseOver,this),e.Jm.add(this.listWrapper,"mouseout",this.onMouseLeave,this))},s.prototype.unWireListEvents=function(){this.listWrapper&&(e.Jm.remove(this.listWrapper,"click",this.onMouseClick),e.Pw.isDevice||(e.Jm.remove(this.listWrapper,"mouseover",this.onMouseOver),e.Jm.remove(this.listWrapper,"mouseout",this.onMouseLeave)))},s.prototype.valueProcess=function(t,i){var a=(0,e.hX)(this.checkDateValue(i))?null:i;+this.prevDate!=+a&&(this.initValue=a,this.changeEvent(t))},s.prototype.changeEvent=function(t){this.addSelection(),this.updateInput(!0,this.initValue);var i={event:t||null,value:this.value,text:this.inputElement.value,isInteracted:!(0,e.hX)(t),element:this.element};i.value=this.valueWithMinutes||this.getDateObject(this.inputElement.value),this.prevDate=this.valueWithMinutes||this.getDateObject(this.inputElement.value),this.isAngular&&this.preventChange?this.preventChange=!1:this.trigger("change",i),this.invalidValueString=null,this.checkErrorState(this.value)},s.prototype.updateInput=function(t,i){t&&(this.prevValue=this.getValue(this.prevDate)),this.prevDate=this.valueWithMinutes=i,("number"!=typeof i||(this.value&&+new Date(+this.value).setMilliseconds(0))!==+i)&&(this.setProperties({value:i},!0),this.enableMask&&this.value&&this.createMask()),!this.strictMode&&(0,e.hX)(this.value)&&this.invalidValueString&&(this.checkErrorState(this.invalidValueString),this.updateInputValue(this.invalidValueString)),this.clearIconState()},s.prototype.setActiveDescendant=function(){!(0,e.hX)(this.selectedElement)&&this.value?(0,e.uK)(this.inputElement,{"aria-activedescendant":this.selectedElement.getAttribute("id")}):this.inputElement.removeAttribute("aria-activedescendant")},s.prototype.removeSelection=function(){if(this.removeHover(ne),!(0,e.hX)(this.popupWrapper)){var t=this.popupWrapper.querySelectorAll("."+ct);t.length&&((0,e.vy)(t,ct),t[0].removeAttribute("aria-selected"))}},s.prototype.removeHover=function(t){var i=this.getHoverItem(t);i&&i.length&&((0,e.vy)(i,t),t===re&&i[0].removeAttribute("aria-selected"))},s.prototype.getHoverItem=function(t){var i;return(0,e.hX)(this.popupWrapper)||(i=this.popupWrapper.querySelectorAll("."+t)),i},s.prototype.setActiveClass=function(){if(!(0,e.hX)(this.popupWrapper)){var t=this.popupWrapper.querySelectorAll("."+tt);if(t.length)for(var i=0;i<t.length;i++)if(this.timeCollections[i]===+this.getDateObject(this.valueWithMinutes)){t[i].setAttribute("aria-selected","true"),this.selectedElement=t[i],this.activeIndex=i;break}}},s.prototype.addSelection=function(){this.selectedElement=null,this.removeSelection(),this.setActiveClass(),(0,e.hX)(this.selectedElement)||((0,e.iQ)([this.selectedElement],ct),this.selectedElement.setAttribute("aria-selected","true"))},s.prototype.isValidLI=function(t){return t&&t.classList.contains(tt)&&!t.classList.contains($)},s.prototype.createDateObj=function(t){var a=this.globalize.formatDate(new Date,{format:null,skeleton:"short",type:"date"}),n=null;return"string"==typeof t?t.toUpperCase().indexOf("AM")>-1||t.toUpperCase().indexOf("PM")>-1?(a=this.defaultCulture.formatDate(new Date,{format:null,skeleton:"short",type:"date"}),n=isNaN(+new Date(a+" "+t))?null:new Date(new Date(a+" "+t).setMilliseconds(0)),(0,e.hX)(n)&&(n=this.timeParse(a,t))):n=this.timeParse(a,t):t instanceof Date&&(n=t),n},s.prototype.timeParse=function(t,i){var a;return a=this.globalize.parseDate(this.getAmPmValue(t+" "+i),{format:this.cldrDateFormat()+" "+this.cldrTimeFormat(),type:"datetime"}),a=(0,e.hX)(a)?this.globalize.parseDate(this.getAmPmValue(t+" "+i),{format:this.cldrDateFormat()+" "+this.dateToNumeric(),type:"datetime"}):a,(0,e.hX)(a)?a:new Date(a.setMilliseconds(0))},s.prototype.createListItems=function(){var t=this;this.listWrapper=this.createElement("div",{className:"e-content",attrs:{tabindex:"-1"}});var i,a=6e4*this.step,n=[];this.timeCollections=[],this.disableItemCollection=[],i=+this.getDateObject(this.initMin).setMilliseconds(0);for(var l=+this.getDateObject(this.initMax).setMilliseconds(0);l>=i;)this.timeCollections.push(i),n.push(this.globalize.formatDate(new Date(i),{format:this.cldrTimeFormat(),type:"time"})),i+=a;this.listTag=qt.iY.createList(this.createElement,n,{itemCreated:function(h){var o={element:h.item,text:h.text,value:t.getDateObject(h.text),isDisabled:!1};t.trigger("itemRender",o,function(p){p.isDisabled&&p.element.classList.add($),p.element.classList.contains($)&&t.disableItemCollection.push(p.element.getAttribute("data-value"))})}},!0),(0,e.uK)(this.listTag,{role:"listbox","aria-hidden":"false",id:this.element.id+"_options",tabindex:"0"}),(0,e.BC)([this.listTag],this.listWrapper)},s.prototype.documentClickHandler=function(t){var i=t.target;!(0,e.hX)(this.popupObj)&&!(0,e.hX)(this.inputWrapper)&&(this.inputWrapper.container.contains(i)&&"mousedown"!==t.type||this.popupObj.element&&this.popupObj.element.contains(i))&&"touchstart"!==t.type&&t.preventDefault(),(0,e.kp)(i,'[id="'+this.popupObj.element.id+'"]')||i===this.inputElement||i===(this.inputWrapper&&this.inputWrapper.buttons[0])||i===(this.inputWrapper&&this.inputWrapper.clearButton)||i===(this.inputWrapper&&this.inputWrapper.container)||i.classList.contains("e-dlg-overlay")?i!==this.inputElement&&(e.Pw.isDevice||(this.isPreventBlur=(e.Pw.isIE||"edge"===e.Pw.info.name)&&document.activeElement===this.inputElement&&i===this.popupWrapper)):this.isPopupOpen()&&(this.hide(),this.focusOut())},s.prototype.setEnableRtl=function(){v.pd.setEnableRtl(this.enableRtl,[this.inputWrapper.container]),this.popupObj&&(this.popupObj.enableRtl=this.enableRtl,this.popupObj.dataBind())},s.prototype.setEnable=function(){v.pd.setEnabled(this.enabled,this.inputElement,this.floatLabelType),this.enabled?((0,e.vy)([this.inputWrapper.container],$),(0,e.uK)(this.inputElement,{"aria-disabled":"false"}),this.inputElement.setAttribute("tabindex",this.tabIndex)):(this.hide(),(0,e.iQ)([this.inputWrapper.container],$),(0,e.uK)(this.inputElement,{"aria-disabled":"true"}),this.inputElement.tabIndex=-1)},s.prototype.getProperty=function(t,i){"min"===i?(this.initMin=this.checkDateValue(new Date(this.checkInValue(t.min))),this.setProperties({min:this.initMin},!0)):(this.initMax=this.checkDateValue(new Date(this.checkInValue(t.max))),this.setProperties({max:this.initMax},!0)),""===this.inputElement.value?this.validateMinMax(this.value,this.min,this.max):this.checkValue(this.inputElement.value),this.checkValueChange(null,!1)},s.prototype.inputBlurHandler=function(t){if(this.enabled){if(this.isPreventBlur&&this.isPopupOpen())return void this.inputElement.focus();this.closePopup(0,t),this.enableMask&&this.maskedDateValue&&this.placeholder&&"Always"!==this.floatLabelType&&this.inputElement.value===this.maskedDateValue&&!this.value&&("Auto"===this.floatLabelType||"Never"===this.floatLabelType||this.placeholder)&&this.updateInputValue(""),(0,e.vy)([this.inputWrapper.container],[le]),this.getText()!==this.inputElement.value?this.updateValue(this.inputElement.value,t):0===this.inputElement.value.trim().length&&this.resetState(),this.cursorDetails=null,this.isNavigate=!1,""===this.inputElement.value&&(this.invalidValueString=null),this.trigger("blur",{model:this})}},s.prototype.focusOut=function(){document.activeElement===this.inputElement&&(this.inputElement.blur(),(0,e.vy)([this.inputWrapper.container],[le]),this.trigger("blur",{model:this}))},s.prototype.isPopupOpen=function(){return!(!this.popupWrapper||!this.popupWrapper.classList.contains(""+Yt))},s.prototype.inputFocusHandler=function(){if(this.enabled){var t={model:this};!this.readonly&&!e.Pw.isDevice&&!this.enableMask&&this.selectInputText(),this.enableMask&&!this.inputElement.value&&this.placeholder&&this.maskedDateValue&&!this.value&&("Auto"===this.floatLabelType||"Never"===this.floatLabelType||this.placeholder)&&(this.updateInputValue(this.maskedDateValue),this.inputElement.selectionStart=0,this.inputElement.selectionEnd=this.inputElement.value.length),this.trigger("focus",t),this.clearIconState(),this.openOnFocus&&this.show()}},s.prototype.focusIn=function(){document.activeElement!==this.inputElement&&this.enabled&&this.inputElement.focus()},s.prototype.hide=function(){this.closePopup(100,null),this.clearIconState()},s.prototype.show=function(t){var i=this;if(!(this.enabled&&this.readonly||!this.enabled||this.popupWrapper)&&(this.popupCreation(),e.Pw.isDevice&&this.listWrapper&&(this.modal=this.createElement("div"),this.modal.className=Yt+" e-time-modal",document.body.className+=" "+_e,document.body.appendChild(this.modal)),e.Pw.isDevice&&(this.mobileTimePopupWrap=this.createElement("div",{className:"e-timepicker-mob-popup-wrap"}),document.body.appendChild(this.mobileTimePopupWrap)),this.openPopupEventArgs={popup:this.popupObj||null,cancel:!1,event:t||null,name:"open",appendTo:e.Pw.isDevice?this.mobileTimePopupWrap:document.body},this.trigger("open",this.openPopupEventArgs,function(l){if(i.openPopupEventArgs=l,i.openPopupEventArgs.cancel||i.inputWrapper.buttons[0].classList.contains($))i.popupObj.destroy(),i.popupWrapper=i.listTag=void 0,i.liCollections=i.timeCollections=i.disableItemCollection=[],i.popupObj=null;else{i.openPopupEventArgs.appendTo.appendChild(i.popupWrapper),i.popupAlignment(i.openPopupEventArgs),i.setScrollPosition(),e.Pw.isDevice||i.inputElement.focus();i.popupObj.refreshPosition(i.anchor),i.popupObj.show(new e.X5({name:"FadeIn",duration:50}),1e3===i.zIndex?i.element:null),i.setActiveDescendant(),(0,e.uK)(i.inputElement,{"aria-expanded":"true"}),(0,e.uK)(i.inputElement,{"aria-owns":i.inputElement.id+"_options"}),(0,e.uK)(i.inputElement,{"aria-controls":i.inputElement.id}),(0,e.iQ)([i.inputWrapper.container],le),e.Jm.add(document,"mousedown touchstart",i.documentClickHandler,i),i.setOverlayIndex(i.mobileTimePopupWrap,i.popupObj.element,i.modal,e.Pw.isDevice)}}),e.Pw.isDevice)){var n=this.createElement("div",{className:"e-dlg-overlay"});n.style.zIndex=(this.zIndex-1).toString(),this.mobileTimePopupWrap.appendChild(n)}},s.prototype.setOverlayIndex=function(t,i,a,n){if(n&&!(0,e.hX)(i)&&!(0,e.hX)(a)&&!(0,e.hX)(t)){var l=parseInt(i.style.zIndex,10)?parseInt(i.style.zIndex,10):1e3;a.style.zIndex=(l-1).toString(),t.style.zIndex=l.toString()}},s.prototype.formatValues=function(t){var i;return"number"==typeof t?i=(0,e.IV)(t):"string"==typeof t&&(i=t.match(/px|%|em/)||isNaN(parseInt(t,10))?t:(0,e.IV)(t)),i},s.prototype.popupAlignment=function(t){if(t.popup.position.X=this.formatValues(t.popup.position.X),t.popup.position.Y=this.formatValues(t.popup.position.Y),(!isNaN(parseFloat(t.popup.position.X))||!isNaN(parseFloat(t.popup.position.Y)))&&(this.popupObj.relateTo=this.anchor=document.body,this.popupObj.targetType="container"),isNaN(parseFloat(t.popup.position.X))||(this.popupObj.offsetX=parseFloat(t.popup.position.X)),isNaN(parseFloat(t.popup.position.Y))||(this.popupObj.offsetY=parseFloat(t.popup.position.Y)),e.Pw.isDevice)"center"===t.popup.position.X&&"center"===t.popup.position.Y&&(this.popupObj.relateTo=this.anchor=document.body,this.popupObj.offsetY=0,this.popupObj.targetType="container",this.popupObj.collision={X:"fit",Y:"fit"});else{switch(t.popup.position.X){case"left":break;case"right":t.popup.offsetX=this.containerStyle.width;break;case"center":t.popup.offsetX=-this.containerStyle.width/2}switch(t.popup.position.Y){case"top":case"bottom":break;case"center":t.popup.offsetY=-this.containerStyle.height/2}"center"===t.popup.position.X&&"center"===t.popup.position.Y&&(this.popupObj.relateTo=this.inputWrapper.container,this.anchor=this.inputElement,this.popupObj.targetType="relative")}},s.prototype.getPersistData=function(){return this.addOnPersist(["value"])},s.prototype.getModuleName=function(){return"timepicker"},s.prototype.onPropertyChanged=function(t,i){for(var a=0,n=Object.keys(t);a<n.length;a++){var l=n[a];switch(l){case"placeholder":v.pd.setPlaceholder(t.placeholder,this.inputElement);break;case"readonly":v.pd.setReadonly(this.readonly,this.inputElement,this.floatLabelType),this.readonly&&this.hide(),this.setTimeAllowEdit();break;case"enabled":this.setProperties({enabled:t.enabled},!0),this.setEnable();break;case"allowEdit":this.setTimeAllowEdit();break;case"enableRtl":this.setProperties({enableRtl:t.enableRtl},!0),this.setEnableRtl();break;case"cssClass":this.updateCssClass(t.cssClass,i.cssClass);break;case"zIndex":this.setProperties({zIndex:t.zIndex},!0),this.setZIndex();break;case"htmlAttributes":this.updateHtmlAttributeToElement(),this.updateHtmlAttributeToWrapper(),this.checkAttributes(!0);break;case"min":case"max":this.getProperty(t,l);break;case"showClearButton":v.pd.setClearButton(this.showClearButton,this.inputElement,this.inputWrapper),this.bindClearEvent();break;case"locale":this.setProperties({locale:t.locale},!0),this.globalize=new e.DL(this.locale),this.l10n.setLocale(this.locale),this.timeOptions&&null==this.timeOptions.placeholder&&this.updatePlaceHolder(),this.setValue(this.value),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"});break;case"width":(0,e.sN)(this.inputWrapper.container,{width:this.setWidth(t.width)}),this.containerStyle=this.inputWrapper.container.getBoundingClientRect(),v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"format":this.setProperties({format:t.format},!0),this.checkTimeFormat(),this.setValue(this.value),this.enableMask&&(this.createMask(),this.value||this.updateInputValue(this.maskedDateValue));break;case"value":this.invalidValueString=null,this.checkInvalidValue(t.value),t.value=this.value,this.invalidValueString?(this.updateInputValue(this.invalidValueString),this.checkErrorState(this.invalidValueString)):("string"==typeof t.value?(this.setProperties({value:this.checkDateValue(new Date(t.value))},!0),t.value=this.value):(t.value&&+new Date(+t.value).setMilliseconds(0))!==+this.value&&(t.value=this.checkDateValue(new Date(""+t.value))),this.initValue=t.value,t.value=this.compareFormatChange(this.checkValue(t.value))),this.enableMask&&(0,e.hX)(t.value)&&(this.updateInputValue(this.maskedDateValue),this.checkErrorState(this.maskedDateValue)),this.checkValueChange(null,!1),this.isPopupOpen()&&this.setScrollPosition(),this.isAngular&&this.preventChange&&(this.preventChange=!1),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"});break;case"floatLabelType":this.floatLabelType=t.floatLabelType,v.pd.removeFloating(this.inputWrapper),v.pd.addFloating(this.inputElement,this.floatLabelType,this.placeholder),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-icon");break;case"strictMode":this.invalidValueString=null,t.strictMode&&this.checkErrorState(null),this.setProperties({strictMode:t.strictMode},!0),this.checkValue(this.inputElement.value),this.checkValueChange(null,!1);break;case"scrollTo":this.checkDateValue(new Date(this.checkInValue(t.scrollTo)))?(this.popupWrapper&&this.setScrollTo(),this.setProperties({scrollTo:this.checkDateValue(new Date(this.checkInValue(t.scrollTo)))},!0)):this.setProperties({scrollTo:null},!0);break;case"enableMask":this.enableMask?(this.notify("createMask",{module:"MaskedDateTime"}),this.updateInputValue(this.maskedDateValue)):this.inputElement.value===this.maskedDateValue&&this.updateInputValue("")}}},s.prototype.checkInValue=function(t){return t instanceof Date?t.toUTCString():""+t},E([(0,e.mA)(null)],s.prototype,"width",void 0),E([(0,e.mA)(null)],s.prototype,"cssClass",void 0),E([(0,e.mA)(!1)],s.prototype,"strictMode",void 0),E([(0,e.mA)(null)],s.prototype,"keyConfigs",void 0),E([(0,e.mA)(null)],s.prototype,"format",void 0),E([(0,e.mA)(!0)],s.prototype,"enabled",void 0),E([(0,e.mA)(!1)],s.prototype,"fullScreenMode",void 0),E([(0,e.mA)(!1)],s.prototype,"readonly",void 0),E([(0,e.mA)({})],s.prototype,"htmlAttributes",void 0),E([(0,e.mA)("Never")],s.prototype,"floatLabelType",void 0),E([(0,e.mA)(null)],s.prototype,"placeholder",void 0),E([(0,e.mA)(1e3)],s.prototype,"zIndex",void 0),E([(0,e.mA)(!1)],s.prototype,"enablePersistence",void 0),E([(0,e.mA)(!0)],s.prototype,"showClearButton",void 0),E([(0,e.mA)(30)],s.prototype,"step",void 0),E([(0,e.mA)(null)],s.prototype,"scrollTo",void 0),E([(0,e.mA)(null)],s.prototype,"value",void 0),E([(0,e.mA)(null)],s.prototype,"min",void 0),E([(0,e.mA)(null)],s.prototype,"max",void 0),E([(0,e.mA)(!0)],s.prototype,"allowEdit",void 0),E([(0,e.mA)(!1)],s.prototype,"openOnFocus",void 0),E([(0,e.mA)(!1)],s.prototype,"enableMask",void 0),E([(0,e.mA)({day:"day",month:"month",year:"year",hour:"hour",minute:"minute",second:"second",dayOfTheWeek:"day of the week"})],s.prototype,"maskPlaceholder",void 0),E([(0,e.mA)(null)],s.prototype,"serverTimezoneOffset",void 0),E([(0,e.Jh)()],s.prototype,"change",void 0),E([(0,e.Jh)()],s.prototype,"created",void 0),E([(0,e.Jh)()],s.prototype,"destroyed",void 0),E([(0,e.Jh)()],s.prototype,"open",void 0),E([(0,e.Jh)()],s.prototype,"itemRender",void 0),E([(0,e.Jh)()],s.prototype,"close",void 0),E([(0,e.Jh)()],s.prototype,"cleared",void 0),E([(0,e.Jh)()],s.prototype,"blur",void 0),E([(0,e.Jh)()],s.prototype,"focus",void 0),E([e.kc],s)}(e.uA),Os=function(){var u=function(s,t){return(u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var n in a)a.hasOwnProperty(n)&&(i[n]=a[n])})(s,t)};return function(s,t){function i(){this.constructor=s}u(s,t),s.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}}(),C=function(u,s,t,i){var l,a=arguments.length,n=a<3?s:null===i?i=Object.getOwnPropertyDescriptor(s,t):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(u,s,t,i);else for(var r=u.length-1;r>=0;r--)(l=u[r])&&(n=(a<3?l(n):a>3?l(s,t,n):l(s,t))||n);return a>3&&n&&Object.defineProperty(s,t,n),n},Ws=(new Date).getDate(),Hs=(new Date).getMonth(),Ls=(new Date).getFullYear(),Fs=(new Date).getHours(),Rs=(new Date).getMinutes(),Ys=(new Date).getSeconds(),Bs=(new Date).getMilliseconds(),wt="e-datetimepicker",ei="e-datetimepopup-wrapper",ii="e-popup",At="e-input-focus",ai="e-icon-anim",ue="e-disabled",ni="e-error",at="e-active",pe="e-hover",nt="e-list-item",li="e-time-overflow",Us=function(u){function s(t,i){var a=u.call(this,t,i)||this;return a.valueWithMinutes=null,a.scrollInvoked=!1,a.moduleName=a.getModuleName(),a.formatRegex=/dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yyy|yy|y|'[^']*'|'[^']*'/g,a.dateFormatString="",a.dateTimeOptions=t,a}return Os(s,u),s.prototype.focusHandler=function(){this.enabled&&(0,e.iQ)([this.inputWrapper.container],At)},s.prototype.focusIn=function(){u.prototype.focusIn.call(this)},s.prototype.focusOut=function(){document.activeElement===this.inputElement&&(this.inputElement.blur(),(0,e.vy)([this.inputWrapper.container],[At]))},s.prototype.blurHandler=function(t){if(this.enabled){if(this.isTimePopupOpen()&&this.isPreventBlur)return void this.inputElement.focus();(0,e.vy)([this.inputWrapper.container],At);var i={model:this};this.isTimePopupOpen()&&this.hide(t),this.trigger("blur",i)}},s.prototype.destroy=function(){this.showClearButton&&(this.clearButton=document.getElementsByClassName("e-clear-icon")[0]),this.popupObject&&this.popupObject.element.classList.contains(ii)&&(this.popupObject.destroy(),(0,e.Yo)(this.dateTimeWrapper),this.dateTimeWrapper=void 0,this.liCollections=this.timeCollections=[],(0,e.hX)(this.rippleFn)||this.rippleFn()),this.inputElement&&v.pd.removeAttributes({"aria-live":"assertive","aria-atomic":"true","aria-invalid":"false",autocorrect:"off",autocapitalize:"off",spellcheck:"false","aria-expanded":"false",role:"combobox",autocomplete:"off"},this.inputElement),this.isCalendar()&&(this.popupWrapper&&(0,e.Yo)(this.popupWrapper),this.popupObject=this.popupWrapper=null,this.keyboardHandler.destroy()),this.unBindInputEvents(),this.liCollections=null,this.rippleFn=null,this.selectedElement=null,this.listTag=null,this.timeIcon=null,this.popupObject=null,this.preventArgs=null,this.keyboardModule=null,v.pd.destroy({element:this.inputElement,floatLabelType:this.floatLabelType,properties:this.properties},this.clearButton),u.prototype.destroy.call(this)},s.prototype.render=function(){this.timekeyConfigure={enter:"enter",escape:"escape",end:"end",tab:"tab",home:"home",down:"downarrow",up:"uparrow",left:"leftarrow",right:"rightarrow",open:"alt+downarrow",close:"alt+uparrow"},this.valueWithMinutes=null,this.previousDateTime=null,this.isPreventBlur=!1,this.cloneElement=this.element.cloneNode(!0),this.dateTimeFormat=this.cldrDateTimeFormat(),this.initValue=this.value,"string"==typeof this.min&&(this.min=this.checkDateValue(new Date(this.min))),"string"==typeof this.max&&(this.max=this.checkDateValue(new Date(this.max))),"string"==typeof this.minTime&&(this.minTime=this.checkDateValue(new Date(this.minTime))),"string"==typeof this.maxTime&&(this.maxTime=this.checkDateValue(new Date(this.maxTime))),!(0,e.hX)((0,e.kp)(this.element,"fieldset"))&&(0,e.kp)(this.element,"fieldset").disabled&&(this.enabled=!1),u.prototype.updateHtmlAttributeToElement.call(this),this.checkAttributes(!1),this.l10n=new e.Wo("datetimepicker",{placeholder:this.placeholder},this.locale),this.setProperties({placeholder:this.placeholder||this.l10n.getConstant("placeholder")},!0),u.prototype.render.call(this),this.createInputElement(),u.prototype.updateHtmlAttributeToWrapper.call(this),this.bindInputEvents(),this.enableMask&&this.notify("createMask",{module:"MaskedDateTime"}),this.setValue(!0),this.enableMask&&!this.value&&this.maskedDateValue&&("Always"===this.floatLabelType||!this.floatLabelType||!this.placeholder)&&v.pd.setValue(this.maskedDateValue,this.inputElement,this.floatLabelType,this.showClearButton),this.setProperties({scrollTo:this.checkDateValue(new Date(this.checkValue(this.scrollTo)))},!0),this.previousDateTime=this.value&&new Date(+this.value),"EJS-DATETIMEPICKER"===this.element.tagName&&(this.tabIndex=this.element.hasAttribute("tabindex")?this.element.getAttribute("tabindex"):"0",this.element.removeAttribute("tabindex"),this.enabled||(this.inputElement.tabIndex=-1)),"Never"!==this.floatLabelType&&v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-date-time-icon"),this.renderComplete()},s.prototype.setValue=function(t){if(void 0===t&&(t=!1),this.initValue=this.validateMinMaxRange(this.value),!this.strictMode&&this.isDateObject(this.initValue)){var i=this.validateMinMaxRange(this.initValue);v.pd.setValue(this.getFormattedValue(i),this.inputElement,this.floatLabelType,this.showClearButton),this.setProperties({value:i},!0)}else(0,e.hX)(this.value)&&(this.initValue=null,this.setProperties({value:null},!0));this.valueWithMinutes=this.value,u.prototype.updateInput.call(this,t)},s.prototype.validateMinMaxRange=function(t){var i=t;return this.isDateObject(t)?i=this.validateValue(t):+this.min>+this.max&&this.disablePopupButton(!0),this.checkValidState(i),i},s.prototype.checkValidState=function(t){this.isValidState=!0,this.strictMode||(+t>+this.max||+t<+this.min||!this.isValidTime(t))&&(this.isValidState=!1),this.checkErrorState()},s.prototype.checkErrorState=function(){this.isValidState?(0,e.vy)([this.inputWrapper.container],ni):(0,e.iQ)([this.inputWrapper.container],ni),(0,e.uK)(this.inputElement,{"aria-invalid":this.isValidState?"false":"true"})},s.prototype.isValidTime=function(t){if(null!=t&&(this.minTime||this.maxTime)){var i=void 0,a=void 0,n=void 0,l=void 0,r=36e5*t.getHours()+6e4*t.getMinutes()+1e3*t.getSeconds()+t.getMilliseconds();if(this.minTime&&(i=36e5*this.minTime.getHours()+6e4*this.minTime.getMinutes()+1e3*this.minTime.getSeconds()+this.minTime.getMilliseconds()),this.maxTime&&(a=36e5*this.maxTime.getHours()+6e4*this.maxTime.getMinutes()+1e3*this.maxTime.getSeconds()+this.maxTime.getMilliseconds()),this.min&&+t.getDate()==+this.min.getDate()&&+t.getMonth()==+this.min.getMonth()&&+t.getFullYear()==+this.min.getFullYear()&&(i=i<(l=36e5*this.min.getHours()+6e4*this.min.getMinutes()+1e3*this.min.getSeconds()+this.min.getMilliseconds())?l:i),this.max&&+t.getDate()==+this.max.getDate()&&+t.getMonth()==+this.max.getMonth()&&+this.max.getFullYear()==+this.max.getFullYear()&&(a=a>(n=36e5*this.max.getHours()+6e4*this.max.getMinutes()+1e3*this.max.getSeconds()+this.max.getMilliseconds())?n:a),this.strictMode){var h=void 0;return i&&r<i?(h=new Date(t.getFullYear(),t.getMonth(),t.getDate(),this.minTime.getHours(),this.minTime.getMinutes(),this.minTime.getSeconds(),this.minTime.getMilliseconds()),this.setProperties({value:h},!0),this.changedArgs={value:this.value}):a&&r>a&&(h=new Date(t.getFullYear(),t.getMonth(),t.getDate(),this.maxTime.getHours(),this.maxTime.getMinutes(),this.maxTime.getSeconds(),this.maxTime.getMilliseconds()),this.setProperties({value:h},!0),this.changedArgs={value:this.value}),!0}return!(i&&r<i||a&&r>a)}return!0},s.prototype.validateValue=function(t){var i=t;return this.strictMode?+this.min>+this.max?(this.disablePopupButton(!0),i=this.max):+t<+this.min?i=this.min:+t>+this.max&&(i=this.max):+this.min>+this.max&&(this.disablePopupButton(!0),i=t),i},s.prototype.disablePopupButton=function(t){t?((0,e.iQ)([this.inputWrapper.buttons[0],this.timeIcon],ue),this.hide()):(0,e.vy)([this.inputWrapper.buttons[0],this.timeIcon],ue)},s.prototype.getFormattedValue=function(t){var i;return(0,e.hX)(t)?null:(i="Gregorian"===this.calendarMode?{format:this.cldrDateTimeFormat(),type:"dateTime",skeleton:"yMd"}:{format:this.cldrDateTimeFormat(),type:"dateTime",skeleton:"yMd",calendar:"islamic"},this.globalize.formatDate(t,i))},s.prototype.isDateObject=function(t){return!(0,e.hX)(t)&&!isNaN(+t)},s.prototype.createInputElement=function(){(0,e.vy)([this.inputElement],"e-datepicker"),(0,e.vy)([this.inputWrapper.container],"e-date-wrapper"),(0,e.iQ)([this.inputWrapper.container],"e-datetime-wrapper"),(0,e.iQ)([this.inputElement],wt),this.renderTimeIcon()},s.prototype.renderTimeIcon=function(){this.timeIcon=v.pd.appendSpan("e-input-group-icon e-time-icon e-icons",this.inputWrapper.container)},s.prototype.bindInputEvents=function(){e.Jm.add(this.timeIcon,"mousedown",this.timeHandler,this),e.Jm.add(this.inputWrapper.buttons[0],"mousedown",this.dateHandler,this),e.Jm.add(this.inputElement,"blur",this.blurHandler,this),e.Jm.add(this.inputElement,"focus",this.focusHandler,this),this.defaultKeyConfigs=(0,e.X$)(this.defaultKeyConfigs,this.keyConfigs),this.keyboardHandler=new e.j9(this.inputElement,{eventName:"keydown",keyAction:this.inputKeyAction.bind(this),keyConfigs:this.defaultKeyConfigs})},s.prototype.unBindInputEvents=function(){e.Jm.remove(this.timeIcon,"mousedown touchstart",this.timeHandler),e.Jm.remove(this.inputWrapper.buttons[0],"mousedown touchstart",this.dateHandler),this.inputElement&&(e.Jm.remove(this.inputElement,"blur",this.blurHandler),e.Jm.remove(this.inputElement,"focus",this.focusHandler)),this.keyboardHandler&&this.keyboardHandler.destroy()},s.prototype.cldrTimeFormat=function(){return this.isNullOrEmpty(this.timeFormat)?"en"===this.locale||"en-US"===this.locale?(0,e._W)("timeFormats.short",(0,e.P)()):this.getCultureTimeObject(e.pE,""+this.locale):this.timeFormat},s.prototype.cldrDateTimeFormat=function(){var a=new e.DL(this.locale).getDatePattern({skeleton:"yMd"});return this.isNullOrEmpty(this.formatString)?a+" "+this.getCldrFormat("time"):this.formatString},s.prototype.getCldrFormat=function(t){return"en"===this.locale||"en-US"===this.locale?(0,e._W)("timeFormats.short",(0,e.P)()):this.getCultureTimeObject(e.pE,""+this.locale)},s.prototype.isNullOrEmpty=function(t){return!!((0,e.hX)(t)||"string"==typeof t&&""===t.trim())},s.prototype.getCultureTimeObject=function(t,i){return(0,e._W)("Gregorian"===this.calendarMode?"main."+this.locale+".dates.calendars.gregorian.timeFormats.short":"main."+this.locale+".dates.calendars.islamic.timeFormats.short",t)},s.prototype.timeHandler=function(t){this.enabled&&(this.isIconClicked=!0,e.Pw.isDevice&&this.inputElement.setAttribute("readonly",""),t.currentTarget===this.timeIcon&&t.preventDefault(),this.enabled&&!this.readonly&&(this.isDatePopupOpen()&&u.prototype.hide.call(this,t),this.isTimePopupOpen()?this.closePopup(t):(this.inputElement.focus(),this.popupCreation("time",t),(0,e.iQ)([this.inputWrapper.container],[At]))),this.isIconClicked=!1)},s.prototype.dateHandler=function(t){this.enabled&&(t.currentTarget===this.inputWrapper.buttons[0]&&t.preventDefault(),this.enabled&&!this.readonly&&(this.isTimePopupOpen()&&this.closePopup(t),(0,e.hX)(this.popupWrapper)||this.popupCreation("date",t)))},s.prototype.show=function(t,i){this.enabled&&this.readonly||!this.enabled||("time"!==t||this.dateTimeWrapper?this.popupObj||(this.isTimePopupOpen()&&this.hide(i),u.prototype.show.call(this),this.popupCreation("date",i)):(this.isDatePopupOpen()&&this.hide(i),this.popupCreation("time",i)))},s.prototype.toggle=function(t){this.isDatePopupOpen()?(u.prototype.hide.call(this,t),this.show("time",null)):this.isTimePopupOpen()?(this.hide(t),u.prototype.show.call(this,null,t),this.popupCreation("date",null)):this.show(null,t)},s.prototype.listCreation=function(){var t;"Gregorian"===this.calendarMode?(this.cldrDateTimeFormat().replace(this.formatRegex,this.TimePopupFormat()),""===this.dateFormatString&&(this.dateFormatString=this.cldrDateTimeFormat()),t=this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),{format:this.dateFormatString,type:"datetime"})):t=this.globalize.parseDate(this.getAmPmValue(this.inputElement.value),{format:this.cldrDateTimeFormat(),type:"datetime",calendar:"islamic"});var i=(0,e.hX)(this.value)?""!==this.inputElement.value?t:new Date:this.value;this.valueWithMinutes=i,this.listWrapper=(0,e.n)("div",{className:"e-content",attrs:{tabindex:"0"}});var a=this.startTime(i),n=this.endTime(i),l=oe.createListItems(this.createElement,a,n,this.globalize,this.cldrTimeFormat(),this.step);this.timeCollections=l.collection,this.listTag=l.list,(0,e.uK)(this.listTag,{role:"listbox","aria-hidden":"false",id:this.element.id+"_options"}),(0,e.BC)([l.list],this.listWrapper),this.wireTimeListEvents(),this.rippleFn=(0,e.CF)(this.listWrapper,{duration:300,selector:"."+nt}),this.liCollections=this.listWrapper.querySelectorAll("."+nt)},s.prototype.popupCreation=function(t,i){e.Pw.isDevice&&this.element.setAttribute("readonly","readonly"),"date"===t?!this.readonly&&this.popupWrapper&&((0,e.iQ)([this.popupWrapper],ei),(0,e.uK)(this.popupWrapper,{id:this.element.id+"_options"})):this.readonly||(this.dateTimeWrapper=(0,e.n)("div",{className:wt+" "+ii,attrs:{id:this.element.id+"_timepopup",style:"visibility:hidden ; display:block"}}),(0,e.hX)(this.cssClass)||(this.dateTimeWrapper.className+=" "+this.cssClass),!(0,e.hX)(this.step)&&this.step>0&&(this.listCreation(),(0,e.BC)([this.listWrapper],this.dateTimeWrapper)),document.body.appendChild(this.dateTimeWrapper),this.addTimeSelection(),this.renderPopup(),this.setTimeScrollPosition(),this.openPopup(i),(!e.Pw.isDevice||e.Pw.isDevice&&!this.fullScreenMode)&&this.popupObject.refreshPosition(this.inputElement),e.Pw.isDevice&&(this.modelWrapper.style.zIndex=(this.popupObject.zIndex-1).toString(),this.fullScreenMode&&(this.dateTimeWrapper.style.left="0px")))},s.prototype.openPopup=function(t){var i=this;this.preventArgs={cancel:!1,popup:this.popupObject,event:t||null},this.trigger("open",this.preventArgs,function(n){if(i.preventArgs=n,!i.preventArgs.cancel&&!i.readonly){i.popupObject.show(new e.X5({name:"FadeIn",duration:100}),1e3===i.zIndex?i.element:null),(0,e.iQ)([i.inputWrapper.container],[ai]),(0,e.uK)(i.inputElement,{"aria-expanded":"true"}),(0,e.uK)(i.inputElement,{"aria-owns":i.inputElement.id+"_options"}),(0,e.uK)(i.inputElement,{"aria-controls":i.inputElement.id}),e.Jm.add(document,"mousedown touchstart",i.documentClickHandler,i)}})},s.prototype.documentClickHandler=function(t){var i=t.target;!(0,e.hX)(this.popupObject)&&(this.inputWrapper.container.contains(i)&&"mousedown"!==t.type||this.popupObject.element&&this.popupObject.element.contains(i))&&"touchstart"!==t.type&&t.preventDefault(),(0,e.kp)(i,'[id="'+(this.popupObject&&this.popupObject.element.id+'"]'))||i===this.inputElement||i===this.timeIcon||(0,e.hX)(this.inputWrapper)||i===this.inputWrapper.container||i.classList.contains("e-dlg-overlay")?i!==this.inputElement&&(e.Pw.isDevice||(this.isPreventBlur=document.activeElement===this.inputElement&&(e.Pw.isIE||"edge"===e.Pw.info.name)&&i===this.popupObject.element)):this.isTimePopupOpen()&&(this.hide(t),this.focusOut())},s.prototype.isTimePopupOpen=function(){return!(!this.dateTimeWrapper||!this.dateTimeWrapper.classList.contains(""+wt))},s.prototype.isDatePopupOpen=function(){return!(!this.popupWrapper||!this.popupWrapper.classList.contains(""+ei))},s.prototype.renderPopup=function(){var t=this;if(this.containerStyle=this.inputWrapper.container.getBoundingClientRect(),e.Pw.isDevice&&(this.timeModal=(0,e.n)("div"),this.timeModal.className=wt+" e-time-modal",document.body.className+=" "+li,this.timeModal.style.display="block",document.body.appendChild(this.timeModal)),e.Pw.isDevice){this.modelWrapper=(0,e.n)("div",{className:"e-datetime-mob-popup-wrap"}),this.modelWrapper.appendChild(this.dateTimeWrapper);var i=(0,e.n)("div",{className:"e-dlg-overlay"});i.style.zIndex=(this.zIndex-1).toString(),this.modelWrapper.appendChild(i),document.body.appendChild(this.modelWrapper)}if(this.popupObject=new It.zD(this.dateTimeWrapper,{width:this.setPopupWidth(),zIndex:this.zIndex,targetType:"container",collision:e.Pw.isDevice?{X:"fit",Y:"fit"}:{X:"flip",Y:"flip"},relateTo:e.Pw.isDevice?document.body:this.inputWrapper.container,position:e.Pw.isDevice?{X:"center",Y:"center"}:{X:"left",Y:"bottom"},enableRtl:this.enableRtl,offsetY:4,open:function(){t.dateTimeWrapper.style.visibility="visible",(0,e.iQ)([t.timeIcon],at),e.Pw.isDevice||(t.timekeyConfigure=(0,e.X$)(t.timekeyConfigure,t.keyConfigs),t.inputEvent=new e.j9(t.inputWrapper.container,{keyAction:t.timeKeyActionHandle.bind(t),keyConfigs:t.timekeyConfigure,eventName:"keydown"}))},close:function(){(0,e.vy)([t.timeIcon],at),t.unWireTimeListEvents(),t.inputElement.removeAttribute("aria-activedescendant"),(0,e.TF)(t.popupObject.element),t.popupObject.destroy(),t.dateTimeWrapper.innerHTML="",t.modelWrapper&&(0,e.TF)(t.modelWrapper),t.listWrapper=t.dateTimeWrapper=void 0,t.inputEvent&&t.inputEvent.destroy()},targetExitViewport:function(){e.Pw.isDevice||t.hide()}}),e.Pw.isDevice&&this.fullScreenMode?(this.popupObject.element.style.maxHeight="100%",this.popupObject.element.style.width="100%"):this.popupObject.element.style.maxHeight="250px",e.Pw.isDevice&&this.fullScreenMode){var n=this.createElement("div",{className:"e-model-header"}),l=this.createElement("span",{className:"e-model-title"});l.textContent="Select time";var r=this.createElement("span",{className:"e-popup-close"});e.Jm.add(r,"mousedown touchstart",this.dateTimeCloseHandler,this),n.appendChild(r),n.appendChild(l),this.dateTimeWrapper.insertBefore(n,this.dateTimeWrapper.firstElementChild)}},s.prototype.dateTimeCloseHandler=function(t){this.hide()},s.prototype.setDimension=function(t){return"number"==typeof t?t=(0,e.IV)(t):"string"==typeof t||(t="100%"),t},s.prototype.setPopupWidth=function(){var t=this.setDimension(this.width);return t.indexOf("%")>-1&&(t=(this.containerStyle.width*parseFloat(t)/100).toString()+"px"),t},s.prototype.wireTimeListEvents=function(){e.Jm.add(this.listWrapper,"click",this.onMouseClick,this),e.Pw.isDevice||(e.Jm.add(this.listWrapper,"mouseover",this.onMouseOver,this),e.Jm.add(this.listWrapper,"mouseout",this.onMouseLeave,this))},s.prototype.unWireTimeListEvents=function(){this.listWrapper&&(e.Jm.remove(this.listWrapper,"click",this.onMouseClick),e.Jm.remove(document,"mousedown touchstart",this.documentClickHandler),e.Pw.isDevice||(e.Jm.add(this.listWrapper,"mouseover",this.onMouseOver,this),e.Jm.add(this.listWrapper,"mouseout",this.onMouseLeave,this)))},s.prototype.onMouseOver=function(t){var i=(0,e.kp)(t.target,"."+nt);this.setTimeHover(i,pe)},s.prototype.onMouseLeave=function(){this.removeTimeHover(pe)},s.prototype.setTimeHover=function(t,i){this.enabled&&this.isValidLI(t)&&!t.classList.contains(i)&&(this.removeTimeHover(i),(0,e.iQ)([t],i))},s.prototype.getPopupHeight=function(){var t=parseInt("250px",10),i=this.dateTimeWrapper.getBoundingClientRect().height;return e.Pw.isDevice&&this.fullScreenMode?i:i>t?t:i},s.prototype.changeEvent=function(t){u.prototype.changeEvent.call(this,t),(this.value&&this.value.valueOf())!==(this.previousDateTime&&+this.previousDateTime.valueOf())&&(this.valueWithMinutes=this.value,this.setInputValue("date"),this.previousDateTime=this.value&&new Date(+this.value))},s.prototype.updateValue=function(t){this.setInputValue("time"),+this.previousDateTime!=+this.value&&(this.changedArgs={value:this.value,event:t||null,isInteracted:!(0,e.hX)(t),element:this.element},this.addTimeSelection(),this.trigger("change",this.changedArgs),this.previousDateTime=this.previousDate=this.value)},s.prototype.setTimeScrollPosition=function(){var t=this.selectedElement;(0,e.hX)(t)?this.dateTimeWrapper&&this.checkDateValue(this.scrollTo)&&this.setScrollTo():this.findScrollTop(t)},s.prototype.findScrollTop=function(t){var i=this.getPopupHeight(),a=t.nextElementSibling,n=a?a.offsetTop:t.offsetTop,l=t.getBoundingClientRect().height;n+t.offsetTop>i?e.Pw.isDevice&&this.fullScreenMode?this.dateTimeWrapper.querySelector(".e-content").scrollTop=a?n-(i/2+l/2):n:this.dateTimeWrapper.scrollTop=a?n-(i/2+l/2):n:this.dateTimeWrapper.scrollTop=0},s.prototype.setScrollTo=function(){var t,i=this.dateTimeWrapper.querySelectorAll("."+nt);if(i.length>=0){this.scrollInvoked=!0;var a=this.timeCollections[0],n=this.getDateObject(this.checkDateValue(this.scrollTo)).getTime();t=i[Math.round((n-a)/(6e4*this.step))]}else this.dateTimeWrapper.scrollTop=0;(0,e.hX)(t)?this.dateTimeWrapper.scrollTop=0:this.findScrollTop(t)},s.prototype.setInputValue=function(t){if("date"===t)this.inputElement.value=this.previousElementValue=this.getFormattedValue(this.getFullDateTime()),this.setProperties({value:this.getFullDateTime()},!0);else{var i=this.getFormattedValue(new Date(this.timeCollections[this.activeIndex]));v.pd.setValue(i,this.inputElement,this.floatLabelType,this.showClearButton),this.previousElementValue=this.inputElement.value,this.setProperties({value:new Date(this.timeCollections[this.activeIndex])},!0),this.enableMask&&this.createMask()}this.updateIconState()},s.prototype.getFullDateTime=function(){var t;return t=this.isDateObject(this.valueWithMinutes)?this.combineDateTime(this.valueWithMinutes):this.previousDate,this.validateMinMaxRange(t)},s.prototype.createMask=function(){this.notify("createMask",{module:"MaskedDateTime"})},s.prototype.combineDateTime=function(t){if(this.isDateObject(t)){var i=this.previousDate.getDate(),a=this.previousDate.getMonth(),n=this.previousDate.getFullYear(),l=t.getHours(),r=t.getMinutes(),h=t.getSeconds();return new Date(n,a,i,l,r,h)}return this.previousDate},s.prototype.onMouseClick=function(t){var a=this.selectedElement=(0,e.kp)(t.target,"."+nt);a&&a.classList.contains(nt)&&(this.timeValue=a.getAttribute("data-value"),this.hide(t)),this.setSelection(a,t)},s.prototype.setSelection=function(t,i){if(this.isValidLI(t)&&!t.classList.contains(at)){this.selectedElement=t;var a=Array.prototype.slice.call(this.liCollections).indexOf(t);this.activeIndex=a,this.valueWithMinutes=new Date(this.timeCollections[this.activeIndex]),(0,e.iQ)([this.selectedElement],at),this.selectedElement.setAttribute("aria-selected","true"),this.updateValue(i)}},s.prototype.setTimeActiveClass=function(){var t=(0,e.hX)(this.dateTimeWrapper)?this.listWrapper:this.dateTimeWrapper;if(!(0,e.hX)(t)){var i=t.querySelectorAll("."+nt);if(i.length)for(var a=0;a<i.length;a++)if(this.timeCollections[a]===+this.valueWithMinutes){i[a].setAttribute("aria-selected","true"),this.selectedElement=i[a],this.activeIndex=a,this.setTimeActiveDescendant();break}}},s.prototype.setTimeActiveDescendant=function(){!(0,e.hX)(this.selectedElement)&&this.value?(0,e.uK)(this.inputElement,{"aria-activedescendant":this.selectedElement.getAttribute("id")}):this.inputElement.removeAttribute("aria-activedescendant")},s.prototype.addTimeSelection=function(){this.selectedElement=null,this.removeTimeSelection(),this.setTimeActiveClass(),(0,e.hX)(this.selectedElement)||((0,e.iQ)([this.selectedElement],at),this.selectedElement.setAttribute("aria-selected","true"))},s.prototype.removeTimeSelection=function(){if(this.removeTimeHover(pe),!(0,e.hX)(this.dateTimeWrapper)){var t=this.dateTimeWrapper.querySelectorAll("."+at);t.length&&((0,e.vy)(t,at),t[0].removeAttribute("aria-selected"))}},s.prototype.removeTimeHover=function(t){var i=this.getTimeHoverItem(t);i&&i.length&&(0,e.vy)(i,t)},s.prototype.getTimeHoverItem=function(t){var a,i=(0,e.hX)(this.dateTimeWrapper)?this.listWrapper:this.dateTimeWrapper;return(0,e.hX)(i)||(a=i.querySelectorAll("."+t)),a},s.prototype.isValidLI=function(t){return t&&t.classList.contains(nt)&&!t.classList.contains(ue)},s.prototype.calculateStartEnd=function(t,i,a){var n=t.getDate(),l=t.getMonth(),r=t.getFullYear(),h=t.getHours(),o=t.getMinutes(),p=t.getSeconds(),c=t.getMilliseconds();return i?"starttime"===a?new Date(r,l,n,0,0,0):new Date(r,l,n,23,59,59):new Date(r,l,n,h,o,p,c)},s.prototype.startTime=function(t){var i,a,o,n=this.min,l=this.max,r=null===t?new Date:t,h=!1;return this.minTime?(o=new Date(r.getFullYear(),r.getMonth(),r.getDate(),this.minTime.getHours(),this.minTime.getMinutes(),this.minTime.getSeconds(),this.minTime.getMilliseconds()),h=!0):o=r,+o.getDate()==+n.getDate()&&+o.getMonth()==+n.getMonth()&&+o.getFullYear()==+n.getFullYear()||+new Date(o.getFullYear(),o.getMonth(),o.getDate())<=+new Date(n.getFullYear(),n.getMonth(),n.getDate())?(a=!1,i=n,h&&+new Date(o.getFullYear(),o.getMonth(),o.getDate(),o.getHours(),o.getMinutes(),o.getSeconds(),o.getMilliseconds())>=+new Date(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds())&&i.setHours(o.getHours(),o.getMinutes(),o.getSeconds(),o.getMilliseconds())):+o<+l&&+o>+n?(a=!h,i=o):+o>=+l&&(a=!0,i=l),this.calculateStartEnd(i,a,"starttime")},s.prototype.TimePopupFormat=function(){var t="",i=0,a=this;return function n(l){switch(l){case"d":case"dd":case"ddd":case"dddd":case"M":case"MM":case"MMM":case"MMMM":case"y":case"yy":case"yyy":case"yyyy":""===t?t+=l:t=t+"/"+l,i+=1}return i>2&&(a.dateFormatString=t),t}},s.prototype.endTime=function(t){var i,a,h,n=this.max,l=null===t?new Date:t,r=!1;return this.maxTime?(h=new Date(l.getFullYear(),l.getMonth(),l.getDate(),this.maxTime.getHours(),this.maxTime.getMinutes(),this.maxTime.getSeconds(),this.maxTime.getMilliseconds()),r=!0):h=l,+h.getDate()==+n.getDate()&&+h.getMonth()==+n.getMonth()&&+h.getFullYear()==+n.getFullYear()||+new Date(h.getUTCFullYear(),h.getMonth(),h.getDate())>=+new Date(n.getFullYear(),n.getMonth(),n.getDate())?(a=!1,i=this.max,r&&+new Date(h.getUTCFullYear(),h.getMonth(),h.getDate(),h.getHours(),h.getMinutes(),h.getSeconds(),h.getMilliseconds())<=+new Date(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds())&&i.setHours(h.getHours(),h.getMinutes(),h.getSeconds(),h.getMilliseconds())):+h<+this.max&&+h>+this.min?(a=!r,i=h):+h<=+this.min&&(a=!0,i=this.min),this.calculateStartEnd(i,a,"endtime")},s.prototype.hide=function(t){var i=this;if(this.popupObj||this.dateTimeWrapper){this.preventArgs={cancel:!1,popup:this.popupObj||this.popupObject,event:t||null};var a=this.preventArgs;(0,e.hX)(this.popupObj)?this.trigger("close",a,function(n){i.dateTimeCloseEventCallback(t,n)}):this.dateTimeCloseEventCallback(t,a)}else e.Pw.isDevice&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly"),this.setAllowEdit()},s.prototype.dateTimeCloseEventCallback=function(t,i){this.preventArgs=i,this.preventArgs.cancel||(this.isDatePopupOpen()?u.prototype.hide.call(this,t):this.isTimePopupOpen()&&(this.closePopup(t),(0,e.vy)([document.body],li),e.Pw.isDevice&&this.timeModal&&(this.timeModal.style.display="none",this.timeModal.outerHTML="",this.timeModal=null),this.setTimeActiveDescendant())),e.Pw.isDevice&&this.allowEdit&&!this.readonly&&this.inputElement.removeAttribute("readonly"),this.setAllowEdit()},s.prototype.closePopup=function(t){this.isTimePopupOpen()&&this.popupObject&&(this.popupObject.hide(new e.X5({name:"FadeOut",duration:100,delay:0})),this.inputWrapper.container.classList.remove(ai),(0,e.uK)(this.inputElement,{"aria-expanded":"false"}),this.inputElement.removeAttribute("aria-owns"),this.inputElement.removeAttribute("aria-controls"),e.Jm.remove(document,"mousedown touchstart",this.documentClickHandler))},s.prototype.preRender=function(){this.checkFormat(),this.dateTimeFormat=this.cldrDateTimeFormat(),u.prototype.preRender.call(this),(0,e.vy)([this.inputElementCopy],[wt])},s.prototype.getProperty=function(t,i){this.setProperties("min"===i?{min:this.validateValue(t.min)}:{max:this.validateValue(t.max)},!0)},s.prototype.checkAttributes=function(t){for(var a,n=0,l=t?(0,e.hX)(this.htmlAttributes)?[]:Object.keys(this.htmlAttributes):["style","name","step","disabled","readonly","value","min","max","placeholder","type"];n<l.length;n++){var r=l[n];if(!(0,e.hX)(this.inputElement.getAttribute(r)))switch(r){case"name":this.inputElement.setAttribute("name",this.inputElement.getAttribute(r));break;case"step":this.step=parseInt(this.inputElement.getAttribute(r),10);break;case"readonly":if((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.readonly||t){var h="disabled"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r);this.setProperties({readonly:h},!t)}break;case"placeholder":((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.placeholder||t)&&this.setProperties({placeholder:this.inputElement.getAttribute(r)},!t);break;case"min":((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.min||t)&&(a=new Date(this.inputElement.getAttribute(r)),!this.isNullOrEmpty(a)&&!isNaN(+a)&&this.setProperties({min:a},!t));break;case"disabled":if((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.enabled||t){var o=!("disabled"===this.inputElement.getAttribute(r)||"true"===this.inputElement.getAttribute(r)||""===this.inputElement.getAttribute(r));this.setProperties({enabled:o},!t)}break;case"value":((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.value||t)&&(a=new Date(this.inputElement.getAttribute(r)),!this.isNullOrEmpty(a)&&!isNaN(+a)&&this.setProperties({value:a},!t));break;case"max":((0,e.hX)(this.dateTimeOptions)||void 0===this.dateTimeOptions.max||t)&&(a=new Date(this.inputElement.getAttribute(r)),!this.isNullOrEmpty(a)&&!isNaN(+a)&&this.setProperties({max:a},!t))}}},s.prototype.requiredModules=function(){var t=[];return"Islamic"===this.calendarMode&&t.push({args:[this],member:"islamic",name:"Islamic"}),this.enableMask&&t.push(this.maskedDateModule()),t},s.prototype.maskedDateModule=function(){return{args:[this],member:"MaskedDateTime"}},s.prototype.getTimeActiveElement=function(){return(0,e.hX)(this.dateTimeWrapper)?null:this.dateTimeWrapper.querySelectorAll("."+at)},s.prototype.createDateObj=function(t){return t instanceof Date?t:null},s.prototype.getDateObject=function(t){if(!this.isNullOrEmpty(t)){var i=this.createDateObj(t),a=this.valueWithMinutes,n=!(0,e.hX)(a);if(this.checkDateValue(i)){var l=n?a.getDate():Ws,r=n?a.getMonth():Hs,h=n?a.getFullYear():Ls,o=n?a.getHours():Fs,p=n?a.getMinutes():Rs,c=n?a.getSeconds():Ys,f=n?a.getMilliseconds():Bs;return this.scrollInvoked?(this.scrollInvoked=!1,new Date(h,r,l,i.getHours(),i.getMinutes(),i.getSeconds(),i.getMilliseconds())):new Date(h,r,l,o,p,c,f)}}return null},s.prototype.findNextTimeElement=function(t){var i=this.inputElement.value,a=(0,e.hX)(this.valueWithMinutes)?this.createDateObj(i):this.getDateObject(this.valueWithMinutes),n=null,l=this.liCollections.length;if(!(0,e.hX)(this.activeIndex)||!(0,e.hX)(this.checkDateValue(a))){if("home"===t.action)n=+this.createDateObj(new Date(this.timeCollections[0])),this.activeIndex=0;else if("end"===t.action)n=+this.createDateObj(new Date(this.timeCollections[this.timeCollections.length-1])),this.activeIndex=this.timeCollections.length-1;else if("down"===t.action){for(var r=0;r<l;r++)if(+a<this.timeCollections[r]){n=+this.createDateObj(new Date(this.timeCollections[r])),this.activeIndex=r;break}}else for(r=l-1;r>=0;r--)if(+a>this.timeCollections[r]){n=+this.createDateObj(new Date(this.timeCollections[r])),this.activeIndex=r;break}this.selectedElement=this.liCollections[this.activeIndex],this.timeElementValue((0,e.hX)(n)?null:new Date(n))}},s.prototype.setTimeValue=function(t,i){var a,n,l=this.validateMinMaxRange(i),r=this.createDateObj(l);return this.getFormattedValue(r)!==((0,e.hX)(this.value)?null:this.getFormattedValue(this.value))?(this.valueWithMinutes=(0,e.hX)(r)?null:r,n=new Date(+this.valueWithMinutes)):(this.strictMode&&(t=r),this.valueWithMinutes=this.checkDateValue(t),n=new Date(+this.valueWithMinutes)),a=this.globalize.formatDate(n,"Gregorian"===this.calendarMode?{format:(0,e.hX)(this.formatString)?this.cldrDateTimeFormat():this.formatString,type:"dateTime",skeleton:"yMd"}:{format:(0,e.hX)(this.formatString)?this.cldrDateTimeFormat():this.formatString,type:"dateTime",skeleton:"yMd",calendar:"islamic"}),!this.strictMode&&(0,e.hX)(n),v.pd.setValue(a,this.inputElement,this.floatLabelType,this.showClearButton),n},s.prototype.timeElementValue=function(t){if(!(0,e.hX)(this.checkDateValue(t))&&!this.isNullOrEmpty(t)){var i=t instanceof Date?t:this.getDateObject(t);return this.setTimeValue(i,t)}return null},s.prototype.timeKeyHandler=function(t){if(!((0,e.hX)(this.step)||this.step<=0)){var i=this.timeCollections.length;if((0,e.hX)(this.getTimeActiveElement())||0===this.getTimeActiveElement().length)this.liCollections.length>0&&((0,e.hX)(this.value)&&(0,e.hX)(this.activeIndex)?(this.activeIndex=0,this.selectedElement=this.liCollections[0],this.timeElementValue(new Date(this.timeCollections[0]))):this.findNextTimeElement(t));else{var a=void 0;if(t.keyCode>=37&&t.keyCode<=40){var n=40===t.keyCode||39===t.keyCode?++this.activeIndex:--this.activeIndex;this.activeIndex=this.activeIndex===i?0:this.activeIndex,this.activeIndex=n=this.activeIndex<0?i-1:this.activeIndex,a=(0,e.hX)(this.timeCollections[n])?this.timeCollections[0]:this.timeCollections[n]}else"home"===t.action?(this.activeIndex=0,a=this.timeCollections[0]):"end"===t.action&&(this.activeIndex=i-1,a=this.timeCollections[i-1]);this.selectedElement=this.liCollections[this.activeIndex],this.timeElementValue(new Date(a))}this.isNavigate=!0,this.setTimeHover(this.selectedElement,"e-navigation"),this.setTimeActiveDescendant(),this.isTimePopupOpen()&&null!==this.selectedElement&&(!t||"click"!==t.type)&&this.setTimeScrollPosition()}},s.prototype.timeKeyActionHandle=function(t){if(this.enabled)switch("right"!==t.action&&"left"!==t.action&&"tab"!==t.action&&t.preventDefault(),t.action){case"up":case"down":case"home":case"end":this.timeKeyHandler(t);break;case"enter":this.isNavigate?(this.selectedElement=this.liCollections[this.activeIndex],this.valueWithMinutes=new Date(this.timeCollections[this.activeIndex]),this.setInputValue("time"),+this.previousDateTime!=+this.value&&(this.changedArgs.value=this.value,this.addTimeSelection(),this.previousDateTime=this.value)):this.updateValue(t),this.hide(t),(0,e.iQ)([this.inputWrapper.container],At),this.isNavigate=!1,t.stopPropagation();break;case"escape":this.hide(t);break;default:this.isNavigate=!1}},s.prototype.inputKeyAction=function(t){"altDownArrow"===t.action&&(this.strictModeUpdate(),this.updateInput(),this.toggle(t))},s.prototype.onPropertyChanged=function(t,i){for(var a=0,n=Object.keys(t);a<n.length;a++){var l=n[a];switch(["blur","change","cleared","close","created","destroyed","focus","navigated","open","renderDayCell"].indexOf(l)>0&&this.isReact&&(this.isDynamicValueChanged=!0),l){case"value":this.isDynamicValueChanged=!0,this.invalidValueString=null,this.checkInvalidValue(t.value),t.value=this.value,t.value=this.validateValue(t.value),v.pd.setValue(this.enableMask?this.maskedDateValue:this.getFormattedValue(t.value),this.inputElement,this.floatLabelType,this.showClearButton),this.valueWithMinutes=t.value,this.setProperties({value:t.value},!0),this.popupObj&&this.popupUpdate(),this.previousDateTime=new Date(this.inputElement.value),this.updateInput(),this.changeTrigger(null),this.preventChange=this.isAngular&&this.preventChange?!this.preventChange:this.preventChange,this.enableMask&&this.value&&this.notify("createMask",{module:"MaskedDateTime"});break;case"min":case"max":this.getProperty(t,l),this.updateInput();break;case"enableRtl":v.pd.setEnableRtl(this.enableRtl,[this.inputWrapper.container]);break;case"cssClass":(0,e.hX)(i.cssClass)||(i.cssClass=i.cssClass.replace(/\s+/g," ").trim()),(0,e.hX)(t.cssClass)||(t.cssClass=t.cssClass.replace(/\s+/g," ").trim()),v.pd.setCssClass(t.cssClass,[this.inputWrapper.container],i.cssClass),this.dateTimeWrapper&&v.pd.setCssClass(t.cssClass,[this.dateTimeWrapper],i.cssClass);break;case"locale":this.globalize=new e.DL(this.locale),this.l10n.setLocale(this.locale),this.dateTimeOptions&&null==this.dateTimeOptions.placeholder&&(this.setProperties({placeholder:this.l10n.getConstant("placeholder")},!0),v.pd.setPlaceholder(this.l10n.getConstant("placeholder"),this.inputElement)),this.dateTimeFormat=this.cldrDateTimeFormat(),u.prototype.updateInput.call(this);break;case"htmlAttributes":this.updateHtmlAttributeToElement(),this.updateHtmlAttributeToWrapper(),this.checkAttributes(!0);break;case"format":this.setProperties({format:t.format},!0),this.checkFormat(),this.dateTimeFormat=this.formatString,this.setValue(),this.enableMask&&(this.notify("createMask",{module:"MaskedDateTime"}),this.value||v.pd.setValue(this.maskedDateValue,this.inputElement,this.floatLabelType,this.showClearButton));break;case"placeholder":v.pd.setPlaceholder(t.placeholder,this.inputElement);break;case"enabled":v.pd.setEnabled(this.enabled,this.inputElement),this.enabled?this.inputElement.setAttribute("tabindex",this.tabIndex):this.inputElement.tabIndex=-1;break;case"strictMode":this.invalidValueString=null,this.updateInput();break;case"width":this.setWidth(t.width),v.pd.calculateWidth(this.inputElement,this.inputWrapper.container),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-date-time-icon");break;case"readonly":v.pd.setReadonly(this.readonly,this.inputElement);break;case"floatLabelType":this.floatLabelType=t.floatLabelType,v.pd.removeFloating(this.inputWrapper),v.pd.addFloating(this.inputElement,this.floatLabelType,this.placeholder),!(0,e.hX)(this.inputWrapper.buttons[0])&&!(0,e.hX)(this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0])&&"Never"!==this.floatLabelType&&this.inputWrapper.container.getElementsByClassName("e-float-text-overflow")[0].classList.add("e-date-time-icon");break;case"scrollTo":this.checkDateValue(new Date(this.checkValue(t.scrollTo)))?(this.dateTimeWrapper&&this.setScrollTo(),this.setProperties({scrollTo:this.checkDateValue(new Date(this.checkValue(t.scrollTo)))},!0)):this.setProperties({scrollTo:null},!0);break;case"enableMask":this.enableMask?(this.notify("createMask",{module:"MaskedDateTime"}),v.pd.setValue(this.maskedDateValue,this.inputElement,this.floatLabelType,this.showClearButton)):this.inputElement.value===this.maskedDateValue&&(this.maskedDateValue="",v.pd.setValue(this.maskedDateValue,this.inputElement,this.floatLabelType,this.showClearButton));break;default:u.prototype.onPropertyChanged.call(this,t,i)}this.isDynamicValueChanged||this.hide(null),this.isDynamicValueChanged=!1}},s.prototype.getModuleName=function(){return"datetimepicker"},s.prototype.restoreValue=function(){this.previousDateTime=this.previousDate,this.currentDate=this.value?this.value:new Date,this.valueWithMinutes=this.value,this.previousDate=this.value,this.previousElementValue=this.previousElementValue=(0,e.hX)(this.inputValueCopy)?"":this.getFormattedValue(this.inputValueCopy)},C([(0,e.mA)(null)],s.prototype,"timeFormat",void 0),C([(0,e.mA)(30)],s.prototype,"step",void 0),C([(0,e.mA)(null)],s.prototype,"scrollTo",void 0),C([(0,e.mA)(1e3)],s.prototype,"zIndex",void 0),C([(0,e.mA)(null)],s.prototype,"value",void 0),C([(0,e.mA)(null)],s.prototype,"keyConfigs",void 0),C([(0,e.mA)({})],s.prototype,"htmlAttributes",void 0),C([(0,e.mA)(!1)],s.prototype,"enablePersistence",void 0),C([(0,e.mA)(!0)],s.prototype,"allowEdit",void 0),C([(0,e.mA)(!1)],s.prototype,"isMultiSelection",void 0),C([(0,e.mA)(null)],s.prototype,"values",void 0),C([(0,e.mA)(!0)],s.prototype,"showClearButton",void 0),C([(0,e.mA)(null)],s.prototype,"placeholder",void 0),C([(0,e.mA)(!1)],s.prototype,"strictMode",void 0),C([(0,e.mA)(!1)],s.prototype,"fullScreenMode",void 0),C([(0,e.mA)(null)],s.prototype,"serverTimezoneOffset",void 0),C([(0,e.mA)(new Date(1900,0,1))],s.prototype,"min",void 0),C([(0,e.mA)(new Date(2099,11,31))],s.prototype,"max",void 0),C([(0,e.mA)(null)],s.prototype,"minTime",void 0),C([(0,e.mA)(null)],s.prototype,"maxTime",void 0),C([(0,e.mA)(null)],s.prototype,"firstDayOfWeek",void 0),C([(0,e.mA)("Gregorian")],s.prototype,"calendarMode",void 0),C([(0,e.mA)("Month")],s.prototype,"start",void 0),C([(0,e.mA)("Month")],s.prototype,"depth",void 0),C([(0,e.mA)(!1)],s.prototype,"weekNumber",void 0),C([(0,e.mA)(!0)],s.prototype,"showTodayButton",void 0),C([(0,e.mA)("Short")],s.prototype,"dayHeaderFormat",void 0),C([(0,e.mA)(!1)],s.prototype,"openOnFocus",void 0),C([(0,e.mA)(!1)],s.prototype,"enableMask",void 0),C([(0,e.mA)({day:"day",month:"month",year:"year",hour:"hour",minute:"minute",second:"second",dayOfTheWeek:"day of the week"})],s.prototype,"maskPlaceholder",void 0),C([(0,e.Jh)()],s.prototype,"open",void 0),C([(0,e.Jh)()],s.prototype,"close",void 0),C([(0,e.Jh)()],s.prototype,"cleared",void 0),C([(0,e.Jh)()],s.prototype,"blur",void 0),C([(0,e.Jh)()],s.prototype,"focus",void 0),C([(0,e.Jh)()],s.prototype,"created",void 0),C([(0,e.Jh)()],s.prototype,"destroyed",void 0),C([e.kc],s)}(Ne),hi="ArrowLeft",oi="ArrowDown",Tt="shiftTab",jt="End",Gs=function(){function u(s){this.mask="",this.defaultConstant={day:"day",month:"month",year:"year",hour:"hour",minute:"minute",second:"second",dayOfTheWeek:"day of the week"},this.hiddenMask="",this.validCharacters="dMyhmHfasz",this.isDayPart=!1,this.isMonthPart=!1,this.isYearPart=!1,this.isHourPart=!1,this.isMinutePart=!1,this.isSecondsPart=!1,this.isMilliSecondsPart=!1,this.monthCharacter="",this.periodCharacter="",this.isHiddenMask=!1,this.isComplete=!1,this.isNavigate=!1,this.navigated=!1,this.isBlur=!1,this.formatRegex=/EEEEE|EEEE|EEE|EE|E|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yyy|yy|y|HH|H|hh|h|mm|m|fff|ff|f|aa|a|ss|s|zzzz|zzz|zz|z|'[^']*'|'[^']*'/g,this.isDeletion=!1,this.isShortYear=!1,this.isDeleteKey=!1,this.isDateZero=!1,this.isMonthZero=!1,this.isYearZero=!1,this.isLeadingZero=!1,this.dayTypeCount=0,this.monthTypeCount=0,this.hourTypeCount=0,this.minuteTypeCount=0,this.secondTypeCount=0,this.parent=s,this.dateformat=this.getCulturedFormat(),this.maskDateValue=null!=this.parent.value?new Date(+this.parent.value):new Date,this.maskDateValue.setMonth(0),this.maskDateValue.setHours(0),this.maskDateValue.setMinutes(0),this.maskDateValue.setSeconds(0),this.previousDate=new Date(this.maskDateValue.getFullYear(),this.maskDateValue.getMonth(),this.maskDateValue.getDate(),this.maskDateValue.getHours(),this.maskDateValue.getMinutes(),this.maskDateValue.getSeconds()),this.removeEventListener(),this.addEventListener()}return u.prototype.getModuleName=function(){return"MaskedDateTime"},u.prototype.addEventListener=function(){this.parent.isDestroyed||(this.parent.on("createMask",this.createMask,this),this.parent.on("setMaskSelection",this.validCharacterCheck,this),this.parent.on("inputHandler",this.maskInputHandler,this),this.parent.on("keyDownHandler",this.maskKeydownHandler,this),this.parent.on("clearHandler",this.clearHandler,this),this.parent.on("maskPasteInputHandler",this.maskPasteInputHandler,this))},u.prototype.removeEventListener=function(){this.parent.isDestroyed||(this.parent.off("createMask",this.createMask),this.parent.off("setMaskSelection",this.validCharacterCheck),this.parent.off("inputHandler",this.maskInputHandler),this.parent.off("keyDownHandler",this.maskKeydownHandler),this.parent.off("clearHandler",this.clearHandler),this.parent.off("maskPasteInputHandler",this.maskPasteInputHandler))},u.prototype.createMask=function(s){this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!1,this.dateformat=this.getCulturedFormat(),this.parent.maskPlaceholder.day&&(this.defaultConstant.day=this.parent.maskPlaceholder.day),this.parent.maskPlaceholder.month&&(this.defaultConstant.month=this.parent.maskPlaceholder.month),this.parent.maskPlaceholder.year&&(this.defaultConstant.year=this.parent.maskPlaceholder.year),this.parent.maskPlaceholder.hour&&(this.defaultConstant.hour=this.parent.maskPlaceholder.hour),this.parent.maskPlaceholder.minute&&(this.defaultConstant.minute=this.parent.maskPlaceholder.minute),this.parent.maskPlaceholder.second&&(this.defaultConstant.second=this.parent.maskPlaceholder.second),this.parent.maskPlaceholder.dayOfTheWeek&&(this.defaultConstant.dayOfTheWeek=this.parent.maskPlaceholder.dayOfTheWeek.toString()),this.getCUltureMaskFormat();var t=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.mask=this.previousValue=t,this.parent.maskedDateValue=this.mask,this.parent.value&&(this.navigated=!0,this.isBlur=s.isBlur,this.setDynamicValue())},u.prototype.getCUltureMaskFormat=function(){this.l10n=new e.Wo(this.parent.moduleName,this.defaultConstant,this.parent.locale),this.objectString=Object.keys(this.defaultConstant);for(var s=0;s<this.objectString.length;s++)this.defaultConstant[this.objectString[s].toString()]=this.l10n.getConstant(this.objectString[s].toString())},u.prototype.validCharacterCheck=function(){var s=this.parent.inputElement.selectionStart;"timepicker"!==this.parent.moduleName&&s===this.hiddenMask.length&&this.mask===this.parent.inputElement.value&&(s=0);for(var t=s,i=s-1;t<this.hiddenMask.length||i>=0;t++,i--){if(t<this.hiddenMask.length&&-1!==this.validCharacters.indexOf(this.hiddenMask[t]))return void this.setSelection(this.hiddenMask[t]);if(i>=0&&-1!==this.validCharacters.indexOf(this.hiddenMask[i]))return void this.setSelection(this.hiddenMask[i])}},u.prototype.setDynamicValue=function(){this.maskDateValue=new Date(+this.parent.value),this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!0,this.updateValue(),this.isBlur||this.validCharacterCheck()},u.prototype.setSelection=function(s){for(var t=-1,i=0,a=0;a<this.hiddenMask.length;a++)this.hiddenMask[a]===s&&(i=a+1,-1===t&&(t=a));t<0&&(t=0),this.parent.inputElement.setSelectionRange(t,i)},u.prototype.maskKeydownHandler=function(s){if("Backspace"!==s.e.key)if(this.dayTypeCount=this.monthTypeCount=this.hourTypeCount=this.minuteTypeCount=this.secondTypeCount=0,"Delete"!==s.e.key){if(!(s.e.altKey||s.e.ctrlKey||s.e.key!==hi&&"ArrowRight"!==s.e.key&&s.e.key!==Tt&&"Tab"!==s.e.key&&s.e.action!==Tt&&s.e.key!==jt&&"Home"!==s.e.key)){var a=this.parent.inputElement.selectionEnd,n=this.parent.inputElement.value.length;0!==(t=this.parent.inputElement.selectionStart)||a!==n||"Tab"!==s.e.key&&s.e.action!==Tt||(this.parent.inputElement.selectionStart=this.parent.inputElement.selectionEnd=s.e.action===Tt?a:0),s.e.key!==jt&&"Home"!==s.e.key||(this.parent.inputElement.selectionStart=this.parent.inputElement.selectionEnd=s.e.key===jt?n:0),this.navigateSelection(s.e.key===hi||s.e.action===Tt||s.e.key===jt)}if(!(s.e.altKey||s.e.ctrlKey||"ArrowUp"!==s.e.key&&s.e.key!==oi)){i="",-1!==this.validCharacters.indexOf(this.hiddenMask[t=this.parent.inputElement.selectionStart])&&(i=this.hiddenMask[t]),this.dateAlteration(s.e.key===oi);var h=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=h,this.parent.inputElement.value=h;for(var o=0;o<this.hiddenMask.length;o++)if(i===this.hiddenMask[o]){t=o;break}this.parent.inputElement.selectionStart=t,this.validCharacterCheck()}}else this.isDeleteKey=!0;else{var t,i="";switch(-1!==this.validCharacters.indexOf(this.hiddenMask[t=this.parent.inputElement.selectionStart])&&(i=this.hiddenMask[t]),i){case"d":this.dayTypeCount=Math.max(this.dayTypeCount-1,0);break;case"M":this.monthTypeCount=Math.max(this.monthTypeCount-1,0);break;case"H":case"h":this.hourTypeCount=Math.max(this.hourTypeCount-1,0);break;case"m":this.minuteTypeCount=Math.max(this.minuteTypeCount-1,0);break;case"s":this.secondTypeCount=Math.max(this.secondTypeCount-1,0)}}},u.prototype.isPersist=function(){return this.parent.isFocused||this.navigated},u.prototype.differenceCheck=function(){var s=this.parent.inputElement.selectionStart,t=this.parent.inputElement.value,i=this.previousValue.substring(0,s+this.previousValue.length-t.length),a=t.substring(0,s),n=new Date(+this.maskDateValue),l=new Date(n.getFullYear(),n.getMonth()+1,0).getDate();if(0===i.indexOf(a)&&(0===a.length||this.previousHiddenMask[a.length-1]!==this.previousHiddenMask[a.length])){for(var r=a.length;r<i.length;r++)""!==this.previousHiddenMask[r]&&this.validCharacters.indexOf(this.previousHiddenMask[r])>=0&&(this.isDeletion=this.handleDeletion(this.previousHiddenMask[r],!1));if(this.isDeletion)return}switch(this.previousHiddenMask[s-1]){case"d":var h=(this.isDayPart&&n.getDate().toString().length<2&&!this.isPersist()?10*n.getDate():0)+parseInt(a[s-1],10);if(this.isDateZero="0"===a[s-1],this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(h))return;for(r=0;h>l;r++)h=parseInt(h.toString().slice(1),10);if(h>=1){if(n.setDate(h),this.isNavigate=2===h.toString().length,this.previousDate=new Date(n.getFullYear(),n.getMonth(),n.getDate()),n.getMonth()!==this.maskDateValue.getMonth())return;this.isDayPart=!0,this.dayTypeCount=this.dayTypeCount+1}else this.isDayPart=!1,this.dayTypeCount=this.isDateZero?this.dayTypeCount+1:this.dayTypeCount;break;case"M":var o=void 0;if(o=n.getMonth().toString().length<2&&!this.isPersist()?(this.isMonthPart?10*(n.getMonth()+1):0)+parseInt(a[s-1],10):parseInt(a[s-1],10),this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,this.isMonthZero="0"===a[s-1],isNaN(o)){var f=this.getCulturedValue("months[stand-alone].wide"),g=Object.keys(f);for(this.monthCharacter+=a[s-1].toLowerCase();this.monthCharacter.length>0;){r=1;for(var d=0,y=g;d<y.length;d++){if(0===f[r].toLowerCase().indexOf(this.monthCharacter))return n.setMonth(r-1),this.isMonthPart=!0,void(this.maskDateValue=n);r++}this.monthCharacter=this.monthCharacter.substring(1,this.monthCharacter.length)}}else{for(;o>12;)o=parseInt(o.toString().slice(1),10);if(o>=1){if(n.setMonth(o-1),o>=10||1===o?this.isLeadingZero&&1===o?(this.isNavigate=1===o.toString().length,this.isLeadingZero=!1):this.isNavigate=2===o.toString().length:this.isNavigate=1===o.toString().length,n.getMonth()!==o-1&&(n.setDate(1),n.setMonth(o-1)),this.isDayPart){var p=new Date(this.previousDate.getFullYear(),this.previousDate.getMonth()+1,0).getDate(),c=new Date(n.getFullYear(),n.getMonth()+1,0).getDate();this.previousDate.getDate()===p&&c<=p&&n.setDate(c)}this.previousDate=new Date(n.getFullYear(),n.getMonth(),n.getDate()),this.isMonthPart=!0,this.monthTypeCount=this.monthTypeCount+1,this.isLeadingZero=!1}else n.setMonth(0),this.isLeadingZero=!0,this.isMonthPart=!1,this.monthTypeCount=this.isMonthZero?this.monthTypeCount+1:this.monthTypeCount}break;case"y":var m=(this.isYearPart&&n.getFullYear().toString().length<4&&!this.isShortYear?10*n.getFullYear():0)+parseInt(a[s-1],10),A=(this.dateformat.match(/y/g)||[]).length;if(A=2!==A?4:A,this.isShortYear=!1,this.isYearZero="0"===a[s-1],isNaN(m))return;for(;m>9999;)m=parseInt(m.toString().slice(1),10);m<1?this.isYearPart=!1:(n.setFullYear(m),m.toString().length===A&&(this.isNavigate=!0),this.previousDate=new Date(n.getFullYear(),n.getMonth(),n.getDate()),this.isYearPart=!0);break;case"h":if(this.hour=(this.isHourPart&&(n.getHours()%12||12).toString().length<2&&!this.isPersist()?10*(n.getHours()%12||12):0)+parseInt(a[s-1],10),this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(this.hour))return;for(;this.hour>12;)this.hour=parseInt(this.hour.toString().slice(1),10);n.setHours(12*Math.floor(n.getHours()/12)+this.hour%12),this.isNavigate=2===this.hour.toString().length,this.isHourPart=!0,this.hourTypeCount=this.hourTypeCount+1;break;case"H":if(this.hour=(this.isHourPart&&n.getHours().toString().length<2&&!this.isPersist()?10*n.getHours():0)+parseInt(a[s-1],10),this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(this.hour))return;for(r=0;this.hour>23;r++)this.hour=parseInt(this.hour.toString().slice(1),10);n.setHours(this.hour),this.isNavigate=2===this.hour.toString().length,this.isHourPart=!0,this.hourTypeCount=this.hourTypeCount+1;break;case"m":var M=(this.isMinutePart&&n.getMinutes().toString().length<2&&!this.isPersist()?10*n.getMinutes():0)+parseInt(a[s-1],10);if(this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(M))return;for(r=0;M>59;r++)M=parseInt(M.toString().slice(1),10);n.setMinutes(M),this.isNavigate=2===M.toString().length,this.isMinutePart=!0,this.minuteTypeCount=this.minuteTypeCount+1;break;case"s":var V=(this.isSecondsPart&&n.getSeconds().toString().length<2&&!this.isPersist()?10*n.getSeconds():0)+parseInt(a[s-1],10);if(this.parent.isFocused=!this.parent.isFocused&&this.parent.isFocused,this.navigated=!this.navigated&&this.navigated,isNaN(V))return;for(r=0;V>59;r++)V=parseInt(V.toString().slice(1),10);n.setSeconds(V),this.isNavigate=2===V.toString().length,this.isSecondsPart=!0,this.secondTypeCount=this.secondTypeCount+1;break;case"a":this.periodCharacter+=a[s-1].toLowerCase();var W=this.getCulturedValue("dayPeriods.format.wide"),w=Object.keys(W);for(r=0;this.periodCharacter.length>0;r++)(0===W[w[0]].toLowerCase().indexOf(this.periodCharacter)&&n.getHours()>=12||0===W[w[1]].toLowerCase().indexOf(this.periodCharacter)&&n.getHours()<12)&&(n.setHours((n.getHours()+12)%24),this.maskDateValue=n),this.periodCharacter=this.periodCharacter.substring(1,this.periodCharacter.length)}this.maskDateValue=n},u.prototype.formatCheck=function(){var s=this;return function t(i){var a,d,n=s.getCulturedValue("days[stand-alone].abbreviated"),l=Object.keys(n),r=s.getCulturedValue("days[stand-alone].wide"),h=Object.keys(r),o=s.getCulturedValue("days[stand-alone].narrow"),p=Object.keys(o),c=s.getCulturedValue("months[stand-alone].abbreviated"),f=s.getCulturedValue("months[stand-alone].wide"),g=s.getCulturedValue("dayPeriods.format.wide");switch(i){case"ddd":case"dddd":case"d":a=s.isDayPart?s.maskDateValue.getDate().toString():s.defaultConstant.day.toString(),a=s.zeroCheck(s.isDateZero,s.isDayPart,a),2===s.dayTypeCount&&(s.isNavigate=!0,s.dayTypeCount=0);break;case"dd":a=s.isDayPart?s.roundOff(s.maskDateValue.getDate(),2):s.defaultConstant.day.toString(),a=s.zeroCheck(s.isDateZero,s.isDayPart,a),2===s.dayTypeCount&&(s.isNavigate=!0,s.dayTypeCount=0);break;case"E":case"EE":case"EEE":a=s.isDayPart&&s.isMonthPart&&s.isYearPart?n[l[s.maskDateValue.getDay()]].toString():s.defaultConstant.dayOfTheWeek.toString();break;case"EEEE":a=s.isDayPart&&s.isMonthPart&&s.isYearPart?r[h[s.maskDateValue.getDay()]].toString():s.defaultConstant.dayOfTheWeek.toString();break;case"EEEEE":a=s.isDayPart&&s.isMonthPart&&s.isYearPart?o[p[s.maskDateValue.getDay()]].toString():s.defaultConstant.dayOfTheWeek.toString();break;case"M":a=s.isMonthPart?(s.maskDateValue.getMonth()+1).toString():s.defaultConstant.month.toString(),a=s.zeroCheck(s.isMonthZero,s.isMonthPart,a),2===s.monthTypeCount&&(s.isNavigate=!0,s.monthTypeCount=0);break;case"MM":a=s.isMonthPart?s.roundOff(s.maskDateValue.getMonth()+1,2):s.defaultConstant.month.toString(),a=s.zeroCheck(s.isMonthZero,s.isMonthPart,a),2===s.monthTypeCount&&(s.isNavigate=!0,s.monthTypeCount=0);break;case"MMM":a=s.isMonthPart?c[s.maskDateValue.getMonth()+1]:s.defaultConstant.month.toString();break;case"MMMM":a=s.isMonthPart?f[s.maskDateValue.getMonth()+1]:s.defaultConstant.month.toString();break;case"yy":a=s.isYearPart?s.roundOff(s.maskDateValue.getFullYear()%100,2):s.defaultConstant.year.toString(),a=s.zeroCheck(s.isYearZero,s.isYearPart,a);break;case"y":case"yyy":case"yyyy":a=s.isYearPart?s.roundOff(s.maskDateValue.getFullYear(),4):s.defaultConstant.year.toString(),a=s.zeroCheck(s.isYearZero,s.isYearPart,a);break;case"h":a=s.isHourPart?(s.maskDateValue.getHours()%12||12).toString():s.defaultConstant.hour.toString(),2===s.hourTypeCount&&(s.isNavigate=!0,s.hourTypeCount=0);break;case"hh":a=s.isHourPart?s.roundOff(s.maskDateValue.getHours()%12||12,2):s.defaultConstant.hour.toString(),2===s.hourTypeCount&&(s.isNavigate=!0,s.hourTypeCount=0);break;case"H":a=s.isHourPart?s.maskDateValue.getHours().toString():s.defaultConstant.hour.toString(),2===s.hourTypeCount&&(s.isNavigate=!0,s.hourTypeCount=0);break;case"HH":a=s.isHourPart?s.roundOff(s.maskDateValue.getHours(),2):s.defaultConstant.hour.toString(),2===s.hourTypeCount&&(s.isNavigate=!0,s.hourTypeCount=0);break;case"m":a=s.isMinutePart?s.maskDateValue.getMinutes().toString():s.defaultConstant.minute.toString(),2===s.minuteTypeCount&&(s.isNavigate=!0,s.minuteTypeCount=0);break;case"mm":a=s.isMinutePart?s.roundOff(s.maskDateValue.getMinutes(),2):s.defaultConstant.minute.toString(),2===s.minuteTypeCount&&(s.isNavigate=!0,s.minuteTypeCount=0);break;case"s":a=s.isSecondsPart?s.maskDateValue.getSeconds().toString():s.defaultConstant.second.toString(),2===s.secondTypeCount&&(s.isNavigate=!0,s.secondTypeCount=0);break;case"ss":a=s.isSecondsPart?s.roundOff(s.maskDateValue.getSeconds(),2):s.defaultConstant.second.toString(),2===s.secondTypeCount&&(s.isNavigate=!0,s.secondTypeCount=0);break;case"f":a=Math.floor(s.maskDateValue.getMilliseconds()/100).toString();break;case"ff":d=s.maskDateValue.getMilliseconds(),s.maskDateValue.getMilliseconds()>99&&(d=Math.floor(s.maskDateValue.getMilliseconds()/10)),a=s.roundOff(d,2);break;case"fff":a=s.roundOff(s.maskDateValue.getMilliseconds(),3);break;case"a":case"aa":a=s.maskDateValue.getHours()<12?g.am:g.pm;break;case"z":case"zz":case"zzz":case"zzzz":a=s.parent.globalize.formatDate(s.maskDateValue,{format:i,type:"dateTime",skeleton:"yMd",calendar:s.parent.calendarMode})}if(a=void 0!==a?a:i.slice(1,i.length-1),s.isHiddenMask){for(var b="",m=0;m<a.length;m++)b+=i[0];return b}return a}},u.prototype.isValidDate=function(s){var t=new Date(s);return!isNaN(t.getTime())},u.prototype.maskPasteInputHandler=function(){if(this.isValidDate(this.parent.inputElement.value))return this.maskDateValue=new Date(this.parent.inputElement.value),this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!0,this.updateValue(),void(this.isBlur||this.validCharacterCheck());this.maskInputHandler()},u.prototype.maskInputHandler=function(){var s=this.parent.inputElement.selectionStart,t="";-1!==this.validCharacters.indexOf(this.hiddenMask[s])&&(t=this.hiddenMask[s]),this.differenceCheck();var i=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isDateZero=this.isMonthZero=this.isYearZero=!1,this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=i,this.parent.inputElement.value=i;for(var a=0;a<this.hiddenMask.length;a++)if(t===this.hiddenMask[a]){s=a;break}if(this.parent.inputElement.selectionStart=s,this.validCharacterCheck(),(this.isNavigate||this.isDeletion)&&!this.isDeleteKey){var n=!this.isNavigate;this.isNavigate=this.isDeletion=!1,this.navigateSelection(n)}this.isDeleteKey&&(this.isDeletion=!1),this.isDeleteKey=!1},u.prototype.navigateSelection=function(s){var a=s?this.parent.inputElement.selectionStart-1:this.parent.inputElement.selectionEnd;for(this.navigated=!0;a<this.hiddenMask.length&&a>=0;){if(this.validCharacters.indexOf(this.hiddenMask[a])>=0){this.setSelection(this.hiddenMask[a]);break}a+=s?-1:1}},u.prototype.roundOff=function(s,t){for(var i=s.toString(),a=t-i.length,n="",l=0;l<a;l++)n+="0";return n+i},u.prototype.zeroCheck=function(s,t,i){var a=i;return s&&!t&&(a="0"),a},u.prototype.handleDeletion=function(s,t){switch(s){case"d":this.isDayPart=t;break;case"M":this.isMonthPart=t,t||(this.maskDateValue.setMonth(0),this.monthCharacter="");break;case"y":this.isYearPart=t;break;case"H":case"h":this.isHourPart=t,t||(this.periodCharacter="");break;case"m":this.isMinutePart=t;break;case"s":this.isSecondsPart=t;break;default:return!1}return!0},u.prototype.dateAlteration=function(s){var t=this.parent.inputElement.selectionStart,i="";if(-1!==this.validCharacters.indexOf(this.hiddenMask[t])){i=this.hiddenMask[t];var a=new Date(this.maskDateValue.getFullYear(),this.maskDateValue.getMonth(),this.maskDateValue.getDate(),this.maskDateValue.getHours(),this.maskDateValue.getMinutes(),this.maskDateValue.getSeconds());this.previousDate=new Date(this.maskDateValue.getFullYear(),this.maskDateValue.getMonth(),this.maskDateValue.getDate(),this.maskDateValue.getHours(),this.maskDateValue.getMinutes(),this.maskDateValue.getSeconds());var n=s?-1:1;switch(i){case"d":a.setDate(a.getDate()+n);break;case"M":var l=a.getMonth()+n;if(a.setDate(1),a.setMonth(l),this.isDayPart){var r=new Date(this.previousDate.getFullYear(),this.previousDate.getMonth()+1,0).getDate(),h=new Date(a.getFullYear(),a.getMonth()+1,0).getDate();this.previousDate.getDate()===r&&h<=r?a.setDate(h):a.setDate(this.previousDate.getDate())}else a.setDate(this.previousDate.getDate());this.previousDate=new Date(a.getFullYear(),a.getMonth(),a.getDate());break;case"y":a.setFullYear(a.getFullYear()+n);break;case"H":case"h":a.setHours(a.getHours()+n);break;case"m":a.setMinutes(a.getMinutes()+n);break;case"s":a.setSeconds(a.getSeconds()+n);break;case"a":a.setHours((a.getHours()+12)%24)}this.maskDateValue=a.getFullYear()>0?a:this.maskDateValue,-1!==this.validCharacters.indexOf(this.hiddenMask[t])&&this.handleDeletion(this.hiddenMask[t],!0)}},u.prototype.getCulturedValue=function(s){var t=this.parent.locale;return"en"===t||"en-US"===t?(0,e._W)(s,(0,e.P)()):(0,e._W)("main."+t+".dates.calendars.gregorian."+s,e.pE)},u.prototype.getCulturedFormat=function(){var s=this.getCulturedValue("dateTimeFormats[availableFormats].yMd").toString();return"datepicker"===this.parent.moduleName&&(s=this.getCulturedValue("dateTimeFormats[availableFormats].yMd").toString(),this.parent.format&&this.parent.formatString&&(s=this.parent.formatString)),"datetimepicker"===this.parent.moduleName&&(s=this.getCulturedValue("dateTimeFormats[availableFormats].yMd").toString(),this.parent.dateTimeFormat&&(s=this.parent.dateTimeFormat)),"timepicker"===this.parent.moduleName&&(s=this.parent.cldrTimeFormat()),s},u.prototype.clearHandler=function(){this.isDayPart=this.isMonthPart=this.isYearPart=this.isHourPart=this.isMinutePart=this.isSecondsPart=!1,this.updateValue()},u.prototype.updateValue=function(){this.monthCharacter=this.periodCharacter="";var s=this.dateformat.replace(this.formatRegex,this.formatCheck());this.isHiddenMask=!0,this.hiddenMask=this.dateformat.replace(this.formatRegex,this.formatCheck()),this.isHiddenMask=!1,this.previousHiddenMask=this.hiddenMask,this.previousValue=s,this.parent.updateInputValue(s)},u.prototype.destroy=function(){this.removeEventListener()},u}()}}]);