"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9717],{3202:(E,M,l)=>{l.d(M,{k:()=>A});var e=l(9842),t=l(4438),v=l(9417),O=l(3924),P=l(8192);function b(d,u){if(1&d&&(t.j41(0,"label",5),t.EFF(1),t.k0s()),2&d){const _=t.XpG();t.AVh("required",_.isRequired),t.R7$(),t.JRh(_.label)}}function x(d,u){1&d&&t.nrm(0,"app-inno-error-message",4)}let A=(()=>{var d;class u{constructor(){(0,e.A)(this,"isRequired",void 0),(0,e.A)(this,"label",""),(0,e.A)(this,"mode","day"),(0,e.A)(this,"id",void 0),(0,e.A)(this,"enableMask",void 0),(0,e.A)(this,"name",void 0),(0,e.A)(this,"format","dd-MM-yyyy"),(0,e.A)(this,"placeholder",void 0),(0,e.A)(this,"value",void 0),(0,e.A)(this,"errorMessages",void 0),(0,e.A)(this,"formControl",void 0),(0,e.A)(this,"onChange",new t.bkB),(0,e.A)(this,"datePickerValue",void 0),(0,e.A)(this,"fields",{text:"label",value:"value"}),(0,e.A)(this,"start","Month"),(0,e.A)(this,"depth","Month"),(0,e.A)(this,"showTodayButton",!0),(0,e.A)(this,"weekNumber",!1),(0,e.A)(this,"invalidDate",!1)}registerOnChange(n){}registerOnTouched(n){}setDisabledState(n){}writeValue(n){}ngOnChanges(n){const i=n.mode?.currentValue??n.value?.currentValue;i&&this.updateModeSettings(i)}updateModeSettings(n){switch(n){case"day":this.start="Month",this.depth="Month",this.showTodayButton=!0,this.weekNumber=!1;break;case"week":this.start="Month",this.depth="Month",this.showTodayButton=!1,this.weekNumber=!0;break;case"month":this.start="Year",this.depth="Year",this.showTodayButton=!1,this.weekNumber=!1}}touchControl(){this.formControl&&(this.formControl.markAsDirty(),this.formControl.markAsTouched())}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const n in this.formControl.errors)if(this.errorMessages[n])return this.errorMessages[n];return""}onBlur(n){n.model.inputWrapper.container.classList.contains("e-error")?(this.invalidDate=!0,this.formControl&&this.formControl.setValue(n.model.inputElement.value),this.onChange.emit(n.model.inputElement.value)):this.invalidDate=!1}handleChangeValue(n){this.touchControl();const i=n?.value??void 0;this.onChange.emit(i),this.formControl&&this.formControl.setValue(i)}}return d=u,(0,e.A)(u,"\u0275fac",function(n){return new(n||d)}),(0,e.A)(u,"\u0275cmp",t.VBU({type:d,selectors:[["app-inno-form-datepicker"]],inputs:{isRequired:"isRequired",label:"label",mode:"mode",id:"id",enableMask:"enableMask",name:"name",format:"format",placeholder:"placeholder",value:"value",errorMessages:"errorMessages",formControl:"formControl"},outputs:{onChange:"onChange"},standalone:!0,features:[t.Jv_([{provide:v.kq,useExisting:(0,t.Rfq)(()=>d),multi:!0}]),t.OA$,t.aNF],decls:5,vars:15,consts:[[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],[1,"customDatePickerV2",3,"blur","change","name","id","format","enableMask","placeholder","value","start","depth","showTodayButton","weekNumber"],[3,"message"],["message","Invalid Date"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"]],template:function(n,i){1&n&&(t.j41(0,"div",0),t.DNE(1,b,2,3,"label",1),t.j41(2,"ejs-datepicker",2),t.bIt("blur",function(C){return i.onBlur(C)})("change",function(C){return i.handleChangeValue(C)}),t.k0s(),t.nrm(3,"app-inno-error-message",3),t.DNE(4,x,1,0,"app-inno-error-message",4),t.k0s()),2&n&&(t.AVh("error",i.hasError()),t.R7$(),t.vxM(i.label?1:-1),t.R7$(),t.FS9("name",i.name||""),t.FS9("id",i.id||""),t.Y8G("format",i.format)("enableMask",i.enableMask)("placeholder",i.placeholder)("value",i.value)("start",i.start)("depth",i.depth)("showTodayButton",i.showTodayButton)("weekNumber",i.weekNumber),t.R7$(),t.Y8G("message",i.getErrorMessage()),t.R7$(),t.vxM(i.invalidDate?4:-1))},dependencies:[O.tZ,O.I,P.Y],styles:[".customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{margin:0!important;padding:8px 12px;display:flex;min-width:125px!important;height:40px!important;border-radius:8px}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{background-color:var(--bg-primary)}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%] > input[_ngcontent-%COMP%]{padding:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:after{display:none}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group-icon.e-date-icon[_ngcontent-%COMP%]{margin:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons[_ngcontent-%COMP%]{min-height:unset!important}.customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%]::selection, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:after, .e-datepicker[_ngcontent-%COMP%]   .e-focused-date.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-cell.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%]{background-color:#0f182e!important}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons.e-active[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:not(.e-focused-date)   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]{color:#0f182e!important}.e-datepicker[_ngcontent-%COMP%]   .e-model-header[_ngcontent-%COMP%]{background-color:#fff!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{border:2px solid #E9EAEB!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:hover{background-color:#24242433!important}"]})),u})()},9248:(E,M,l)=>{l.d(M,{M:()=>g});var e=l(9842),t=l(4438),v=l(6146),O=l(8192),P=l(3652),b=l(5599),x=l(9417),A=l(177),d=l(5236);const u=p=>({"text-text-primary":p});function _(p,f){if(1&p&&(t.j41(0,"label",10),t.EFF(1),t.k0s()),2&p){const c=t.XpG();t.AVh("required",c.isRequired),t.R7$(),t.JRh(c.label)}}function n(p,f){if(1&p&&(t.j41(0,"p",16),t.EFF(1),t.k0s()),2&p){const c=t.XpG().$implicit;t.R7$(),t.SpI(" ",c.description," ")}}function i(p,f){if(1&p){const c=t.RV6();t.j41(0,"div",13),t.bIt("click",function(){const a=t.eBV(c).$implicit,r=t.XpG(2);return t.Njj(r.handleChooseOption(a))}),t.j41(1,"div",14)(2,"p",15),t.EFF(3),t.k0s(),t.DNE(4,n,2,1,"p",16),t.k0s()()}if(2&p){const c=f.$implicit,o=t.XpG(2);t.AVh("selected",c.value===o.value),t.R7$(3),t.SpI(" ",c.label," "),t.R7$(),t.vxM(c.description?4:-1)}}function s(p,f){if(1&p){const c=t.RV6();t.j41(0,"button",17),t.bIt("click",function(){t.eBV(c);const a=t.XpG(2);return t.Njj(a.handleCreateNew())}),t.nrm(1,"img",18),t.EFF(2),t.nI1(3,"translate"),t.k0s()}if(2&p){const c=t.XpG(2);t.R7$(2),t.Lme(" ",t.bMT(3,2,"COMMON.CreateNew"),' "',c.textSearch,'" ')}}function C(p,f){if(1&p){const c=t.RV6();t.j41(0,"div",11)(1,"app-inno-input-search-result",12),t.nI1(2,"translate"),t.bIt("onChange",function(a){t.eBV(c);const r=t.XpG();return t.Njj(r.handleSearch(a))}),t.DNE(3,i,5,4,"ng-template",null,1,t.C5r)(5,s,4,4,"ng-template",null,2,t.C5r),t.k0s()()}if(2&p){let c;const o=t.sdS(4),a=t.sdS(6),r=t.XpG();t.R7$(),t.Y8G("placeholder",t.bMT(2,7,"COMMON.Search"))("data",r.listOptionPreview)("isNotFound",!r.listOptionPreview.length)("isEmptyData",!r.listOptionOriginal.length)("isDisableSearch",r.isDisableSearch)("optionTemplate",null!==(c=r.customOptionTemplate)&&void 0!==c?c:o)("footerTemplate",r.isShowCreateButton?a:null)}}let g=(()=>{var p;class f{constructor(){(0,e.A)(this,"isRequired",void 0),(0,e.A)(this,"label",""),(0,e.A)(this,"options",[]),(0,e.A)(this,"placeholder",""),(0,e.A)(this,"value",""),(0,e.A)(this,"projectId",""),(0,e.A)(this,"isProjectClient",!1),(0,e.A)(this,"errorMessages",void 0),(0,e.A)(this,"formControl",void 0),(0,e.A)(this,"customOptionTemplate",null),(0,e.A)(this,"isDisableSearch",!1),(0,e.A)(this,"isForYear",!1),(0,e.A)(this,"onChange",new t.bkB),(0,e.A)(this,"onSelect",new t.bkB),(0,e.A)(this,"onCreateNew",new t.bkB),(0,e.A)(this,"textSearch",""),(0,e.A)(this,"clientName",""),(0,e.A)(this,"labelOfValueSelected",""),(0,e.A)(this,"listOptionPreview",[]),(0,e.A)(this,"listOptionOriginal",[]),(0,e.A)(this,"searchResultComponent",void 0)}registerOnChange(o){}registerOnTouched(o){}setDisabledState(o){}writeValue(o){}ngOnChanges(o){const a=o?.value?.currentValue??null,r=o?.projectId?.currentValue??null;a&&(this.labelOfValueSelected=this.listOptionOriginal.find(m=>m.value===a)?.label,this.clientName=this.listOptionOriginal.find(m=>m.value===a)?.label);const h=o?.options?.currentValue;h?.length&&(this.options=h,this.listOptionOriginal=this.options,this.listOptionPreview=this.listOptionOriginal,this.formControl?.value&&(this.value=this.formControl.value,this.labelOfValueSelected=this.listOptionOriginal.find(m=>m.value===this.value)?.label)),r?(this.clientName=this.listOptionOriginal.find(m=>m.value===r)?.metadata?.objectClient?.clientName,this.labelOfValueSelected=this.clientName+"-"+this.listOptionOriginal.find(m=>m.value===r)?.label):this.labelOfValueSelected=this.listOptionOriginal.find(m=>m.value===a)?.label,this.value&&!this.labelOfValueSelected&&(this.labelOfValueSelected=this.listOptionOriginal.find(m=>m.value===this.value)?.label)}get isShowCreateButton(){return this.onCreateNew?.observed&&this.textSearch.length&&(!this.listOptionOriginal?.length||!this.listOptionPreview?.length)}handleChange(o){this.onChange?.emit&&this.onChange.emit(o)}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const o in this.formControl.errors)if(this.errorMessages[o])return this.errorMessages[o];return""}handleSearch(o){if(this.isProjectClient){if(o=o?.trim()?.toLowerCase(),!o?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(r=>r.label.toLowerCase().indexOf(o)>-1);const a=[];this.listOptionPreview.forEach(r=>{let h=!1,m=!1;this.listOptionOriginal.filter(D=>"project"==D.metadata.type).forEach(D=>{if(r.value==D.metadata.objectClient.id)h||(a.push(r),h=!0),a.push(D);else{let T=this.listOptionPreview.find(k=>"client"==k.metadata?.type);if(!h&&!T){let k=this.listOptionOriginal.find(S=>S.metadata?.client?.id==r.metadata?.objectClient?.id),I=a.find(S=>S.value==r.metadata?.objectClient?.id);I||(m=!0,a.push(k)),(m||I)&&(a.push(r),h=!0)}}})}),this.listOptionPreview=a}else{if(o=o?.trim()?.toLowerCase(),this.textSearch=o,!o?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(r=>r.label.toLowerCase().indexOf(o)>-1);const a=[];this.listOptionPreview.forEach(r=>{a.push(r),this.listOptionOriginal.filter(h=>"project"==h.metadata?.type).forEach(h=>{r.value==h.metadata.objectClient.id&&a.push(h)})})}}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}touchControl(){this.formControl&&(this.formControl.markAsDirty(),this.formControl.markAsTouched())}handleChooseOption(o){o.value!=this.value&&(this.formControl&&this.formControl.setValue(o.value),this.labelOfValueSelected=o.label,this.value=o.value,this.onSelect.emit(o),this.handleCloseSearchResult())}callbackAfterHideSearchResult(){this.listOptionPreview=this.listOptionOriginal}handleCreateNew(){this.onCreateNew.emit(this.textSearch),this.handleCloseSearchResult()}}return p=f,(0,e.A)(f,"\u0275fac",function(o){return new(o||p)}),(0,e.A)(f,"\u0275cmp",t.VBU({type:p,selectors:[["app-inno-form-select-search"]],viewQuery:function(o,a){if(1&o&&t.GBs(b.x,5),2&o){let r;t.mGM(r=t.lsd())&&(a.searchResultComponent=r.first)}},inputs:{isRequired:"isRequired",label:"label",options:"options",placeholder:"placeholder",value:"value",projectId:"projectId",isProjectClient:"isProjectClient",errorMessages:"errorMessages",formControl:"formControl",customOptionTemplate:"customOptionTemplate",isDisableSearch:"isDisableSearch",isForYear:"isForYear"},outputs:{onChange:"onChange",onSelect:"onSelect",onCreateNew:"onCreateNew"},standalone:!0,features:[t.Jv_([{provide:x.kq,useExisting:(0,t.Rfq)(()=>p),multi:!0}]),t.OA$,t.aNF],decls:10,vars:9,consts:[["templateSearchProject",""],["optionTemplate",""],["buttonCreateNew",""],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],["position","bottom-start",3,"onOpen","onClose","content","isClickOnContentToClose","isClearPadding"],["type","button","target","",1,"dropdown-md","w-full"],[1,"w-full","text-left","line-clamp-1","text-text-placeholder-slight",3,"ngClass"],["src","../../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"shrink-0"],[3,"message"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","max-w-[90dvw]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isDisableSearch","optionTemplate","footerTemplate"],[1,"w-full","flex","p-[8px]","my-1","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-teapp-inno-input-search-resultxt-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(o,a){if(1&o){const r=t.RV6();t.j41(0,"div",3),t.DNE(1,_,2,3,"label",4),t.j41(2,"app-inno-popover",5),t.bIt("onOpen",function(){return t.eBV(r),t.Njj(a.touchControl())})("onClose",function(){return t.eBV(r),t.Njj(a.callbackAfterHideSearchResult())}),t.j41(3,"button",6)(4,"div",7),t.EFF(5),t.k0s(),t.nrm(6,"img",8),t.k0s()(),t.DNE(7,C,7,9,"ng-template",null,0,t.C5r),t.nrm(9,"app-inno-error-message",9),t.k0s()}if(2&o){const r=t.sdS(8);t.R7$(),t.vxM(a.label?1:-1),t.R7$(),t.Y8G("content",r)("isClickOnContentToClose",!1)("isClearPadding",!0),t.R7$(2),t.Y8G("ngClass",t.eq3(7,u,a.labelOfValueSelected)),t.R7$(),t.SpI(" ",a.labelOfValueSelected||a.placeholder," "),t.R7$(4),t.Y8G("message",a.getErrorMessage())}},dependencies:[v.G,A.YU,d.D9,b.x,P.t,O.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}']})),f})()},344:(E,M,l)=>{l.d(M,{k:()=>_});var e=l(9842),t=l(4438),v=l(6146),O=l(177),P=l(5236);function b(n,i){if(1&n&&t.eu8(0,1),2&n){const s=t.XpG();t.Y8G("ngTemplateOutlet",s.leftAction)}}function x(n,i){if(1&n&&t.eu8(0,1),2&n){const s=t.XpG();t.Y8G("ngTemplateOutlet",s.customCancelButton)}}function A(n,i){if(1&n){const s=t.RV6();t.j41(0,"button",5),t.bIt("click",function(){t.eBV(s);const g=t.XpG();return t.Njj(g.handleCancel())}),t.EFF(1),t.nI1(2,"translate"),t.k0s()}if(2&n){const s=t.XpG();t.HbH(s.classNameCancelButton),t.R7$(),t.SpI(" ",t.bMT(2,3,s.textCancel||"BUTTON.Cancel")," ")}}function d(n,i){if(1&n&&t.eu8(0,1),2&n){const s=t.XpG();t.Y8G("ngTemplateOutlet",s.customSubmitButton)}}function u(n,i){if(1&n){const s=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(s);const g=t.XpG();return t.Njj(g.handleSubmit())}),t.EFF(1),t.nI1(2,"translate"),t.k0s()}if(2&n){const s=t.XpG();t.HbH(s.classNameSubmitButton),t.Y8G("disabled",s.isDisableSubmit),t.R7$(),t.SpI(" ",t.bMT(2,4,s.textSubmit||"BUTTON.Save")," ")}}let _=(()=>{var n;class i{constructor(){(0,e.A)(this,"classNameSubmitButton",""),(0,e.A)(this,"classNameCancelButton",""),(0,e.A)(this,"leftAction",void 0),(0,e.A)(this,"customSubmitButton",void 0),(0,e.A)(this,"customCancelButton",void 0),(0,e.A)(this,"textSubmit",void 0),(0,e.A)(this,"textCancel",void 0),(0,e.A)(this,"idSubmit",""),(0,e.A)(this,"idCancel",""),(0,e.A)(this,"isDisableSubmit",!1),(0,e.A)(this,"onSubmit",new t.bkB),(0,e.A)(this,"onCancel",new t.bkB)}handleSubmit(){this.onSubmit.emit()}handleCancel(){this.onCancel.emit()}}return n=i,(0,e.A)(i,"\u0275fac",function(C){return new(C||n)}),(0,e.A)(i,"\u0275cmp",t.VBU({type:n,selectors:[["app-inno-modal-footer"]],inputs:{classNameSubmitButton:"classNameSubmitButton",classNameCancelButton:"classNameCancelButton",leftAction:"leftAction",customSubmitButton:"customSubmitButton",customCancelButton:"customCancelButton",textSubmit:"textSubmit",textCancel:"textCancel",idSubmit:"idSubmit",idCancel:"idCancel",isDisableSubmit:"isDisableSubmit"},outputs:{onSubmit:"onSubmit",onCancel:"onCancel"},standalone:!0,features:[t.aNF],decls:7,vars:3,consts:[[1,"w-full","p-[16px]","border-t","border-border-primary","flex","items-center","gap-[12px]"],[3,"ngTemplateOutlet"],[1,"ml-auto","flex","gap-[12px]","items-center"],["type","button",1,"button-outline","button-size-md",3,"class"],["type","button",1,"button-primary","button-size-md",3,"class","disabled"],["type","button",1,"button-outline","button-size-md",3,"click"],["type","button",1,"button-primary","button-size-md",3,"click","disabled"]],template:function(C,g){1&C&&(t.j41(0,"div",0),t.DNE(1,b,1,1,"ng-container",1),t.j41(2,"div",2),t.DNE(3,x,1,1,"ng-container",1)(4,A,3,5,"button",3)(5,d,1,1,"ng-container",1)(6,u,3,6,"button",4),t.k0s()()),2&C&&(t.R7$(),t.vxM(g.leftAction?1:-1),t.R7$(2),t.vxM(g.customCancelButton?3:g.onCancel.observers.length?4:-1),t.R7$(2),t.vxM(g.customSubmitButton?5:g.onSubmit.observers.length?6:-1))},dependencies:[v.G,O.T3,P.D9],styles:[".zipplexActionModal[_ngcontent-%COMP%]{width:100%;margin-top:16px}.leftAction[_ngcontent-%COMP%]:empty, .rightAction[_ngcontent-%COMP%]:empty{display:none!important}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%], .zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .leftAction[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%]{flex:1;display:flex;gap:8px}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{width:100%}"]})),i})()},4978:(E,M,l)=>{l.d(M,{I:()=>d});var e=l(9842),t=l(4438),v=l(6146),O=l(5236);const P=["*",[["","footer",""]]],b=["*","[footer]"];function x(u,_){if(1&u){const n=t.RV6();t.j41(0,"button",7),t.bIt("click",function(){t.eBV(n);const s=t.XpG(2);return t.Njj(s.handleClose())}),t.nrm(1,"img",8),t.k0s()}}function A(u,_){if(1&u&&(t.j41(0,"div",4)(1,"p",5),t.EFF(2),t.nI1(3,"translate"),t.k0s()(),t.DNE(4,x,2,0,"button",6)),2&u){const n=t.XpG();t.R7$(2),t.JRh(t.bMT(3,2,n.title)),t.R7$(2),t.vxM(n.onClose.observers.length?4:-1)}}let d=(()=>{var u;class _{constructor(){(0,e.A)(this,"title",void 0),(0,e.A)(this,"onClose",new t.bkB)}handleClose(){this.onClose.emit()}}return u=_,(0,e.A)(_,"\u0275fac",function(i){return new(i||u)}),(0,e.A)(_,"\u0275cmp",t.VBU({type:u,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[t.aNF],ngContentSelectors:b,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(i,s){1&i&&(t.NAR(P),t.j41(0,"div",0)(1,"div",1),t.DNE(2,A,5,4),t.k0s(),t.j41(3,"div",2),t.SdG(4),t.k0s(),t.j41(5,"div",3),t.SdG(6,1),t.k0s()()),2&i&&(t.R7$(2),t.vxM(s.title?2:-1))},dependencies:[v.G,O.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),_})()}}]);