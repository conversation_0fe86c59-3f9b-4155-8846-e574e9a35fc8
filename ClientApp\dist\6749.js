"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6749],{6749:(R,l,i)=>{i.r(l),i.d(l,{ReportComponent:()=>b});var a=i(9842),c=i(6146),e=i(4438),p=i(177);function d(t,n){1&t&&(e.j41(0,"div",6),e.EFF(1," \u2605 "),e.k0s())}let m=(()=>{var t;class n{constructor(){(0,a.A)(this,"card",void 0)}}return t=n,(0,a.A)(n,"\u0275fac",function(r){return new(r||t)}),(0,a.A)(n,"\u0275cmp",e.VBU({type:t,selectors:[["app-report-card"]],inputs:{card:"card"},standalone:!0,features:[e.aNF],decls:8,vars:3,consts:[[1,"p-4","bg-bg-primary","rounded-lg","shadow","relative","h-[180px]"],[1,"bg-bg-overlay-brand","rounded-full","w-12","h-12","flex","justify-center","items-center"],["alt","icon","src","../../../assets/img/svg/ic_Invoice.svg",1,"w-6","h-6"],[1,"text-base","font-normal"],[1,"text-sm","text-gray-500"],["class","absolute top-2 right-2 text-orange-500",4,"ngIf"],[1,"absolute","top-2","right-2","text-orange-500"]],template:function(r,s){1&r&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"img",2),e.k0s(),e.j41(3,"h3",3),e.EFF(4),e.k0s(),e.j41(5,"p",4),e.EFF(6),e.k0s(),e.DNE(7,d,2,0,"div",5),e.k0s()),2&r&&(e.R7$(4),e.JRh(s.card.title),e.R7$(2),e.JRh(s.card.description),e.R7$(),e.Y8G("ngIf",s.card.starred))},dependencies:[c.G,p.bT]})),n})();var u=i(5236);function f(t,n){if(1&t&&(e.j41(0,"div",6)(1,"div",7)(2,"div",8),e.nrm(3,"img",9),e.k0s()(),e.j41(4,"div",10),e.EFF(5),e.k0s()()),2&t){const o=n.$implicit;e.R7$(3),e.Mz_("src","../../../assets/img/svg/",o.icon,"",e.B4B),e.R7$(2),e.SpI(" ",o.label," ")}}function v(t,n){1&t&&e.nrm(0,"app-report-card",13),2&t&&e.Y8G("card",n.$implicit)}function g(t,n){if(1&t&&(e.j41(0,"h2",11),e.EFF(1),e.k0s(),e.j41(2,"div",12),e.Z7z(3,v,1,1,"app-report-card",13,e.fX1),e.k0s()),2&t){const o=n.$implicit;e.R7$(),e.JRh(o.title),e.R7$(2),e.Dyx(o.submenu)}}let b=(()=>{var t;class n{constructor(){(0,a.A)(this,"topNavItems",[{label:"Expense Report",icon:"ic_Report.svg"},{label:"Item Sales",icon:"ic_Sales.svg"},{label:"Accounts Aging",icon:"ic_Sales.svg"},{label:"Trial Balance",icon:"ic_Balance.svg"},{label:"Cash Flow",icon:"ic_Flow.svg"},{label:"Retainer Summary",icon:"ic_Summary.svg"}]),(0,a.A)(this,"MenuReport",[{title:"Invoice and Expense Reports",submenu:[{title:"Invoice Details",description:"Summary of all invoices sent...",icon:"fas fa-file-alt"},{title:"Expense Report",description:"Overview of your spending...",icon:"fas fa-receipt",starred:!0},{title:"Item Sales",description:"Breakdown of income...",icon:"fas fa-tag",starred:!0},{title:"Revenue by Client",description:"Track revenue per client...",icon:"fas fa-user"}]},{title:"Payment Reports",submenu:[{title:"Accounts Aging",description:"Identify clients with overdue...",icon:"fas fa-clock",starred:!0},{title:"Payments Collected",description:"List of payments received...",icon:"fas fa-money-check"},{title:"Credit Balance",description:"See available client balances...",icon:"fas fa-wallet"}]}])}}return t=n,(0,a.A)(n,"\u0275fac",function(r){return new(r||t)}),(0,a.A)(n,"\u0275cmp",e.VBU({type:t,selectors:[["app-report"]],standalone:!0,features:[e.aNF],decls:12,vars:3,consts:[[1,"w-full","pb-3"],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"p-6","bg-gray-50","min-h-screen"],[1,"flex","flex-wrap","justify-between","mb-6","gap-3"],[1,"flex","flex-col","items-center","rounded-md","overflow-hidden","w-[150px]","shadow","hover:shadow-md","cursor-pointer"],[1,"bg-bg-selected","w-full","flex","justify-center","py-6"],[1,"bg-bg-primary","rounded-full","p-3","w-16","h-16","shadow-sm"],["alt","icon menu",1,"shrink-0","w-8","h-8",3,"src"],[1,"bg-white","w-full","p-6","h-full","text-center","text-m","font-medium"],[1,"text-lg","font-bold","mb-4"],[1,"grid","grid-cols-1","md:grid-cols-2","lg:grid-cols-4","gap-4","mb-8"],[3,"card"]],template:function(r,s){1&r&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"p",3),e.EFF(4),e.nI1(5,"translate"),e.k0s()()()(),e.j41(6,"div",4)(7,"div",5),e.Z7z(8,f,6,3,"div",6,e.fX1),e.k0s(),e.Z7z(10,g,5,1,null,null,e.fX1),e.k0s()),2&r&&(e.R7$(4),e.SpI(" ",e.bMT(5,1,"REPORT.Title")," "),e.R7$(4),e.Dyx(s.topNavItems),e.R7$(2),e.Dyx(s.MenuReport))},dependencies:[c.G,u.D9,m]})),n})()}}]);