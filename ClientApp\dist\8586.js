"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8586],{3200:(O,b,a)=>{a.d(b,{J:()=>A});var l=a(9842),D=a(177),C=a(5236),d=a(4438);function S(E,u){if(1&E&&(d.j41(0,"p",4),d.<PERSON>(1),d.nI1(2,"translate"),d.k0s()),2&E){const f=d.XpG();d.R7$(),d.SpI(" ",d.bMT(2,1,f.description)," ")}}let A=(()=>{var E;class u{constructor(){(0,l.A)(this,"title",""),(0,l.A)(this,"description",""),(0,l.A)(this,"icon",""),(0,l.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return E=u,(0,l.A)(u,"\u0275fac",function(r){return new(r||E)}),(0,l.A)(u,"\u0275cmp",d.VBU({type:E,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[d.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(r,p){1&r&&(d.j41(0,"div",0),d.nrm(1,"img",1),d.j41(2,"div",2)(3,"p",3),d.EFF(4),d.nI1(5,"translate"),d.nI1(6,"translate"),d.k0s(),d.DNE(7,S,3,3,"p",4),d.k0s()()),2&r&&(d.R7$(),d.Y8G("src",p.icon||p.defaultIcon,d.B4B),d.R7$(3),d.SpI(" ",p.title?d.bMT(5,3,p.title):d.bMT(6,5,"COMMON.EmptyData")," "),d.R7$(3),d.vxM(p.description?7:-1))},dependencies:[D.MD,C.h,C.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),u})()},5936:(O,b,a)=>{a.d(b,{H:()=>A});var l=a(9842),D=a(1626),C=a(4438);const S=a(5312).c.HOST_API+"/api";let A=(()=>{var E;class u{constructor(){(0,l.A)(this,"http",(0,C.WQX)(D.Qq))}GetFile(r){return this.http.get(S+`/Images/GetFile?nameFile=${r}`,{responseType:"blob"})}GetFileURL(r){return this.http.get(S+`/Images/GetFileURL?nameFile=${r}`,{responseType:"blob"})}}return E=u,(0,l.A)(u,"\u0275fac",function(r){return new(r||E)}),(0,l.A)(u,"\u0275prov",C.jDH({token:E,factory:E.\u0275fac,providedIn:"root"})),u})()},2387:(O,b,a)=>{a.d(b,{I:()=>A});var l=a(467),D=a(9842),C=a(2716),d=a(7987),S=a(4438);let A=(()=>{var E;class u extends C.H{open(r){var p=this;return(0,l.A)(function*(){const m=yield Promise.all([a.e(1328),a.e(2076),a.e(4592)]).then(a.bind(a,4592));return p.matDialog.open(m.ModifyTaxesComponent.getComponent(),{panelClass:"custom_dialog",data:r,width:"500px",scrollStrategy:new d.t0})})()}}return E=u,(0,D.A)(u,"\u0275fac",(()=>{let f;return function(p){return(f||(f=S.xGo(E)))(p||E)}})()),(0,D.A)(u,"\u0275prov",S.jDH({token:E,factory:E.\u0275fac,providedIn:"root"})),u})()},5909:(O,b,a)=>{a.d(b,{R2:()=>C,Xj:()=>D,az:()=>E,jQ:()=>l,yo:()=>A});const l=(u,f)=>{if(!u||!f)return 0;const r=u.split(":").map(Number);let p=0,m=0,e=0;return 3===r.length?[p,m,e]=r:2===r.length?[p,m]=r:1===r.length&&([p]=r),Number(((p+m/60+e/3600)*f).toFixed(2))},D=(u=[],f=!1)=>(u.some(p=>p.companyTax||p.selected||void 0===p.selected)?u:u.filter(p=>p.selected)).map(p=>{let m,e;return p.companyTax?(m=p.companyTax.name,e=p.companyTax.amount):(m=p.name,e=p.amount),f?`${m} (${e}%)`:m}).filter(Boolean).sort((p,m)=>p.localeCompare(m)).join(", "),C=(u,f)=>u&&f?Number((u*f).toFixed(2)):0,A=u=>{let f=0,r={};u.forEach(({rate:m,qty:e,taxes:T})=>{if(!m||!e)return;const _=Number((m*e).toFixed(2));f+=_,T&&0!==T.length&&(T.some(I=>I.companyTaxId)?T.filter(I=>I.companyTaxId):T.filter(I=>I.selected)).forEach(I=>{const R=I.name||"Unknown Tax",k=I.taxeNumber||"",w=Number(I?.companyTax?.amount??I.amount??0);r[R]||(r[R]={name:R,numberTax:k,amount:w,taxableAmount:0,total:0}),r[R].taxableAmount+=_})});let p=0;return Object.values(r).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),p+=m.total}),{subtotal:Number(f.toFixed(2)),totalTaxes:r,grandTotalTax:Number(p.toFixed(2))}},E=u=>{let f=0,r={};u.forEach(({total:m,taxes:e})=>{m&&(f+=m,e&&0!==e.length)&&(e.some(v=>v.companyTaxId)?e.filter(v=>v.companyTaxId):e.filter(v=>v.selected)).forEach(v=>{const F=v.name||"Unknown Tax",I=v.taxeNumber||"",R=Number(v?.companyTax?.amount??v.amount??0);r[F]||(r[F]={name:F,numberTax:I,amount:R,taxableAmount:0,total:0}),r[F].taxableAmount+=m})});let p=0;return Object.values(r).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),p+=m.total}),{subtotal:Number(f.toFixed(2)),totalTaxes:r,grandTotalTax:Number(p.toFixed(2))}}},8599:(O,b,a)=>{a.r(b),a.d(b,{NewExpenseFormComponent:()=>ue});var l=a(9842),D=a(5936),C=a(4433),d=a(2928),S=a(6617),A=a(8556),E=a(1328),u=a(3492),f=a(301),r=a(1110),p=a(6146),m=a(177),e=a(4438),T=a(1970),_=a(9417),v=a(4006),F=a(9079),I=a(4978),R=a(344),k=a(9248),w=a(7086),U=a(6463),L=a(3202),Q=a(1456),z=a(1342),P=a(5909),Y=a(3705),G=a(5236),B=a(467),V=a(2716),W=a(7987);let K=(()=>{var i;class h extends V.H{open(t){var n=this;return(0,B.A)(function*(){const o=yield Promise.all([a.e(2076),a.e(2967)]).then(a.bind(a,2967));return n.matDialog.open(o.AddCategoryExpensesComponent.getComponent(),{data:t,width:"550px",disableClose:!0,scrollStrategy:new W.t0})})()}}return i=h,(0,l.A)(h,"\u0275fac",(()=>{let s;return function(n){return(s||(s=e.xGo(i)))(n||i)}})()),(0,l.A)(h,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),h})(),H=(()=>{var i;class h extends V.H{open(t){var n=this;return(0,B.A)(function*(){const o=yield a.e(7386).then(a.bind(a,7386));return n.matDialog.open(o.ModifyExpenseItemComponent.getComponent(),{panelClass:"custom_dialog",data:t,width:"450px",disableClose:!0,scrollStrategy:new W.t0})})()}}return i=h,(0,l.A)(h,"\u0275fac",(()=>{let s;return function(n){return(s||(s=e.xGo(i)))(n||i)}})()),(0,l.A)(h,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),h})();var J=a(2387);const Z=["selectSearchClientElement"],q=["selectSearchCategoryElement"],$=i=>({required:i}),ee=i=>({"pl-8":i}),te=i=>({"pl-10":i});function ne(i,h){if(1&i&&e.nrm(0,"ngx-avatars",35),2&i){const s=e.XpG().$implicit;e.Y8G("size",32)("name",s.label)}}function oe(i,h){1&i&&(e.j41(0,"div",36),e.nrm(1,"img",40),e.k0s())}function ae(i,h){if(1&i&&(e.j41(0,"p",39),e.EFF(1),e.k0s()),2&i){const s=e.XpG().$implicit;e.R7$(),e.SpI(" ",s.metadata.description," ")}}function se(i,h){if(1&i){const s=e.RV6();e.j41(0,"div",34),e.bIt("click",function(){const n=e.eBV(s).$implicit,o=e.XpG();return e.Njj(o.handleSelectProject(n))}),e.DNE(1,ne,1,2,"ngx-avatars",35)(2,oe,2,0,"div",36),e.j41(3,"div",37)(4,"p",38),e.EFF(5),e.k0s(),e.DNE(6,ae,2,1,"p",39),e.k0s()()}if(2&i){const s=h.$implicit,t=e.XpG();e.AVh("selected",s.value===t.f.projectId.value),e.Y8G("ngClass",e.eq3(6,ee,"project"==(null==s||null==s.metadata?null:s.metadata.type))),e.R7$(),e.vxM("client"==(null==s||null==s.metadata?null:s.metadata.type)?1:2),e.R7$(4),e.SpI(" ",s.label," "),e.R7$(),e.vxM(null!=s.metadata&&s.metadata.description?6:-1)}}function ie(i,h){1&i&&(e.j41(0,"div",36),e.nrm(1,"img",40),e.k0s())}function le(i,h){if(1&i&&e.nrm(0,"ngx-avatars",35),2&i){const s=e.XpG().$implicit;e.Y8G("size",32)("name",s.label)}}function re(i,h){if(1&i){const s=e.RV6();e.j41(0,"div",34),e.bIt("click",function(){const n=e.eBV(s).$implicit,o=e.XpG();return e.Njj(o.handleSelectCategory(n))}),e.j41(1,"div",41),e.DNE(2,ie,2,0,"div",36)(3,le,1,2,"ngx-avatars",35),e.j41(4,"p",42),e.EFF(5),e.k0s()()()}if(2&i){const s=h.$implicit,t=e.XpG();e.AVh("selected",s.value===t.f.categoryItemId.value),e.Y8G("ngClass",e.eq3(5,te,"childCategory"==(null==s||null==s.metadata?null:s.metadata.type))),e.R7$(2),e.vxM("childCategory"==(null==s||null==s.metadata?null:s.metadata.type)?2:3),e.R7$(3),e.SpI(" ",s.label," ")}}function pe(i,h){if(1&i){const s=e.RV6();e.j41(0,"p",49),e.bIt("click",function(){e.eBV(s);const n=e.XpG(),o=n.$implicit,c=n.$index,x=e.XpG();return e.Njj(x.handleModifyTaxes(null==o?null:o.taxes,c))}),e.EFF(1),e.k0s()}if(2&i){const s=e.XpG().$implicit,t=e.XpG();e.R7$(),e.SpI(" ",t.getNameSelectedTaxes(null==s?null:s.taxes)," ")}}function ce(i,h){if(1&i){const s=e.RV6();e.j41(0,"p",50),e.bIt("click",function(){e.eBV(s);const n=e.XpG(),o=n.$implicit,c=n.$index,x=e.XpG();return e.Njj(x.handleModifyTaxes(null==o?null:o.taxes,c))}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}2&i&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"EXPENSES.NEW_ADD_FORM.AddTaxes")," "))}function me(i,h){if(1&i){const s=e.RV6();e.j41(0,"div",17)(1,"p",43),e.EFF(2),e.k0s(),e.j41(3,"p",44),e.EFF(4),e.nI1(5,"formatNumber"),e.k0s(),e.j41(6,"p",44),e.EFF(7),e.nI1(8,"formatNumber"),e.k0s(),e.DNE(9,pe,2,1,"p",45)(10,ce,3,3,"p",46),e.j41(11,"p",47),e.EFF(12),e.nI1(13,"decimal"),e.nI1(14,"formatNumber"),e.k0s(),e.j41(15,"app-inno-table-action",48),e.bIt("onEdit",function(){const n=e.eBV(s),o=n.$implicit,c=n.$index,x=e.XpG();return e.Njj(x.handleModifyExpenseItem(c,o))})("onDelete",function(){const n=e.eBV(s).$index,o=e.XpG();return e.Njj(o.handleDeleteInvoiceItem(n))}),e.k0s()()}if(2&i){let s,t,n;const o=h.$implicit,c=e.XpG();e.R7$(2),e.SpI(" ",null!==(s=null==o?null:o.description)&&void 0!==s?s:""," "),e.R7$(2),e.SpI(" $",e.bMT(5,5,null!==(t=null==o?null:o.rate)&&void 0!==t?t:0)," "),e.R7$(3),e.SpI(" ",e.bMT(8,7,null!==(n=null==o?null:o.qty)&&void 0!==n?n:0)," "),e.R7$(2),e.vxM((null==o?null:o.taxes.length)>0&&""!=c.getNameSelectedTaxes(null==o?null:o.taxes)?9:10),e.R7$(3),e.SpI(" $",e.bMT(14,12,e.i5U(13,9,c.calculateTotalInvoiceItem(null==o?null:o.rate,null==o?null:o.qty),2))," ")}}function de(i,h){if(1&i){const s=e.RV6();e.j41(0,"div",28)(1,"div",51),e.bIt("click",function(){const n=e.eBV(s).$implicit,o=e.XpG();return e.Njj(o.handleDowload(n))}),e.nrm(2,"img",52),e.j41(3,"div",53)(4,"span",44),e.EFF(5),e.k0s(),e.j41(6,"span",54),e.EFF(7),e.nI1(8,"size"),e.k0s()()(),e.j41(9,"div",55)(10,"button",56),e.bIt("click",function(){const n=e.eBV(s).$index,o=e.XpG();return e.Njj(o.RemoveChoseFile(n))}),e.nrm(11,"img",57),e.k0s()()()}if(2&i){const s=h.$implicit,t=e.XpG();e.R7$(2),e.Y8G("src",t._storeService.getIconFile(s.type),e.B4B),e.R7$(3),e.JRh(s.filename),e.R7$(2),e.SpI("(",e.bMT(8,3,s.size),")")}}let ue=(()=>{var i;class h{static getComponent(){return h}constructor(t,n,o,c,x,g){(0,l.A)(this,"translate",void 0),(0,l.A)(this,"dialogRef",void 0),(0,l.A)(this,"addCategoryExpensesDialog",void 0),(0,l.A)(this,"modifyExpensesItemDialog",void 0),(0,l.A)(this,"modifyTaxesDialog",void 0),(0,l.A)(this,"data",void 0),(0,l.A)(this,"title","EXPENSES.NewExpenseButton"),(0,l.A)(this,"merchantOptions",[]),(0,l.A)(this,"projectAndClientOptions",[]),(0,l.A)(this,"categoryOptions",[]),(0,l.A)(this,"newpExpensesForm",void 0),(0,l.A)(this,"subtotal",0),(0,l.A)(this,"Attachment",[]),(0,l.A)(this,"totalAmount",0),(0,l.A)(this,"sumtax",0),(0,l.A)(this,"calculateTotalInvoiceItem",P.R2),(0,l.A)(this,"getNameSelectedTaxes",P.Xj),(0,l.A)(this,"_subscriptions",[]),(0,l.A)(this,"formBuilder",(0,e.WQX)(_.ze)),(0,l.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,l.A)(this,"_spinnerService",(0,e.WQX)(z.D)),(0,l.A)(this,"_toastService",(0,e.WQX)(u.f)),(0,l.A)(this,"_storeService",(0,e.WQX)(r.n)),(0,l.A)(this,"_authenticationService",(0,e.WQX)(d.k)),(0,l.A)(this,"_merchantService",(0,e.WQX)(f.A)),(0,l.A)(this,"dropdownOptionService",(0,e.WQX)(U.R)),(0,l.A)(this,"_expeneseService",(0,e.WQX)(Q.D)),(0,l.A)(this,"cdnService",(0,e.WQX)(D.H)),(0,l.A)(this,"selectSearchClientElement",void 0),(0,l.A)(this,"selectSearchCategoryElement",void 0),this.translate=t,this.dialogRef=n,this.addCategoryExpensesDialog=o,this.modifyExpensesItemDialog=c,this.modifyTaxesDialog=x,this.data=g,this.data&&(this.title="EXPENSES.UpdateExpense"),this.newpExpensesForm=this.formBuilder.group({expensesName:["",_.k0.compose([_.k0.required])],projectId:[""],clientId:[""],categoryId:[""],categoryItemId:["",_.k0.compose([_.k0.required])],merchantId:["",_.k0.compose([_.k0.required])],date:[null,_.k0.compose([_.k0.required])],paidAmount:[""],note:[""],filename:[""],attachments:[[]],itemExpenses:[[]],taxes:[[]],base64:[""]}),this.newpExpensesForm.get("itemExpenses")?.valueChanges.subscribe(M=>{this.subtotal=0,M?.forEach(y=>{const N=(0,P.R2)(y?.rate,y?.qty);this.subtotal+=N}),this.calculateAllTax()})}handleClose(){this.dialogRef.close()}calculateAllTax(){this.sumtax=0;const t=(0,P.yo)(this.f.itemExpenses.value);this.sumtax=t.grandTotalTax,this.totalAmount=this.subtotal+this.CheckIsNaN(this.sumtax)}CheckIsNaN(t){return isNaN(t)?0:t}reloadListMerchantOptions(){this.dropdownOptionService.getDropdownOptionsMerchant().then(t=>{this.merchantOptions=t})}reloadListCategoryOptions(){this.dropdownOptionService.getDropdownOptionsCategories().then(t=>{this.categoryOptions=t})}handleDataEdit(t){t.itemExpenses.forEach(n=>{n.taxes.forEach(o=>{o.selected=!0})}),this.f.expensesName.setValue(t?.expensesName),this.f.projectId.setValue(t?.projectId),this.f.clientId.setValue(t?.clientId),this.f.categoryId.setValue(t?.categoryId),this.f.categoryItemId.setValue(t?.categoryItemId),this.f.merchantId.setValue(t?.merchantId),this.f.date.setValue(t?.date),this.f.paidAmount.setValue(t?.paidAmount||0),this.f.note.setValue(t?.note),this.f.attachments.setValue(t?.attachments),this.f.itemExpenses.setValue(t?.itemExpenses),this.totalAmount=t?.paidAmount??0,this.Attachment=t?.attachments??[]}GetExpensesById(t){this._expeneseService.GetExpensesById(t).pipe((0,F.pQ)(this.destroyRef)).subscribe(n=>{n&&this.handleDataEdit(n)})}ngOnInit(){this.reloadListMerchantOptions(),Promise.all([this.dropdownOptionService.getDropdownOptionsCategories(),this.dropdownOptionService.getDropdownOptionsProjectAndClient()]).then(([t,n])=>{this.categoryOptions=t,this.projectAndClientOptions=n}),this.GetExpensesById(this.data)}get f(){return this.newpExpensesForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(t=>{t.markAsTouched()})}handleSubmit(){if(this.newpExpensesForm.invalid)return void this.markAllControlsAsTouched();this._spinnerService.show();const t={expensesName:this.f.expensesName.value,clientId:this.f.clientId.value,projectId:this.f.projectId.value,categoryId:this.f.categoryId.value,categoryItemId:this.f.categoryItemId.value,date:this.f.date.value,merchantId:this.f.merchantId.value,note:this.f.note.value,paidAmount:this.totalAmount,itemExpense:this.f.itemExpenses.value.map(n=>({...n,taxes:n.taxes.some(o=>o.companyTax)?n.taxes.map(({companyTax:o,...c})=>c):n.taxes.filter(o=>o.selected)})),base64:void 0,attachments:this.Attachment,filename:void 0};this.data?(t.id=this.data,this._expeneseService.UpdateExpenses(t).pipe((0,F.pQ)(this.destroyRef)).subscribe(n=>{n&&(this._spinnerService.hide(),this.dialogRef.close(n))})):this._expeneseService.CreateExpenses(t).pipe((0,F.pQ)(this.destroyRef)).subscribe({next:n=>{n&&(this._spinnerService.hide(),this.dialogRef.close(n))}})}handleCancel(){this.dialogRef.close()}handleSelectProject(t){if("project"!=t.metadata?.type)return this.f.clientId.setValue(t.metadata?.client?.id),this.f.projectId.setValue(null),void this.selectSearchClientElement.handleCloseSearchResult();this.f.projectId.setValue(t.value),this.f.clientId.setValue(t.metadata?.objectClient?.id),this.selectSearchClientElement.handleCloseSearchResult()}handleSelectCategory(t){"childCategory"==t.metadata?.type&&(this.f.categoryItemId.setValue(t.value),this.f.categoryId.setValue(t.metadata?.parentCategory?.id),this.selectSearchCategoryElement.handleCloseSearchResult())}handleCreateNewMerchant(t){t?.length&&this._merchantService.CreateMerchant({merchantName:t}).pipe((0,F.pQ)(this.destroyRef)).subscribe(o=>{o&&(this.f.merchantId.setValue(o.id),this.reloadListMerchantOptions(),this._toastService.showSuccess(this.translate.instant("TOAST.Create"),this.translate.instant("TOAST.Success")))})}handleCreateCategory(t){this.OpenDialogCategory(t)}OpenDialogCategory(t){this.addCategoryExpensesDialog.open({item:t}).then(o=>{o.afterClosed().subscribe(c=>{c&&this.reloadListCategoryOptions()})})}handleDeleteInvoiceItem(t){const n=[...this.f.itemExpenses?.value??[]];n?.length&&(n.splice(t,1),this.f.itemExpenses.setValue(n))}handleModifyExpenseItem(t,n){this.modifyExpensesItemDialog.open(n).then(c=>{c.afterClosed().subscribe(x=>{if(!x)return;const g=this.f.itemExpenses.value??[];void 0===t?(g.push(x),this.f.itemExpenses.setValue(g)):(g[t]=x,this.f.itemExpenses.setValue(g)),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemExpenses.value.forEach(y=>{y.taxes.length>0?x.taxes.filter(N=>1==N.selected).forEach(N=>{y.taxes.some(j=>j.companyTax?j?.companyTax.name===N?.name:j.name===N.name)||y.taxes.push(N)}):x.taxes.forEach(N=>{y.taxes.push(N)})}))})})}handleModifyTaxes(t,n){this.modifyTaxesDialog.open(t).then(c=>{c.afterClosed().subscribe(x=>{if(!x)return;const g=this.f.itemExpenses.value??[];g[n].taxes=x.taxes.filter(M=>M.selected),this.f.itemExpenses.setValue(g),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemExpenses.value.forEach(y=>{const N=x.taxes.filter(X=>X.selected);if(y.taxes.length>0){const X=new Set(y.taxes.map(j=>j.name));N.forEach(j=>{X.has(j.name)||y.taxes.push(j)})}else y.taxes.push(...x.taxes)}))})})}RemoveChoseFile(t){this.Attachment.splice(t,1)}onSelectFile(t){if(t.target.files&&t.target.files[0]){var n=t.target.files,o=t.target.files.length;for(let g=0;g<o;g++){var c=t.target.files[g];if(c.size>5242880){let M="en"==this.translate.getLangs()[0]?`File ${c.name} is larger than 5MB and will not be uploaded`:`Le fichier ${c.name} d\xe9passe 5 Mo et ne sera pas t\xe9l\xe9vers\xe9`;this._toastService.showWarning(this.translate.instant("TOAST.SizeFile"),M)}else{var x=new FileReader;x.onload=M=>{this.Attachment.push({base64:M.target.result,filename:n[g].name,type:n[g].type,size:n[g].size})},x.readAsDataURL(t.target.files[g])}}}}handleDowload(t){this._spinnerService.show(),this.cdnService.GetFile(t.filename).pipe((0,F.pQ)(this.destroyRef)).subscribe(n=>{if(n){const o=new Blob([n],{type:t.type}),c=document.createElement("a");c.href=URL.createObjectURL(o),c.download=t.filename,document.body.appendChild(c),this._spinnerService.hide(),c.click(),document.body.removeChild(c)}})}ngOnDestroy(){this._subscriptions.forEach(t=>t.unsubscribe())}}return i=h,(0,l.A)(h,"\u0275fac",function(t){return new(t||i)(e.rXU(G.c$),e.rXU(v.CP),e.rXU(K),e.rXU(H),e.rXU(J.I),e.rXU(v.Vh))}),(0,l.A)(h,"\u0275cmp",e.VBU({type:i,selectors:[["app-new-expense-form"]],viewQuery:function(t,n){if(1&t&&(e.GBs(Z,5),e.GBs(q,5)),2&t){let o;e.mGM(o=e.lsd())&&(n.selectSearchClientElement=o.first),e.mGM(o=e.lsd())&&(n.selectSearchCategoryElement=o.first)}},standalone:!0,features:[e.Jv_([m.oe]),e.aNF],decls:103,vars:134,consts:[["selectSearchClientElement",""],["projectOptionTemplate",""],["selectSearchCategoryElement",""],["categoryOptionTemplate",""],["hiddenfileinput",""],[3,"onClose","title"],[3,"formGroup"],[1,"w-full","p-[16px]","flex","flex-col","gap-[16px]"],[3,"onCreateNew","label","options","formControl","value","errorMessages","placeholder"],[3,"label","placeholder","formControl","value","errorMessages"],[3,"label","formControl","value","placeholder"],[1,"grid","grid-cols-2","gap-3"],[3,"label","options","isProjectClient","formControl","projectId","value","placeholder","customOptionTemplate"],[3,"onCreateNew","label","placeholder","options","formControl","value","customOptionTemplate","errorMessages"],[3,"label","formControl","value","placeholder","errorMessages"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],[1,"overflow-auto","w-full"],[1,"expensesTableLayout"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"mt-[8px]","button-size-md","button-outline-primary","w-full","border-dashed","justify-center",3,"click"],["src","../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"block"],[1,"button-link-primary"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","items-center"],[1,"button-link-primary",3,"click"],["src","../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],["accept","application/vnd.ms-excel, application/pdf,application/msword,.pdf, .xls, .xlsx, application/vnd.ms-powerpoint","type","file","id","PDFInpdd","multiple","",2,"display","none",3,"change"],["footer",""],[3,"onSubmit","onCancel"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"w-full","flex","items-center"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle","pl-2"],[1,"text-text-primary","text-text-md-semibold"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","cursor-pointer"],[1,"text-blue-500","text-sm","cursor-pointer"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],[1,"text-text-primary","text-text-md-regular","cursor-pointer",3,"click"],[1,"text-blue-500","text-sm","cursor-pointer",3,"click"],[1,"flex","cursor-pointer",3,"click"],[1,"w-4",3,"src"],[1,"ml-2"],[1,"pl-2","text-gray-500"],[1,"pl-2"],[1,"button-icon",3,"click"],["src","../../../../../../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(t,n){if(1&t){const o=e.RV6();e.j41(0,"app-inno-modal-wrapper",5),e.bIt("onClose",function(){return e.eBV(o),e.Njj(n.handleClose())}),e.j41(1,"form",6)(2,"div",7)(3,"app-inno-form-select-search",8),e.nI1(4,"translate"),e.nI1(5,"translate"),e.nI1(6,"translate"),e.bIt("onCreateNew",function(x){return e.eBV(o),e.Njj(n.handleCreateNewMerchant(x))}),e.k0s(),e.nrm(7,"app-inno-form-input",9),e.nI1(8,"translate"),e.nI1(9,"translate"),e.nI1(10,"translate"),e.nrm(11,"app-inno-form-textarea",10),e.nI1(12,"translate"),e.nI1(13,"translate"),e.j41(14,"div",11)(15,"app-inno-form-select-search",12,0),e.nI1(17,"translate"),e.nI1(18,"translate"),e.DNE(19,se,7,8,"ng-template",null,1,e.C5r),e.k0s(),e.j41(21,"app-inno-form-select-search",13,2),e.nI1(23,"translate"),e.nI1(24,"translate"),e.nI1(25,"translate"),e.bIt("onCreateNew",function(x){return e.eBV(o),e.Njj(n.handleCreateCategory(x))}),e.DNE(26,re,6,7,"ng-template",null,3,e.C5r),e.k0s()(),e.nrm(28,"app-inno-form-datepicker",14),e.nI1(29,"translate"),e.nI1(30,"translate"),e.nI1(31,"translate"),e.j41(32,"div",15)(33,"div",16)(34,"div",17)(35,"p",18),e.EFF(36),e.nI1(37,"translate"),e.k0s(),e.j41(38,"p",18),e.EFF(39),e.nI1(40,"translate"),e.k0s(),e.j41(41,"p",18),e.EFF(42),e.nI1(43,"translate"),e.k0s(),e.j41(44,"p",18),e.EFF(45),e.nI1(46,"translate"),e.k0s(),e.j41(47,"p",18),e.EFF(48),e.nI1(49,"translate"),e.k0s()(),e.Z7z(50,me,16,14,"div",17,e.fX1),e.k0s(),e.j41(52,"button",19),e.bIt("click",function(){return e.eBV(o),e.Njj(n.handleModifyExpenseItem())}),e.nrm(53,"img",20),e.EFF(54),e.nI1(55,"translate"),e.k0s()(),e.j41(56,"div",21)(57,"div",22)(58,"p",23),e.EFF(59),e.nI1(60,"translate"),e.k0s(),e.j41(61,"p",24),e.EFF(62),e.nI1(63,"decimal"),e.nI1(64,"formatNumber"),e.k0s()(),e.j41(65,"div",22)(66,"p",23),e.EFF(67),e.nI1(68,"translate"),e.k0s(),e.j41(69,"p",24),e.EFF(70),e.nI1(71,"decimal"),e.nI1(72,"formatNumber"),e.k0s()(),e.j41(73,"div",22)(74,"div",25)(75,"p",23),e.EFF(76),e.nI1(77,"translate"),e.k0s(),e.j41(78,"button",26),e.EFF(79),e.nI1(80,"translate"),e.k0s()(),e.j41(81,"p",24),e.EFF(82," $0 "),e.k0s()(),e.j41(83,"div",22)(84,"p",23),e.EFF(85),e.nI1(86,"translate"),e.nI1(87,"async"),e.k0s(),e.j41(88,"p",27),e.EFF(89),e.nI1(90,"decimal"),e.nI1(91,"formatNumber"),e.k0s()()(),e.nrm(92,"hr"),e.Z7z(93,de,12,5,"div",28,e.fX1),e.j41(95,"button",29),e.bIt("click",function(){e.eBV(o);const x=e.sdS(100);return e.Njj(x.click())}),e.nrm(96,"img",30),e.EFF(97),e.nI1(98,"translate"),e.k0s(),e.j41(99,"input",31,4),e.bIt("change",function(x){return e.eBV(o),e.Njj(n.onSelectFile(x))}),e.k0s()()(),e.j41(101,"div",32)(102,"app-inno-modal-footer",33),e.bIt("onSubmit",function(){return e.eBV(o),e.Njj(n.handleSubmit())})("onCancel",function(){return e.eBV(o),e.Njj(n.handleCancel())}),e.k0s()()()}if(2&t){const o=e.sdS(20),c=e.sdS(27);e.Y8G("title",n.title),e.R7$(),e.Y8G("formGroup",n.newpExpensesForm),e.R7$(2),e.Y8G("label",e.bMT(4,53,"EXPENSES.NEW_ADD_FORM.MerchantName"))("options",n.merchantOptions)("formControl",n.f.merchantId)("value",n.f.merchantId.value)("errorMessages",e.eq3(126,$,e.bMT(5,55,"EXPENSES.NEW_ADD_FORM.ValidationMerchantRequired")))("placeholder",e.bMT(6,57,"EXPENSES.NEW_ADD_FORM.MerchantPlaceholder")),e.R7$(4),e.Y8G("label",e.bMT(8,59,"EXPENSES.NEW_ADD_FORM.ExpenseName"))("placeholder",e.bMT(9,61,"EXPENSES.NEW_ADD_FORM.ExpensePlaceholder"))("formControl",n.f.expensesName)("value",n.f.expensesName.value)("errorMessages",e.eq3(128,$,e.bMT(10,63,"EXPENSES.NEW_ADD_FORM.ValidationExpenseRequired"))),e.R7$(4),e.Y8G("label",e.bMT(12,65,"EXPENSES.NEW_ADD_FORM.Description"))("formControl",n.f.note)("value",n.f.note.value)("placeholder",e.bMT(13,67,"EXPENSES.NEW_ADD_FORM.DescriptionPlaceholder")),e.R7$(4),e.Y8G("label",e.bMT(17,69,"EXPENSES.NEW_ADD_FORM.ClientProject"))("options",n.projectAndClientOptions)("isProjectClient",!0)("formControl",n.f.projectId)("projectId",n.f.projectId.value)("value",n.f.clientId.value)("placeholder",e.bMT(18,71,"EXPENSES.NEW_ADD_FORM.ClientProjectPlaceholder"))("customOptionTemplate",o),e.R7$(6),e.Y8G("label",e.bMT(23,73,"EXPENSES.NEW_ADD_FORM.Category"))("placeholder",e.bMT(24,75,"EXPENSES.NEW_ADD_FORM.CategoryPlaceholder"))("options",n.categoryOptions)("formControl",n.f.categoryItemId)("value",n.f.categoryItemId.value)("customOptionTemplate",c)("errorMessages",e.eq3(130,$,e.bMT(25,77,"EXPENSES.NEW_ADD_FORM.ValidationCategoryRequired"))),e.R7$(7),e.Y8G("label",e.bMT(29,79,"EXPENSES.NEW_ADD_FORM.Date"))("formControl",n.f.date)("value",n.f.date.value)("placeholder",e.bMT(30,81,"EXPENSES.NEW_ADD_FORM.DatePlaceholder"))("errorMessages",e.eq3(132,$,e.bMT(31,83,"EXPENSES.NEW_ADD_FORM.ValidationDateRequired"))),e.R7$(8),e.SpI(" ",e.bMT(37,85,"EXPENSES.NEW_ADD_FORM.ExpensesItem")," "),e.R7$(3),e.SpI(" ",e.bMT(40,87,"EXPENSES.NEW_ADD_FORM.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(43,89,"EXPENSES.NEW_ADD_FORM.Quantity")," "),e.R7$(3),e.SpI(" ",e.bMT(46,91,"EXPENSES.NEW_ADD_FORM.Tax")," "),e.R7$(3),e.SpI(" ",e.bMT(49,93,"EXPENSES.NEW_ADD_FORM.LineTotal")," "),e.R7$(2),e.Dyx(n.f.itemExpenses.value),e.R7$(4),e.SpI(" ",e.bMT(55,95,"EXPENSES.NEW_ADD_FORM.AddNewLine")," "),e.R7$(5),e.SpI(" ",e.bMT(60,97,"EXPENSES.NEW_ADD_FORM.Subtotal")," "),e.R7$(3),e.SpI(" $",e.bMT(64,102,e.i5U(63,99,n.subtotal,2))," "),e.R7$(5),e.SpI(" ",e.bMT(68,104,"EXPENSES.NEW_ADD_FORM.Tax")," "),e.R7$(3),e.SpI(" $",e.bMT(72,109,e.i5U(71,106,n.CheckIsNaN(n.sumtax),2))," "),e.R7$(6),e.SpI(" ",e.bMT(77,111,"EXPENSES.NEW_ADD_FORM.Discount")," "),e.R7$(3),e.SpI(" ",e.bMT(80,113,"EXPENSES.NEW_ADD_FORM.AddDiscount")," "),e.R7$(6),e.Lme(" ",e.bMT(86,115,"EXPENSES.NEW_ADD_FORM.AmountDue")," (",e.bMT(87,117,n._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(91,122,e.i5U(90,119,n.totalAmount,2))," "),e.R7$(4),e.Dyx(n.Attachment),e.R7$(4),e.SpI(" ",e.bMT(98,124,"EXPENSES.NEW_ADD_FORM.Attachment")," ")}},dependencies:[T.mC,T.fw,p.G,m.YU,_.qT,_.BC,_.cb,_.l_,_.j4,m.Jj,G.D9,I.I,R.k,k.M,w.C,L.k,E.a,A.K,S.p,C.Q,Y.M],styles:[".selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}p[_ngcontent-%COMP%]{margin-bottom:0}.expensesTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(200px,1fr) 100px 100px 100px 200px 100px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),h})()}}]);