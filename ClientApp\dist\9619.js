"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9619],{9619:(g,f,t)=>{t.r(f),t.d(f,{RegisterComponent:()=>G});var o=t(9842),e=t(4438),r=t(9417),d=t(3719),E=t(33),T=t(6146),v=t(1225),M=t(828),l=t(1342),_=t(2928),i=t(9079),s=t(3492),p=t(5861),R=t(1149),b=t(1328),k=t(7656),F=t(5236);const C=a=>({required:a}),S=(a,m)=>({required:a,email:m}),x=(a,m)=>({required:a,minlength:m}),O=(a,m)=>({required:a,confirmPasswordValidator:m});function P(a,m){if(1&a&&(e.j41(0,"div",8)(1,"p",9),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",10)(5,"i",11),e.EFF(6,"business"),e.k0s(),e.nrm(7,"app-inno-form-input",26),e.k0s()()),2&a){const u=e.XpG();e.R7$(2),e.SpI(" ",e.bMT(3,3,"REGISTER.REGISTER_FORM.Company")," "),e.R7$(5),e.Y8G("formControl",u.f.company)("value",u.f.company.value)}}let G=(()=>{var a;class m{constructor(){(0,o.A)(this,"isShowPassword",!1),(0,o.A)(this,"listCompany",[]),(0,o.A)(this,"showPassword",void 0),(0,o.A)(this,"registerForm",void 0),(0,o.A)(this,"with_button",void 0),(0,o.A)(this,"businessId",void 0),(0,o.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,o.A)(this,"router",(0,e.WQX)(E.Ix)),(0,o.A)(this,"auth_services",(0,e.WQX)(_.k)),(0,o.A)(this,"spinnerService",(0,e.WQX)(l.D)),(0,o.A)(this,"formBuilder",(0,e.WQX)(r.ze)),(0,o.A)(this,"_toastService",(0,e.WQX)(s.f)),(0,o.A)(this,"activatedRoute",(0,e.WQX)(E.nX)),this.registerForm=this.formBuilder.group({firstname:["",r.k0.compose([r.k0.required])],lastname:["",r.k0.compose([r.k0.required])],email:["",r.k0.compose([r.k0.required,r.k0.email])],password:["",r.k0.compose([r.k0.required,r.k0.minLength(6)])],confirmPassword:["",r.k0.compose([r.k0.required])],company:[""],agree:[!1,r.k0.compose([r.k0.required])]},{validator:(0,v.S)("password","confirmPassword")})}ngOnInit(){this.auth_services.getAccessToken()&&this.router.navigate(["/"])}handleToggleShowPassword(){this.isShowPassword=!this.isShowPassword}get f(){return this.registerForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(c=>{c.markAsTouched()})}onSubmit(){if(this.registerForm.invalid)return void this.markAllControlsAsTouched();this.spinnerService.show();const c=Intl.DateTimeFormat().resolvedOptions().timeZone,n=p.S[c]||c,I=p.A.find(h=>h.Moment.includes(n));this.auth_services.Register({firstName:this.registerForm.controls.firstname.value,lastName:this.registerForm.controls.lastname.value,email:this.registerForm.controls.email.value,timeZoneId:I?.Code,password:this.registerForm.controls.password.value}).pipe((0,i.pQ)(this.destroyRef)).subscribe(h=>{h?(this.spinnerService.hide(),this.auth_services.saveToken_cookie(h.accessToken,h.refreshToken),this.router.navigate(["/"])):(this.spinnerService.hide(),this._toastService.showError("Your account already exists","Error"))})}}return a=m,(0,o.A)(m,"\u0275fac",function(c){return new(c||a)}),(0,o.A)(m,"\u0275cmp",e.VBU({type:a,selectors:[["app-register"]],standalone:!0,features:[e.aNF],decls:94,vars:102,consts:[[1,"w-full","max-w-md","space-y-8","animate-fade-up"],[1,"text-center"],[1,"text-headline-lg-bold"],[1,"mt-2","text-text-sm-regular","text-text-secondary"],[1,"mt-10"],[3,"ngSubmit","formGroup"],[1,"flex","flex-col","gap-[16px]"],[1,"w-full","grid","md:grid-cols-2","gap-[10px]"],[1,"w-full"],[1,"text-text-secondary","text-text-sm-semibold","!mb-[5px]"],[1,"relative"],[1,"material-icons","absolute","top-[12px]","left-[10px]","z-10","text-text-tertiary","!text-[22px]"],["inputClassName","!h-[46px] pl-[40px]",3,"placeholder","formControl","value","errorMessages"],["type","email","inputClassName","!h-[46px] pl-[40px]",3,"placeholder","formControl","value","errorMessages"],["inputClassName","!h-[46px] px-[40px]",3,"type","placeholder","formControl","value","errorMessages"],[1,"material-icons","absolute","top-[12px]","right-[10px]","z-10","text-text-tertiary","!text-[22px]","cursor-pointer",3,"click"],[3,"formControl"],["type","submit",1,"button-size-md","button-primary","w-full","justify-center","mt-[34px]"],[1,"mt-6"],[1,"absolute","inset-0","flex","items-center"],[1,"w-full","border-t","border-border-primary"],[1,"relative","flex","justify-center","text-sm"],[1,"px-2","bg-white","text-text-tertiary"],[1,"w-full","mt-6"],[1,"mt-6","text-center","text-text-sm-regular"],["routerLink","/sign-in",1,"font-semibold","text-text-brand-primary"],["placeholder","Enter company","inputClassName","!h-[46px] pl-[40px]",3,"formControl","value"]],template:function(c,n){1&c&&(e.j41(0,"app-auth-layout")(1,"div",0)(2,"div",1)(3,"p",2),e.EFF(4),e.nI1(5,"translate"),e.k0s(),e.j41(6,"p",3),e.EFF(7),e.nI1(8,"translate"),e.k0s()(),e.j41(9,"div",4)(10,"form",5),e.bIt("ngSubmit",function(){return n.onSubmit()}),e.j41(11,"div",6)(12,"div",7)(13,"div",8)(14,"p",9),e.EFF(15),e.nI1(16,"translate"),e.k0s(),e.j41(17,"div",10)(18,"i",11),e.EFF(19,"person"),e.k0s(),e.nrm(20,"app-inno-form-input",12),e.nI1(21,"translate"),e.nI1(22,"translate"),e.k0s()(),e.j41(23,"div",8)(24,"p",9),e.EFF(25),e.nI1(26,"translate"),e.k0s(),e.j41(27,"div",10)(28,"i",11),e.EFF(29,"person"),e.k0s(),e.nrm(30,"app-inno-form-input",12),e.nI1(31,"translate"),e.nI1(32,"translate"),e.k0s()()(),e.j41(33,"div",8)(34,"p",9),e.EFF(35),e.nI1(36,"translate"),e.k0s(),e.j41(37,"div",10)(38,"i",11),e.EFF(39,"email"),e.k0s(),e.nrm(40,"app-inno-form-input",13),e.nI1(41,"translate"),e.nI1(42,"translate"),e.nI1(43,"translate"),e.k0s()(),e.j41(44,"div",8)(45,"p",9),e.EFF(46),e.nI1(47,"translate"),e.k0s(),e.j41(48,"div",10)(49,"i",11),e.EFF(50,"lock"),e.k0s(),e.nrm(51,"app-inno-form-input",14),e.nI1(52,"translate"),e.nI1(53,"translate"),e.nI1(54,"translate"),e.j41(55,"i",15),e.bIt("click",function(){return n.handleToggleShowPassword()}),e.EFF(56),e.k0s()()(),e.j41(57,"div",8)(58,"p",9),e.EFF(59),e.nI1(60,"translate"),e.k0s(),e.j41(61,"div",10)(62,"i",11),e.EFF(63,"lock"),e.k0s(),e.nrm(64,"app-inno-form-input",14),e.nI1(65,"translate"),e.nI1(66,"translate"),e.nI1(67,"translate"),e.j41(68,"i",15),e.bIt("click",function(){return n.handleToggleShowPassword()}),e.EFF(69),e.k0s()()(),e.DNE(70,P,8,5,"div",8),e.j41(71,"div",8)(72,"app-inno-form-checkbox",16),e.EFF(73),e.nI1(74,"translate"),e.k0s()()(),e.j41(75,"button",17),e.EFF(76),e.nI1(77,"translate"),e.k0s()(),e.j41(78,"div",18)(79,"div",10)(80,"div",19),e.nrm(81,"div",20),e.k0s(),e.j41(82,"div",21)(83,"span",22),e.EFF(84),e.nI1(85,"translate"),e.k0s()()()(),e.j41(86,"div",23),e.nrm(87,"app-login-social"),e.k0s(),e.j41(88,"div",24),e.EFF(89),e.nI1(90,"translate"),e.j41(91,"a",25),e.EFF(92),e.nI1(93,"translate"),e.k0s()()()()()),2&c&&(e.R7$(4),e.SpI("",e.bMT(5,39,"REGISTER.TitleCreateAccount")," "),e.R7$(3),e.SpI(" ",e.bMT(8,41,"REGISTER.Describe")," "),e.R7$(3),e.Y8G("formGroup",n.registerForm),e.R7$(5),e.SpI(" ",e.bMT(16,43,"REGISTER.REGISTER_FORM.FirstName")," "),e.R7$(5),e.Y8G("placeholder",e.bMT(21,45,"REGISTER.REGISTER_FORM.EnterFirstName"))("formControl",n.f.firstname)("value",n.f.firstname.value)("errorMessages",e.eq3(89,C,e.bMT(22,47,"REGISTER.REGISTER_FORM.FirstNameRequired"))),e.R7$(5),e.SpI(" ",e.bMT(26,49,"REGISTER.REGISTER_FORM.LastName")," "),e.R7$(5),e.Y8G("placeholder",e.bMT(31,51,"REGISTER.REGISTER_FORM.EnterLastName"))("formControl",n.f.lastname)("value",n.f.lastname.value)("errorMessages",e.eq3(91,C,e.bMT(32,53,"REGISTER.REGISTER_FORM.LastNameRequired"))),e.R7$(5),e.SpI(" ",e.bMT(36,55,"REGISTER.REGISTER_FORM.Email")," "),e.R7$(5),e.Y8G("placeholder",e.bMT(41,57,"REGISTER.REGISTER_FORM.EnterEmail"))("formControl",n.f.email)("value",n.f.email.value)("errorMessages",e.l_i(93,S,e.bMT(42,59,"REGISTER.REGISTER_FORM.EmailRequired"),e.bMT(43,61,"REGISTER.REGISTER_FORM.InvalidEmail"))),e.R7$(6),e.SpI(" ",e.bMT(47,63,"REGISTER.REGISTER_FORM.Password")," "),e.R7$(5),e.Y8G("type",n.isShowPassword?"text":"password")("placeholder",e.bMT(52,65,"REGISTER.REGISTER_FORM.EnterPassword"))("formControl",n.f.password)("value",n.f.password.value)("errorMessages",e.l_i(96,x,e.bMT(53,67,"REGISTER.REGISTER_FORM.PasswordRequired"),e.bMT(54,69,"REGISTER.REGISTER_FORM.PasswordMinLength"))),e.R7$(5),e.SpI(" ",n.isShowPassword?"visibility_off":"visibility"," "),e.R7$(3),e.SpI(" ",e.bMT(60,71,"REGISTER.REGISTER_FORM.ConfirmPassword")," "),e.R7$(5),e.Y8G("type",n.isShowPassword?"text":"password")("placeholder",e.bMT(65,73,"REGISTER.REGISTER_FORM.EnterPasswordAgain"))("formControl",n.f.confirmPassword)("value",n.f.confirmPassword.value)("errorMessages",e.l_i(99,O,e.bMT(66,75,"REGISTER.REGISTER_FORM.PasswordRequired"),e.bMT(67,77,"REGISTER.REGISTER_FORM.ConfirmPasswordValidator"))),e.R7$(5),e.SpI(" ",n.isShowPassword?"visibility_off":"visibility"," "),e.R7$(),e.vxM(n.businessId?70:-1),e.R7$(2),e.Y8G("formControl",n.f.agree),e.R7$(),e.SpI(" ",e.bMT(74,79,"REGISTER.REGISTER_FORM.AgreeTo")," "),e.R7$(3),e.SpI(" ",e.bMT(77,81,"REGISTER.REGISTER_FORM.CreateButton")," "),e.R7$(8),e.SpI(" ",e.bMT(85,83,"REGISTER.REGISTER_FORM.OrContinue"),""),e.R7$(5),e.SpI(" ",e.bMT(90,85,"REGISTER.REGISTER_FORM.AlreadyAccount")," "),e.R7$(3),e.SpI(" ",e.bMT(93,87,"REGISTER.REGISTER_FORM.SignIn"),""))},dependencies:[T.G,r.qT,r.BC,r.cb,r.l_,r.j4,F.D9,d.RG,E.iI,E.Wk,M.G,R.K,b.a,k.V],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),m})()},7656:(g,f,t)=>{t.d(f,{V:()=>M});var o=t(9842),e=t(4438),r=t(6146),d=t(9417);const E=["*"];function T(l,_){if(1&l){const i=e.RV6();e.j41(0,"input",5),e.bIt("change",function(p){e.eBV(i);const R=e.XpG();return e.Njj(R.handleChange(p))}),e.k0s()}if(2&l){const i=e.XpG();e.Y8G("checked",i.checked)("formControl",i.formControl)}}function v(l,_){if(1&l){const i=e.RV6();e.j41(0,"input",6),e.bIt("change",function(p){e.eBV(i);const R=e.XpG();return e.Njj(R.handleChange(p))}),e.k0s()}if(2&l){const i=e.XpG();e.Y8G("checked",i.checked)}}let M=(()=>{var l;class _{constructor(){(0,o.A)(this,"checked",void 0),(0,o.A)(this,"onChange",new e.bkB),(0,o.A)(this,"formControl",void 0),(0,o.A)(this,"errorMessages",void 0)}registerOnChange(s){}registerOnTouched(s){}setDisabledState(s){}writeValue(s){}handleChange(s){this.onChange.emit(s?.target?.checked??!1)}}return l=_,(0,o.A)(_,"\u0275fac",function(s){return new(s||l)}),(0,o.A)(_,"\u0275cmp",e.VBU({type:l,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:d.kq,useExisting:(0,e.Rfq)(()=>l),multi:!0}]),e.aNF],ngContentSelectors:E,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(s,p){1&s&&(e.NAR(),e.j41(0,"div",0)(1,"label",1),e.DNE(2,T,1,2,"input",2)(3,v,1,1,"input",3),e.j41(4,"div",4),e.SdG(5),e.k0s()()()),2&s&&(e.R7$(2),e.vxM(p.formControl?2:3))},dependencies:[r.G,d.Zm,d.BC,d.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),_})()}}]);