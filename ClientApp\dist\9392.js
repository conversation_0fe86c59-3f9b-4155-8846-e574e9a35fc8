"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9392],{8556:(O,D,o)=>{o.d(D,{K:()=>B});var T=o(9842),t=o(4438),S=o(5599),j=o(6146),y=o(4823),N=o(5236);function I(p,b){if(1&p){const f=t.RV6();t.j41(0,"button",4),t.bIt("click",function(){t.eBV(f);const g=t.XpG();return t.Njj(g.handleResume())}),t.nrm(1,"img",5),t.k0s()}}function r(p,b){if(1&p){const f=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(f);const g=t.XpG();return t.Njj(g.handleEdit())}),t.nrm(1,"img",7),t.k0s()}}function u(p,b){if(1&p){const f=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(f);const g=t.XpG();return t.Njj(g.handleDowload())}),t.nrm(1,"img",8),t.k0s()}}function e(p,b){if(1&p){const f=t.RV6();t.j41(0,"div",12)(1,"button",13),t.bIt("click",function(){t.eBV(f);const g=t.XpG(2);return t.Njj(g.handleArchive())}),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"button",14),t.bIt("click",function(){t.eBV(f);const g=t.XpG(2);return t.Njj(g.handleDelete())}),t.EFF(5),t.nI1(6,"translate"),t.k0s()()}2&p&&(t.R7$(2),t.SpI(" ",t.bMT(3,2,"COMMON.Archive")," "),t.R7$(3),t.SpI(" ",t.bMT(6,4,"COMMON.Delete")," "))}function l(p,b){if(1&p&&(t.j41(0,"app-inno-popover",9)(1,"button",10),t.nrm(2,"img",11),t.k0s()(),t.DNE(3,e,7,6,"ng-template",null,0,t.C5r)),2&p){const f=t.sdS(4);t.Y8G("content",f)}}function m(p,b){if(1&p){const f=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(f);const g=t.XpG(2);return t.Njj(g.handleArchive())}),t.nrm(1,"img",15),t.k0s()}}function E(p,b){if(1&p){const f=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(f);const g=t.XpG(2);return t.Njj(g.handleDelete())}),t.nrm(1,"img",16),t.k0s()}}function F(p,b){if(1&p&&t.DNE(0,m,2,0,"button",3)(1,E,2,0,"button",3),2&p){const f=t.XpG();t.vxM(f.onArchive.observed?0:-1),t.R7$(),t.vxM(f.onDelete.observed?1:-1)}}let B=(()=>{var p;class b{constructor(){(0,T.A)(this,"onEdit",new t.bkB),(0,T.A)(this,"onResume",new t.bkB),(0,T.A)(this,"onDelete",new t.bkB),(0,T.A)(this,"onArchive",new t.bkB),(0,T.A)(this,"onDowload",new t.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return p=b,(0,T.A)(b,"\u0275fac",function(A){return new(A||p)}),(0,T.A)(b,"\u0275cmp",t.VBU({type:p,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[t.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(A,g){1&A&&(t.j41(0,"div",1),t.DNE(1,I,2,0,"button",2)(2,r,2,0,"button",3)(3,u,2,0,"button",3)(4,l,5,1)(5,F,2,2),t.k0s()),2&A&&(t.R7$(),t.vxM(g.onResume.observed?1:-1),t.R7$(),t.vxM(g.onEdit.observed?2:-1),t.R7$(),t.vxM(g.onDowload.observed?3:-1),t.R7$(),t.vxM(g.onArchive.observed&&g.onDelete.observed?4:5))},dependencies:[j.G,N.D9,y.oV,S.x]})),b})()},5936:(O,D,o)=>{o.d(D,{H:()=>N});var T=o(9842),t=o(1626),S=o(4438);const y=o(5312).c.HOST_API+"/api";let N=(()=>{var I;class r{constructor(){(0,T.A)(this,"http",(0,S.WQX)(t.Qq))}GetFile(e){return this.http.get(y+`/Images/GetFile?nameFile=${e}`,{responseType:"blob"})}GetFileURL(e){return this.http.get(y+`/Images/GetFileURL?nameFile=${e}`,{responseType:"blob"})}}return I=r,(0,T.A)(r,"\u0275fac",function(e){return new(e||I)}),(0,T.A)(r,"\u0275prov",S.jDH({token:I,factory:I.\u0275fac,providedIn:"root"})),r})()},9211:(O,D,o)=>{o.d(D,{z:()=>N});var T=o(467),t=o(9842),S=o(2716),j=o(7987),y=o(4438);let N=(()=>{var I;class r extends S.H{open(e){var l=this;return(0,T.A)(function*(){const m=yield Promise.all([o.e(6437),o.e(2591),o.e(9775),o.e(1448),o.e(1328),o.e(2076),o.e(9971)]).then(o.bind(o,9971));return l.matDialog.open(m.AddNewItemComponent.getComponent(),{panelClass:"custom_dialog",width:"100%",maxWidth:"60vw",scrollStrategy:new j.t0})})()}}return I=r,(0,t.A)(r,"\u0275fac",(()=>{let u;return function(l){return(u||(u=y.xGo(I)))(l||I)}})()),(0,t.A)(r,"\u0275prov",y.jDH({token:I,factory:I.\u0275fac,providedIn:"root"})),r})()},97:(O,D,o)=>{o.d(D,{r:()=>N});var T=o(467),t=o(9842),S=o(2716),j=o(7987),y=o(4438);let N=(()=>{var I;class r extends S.H{open(e){var l=this;return(0,T.A)(function*(){const m=yield Promise.all([o.e(1328),o.e(469)]).then(o.bind(o,469));return l.matDialog.open(m.ModifyInvoiceItemComponent.getComponent(),{panelClass:"custom_dialog",data:e,width:"450px",disableClose:!0,scrollStrategy:new j.t0})})()}}return I=r,(0,t.A)(r,"\u0275fac",(()=>{let u;return function(l){return(u||(u=y.xGo(I)))(l||I)}})()),(0,t.A)(r,"\u0275prov",y.jDH({token:I,factory:I.\u0275fac,providedIn:"root"})),r})()},2387:(O,D,o)=>{o.d(D,{I:()=>N});var T=o(467),t=o(9842),S=o(2716),j=o(7987),y=o(4438);let N=(()=>{var I;class r extends S.H{open(e){var l=this;return(0,T.A)(function*(){const m=yield Promise.all([o.e(1328),o.e(2076),o.e(4592)]).then(o.bind(o,4592));return l.matDialog.open(m.ModifyTaxesComponent.getComponent(),{panelClass:"custom_dialog",data:e,width:"500px",scrollStrategy:new j.t0})})()}}return I=r,(0,t.A)(r,"\u0275fac",(()=>{let u;return function(l){return(u||(u=y.xGo(I)))(l||I)}})()),(0,t.A)(r,"\u0275prov",y.jDH({token:I,factory:I.\u0275fac,providedIn:"root"})),r})()},3989:(O,D,o)=>{o.d(D,{d:()=>N});var T=o(467),t=o(9842),S=o(2716),j=o(7987),y=o(4438);let N=(()=>{var I;class r extends S.H{open(e){var l=this;return(0,T.A)(function*(){const m=yield Promise.all([o.e(2591),o.e(1448),o.e(3099)]).then(o.bind(o,3099));return l.matDialog.open(m.SelectTimeTrackingComponent.getComponent(),{width:"100%",maxWidth:"1300px",data:e,panelClass:"custom_dialog",disableClose:!0,scrollStrategy:new j.t0})})()}}return I=r,(0,t.A)(r,"\u0275fac",(()=>{let u;return function(l){return(u||(u=y.xGo(I)))(l||I)}})()),(0,t.A)(r,"\u0275prov",y.jDH({token:I,factory:I.\u0275fac,providedIn:"root"})),r})()},5909:(O,D,o)=>{o.d(D,{R2:()=>S,Xj:()=>t,az:()=>I,jQ:()=>T,yo:()=>N});const T=(r,u)=>{if(!r||!u)return 0;const e=r.split(":").map(Number);let l=0,m=0,E=0;return 3===e.length?[l,m,E]=e:2===e.length?[l,m]=e:1===e.length&&([l]=e),Number(((l+m/60+E/3600)*u).toFixed(2))},t=(r=[],u=!1)=>(r.some(l=>l.companyTax||l.selected||void 0===l.selected)?r:r.filter(l=>l.selected)).map(l=>{let m,E;return l.companyTax?(m=l.companyTax.name,E=l.companyTax.amount):(m=l.name,E=l.amount),u?`${m} (${E}%)`:m}).filter(Boolean).sort((l,m)=>l.localeCompare(m)).join(", "),S=(r,u)=>r&&u?Number((r*u).toFixed(2)):0,N=r=>{let u=0,e={};r.forEach(({rate:m,qty:E,taxes:F})=>{if(!m||!E)return;const B=Number((m*E).toFixed(2));u+=B,F&&0!==F.length&&(F.some(f=>f.companyTaxId)?F.filter(f=>f.companyTaxId):F.filter(f=>f.selected)).forEach(f=>{const A=f.name||"Unknown Tax",g=f.taxeNumber||"",W=Number(f?.companyTax?.amount??f.amount??0);e[A]||(e[A]={name:A,numberTax:g,amount:W,taxableAmount:0,total:0}),e[A].taxableAmount+=B})});let l=0;return Object.values(e).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),l+=m.total}),{subtotal:Number(u.toFixed(2)),totalTaxes:e,grandTotalTax:Number(l.toFixed(2))}},I=r=>{let u=0,e={};r.forEach(({total:m,taxes:E})=>{m&&(u+=m,E&&0!==E.length)&&(E.some(p=>p.companyTaxId)?E.filter(p=>p.companyTaxId):E.filter(p=>p.selected)).forEach(p=>{const b=p.name||"Unknown Tax",f=p.taxeNumber||"",A=Number(p?.companyTax?.amount??p.amount??0);e[b]||(e[b]={name:b,numberTax:f,amount:A,taxableAmount:0,total:0}),e[b].taxableAmount+=m})});let l=0;return Object.values(e).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),l+=m.total}),{subtotal:Number(u.toFixed(2)),totalTaxes:e,grandTotalTax:Number(l.toFixed(2))}}},9392:(O,D,o)=>{o.r(D),o.d(D,{NewEstimateComponent:()=>Ae});var T=o(467),t=o(9842),S=o(9088),j=o(5936),y=o(4433),N=o(1342),I=o(9211),r=o(5644),u=o(1110),e=o(4438),l=o(4006),m=o(6146),E=o(9079),F=o(3492),B=o(6508),p=o.n(B),b=o(4978),f=o(9248),A=o(1328),g=o(3202),W=o(7086),z=o(8556),Y=o(344),$=o(9417),J=o(6463),L=o(1970),Z=o(7572),w=o(6473),K=o(33),q=o(5277),ee=o(6617),Q=o(1556),V=o(1875),X=o(177),U=o(5909),te=o(97),ne=o(2387),oe=o(3989),ie=o(2716),ae=o(7987);let se=(()=>{var c;class _ extends ie.H{open(n){var i=this;return(0,T.A)(function*(){const a=yield Promise.all([o.e(3719),o.e(8026)]).then(o.bind(o,8026));return i.matDialog.open(a.SendEstimateComponent.getComponent(),{width:"480px",data:n,panelClass:"custom_dialog",disableClose:!0,scrollStrategy:new ae.t0})})()}}return c=_,(0,t.A)(_,"\u0275fac",(()=>{let s;return function(i){return(s||(s=e.xGo(c)))(i||c)}})()),(0,t.A)(_,"\u0275prov",e.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),_})();var le=o(5236);const re=["selectSearchClientElement"],ce=()=>({required:"Client is required"}),me=()=>({required:"Invoice date is required"}),de=c=>({"pl-8":c});function ue(c,_){1&c&&(e.j41(0,"p",10),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&c&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"COMMON.Loading")," "))}function pe(c,_){if(1&c&&(e.j41(0,"div",12)(1,"p",38),e.EFF(2),e.k0s()()),2&c){const s=e.XpG();e.R7$(2),e.SpI(" ",s.businessInfo.businessPhoneNumber," ")}}function ve(c,_){if(1&c&&(e.j41(0,"p",13),e.EFF(1),e.k0s()),2&c){const s=e.XpG();e.R7$(),e.SpI(" ",s.businessInfo.businessAddress," ")}}function he(c,_){if(1&c&&e.nrm(0,"ngx-avatars",40),2&c){const s=e.XpG().$implicit;e.Y8G("size",32)("name",s.label)}}function fe(c,_){1&c&&(e.j41(0,"div",41),e.nrm(1,"img",44),e.k0s())}function Ie(c,_){if(1&c&&(e.j41(0,"p",43),e.EFF(1),e.k0s()),2&c){const s=e.XpG().$implicit;e.R7$(),e.SpI(" ",s.metadata.description," ")}}function _e(c,_){if(1&c){const s=e.RV6();e.j41(0,"div",39),e.bIt("click",function(){const i=e.eBV(s).$implicit,a=e.XpG();return e.Njj(a.handleSelectProject(i))}),e.DNE(1,he,1,2,"ngx-avatars",40)(2,fe,2,0,"div",41),e.j41(3,"div",9)(4,"p",42),e.EFF(5),e.k0s(),e.DNE(6,Ie,2,1,"p",43),e.k0s()()}if(2&c){const s=_.$implicit,n=e.XpG();e.AVh("selected",s.value===n.f.projectId.value),e.Y8G("ngClass",e.eq3(6,de,"project"==(null==s||null==s.metadata?null:s.metadata.type))),e.R7$(),e.vxM("client"==(null==s||null==s.metadata?null:s.metadata.type)?1:2),e.R7$(4),e.SpI(" ",s.label," "),e.R7$(),e.vxM(null!=s.metadata&&s.metadata.description?6:-1)}}function Te(c,_){if(1&c){const s=e.RV6();e.j41(0,"p",52),e.bIt("click",function(){e.eBV(s);const i=e.XpG(),a=i.$implicit,d=i.$index,x=e.XpG();return e.Njj(x.handleModifyTaxes(null==a?null:a.taxes,d))}),e.EFF(1),e.k0s()}if(2&c){const s=e.XpG().$implicit,n=e.XpG();e.R7$(),e.SpI(" ",n.getNameSelectedTaxes(null==s?null:s.taxes)," ")}}function Ee(c,_){if(1&c){const s=e.RV6();e.j41(0,"p",53),e.bIt("click",function(){e.eBV(s);const i=e.XpG(),a=i.$implicit,d=i.$index,x=e.XpG();return e.Njj(x.handleModifyTaxes(null==a?null:a.taxes,d))}),e.EFF(1," + Add Taxes "),e.k0s()}}function ge(c,_){if(1&c){const s=e.RV6();e.j41(0,"div",25)(1,"div",45)(2,"p",46),e.EFF(3),e.k0s()(),e.j41(4,"p",47),e.EFF(5),e.nI1(6,"formatNumber"),e.k0s(),e.j41(7,"p",47),e.EFF(8),e.nI1(9,"decimal"),e.nI1(10,"formatNumber"),e.k0s(),e.DNE(11,Te,2,1,"p",48)(12,Ee,2,0,"p",49),e.j41(13,"p",50),e.EFF(14),e.nI1(15,"decimal"),e.nI1(16,"formatNumber"),e.k0s(),e.j41(17,"app-inno-table-action",51),e.bIt("onEdit",function(){const i=e.eBV(s),a=i.$implicit,d=i.$index,x=e.XpG();return e.Njj(x.handleModifyInvoiceItem(d,a))})("onDelete",function(){const i=e.eBV(s).$index,a=e.XpG();return e.Njj(a.handleDeleteInvoiceItem(i))}),e.k0s()()}if(2&c){let s,n,i;const a=_.$implicit,d=e.XpG();e.R7$(3),e.SpI(" ",null!==(s=d.createDescription(a))&&void 0!==s?s:""," "),e.R7$(2),e.SpI(" $",e.bMT(6,5,null!==(n=null==a?null:a.rate)&&void 0!==n?n:0)," "),e.R7$(3),e.SpI(" ",e.bMT(10,10,e.i5U(9,7,null!==(i=null==a?null:a.qty)&&void 0!==i?i:0,2))," "),e.R7$(3),e.vxM((null==a?null:a.taxes.length)>0&&""!=d.getNameSelectedTaxes(null==a?null:a.taxes)?11:12),e.R7$(3),e.SpI(" $",e.bMT(16,15,e.i5U(15,12,d.calculateTotalInvoiceItem(null==a?null:a.rate,null==a?null:a.qty),2))," ")}}function xe(c,_){if(1&c&&(e.j41(0,"div",32)(1,"div",54)(2,"p",55),e.EFF(3),e.k0s(),e.j41(4,"p",55),e.EFF(5),e.k0s()(),e.j41(6,"p",31),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&c){const s=_.$implicit;e.R7$(3),e.Lme(" ",s.name," (",s.amount,"%) "),e.R7$(2),e.SpI(" #",s.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,s.total,2))," ")}}function be(c,_){if(1&c){const s=e.RV6();e.j41(0,"div",56)(1,"button",57),e.bIt("click",function(){e.eBV(s);const i=e.XpG();return e.Njj(i.handleSave())}),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"button",58),e.bIt("click",function(){e.eBV(s);const i=e.XpG();return e.Njj(i.handleSendInvoice())}),e.EFF(5),e.nI1(6,"translate"),e.k0s()()}2&c&&(e.R7$(2),e.SpI(" ",e.bMT(3,2,"BUTTON.Save")," "),e.R7$(3),e.SpI(" ",e.bMT(6,4,"ESTIMATE.Buttons.SendEstimate")," "))}let Ae=(()=>{var c;class _{static getComponent(){return _}constructor(n,i,a,d,x,v){(0,t.A)(this,"dialogRef",void 0),(0,t.A)(this,"modifyInvoiceItemDialog",void 0),(0,t.A)(this,"modifyTaxesDialog",void 0),(0,t.A)(this,"selectTimeTrackingDialog",void 0),(0,t.A)(this,"sendEstimateDialog",void 0),(0,t.A)(this,"data",void 0),(0,t.A)(this,"imageUrl",void 0),(0,t.A)(this,"projectName",void 0),(0,t.A)(this,"projectId",void 0),(0,t.A)(this,"inforUser",void 0),(0,t.A)(this,"InforCompany",void 0),(0,t.A)(this,"invoiceForm",void 0),(0,t.A)(this,"itemInvoice2",[]),(0,t.A)(this,"calculateTotalInvoiceItem",U.R2),(0,t.A)(this,"getNameSelectedTaxes",U.Xj),(0,t.A)(this,"calculateGroupedTaxes",U.yo),(0,t.A)(this,"projectAndClientOptions",[]),(0,t.A)(this,"subtotal",0),(0,t.A)(this,"totalAmount",0),(0,t.A)(this,"selectedDateStart",this.formatDate(new Date)),(0,t.A)(this,"selectedDateEnd",this.formatDate(new Date)),(0,t.A)(this,"taxArray",[]),(0,t.A)(this,"base64",void 0),(0,t.A)(this,"type",void 0),(0,t.A)(this,"filename",void 0),(0,t.A)(this,"payment",[]),(0,t.A)(this,"sumtax",0),(0,t.A)(this,"invoiceNumber","0000001"),(0,t.A)(this,"listTax",[]),(0,t.A)(this,"formBuilder",(0,e.WQX)($.ze)),(0,t.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,t.A)(this,"router",(0,e.WQX)(K.Ix)),(0,t.A)(this,"_toastService",(0,e.WQX)(F.f)),(0,t.A)(this,"_storeService",(0,e.WQX)(u.n)),(0,t.A)(this,"_invoiceService",(0,e.WQX)(r.p)),(0,t.A)(this,"dataService",(0,e.WQX)(q.u)),(0,t.A)(this,"_spinnerService",(0,e.WQX)(N.D)),(0,t.A)(this,"dropdownOptionService",(0,e.WQX)(J.R)),(0,t.A)(this,"layoutUtilsService",(0,e.WQX)(Q.Z)),(0,t.A)(this,"addNewItemDialog",(0,e.WQX)(I.z)),(0,t.A)(this,"cdnService",(0,e.WQX)(j.H)),(0,t.A)(this,"_companyServices",(0,e.WQX)(S.B)),(0,t.A)(this,"selectSearchClientElement",void 0),this.dialogRef=n,this.modifyInvoiceItemDialog=i,this.modifyTaxesDialog=a,this.selectTimeTrackingDialog=d,this.sendEstimateDialog=x,this.data=v,this.invoiceForm=this.formBuilder.group({clientId:[v?.clientId??"",$.k0.compose([$.k0.required])],invoiceDate:[v?.invoiceDate??null,$.k0.compose([$.k0.required])],invoiceNumber:[v?.invoiceNumber],projectId:[""],notes:[v?.notes??""],itemInvoice:[[]]}),this.invoiceForm.get("itemInvoice")?.valueChanges.subscribe(h=>{this.subtotal=0,h?.forEach(M=>{const R=(0,U.R2)(M?.rate,M?.qty);this.subtotal=this.subtotal+R}),this.calculateAllTax()}),this.invoiceForm.get("invoiceNumber")?.disable(),this.inforUser=this._storeService.get_InforUser(),this.data&&(this.data.itemInvoices.forEach(h=>{h.taxes.forEach(M=>{M.selected=!0})}),this.projectId=this.data?.projectId,this.projectName=this.data?.project?.projectName,this.f.itemInvoice.setValue(this.data?.itemInvoices??[]),this.data?.img&&this.GetImg(this.data?.img))}handleSelectProject(n){var i=this;return(0,T.A)(function*(){let a="",d="";"client"==n.metadata?.type?(a=n.value,d=""):(i.projectName=n.label,a=n.metadata?.objectClient?.id,d=n.value);const v=i.f.clientId.value===a,h=[...i.f.itemInvoice?.value??[]];if(!v&&h.length){if(!(yield i.layoutUtilsService.alertConfirm({title:"Warning",description:"You are changing the client, and the invoices will be reset. Are you sure you want to continue?"})))return;i.f.itemInvoice.setValue([])}i.f.clientId.setValue(a),i.f.projectId.setValue(d),i.selectSearchClientElement.handleCloseSearchResult()})()}ngOnInit(){this.dropdownOptionService.getDropdownOptionsProjectAndClient().then(n=>this.projectAndClientOptions=n),this.data||this.CountEstimate(),this.GetInforCompany()}GetInforCompany(){this._companyServices.GetInforCompany().pipe((0,E.pQ)(this.destroyRef)).subscribe(n=>{n&&(this.InforCompany=n)})}get businessInfo(){const n=this.InforCompany;return{businessName:n?.businessName??"",businessPhoneNumber:n?.phone??"",businessAddress:(0,w.Aw)({addressLine1:n?.adress??"",addressLine2:n?.adress2??"",stateProvince:n?.province??"",postalCode:n?.postalCode??"",country:n?.country??""})}}get f(){return this.invoiceForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(n=>{n.markAsTouched()})}formatDate(n){return`${n.getFullYear()}-${String(n.getMonth()+1).padStart(2,"0")}-${String(n.getDate()).padStart(2,"0")}`}convertToHours(n){const[i,a]=n.split(":").map(Number);return i+a/60}CountEstimate(){this._invoiceService.CountEstimate().pipe((0,E.pQ)(this.destroyRef)).subscribe(n=>n?0==n?(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber=(n+1).toString().padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)))}calculateAllTax(){this.taxArray=[],this.sumtax=0;const n=(0,U.yo)(this.f.itemInvoice.value);this.taxArray=Object.values(n.totalTaxes),this.sumtax=n.grandTotalTax,this.totalAmount=this.subtotal+this.CheckIsNaN(this.sumtax)}handleDeleteInvoiceItem(n){const i=[...this.f.itemInvoice?.value??[]];i?.length&&(i.splice(n,1),this.f.itemInvoice.setValue(i))}handleCancel(){this.dialogRef.close()}get getInvoicePayload(){if(this.invoiceForm.invalid)return null;let n={clientId:this.f.clientId.value,invoiceNumber:this.f.invoiceNumber.value,invoiceDate:p().utc(this.f.invoiceDate.value).toDate(),reference:"",notes:this.f.notes.value,projectId:this.f.projectId.value,payments:this.payment,isEstimate:!0,taxes:this.listTax,base64:this.base64,type:this.type,filename:this.filename,itemInvoices:this.f.itemInvoice.value.map(i=>({...i,taxes:i.taxes.some(a=>a.companyTax)?i.taxes.map(({companyTax:a,...d})=>d):i.taxes.filter(a=>a.selected)})),paidAmount:this.subtotal,taxAmount:this.sumtax,totalAmount:this.totalAmount,rate:0,status:0,timeAmount:0};return this.data&&(n.id=this.data?.id,n.createdAt=this.data?.createdAt),n}handleAddUnBillTime(){const n=this.f.clientId.value;if(!n)return void this._toastService.showWarning("No selected client","Please select a client to add the time.");const i=this.projectAndClientOptions.find(h=>h.value===n)?.metadata?.client;if(!i)return this._toastService.showWarning("Not fount client");const a=[...this.f.itemInvoice?.value??[]],d=a.map(h=>h.trackingId).filter(h=>h);this.selectTimeTrackingDialog.open({client:i,listIdTimeTrackingSelected:d}).then(h=>{h.afterClosed().subscribe(M=>{M?.length&&(a.push(...M.map(({user:R,...C})=>C)),this.f.itemInvoice.setValue(a))})})}handleSave(){this.invoiceForm.invalid?this.markAllControlsAsTouched():(this._spinnerService.show(),this.data?this._invoiceService.UpdateInvoice(this.getInvoicePayload).pipe((0,E.pQ)(this.destroyRef)).subscribe(n=>{n&&(this._spinnerService.hide(),this.dataService.triggerRefreshInvoice(),this.dialogRef.close(n),this._toastService.showSuccess("Save","Success"))}):this._invoiceService.CreatedInvoice(this.getInvoicePayload).pipe((0,E.pQ)(this.destroyRef)).subscribe(n=>{n&&(this._spinnerService.hide(),this.dataService.triggerRefreshInvoice(),this.dialogRef.close(),this._toastService.showSuccess("Save","Success"))}))}handleAddNewItem(){const n=this.addNewItemDialog.open(this.getInvoicePayload),i=[...this.f.itemInvoice?.value??[]];n.then(a=>{a.afterClosed().subscribe(d=>{if(!d?.length)return;const x=d.map(({id:v,isServices:h,taxes:M,itemName:R,projectId:C,serviceId:P,serviceName:k,projectName:G,description:Ce,createdAt:H,...ye})=>({...ye,description:Ce,itemName:R,itemId:0==h?v:null,projectId:1==h?C:null,projectName:G??null,serviceId:1==h?v:null,serviceName:k,date:H??new Date,dateSelectItem:H??new Date,taxes:M?.map(({id:De,itemId:Se,serviceId:Ne,...Me})=>Me)}));i.push(...x),this.f.itemInvoice.setValue(i)})})}handleSendInvoice(){if(this.invoiceForm.invalid)return this.markAllControlsAsTouched(),void this._toastService.showWarning("Please fill in all the invoice information completely."," ");this.sendEstimateDialog.open(this.getInvoicePayload).then(i=>{i.afterClosed().subscribe(a=>{a&&(this.dataService.triggerRefreshInvoice(),this.dialogRef.close())})})}handleModifyInvoiceItem(n,i){const a=i&&{...i};0==a?.position&&(a.description=this.createDescription(a)),this.modifyInvoiceItemDialog.open(a).then(x=>{x.afterClosed().subscribe(v=>{if(!v)return;const h=this.f.itemInvoice.value??[];void 0===n?(v.projectName=this.projectName,v.projectId=this.projectId??null,h.push(v),this.f.itemInvoice.setValue(h)):(v.projectName=this.projectName,v.projectId=this.projectId??null,h[n]=h[n].dateSelectItem?{...v,itemName:h[n]?.itemName,date:v?.dateSelectItem??new Date,dateSelectItem:h[n].dateSelectItem,serviceId:h[n]?.serviceId,serviceName:h[n]?.service?.serviceName??h[n]?.serviceName??""}:v,this.f.itemInvoice.setValue(h)),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemInvoice.value.forEach(R=>{R.taxes.length>0?v.taxes.filter(C=>1==C.selected).forEach(C=>{R.taxes.some(k=>k.companyTax?k?.companyTax.name===C?.name:k.name===C.name)||R.taxes.push(C)}):v.taxes.forEach(C=>{R.taxes.push(C)})}))})})}handleSelectClient(n){this.f.clientId.setValue(n.value),this.selectSearchClientElement.handleCloseSearchResult()}RouterSetting(){this.dialogRef.close(),this.router.navigate(["/settings/business"])}handleChangePicture(n){var i=this;return(0,T.A)(function*(){const a=n?.[0],{base64:d,fileName:x,type:v}=yield(0,w.EX)(a);i.base64=d,i.type=v,i.filename=x})()}handleClose(){this.dialogRef.close()}handleModifyTaxes(n,i){const a=structuredClone(this.f.itemInvoice.value),d=n.map(v=>v.companyTax?(v.companyTax.selected=!0,v.companyTax):v);this.modifyTaxesDialog.open(d.filter(v=>v.selected)).then(v=>{v.afterClosed().subscribe(h=>{if(!h)return void this.f.itemInvoice.setValue(a);const M=this.f.itemInvoice.value??[];a.forEach((R,C)=>{i!=C&&(M[C].taxes=R.taxes)}),M[i].taxes=h.taxes,this.f.itemInvoice.setValue(M),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemInvoice.value.forEach(C=>{C.taxes.length>0?h.taxes.filter(P=>1==P.selected).forEach(P=>{C.taxes.some(G=>G.companyTax?G?.companyTax.name===P?.name:G.name===P.name)||C.taxes.push(P)}):h.taxes.forEach(P=>{C.taxes.push(P)})}),this.calculateAllTax())})})}CheckIsNaN(n){return isNaN(n)?0:n}getFullName(){return this.inforUser?.firstName&&this.inforUser?.lastName?this.inforUser.firstName+" "+this.inforUser.lastName:this.inforUser?.email??""}createDescription(n){const i=new X.vh("en-US");if(0==n?.position||this.data)return n?.description;{const a=n?.projectName,d=n?.itemName,x=n?.description,v=n?.serviceName,h=i.transform(n.dateSelectItem,"MMM, d yyyy");return[a,d,v,`${this.getFullName()}- ${h}`,x].filter(P=>null!=P&&""!==P).join("\n")}}GetImg(n){this.cdnService.GetFile(n).pipe((0,E.pQ)(this.destroyRef)).subscribe(i=>{if(i){const a=new FileReader;a.onload=()=>{this.imageUrl=a.result},a.readAsDataURL(i)}})}drop(n){n.previousContainer===n.container?(0,V.HD)(n.container.data,n.previousIndex,n.currentIndex):(0,V.eg)(n.previousContainer.data,n.container.data,n.previousIndex,n.currentIndex)}}return c=_,(0,t.A)(_,"\u0275fac",function(n){return new(n||c)(e.rXU(l.CP),e.rXU(te.r),e.rXU(ne.I),e.rXU(oe.d),e.rXU(se),e.rXU(l.Vh))}),(0,t.A)(_,"\u0275cmp",e.VBU({type:c,selectors:[["app-new-estimate"]],viewQuery:function(n,i){if(1&n&&e.GBs(re,5),2&n){let a;e.mGM(a=e.lsd())&&(i.selectSearchClientElement=a.first)}},standalone:!0,features:[e.Jv_([Q.Z]),e.aNF],decls:99,vars:101,consts:[["selectSearchClientElement",""],["projectOptionTemplate",""],["customSubmitNewInvoice",""],[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"flex","w-full","gap-[18px]","xl:flex-row","flex-col"],[1,"w-[160px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'",3,"onChange","imageUrl"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]","font-bold"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","text-text-xs-regular","text-text-secondary"],[1,"button-link-primary","mt-[3px]",3,"click"],[1,"w-full","grid","md:grid-cols-2","lg:grid-cols-4","gap-[16px]"],[3,"label","options","formControl","projectId","value","placeholder","errorMessages","customOptionTemplate"],["type","number","placeholder","Enter invoice number",3,"label","formControl","value"],[3,"label","placeholder","formControl","value","errorMessages"],[3,"label","placeholder","formControl"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],["type","button",1,"button-outline","button-size-md","mt-[16px]",3,"click"],["cdkDropList","",1,"overflow-auto","w-full",3,"cdkDropListDropped","cdkDropListData"],[1,"invoiceTableLayout"],[1,"text-text-tertiary","text-text-sm-semibold"],["cdkDrag","",1,"invoiceTableLayout"],[1,"mt-[8px]","button-size-md","button-outline-primary","w-full","border-dashed","justify-center",3,"click"],["src","../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"block"],[1,"button-link-primary"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["footer",""],[3,"onCancel","customSubmitButton"],[1,"text-text-xs-regular","text-text-secondary"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"flex","flex-col"],[1,"text-text-primary","text-text-md-regular","whitespace-pre-line"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","cursor-pointer"],[1,"text-blue-500","text-sm","cursor-pointer"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],[1,"text-text-primary","text-text-md-regular","cursor-pointer",3,"click"],[1,"text-blue-500","text-sm","cursor-pointer",3,"click"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"],[1,"flex","items-center","gap-[12px]"],[1,"button-outline-primary","button-size-md",3,"click"],[1,"button-primary","button-size-md",3,"click"]],template:function(n,i){if(1&n){const a=e.RV6();e.j41(0,"app-inno-modal-wrapper",3),e.nI1(1,"translate"),e.bIt("onClose",function(){return e.eBV(a),e.Njj(i.handleClose())}),e.j41(2,"div",4)(3,"div",5)(4,"div",6)(5,"app-inno-upload",7),e.bIt("onChange",function(x){return e.eBV(a),e.Njj(i.handleChangePicture(x))}),e.k0s()(),e.j41(6,"div",8)(7,"div",9),e.DNE(8,ue,3,3,"p",10),e.j41(9,"p",11),e.EFF(10),e.k0s(),e.DNE(11,pe,3,1,"div",12)(12,ve,2,1,"p",13),e.j41(13,"button",14),e.bIt("click",function(){return e.eBV(a),e.Njj(i.RouterSetting())}),e.EFF(14),e.nI1(15,"translate"),e.k0s()(),e.j41(16,"div",15)(17,"app-inno-form-select-search",16,0),e.nI1(19,"translate"),e.nI1(20,"translate"),e.DNE(21,_e,7,8,"ng-template",null,1,e.C5r),e.k0s(),e.nrm(23,"app-inno-form-input",17),e.nI1(24,"translate"),e.nrm(25,"app-inno-form-datepicker",18),e.nI1(26,"translate"),e.nI1(27,"translate"),e.k0s(),e.j41(28,"div",9),e.nrm(29,"app-inno-form-textarea",19),e.nI1(30,"translate"),e.nI1(31,"translate"),e.k0s()()(),e.j41(32,"div",20)(33,"button",21),e.bIt("click",function(){return e.eBV(a),e.Njj(i.handleAddNewItem())}),e.EFF(34),e.nI1(35,"translate"),e.k0s(),e.j41(36,"div",22),e.bIt("cdkDropListDropped",function(x){return e.eBV(a),e.Njj(i.drop(x))}),e.j41(37,"div",23)(38,"p",24),e.EFF(39),e.nI1(40,"translate"),e.k0s(),e.j41(41,"p",24),e.EFF(42),e.nI1(43,"translate"),e.k0s(),e.j41(44,"p",24),e.EFF(45),e.nI1(46,"translate"),e.k0s(),e.j41(47,"p",24),e.EFF(48),e.nI1(49,"translate"),e.k0s(),e.j41(50,"p",24),e.EFF(51),e.nI1(52,"translate"),e.k0s()(),e.Z7z(53,ge,18,17,"div",25,e.fX1),e.k0s(),e.j41(55,"button",26),e.bIt("click",function(){return e.eBV(a),e.Njj(i.handleModifyInvoiceItem())}),e.nrm(56,"img",27),e.EFF(57),e.nI1(58,"translate"),e.k0s()(),e.j41(59,"div",28)(60,"div",29)(61,"p",30),e.EFF(62),e.nI1(63,"translate"),e.k0s(),e.j41(64,"p",31),e.EFF(65),e.nI1(66,"decimal"),e.k0s()(),e.Z7z(67,xe,10,9,"div",32,e.fX1),e.j41(69,"div",29)(70,"p",30),e.EFF(71),e.nI1(72,"translate"),e.k0s(),e.j41(73,"p",31),e.EFF(74),e.nI1(75,"decimal"),e.nI1(76,"formatNumber"),e.k0s()(),e.j41(77,"div",29)(78,"div",33)(79,"p",30),e.EFF(80),e.nI1(81,"translate"),e.k0s(),e.j41(82,"button",34),e.EFF(83),e.nI1(84,"translate"),e.k0s()(),e.j41(85,"p",31),e.EFF(86," $0 "),e.k0s()(),e.j41(87,"div",29)(88,"p",30),e.EFF(89),e.nI1(90,"translate"),e.nI1(91,"async"),e.k0s(),e.j41(92,"p",35),e.EFF(93),e.nI1(94,"decimal"),e.k0s()()()(),e.j41(95,"div",36)(96,"app-inno-modal-footer",37),e.bIt("onCancel",function(){return e.eBV(a),e.Njj(i.handleCancel())}),e.k0s(),e.DNE(97,be,7,6,"ng-template",null,2,e.C5r),e.k0s()()}if(2&n){const a=e.sdS(22),d=e.sdS(98);e.Y8G("title",e.bMT(1,44,null!=i.data&&i.data.id?"ESTIMATE.Buttons.EditEstimate":"ESTIMATE.Buttons.NewEstimate")),e.R7$(5),e.Y8G("imageUrl",i.imageUrl),e.R7$(3),e.vxM(i.businessInfo.businessName?-1:8),e.R7$(2),e.SpI("",null==i.businessInfo?null:i.businessInfo.businessName," "),e.R7$(),e.vxM(i.businessInfo.businessPhoneNumber?11:-1),e.R7$(),e.vxM(i.businessInfo.businessAddress?12:-1),e.R7$(2),e.SpI(" ",e.bMT(15,46,"ESTIMATE.ESTIMATE_FORM.EditBusinessInfo")," "),e.R7$(3),e.Y8G("label",e.bMT(19,48,"ESTIMATE.ESTIMATE_FORM.ClientProject"))("options",i.projectAndClientOptions)("formControl",i.f.clientId)("projectId",i.f.projectId.value)("value",i.f.clientId.value)("placeholder",e.bMT(20,50,"ESTIMATE.ESTIMATE_FORM.ClientProjectPlaceholder"))("errorMessages",e.lJ4(99,ce))("customOptionTemplate",a),e.R7$(6),e.Y8G("label",e.bMT(24,52,"ESTIMATE.ESTIMATE_FORM.EstimateNumber"))("formControl",i.f.invoiceNumber)("value",i.f.invoiceNumber.value),e.R7$(2),e.Y8G("label",e.bMT(26,54,"ESTIMATE.ESTIMATE_FORM.IssueDate"))("placeholder",e.bMT(27,56,"ESTIMATE.ESTIMATE_FORM.IssueDatePlaceholder"))("formControl",i.f.invoiceDate)("value",i.f.invoiceDate.value)("errorMessages",e.lJ4(100,me)),e.R7$(4),e.Y8G("label",e.bMT(30,58,"ESTIMATE.ESTIMATE_FORM.Description"))("placeholder",e.bMT(31,60,"ESTIMATE.ESTIMATE_FORM.DescriptionPlaceholder"))("formControl",i.f.notes),e.R7$(5),e.SpI(" ",e.bMT(35,62,"ESTIMATE.ESTIMATE_FORM.Buttons.AddNewItem")," "),e.R7$(2),e.Y8G("cdkDropListData",i.f.itemInvoice.value),e.R7$(3),e.SpI(" ",e.bMT(40,64,"ESTIMATE.ESTIMATE_FORM.TableHeaders.EstimateItem")," "),e.R7$(3),e.SpI(" ",e.bMT(43,66,"ESTIMATE.ESTIMATE_FORM.TableHeaders.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(46,68,"ESTIMATE.ESTIMATE_FORM.TableHeaders.Quantity")," "),e.R7$(3),e.SpI(" ",e.bMT(49,70,"ESTIMATE.ESTIMATE_FORM.TableHeaders.Tax")," "),e.R7$(3),e.SpI(" ",e.bMT(52,72,"ESTIMATE.ESTIMATE_FORM.TableHeaders.LineTotal")," "),e.R7$(2),e.Dyx(i.f.itemInvoice.value),e.R7$(4),e.SpI(" ",e.bMT(58,74,"ESTIMATE.ESTIMATE_FORM.Buttons.AddNewLine")," "),e.R7$(5),e.SpI(" ",e.bMT(63,76,"ESTIMATE.ESTIMATE_FORM.Subtotal")," "),e.R7$(3),e.SpI(" $",e.i5U(66,78,i.subtotal,2)," "),e.R7$(2),e.Dyx(i.taxArray),e.R7$(4),e.SpI(" ",e.bMT(72,81,"ESTIMATE.ESTIMATE_FORM.Tax")," "),e.R7$(3),e.SpI(" $",e.bMT(76,86,e.i5U(75,83,i.CheckIsNaN(i.sumtax),2))," "),e.R7$(6),e.SpI(" ",e.bMT(81,88,"ESTIMATE.ESTIMATE_FORM.Discount")," "),e.R7$(3),e.SpI(" ",e.bMT(84,90,"ESTIMATE.ESTIMATE_FORM.AddDiscount")," "),e.R7$(6),e.Lme(" ",e.bMT(90,92,"ESTIMATE.ESTIMATE_FORM.AmountDue")," (",e.bMT(91,94,i._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.i5U(94,96,i.totalAmount,2)," "),e.R7$(3),e.Y8G("customSubmitButton",d)}},dependencies:[m.G,X.YU,$.BC,$.l_,X.Jj,le.D9,L.mC,L.fw,K.iI,b.I,f.M,A.a,g.k,W.C,z.K,Y.k,Z.P,ee.p,y.Q,V.O7,V.T1],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(200px,1fr) 100px 100px 100px 200px 100px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),_})()}}]);