"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2365],{3200:(P,A,e)=>{e.d(A,{J:()=>v});var o=e(9842),n=e(177),p=e(5236),a=e(4438);function f(d,_){if(1&d&&(a.j41(0,"p",4),a.EFF(1),a.nI1(2,"translate"),a.k0s()),2&d){const s=a.XpG();a.R7$(),a.SpI(" ",a.bMT(2,1,s.description)," ")}}let v=(()=>{var d;class _{constructor(){(0,o.A)(this,"title",""),(0,o.A)(this,"description",""),(0,o.A)(this,"icon",""),(0,o.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return d=_,(0,o.A)(_,"\u0275fac",function(h){return new(h||d)}),(0,o.A)(_,"\u0275cmp",a.VBU({type:d,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[a.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(h,D){1&h&&(a.j41(0,"div",0),a.nrm(1,"img",1),a.j41(2,"div",2)(3,"p",3),a.EFF(4),a.nI1(5,"translate"),a.nI1(6,"translate"),a.k0s(),a.DNE(7,f,3,3,"p",4),a.k0s()()),2&h&&(a.R7$(),a.Y8G("src",D.icon||D.defaultIcon,a.B4B),a.R7$(3),a.SpI(" ",D.title?a.bMT(5,3,D.title):a.bMT(6,5,"COMMON.EmptyData")," "),a.R7$(3),a.vxM(D.description?7:-1))},dependencies:[n.MD,p.h,p.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),_})()},9424:(P,A,e)=>{e.d(A,{f:()=>f});var o=e(9842),n=e(177),p=e(4438);const a=(v,d,_)=>({"w-4 h-4":v,"w-6 h-6":d,"w-10 h-10":_});let f=(()=>{var v;class d{constructor(){(0,o.A)(this,"size","md")}}return v=d,(0,o.A)(d,"\u0275fac",function(s){return new(s||v)}),(0,o.A)(d,"\u0275cmp",p.VBU({type:v,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[p.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(s,h){1&s&&(p.j41(0,"div",0),p.qSk(),p.j41(1,"svg",1),p.nrm(2,"path",2)(3,"path",3),p.k0s(),p.joV(),p.j41(4,"span",4),p.EFF(5,"Loading..."),p.k0s()()),2&s&&(p.R7$(),p.Y8G("ngClass",p.sMw(1,a,"sm"===h.size,"md"===h.size,"lg"===h.size)))},dependencies:[n.MD,n.YU]})),d})()},8556:(P,A,e)=>{e.d(A,{K:()=>M});var o=e(9842),n=e(4438),p=e(5599),a=e(6146),f=e(4823),v=e(5236);function d(c,g){if(1&c){const t=n.RV6();n.j41(0,"button",4),n.bIt("click",function(){n.eBV(t);const m=n.XpG();return n.Njj(m.handleResume())}),n.nrm(1,"img",5),n.k0s()}}function _(c,g){if(1&c){const t=n.RV6();n.j41(0,"button",6),n.bIt("click",function(){n.eBV(t);const m=n.XpG();return n.Njj(m.handleEdit())}),n.nrm(1,"img",7),n.k0s()}}function s(c,g){if(1&c){const t=n.RV6();n.j41(0,"button",6),n.bIt("click",function(){n.eBV(t);const m=n.XpG();return n.Njj(m.handleDowload())}),n.nrm(1,"img",8),n.k0s()}}function h(c,g){if(1&c){const t=n.RV6();n.j41(0,"div",12)(1,"button",13),n.bIt("click",function(){n.eBV(t);const m=n.XpG(2);return n.Njj(m.handleArchive())}),n.EFF(2),n.nI1(3,"translate"),n.k0s(),n.j41(4,"button",14),n.bIt("click",function(){n.eBV(t);const m=n.XpG(2);return n.Njj(m.handleDelete())}),n.EFF(5),n.nI1(6,"translate"),n.k0s()()}2&c&&(n.R7$(2),n.SpI(" ",n.bMT(3,2,"COMMON.Archive")," "),n.R7$(3),n.SpI(" ",n.bMT(6,4,"COMMON.Delete")," "))}function D(c,g){if(1&c&&(n.j41(0,"app-inno-popover",9)(1,"button",10),n.nrm(2,"img",11),n.k0s()(),n.DNE(3,h,7,6,"ng-template",null,0,n.C5r)),2&c){const t=n.sdS(4);n.Y8G("content",t)}}function T(c,g){if(1&c){const t=n.RV6();n.j41(0,"button",6),n.bIt("click",function(){n.eBV(t);const m=n.XpG(2);return n.Njj(m.handleArchive())}),n.nrm(1,"img",15),n.k0s()}}function b(c,g){if(1&c){const t=n.RV6();n.j41(0,"button",6),n.bIt("click",function(){n.eBV(t);const m=n.XpG(2);return n.Njj(m.handleDelete())}),n.nrm(1,"img",16),n.k0s()}}function R(c,g){if(1&c&&n.DNE(0,T,2,0,"button",3)(1,b,2,0,"button",3),2&c){const t=n.XpG();n.vxM(t.onArchive.observed?0:-1),n.R7$(),n.vxM(t.onDelete.observed?1:-1)}}let M=(()=>{var c;class g{constructor(){(0,o.A)(this,"onEdit",new n.bkB),(0,o.A)(this,"onResume",new n.bkB),(0,o.A)(this,"onDelete",new n.bkB),(0,o.A)(this,"onArchive",new n.bkB),(0,o.A)(this,"onDowload",new n.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return c=g,(0,o.A)(g,"\u0275fac",function(E){return new(E||c)}),(0,o.A)(g,"\u0275cmp",n.VBU({type:c,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[n.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(E,m){1&E&&(n.j41(0,"div",1),n.DNE(1,d,2,0,"button",2)(2,_,2,0,"button",3)(3,s,2,0,"button",3)(4,D,5,1)(5,R,2,2),n.k0s()),2&E&&(n.R7$(),n.vxM(m.onResume.observed?1:-1),n.R7$(),n.vxM(m.onEdit.observed?2:-1),n.R7$(),n.vxM(m.onDowload.observed?3:-1),n.R7$(),n.vxM(m.onArchive.observed&&m.onDelete.observed?4:5))},dependencies:[a.G,v.D9,f.oV,p.x]})),g})()},1556:(P,A,e)=>{e.d(A,{Z:()=>_});var o=e(9842),n=e(4438),p=e(467),a=e(2716),f=e(7987);let v=(()=>{var s;class h extends a.H{open(T){var b=this;return(0,p.A)(function*(){const R=yield e.e(3190).then(e.bind(e,3190));return b.matDialog.open(R.AlertConfirmComponent.getComponent(),{data:T,width:"440px",panelClass:"custom_dialog",scrollStrategy:new f.t0,disableClose:!0})})()}}return s=h,(0,o.A)(h,"\u0275fac",(()=>{let D;return function(b){return(D||(D=n.xGo(s)))(b||s)}})()),(0,o.A)(h,"\u0275prov",n.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})),h})(),_=(()=>{var s;class h{constructor(T){(0,o.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=T}alertDelete(T){const{title:b,description:R,textSubmit:M="COMMON.Delete",textCancel:c}=T;return new Promise(g=>{this.alertConfirmDialog.open({title:b,description:R,textSubmit:M,textCancel:c,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(E=>{E.afterClosed().subscribe(m=>{g(m??!1)})})})}alertConfirm(T){const{title:b,description:R,textSubmit:M,textCancel:c}=T;return new Promise(g=>{this.alertConfirmDialog.open({title:b,description:R,textSubmit:M,textCancel:c}).then(E=>{E.afterClosed().subscribe(m=>{g(m??!1)})})})}}return s=h,(0,o.A)(h,"\u0275fac",function(T){return new(T||s)(n.KVO(v))}),(0,o.A)(h,"\u0275prov",n.jDH({token:s,factory:s.\u0275fac})),h})()},4433:(P,A,e)=>{e.d(A,{Q:()=>p});var o=e(9842),n=e(4438);let p=(()=>{var a;class f{transform(d,_=2){const s=Math.pow(10,_);return(Math.trunc(Number((d*s).toFixed(_+5)))/s).toFixed(_)}}return a=f,(0,o.A)(f,"\u0275fac",function(d){return new(d||a)}),(0,o.A)(f,"\u0275pipe",n.EJ8({name:"decimal",type:a,pure:!0,standalone:!0})),f})()},6617:(P,A,e)=>{e.d(A,{p:()=>a});var o=e(9842),n=e(6473),p=e(4438);let a=(()=>{var f;class v{transform(_){return(0,n.ZV)(_)}}return f=v,(0,o.A)(v,"\u0275fac",function(_){return new(_||f)}),(0,o.A)(v,"\u0275pipe",p.EJ8({name:"formatNumber",type:f,pure:!0,standalone:!0})),v})()},2365:(P,A,e)=>{e.r(A),e.d(A,{ClientsComponent:()=>V});var o=e(9842),n=e(9424),p=e(4433),a=e(6617),f=e(1556),v=e(6146),d=e(2840),_=e(1448),s=e(9079),h=e(9115),D=e(1413),T=e(152),b=e(8600),R=e(5900),M=e(8556),c=e(3200),g=e(1970),t=e(4438),E=e(5236),m=e(33),O=e(1110),B=e(4982),j=e(3492),y=e(9589),S=e(1719),L=e(177);const N=["grid"],U=C=>({"mb-28":C});function F(C,I){1&C&&(t.j41(0,"div",8),t.nrm(1,"app-inno-spin"),t.k0s())}function G(C,I){if(1&C&&(t.j41(0,"div",26),t.nrm(1,"ngx-avatars",27),t.j41(2,"div",28)(3,"p",29),t.EFF(4),t.k0s()()()),2&C){let l;const i=I.$implicit;t.R7$(),t.Y8G("size",35)("name",null==i?null:i.clientName),t.R7$(3),t.SpI(" ",null!==(l=null==i?null:i.clientName)&&void 0!==l?l:""," ")}}function K(C,I){if(1&C&&(t.j41(0,"span",29),t.EFF(1),t.nI1(2,"decimal"),t.nI1(3,"formatNumber"),t.k0s()),2&C){const l=I.$implicit;t.R7$(),t.SpI(" $",t.bMT(3,4,t.i5U(2,1,null==l?null:l.totalOutstanding,2))," ")}}function W(C,I){if(1&C){const l=t.RV6();t.j41(0,"app-inno-table-action",30),t.bIt("onEdit",function(){const r=t.eBV(l).$implicit,u=t.XpG(3);return t.Njj(u.handleEditClient(r))})("onDelete",function(){const r=t.eBV(l).$implicit,u=t.XpG(3);return t.Njj(u.handleDeleteClient(r))}),t.k0s()}}function $(C,I){if(1&C){const l=t.RV6();t.j41(0,"div",13)(1,"div",14)(2,"p",15),t.EFF(3),t.nI1(4,"translate"),t.k0s(),t.j41(5,"p",5),t.EFF(6),t.k0s()(),t.j41(7,"div",14)(8,"p",15),t.EFF(9),t.nI1(10,"translate"),t.k0s(),t.j41(11,"p",5),t.EFF(12),t.nI1(13,"decimal"),t.nI1(14,"formatNumber"),t.k0s()(),t.j41(15,"div",14)(16,"p",15),t.EFF(17),t.nI1(18,"translate"),t.k0s(),t.j41(19,"p",5),t.EFF(20),t.nI1(21,"decimal"),t.nI1(22,"formatNumber"),t.k0s()(),t.j41(23,"div",14)(24,"p",15),t.EFF(25),t.nI1(26,"translate"),t.k0s(),t.j41(27,"p",5),t.EFF(28),t.nI1(29,"decimal"),t.nI1(30,"formatNumber"),t.k0s()()(),t.j41(31,"div",16)(32,"div",17)(33,"app-inno-input-search",18),t.bIt("onChange",function(r){t.eBV(l);const u=t.XpG(2);return t.Njj(u.handleSearch(r))}),t.k0s()()(),t.j41(34,"div",19)(35,"ejs-grid",20,0),t.bIt("actionBegin",function(r){t.eBV(l);const u=t.XpG(2);return t.Njj(u.onActionBegin(r))}),t.j41(37,"e-columns")(38,"e-column",21),t.DNE(39,G,5,3,"ng-template",null,1,t.C5r),t.k0s(),t.nrm(41,"e-column",22),t.nI1(42,"translate"),t.nrm(43,"e-column",23),t.nI1(44,"translate"),t.j41(45,"e-column",24),t.nI1(46,"translate"),t.DNE(47,K,4,6,"ng-template",null,1,t.C5r),t.k0s(),t.j41(49,"e-column",23),t.nI1(50,"translate"),t.DNE(51,W,1,0,"ng-template",null,1,t.C5r),t.k0s()()(),t.j41(53,"ejs-pager",25),t.bIt("click",function(r){t.eBV(l);const u=t.XpG(2);return t.Njj(u.onPageChange(r))}),t.k0s()()}if(2&C){const l=t.XpG(2);t.R7$(3),t.JRh(t.bMT(4,21,"CLIENT.ActiveClient")),t.R7$(3),t.JRh(l.totalPages),t.R7$(3),t.JRh(t.bMT(10,23,"CLIENT.TotalOverdue")),t.R7$(3),t.SpI("$",t.bMT(14,28,t.i5U(13,25,null==l.clientResponse?null:l.clientResponse.totalOverdue,2)),""),t.R7$(5),t.JRh(t.bMT(18,30,"CLIENT.TotalDraft")),t.R7$(3),t.SpI("$",t.bMT(22,35,t.i5U(21,32,null==l.clientResponse?null:l.clientResponse.totalDraft,2)),""),t.R7$(5),t.SpI(" ",t.bMT(26,37,"CLIENT.TotalOutstanding"),""),t.R7$(3),t.SpI("$",t.bMT(30,42,t.i5U(29,39,null==l.clientResponse?null:l.clientResponse.totalAmount,2)),""),t.R7$(5),t.Y8G("value",l.search),t.R7$(),t.Y8G("ngClass",t.eq3(52,U,l.storeService.getIsRunning())),t.R7$(),t.Y8G("dataSource",l.dataSource)("allowSorting",!0)("sortSettings",l.sortOptions),t.R7$(6),t.Y8G("headerText",t.bMT(42,44,"CLIENT.GIRD.Company")),t.R7$(2),t.Y8G("headerText",t.bMT(44,46,"CLIENT.GIRD.Credit")),t.R7$(2),t.Y8G("headerText",t.bMT(46,48,"CLIENT.TotalOutstanding")),t.R7$(4),t.Y8G("headerText",t.bMT(50,50,"COMMON.Action")),t.R7$(4),t.Y8G("pageSize",l.pageSizesDefault)("totalRecordsCount",l.totalPages)("currentPage",l.currentPage)("pageSizes",l.pageSizes)}}function k(C,I){1&C&&(t.j41(0,"div",12),t.nrm(1,"app-inno-empty-data",31),t.k0s())}function w(C,I){if(1&C){const l=t.RV6();t.j41(0,"div",9)(1,"app-inno-tabs",10),t.bIt("onChange",function(r){t.eBV(l);const u=t.XpG();return t.Njj(u.handleChangeTab(r))}),t.k0s()(),t.j41(2,"div",11),t.DNE(3,$,54,54)(4,k,2,0,"div",12),t.k0s()}if(2&C){const l=t.XpG();t.R7$(),t.Y8G("tabs",l.tabs)("value",l.currentTab),t.R7$(2),t.vxM(l.currentTab===l.TYPE_TAB.ALL_CLIENTS?3:-1),t.R7$(),t.vxM(l.currentTab===l.TYPE_TAB.MAIL_HISTORY?4:-1)}}d.is5.Inject(d.Rav);let V=(()=>{var C;class I{constructor(i,r,u,x,X,z,Y,J,H,Q){(0,o.A)(this,"translate",void 0),(0,o.A)(this,"layoutUtilsService",void 0),(0,o.A)(this,"router",void 0),(0,o.A)(this,"activatedRoute",void 0),(0,o.A)(this,"storeService",void 0),(0,o.A)(this,"clientService",void 0),(0,o.A)(this,"toastService",void 0),(0,o.A)(this,"destroyRef",void 0),(0,o.A)(this,"switchWorkspaceDialog",void 0),(0,o.A)(this,"addClientsDialog",void 0),(0,o.A)(this,"sort",void 0),(0,o.A)(this,"isLoading",!1),(0,o.A)(this,"_subscriptions",[]),(0,o.A)(this,"sortOptions",{columns:[]}),(0,o.A)(this,"selectionOptions",{type:"Multiple",checkboxOnly:!0}),(0,o.A)(this,"TYPE_TAB",{ALL_CLIENTS:1,MAIL_HISTORY:2}),(0,o.A)(this,"tabs",[]),(0,o.A)(this,"currentTab",this.TYPE_TAB.ALL_CLIENTS),(0,o.A)(this,"dataSource",void 0),(0,o.A)(this,"search",""),(0,o.A)(this,"totalPages",1),(0,o.A)(this,"currentPage",1),(0,o.A)(this,"pageSizes",[10,20,50,100]),(0,o.A)(this,"pageSizesDefault",10),(0,o.A)(this,"searchSubject",new D.B),(0,o.A)(this,"timeoutId",void 0),(0,o.A)(this,"columnName",void 0),(0,o.A)(this,"direction",void 0),(0,o.A)(this,"grid",void 0),(0,o.A)(this,"clientResponse",null),this.translate=i,this.layoutUtilsService=r,this.router=u,this.activatedRoute=x,this.storeService=X,this.clientService=z,this.toastService=Y,this.destroyRef=J,this.switchWorkspaceDialog=H,this.addClientsDialog=Q}handleChangeTab(i){this.currentTab=i}onPageChange(i){i?.newProp?.pageSize&&(this.pageSizesDefault=i.newProp.pageSize,this.GetAllClient()),i?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:i.currentPage},queryParamsHandling:"merge"})}handleSearch(i){this.searchSubject.next(i)}CalculationClient(){this.clientService.CalculationClient().pipe((0,s.pQ)(this.destroyRef)).subscribe(i=>{this.clientResponse=i})}ngOnInit(){this.tabs=[{label:this.translate.instant("CLIENT.AllClient"),value:this.TYPE_TAB.ALL_CLIENTS},{label:this.translate.instant("CLIENT.MailsHistory"),value:this.TYPE_TAB.MAIL_HISTORY}],this.activatedRoute.queryParams.pipe((0,s.pQ)(this.destroyRef)).subscribe(r=>{r?.page&&(this.currentPage=r.page),this.GetAllClient()});const i=this.searchSubject.pipe((0,T.B)(550)).subscribe(r=>{this.search=r??"",this.GetAllClient()});this._subscriptions.push(i),this.CalculationClient()}GetAllClient(){this.isLoading=!0;let i={Page:this.currentPage,PageSize:this.pageSizesDefault,Search:this.search,...this.sort};this.clientService.GetAllClient(i).pipe((0,s.pQ)(this.destroyRef)).subscribe(r=>{r&&(this.isLoading=!1,this.totalPages=r.totalRecords,this.dataSource=r.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))})}OpenDialogNewBusiness(){this.switchWorkspaceDialog.open({}).then(r=>{r.afterClosed().subscribe(u=>{this.storeService.getChooseBusiness()&&window.location.reload()})})}NewClient(){this.storeService.getChooseBusiness()?this.OpenDialog():this.OpenDialogNewBusiness()}OpenDialog(){this.addClientsDialog.open(null).then(r=>{r.afterClosed().subscribe(u=>{u&&this.GetAllClient()})})}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleEditClient(i){this.addClientsDialog.open(i.id).then(u=>{u.afterClosed().subscribe(x=>{x&&this.GetAllClient()})})}handleDeleteClient(i){this.layoutUtilsService.alertDelete({title:this.translate.instant("CLIENT.DeleteClient"),description:this.translate.instant("COMMON.ConfirmDelete")}).then(r=>{r&&this.clientService.DeleteClient([i.id]).pipe((0,s.pQ)(this.destroyRef)).subscribe(u=>{u&&(this.GetAllClient(),this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success")),this.CalculationClient())})})}onActionBegin(i){if("sorting"===i.requestType){if(this.columnName=i.columnName,this.direction=i.direction,this.sort={columnName:i.columnName,direction:i.direction},this.columnName)return void this.GetAllClient();this.sort=null,this.sortOptions={columns:[]},this.GetAllClient()}}ngOnDestroy(){this.clearTimeout(),this._subscriptions&&this._subscriptions.forEach(i=>i.unsubscribe())}}return C=I,(0,o.A)(I,"\u0275fac",function(i){return new(i||C)(t.rXU(E.c$),t.rXU(f.Z),t.rXU(m.Ix),t.rXU(m.nX),t.rXU(O.n),t.rXU(B.X),t.rXU(j.f),t.rXU(t.abz),t.rXU(y.y),t.rXU(S.E))}),(0,o.A)(I,"\u0275cmp",t.VBU({type:C,selectors:[["app-clients"]],viewQuery:function(i,r){if(1&i&&t.GBs(N,5),2&i){let u;t.mGM(u=t.lsd())&&(r.grid=u.first)}},standalone:!0,features:[t.Jv_([f.Z]),t.aNF],decls:11,vars:4,consts:[["grid",""],["template",""],[1,"w-full","pb-3"],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"button-size-md","button-primary",3,"click"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"],[1,"flex","justify-center","items-center","grow","py-3"],[1,"container-full","mt-[24px]","flex","items-center","justify-between","flex-wrap","gap-2"],[3,"onChange","tabs","value"],[1,"w-full","mt-[24px]"],[1,"container-full","pt-[20dvh]"],[1,"container-full","grid","grid-cols-4","mxw1100:grid-cols-2","mxw600:grid-cols-1"],[1,"w-full","min-h-[112px]","border-[4px]","border-border-primary","flex","flex-col","justify-end","gap-[8px]","pb-[16px]","px-[24px]","bg-bg-primary"],[1,"text-text-tertiary","text-text-md-semibold"],[1,"container-full","mt-[32px]"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"w-full","mt-[12px]",3,"ngClass"],[1,"customTable",3,"actionBegin","dataSource","allowSorting","sortSettings"],["headerText","Clients","width","170","field","clientName"],["field","CompanyName","width","170",3,"headerText"],["width","100",3,"headerText"],["field","totalOutstanding","width","180",3,"headerText"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","gap-[12px]","items-center"],[1,"shrink-0",3,"size","name"],[1,"w-full"],[1,"text-text-primary","text-text-md-semibold"],[3,"onEdit","onDelete"],["title","The feature is in development."]],template:function(i,r){1&i&&(t.j41(0,"div",2)(1,"div",3)(2,"div",4)(3,"p",5),t.EFF(4," Clients "),t.k0s(),t.j41(5,"button",6),t.bIt("click",function(){return r.NewClient()}),t.nrm(6,"img",7),t.EFF(7),t.nI1(8,"translate"),t.k0s()()(),t.DNE(9,F,2,0,"div",8)(10,w,5,4),t.k0s()),2&i&&(t.R7$(7),t.SpI(" ",t.bMT(8,2,"CLIENT.NewClient")," "),t.R7$(2),t.vxM(r.isLoading?9:10))},dependencies:[_.gFV,_._ab,_.eeu,_.rFS,_.LGG,_.cvh,_.iov,_.BzB,v.G,L.YU,E.D9,h.Cn,g.mC,g.fw,b.k,R.M,M.K,c.J,n.f,a.p,p.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),I})()}}]);