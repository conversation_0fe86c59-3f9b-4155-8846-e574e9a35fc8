"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3734],{4433:(j,y,s)=>{s.d(y,{Q:()=>R});var D=s(9842),l=s(4438);let R=(()=>{var e;class I{transform(x,p=2){const v=Math.pow(10,p);return(Math.trunc(Number((x*v).toFixed(p+5)))/v).toFixed(p)}}return e=I,(0,D.A)(I,"\u0275fac",function(x){return new(x||e)}),(0,D.A)(I,"\u0275pipe",l.EJ8({name:"decimal",type:e,pure:!0,standalone:!0})),I})()},6617:(j,y,s)=>{s.d(y,{p:()=>e});var D=s(9842),l=s(6473),R=s(4438);let e=(()=>{var I;class f{transform(p){return(0,l.ZV)(p)}}return I=f,(0,D.A)(f,"\u0275fac",function(p){return new(p||I)}),(0,D.A)(f,"\u0275pipe",R.EJ8({name:"formatNumber",type:I,pure:!0,standalone:!0})),f})()},97:(j,y,s)=>{s.d(y,{r:()=>f});var D=s(467),l=s(9842),R=s(2716),e=s(7987),I=s(4438);let f=(()=>{var x;class p extends R.H{open(g){var m=this;return(0,D.A)(function*(){const h=yield Promise.all([s.e(1328),s.e(469)]).then(s.bind(s,469));return m.matDialog.open(h.ModifyInvoiceItemComponent.getComponent(),{panelClass:"custom_dialog",data:g,width:"450px",disableClose:!0,scrollStrategy:new e.t0})})()}}return x=p,(0,l.A)(p,"\u0275fac",(()=>{let v;return function(m){return(v||(v=I.xGo(x)))(m||x)}})()),(0,l.A)(p,"\u0275prov",I.jDH({token:x,factory:x.\u0275fac,providedIn:"root"})),p})()},1456:(j,y,s)=>{s.d(y,{D:()=>x});var D=s(9842),l=s(5312),R=s(1626),e=s(4438),I=s(6473);const f=l.c.HOST_API+"/api";let x=(()=>{var p;class v{constructor(){(0,D.A)(this,"http",(0,e.WQX)(R.Qq))}CreateExpenses(m){return this.http.post(f+"/Expenses/CreateExpenses",m)}UpdateExpenses(m){return this.http.post(f+"/Expenses/UpdateExpenses",m)}GetExpensesById(m){return this.http.get(f+`/Expenses/GetExpensesById?Id=${m}`)}GetAllExpenses(m){const h=(0,I.yU)(m);return this.http.get(f+"/Expenses/GetAllExpenses",{params:h})}GetAllUploadExpenses(m){const h=(0,I.yU)(m);return this.http.get(f+"/Expenses/GetAllUploadExpenses",{params:h})}DeleteFileExpenses(m){return this.http.post(f+"/Expenses/DeleteExpenses",m)}DeleteExpenses(m){return this.http.post(f+"/Expenses/DeleteExpenses",m)}MarkAsPaid(m){return this.http.put(f+`/Expenses/MarkAsPaid?Id=${m}`,null)}GetExpenseItemsByExpenseIds(m){return this.http.post(f+"/Expenses/GetExpenseItemsByExpenseIds",m)}}return p=v,(0,D.A)(v,"\u0275fac",function(m){return new(m||p)}),(0,D.A)(v,"\u0275prov",e.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})),v})()},5909:(j,y,s)=>{s.d(y,{R2:()=>R,Xj:()=>l,az:()=>x,jQ:()=>D,yo:()=>f});const D=(p,v)=>{if(!p||!v)return 0;const g=p.split(":").map(Number);let m=0,h=0,E=0;return 3===g.length?[m,h,E]=g:2===g.length?[m,h]=g:1===g.length&&([m]=g),Number(((m+h/60+E/3600)*v).toFixed(2))},l=(p=[],v=!1)=>(p.some(m=>m.companyTax||m.selected||void 0===m.selected)?p:p.filter(m=>m.selected)).map(m=>{let h,E;return m.companyTax?(h=m.companyTax.name,E=m.companyTax.amount):(h=m.name,E=m.amount),v?`${h} (${E}%)`:h}).filter(Boolean).sort((m,h)=>m.localeCompare(h)).join(", "),R=(p,v)=>p&&v?Number((p*v).toFixed(2)):0,f=p=>{let v=0,g={};p.forEach(({rate:h,qty:E,taxes:S})=>{if(!h||!E)return;const O=Number((h*E).toFixed(2));v+=O,S&&0!==S.length&&(S.some(k=>k.companyTaxId)?S.filter(k=>k.companyTaxId):S.filter(k=>k.selected)).forEach(k=>{const P=k.name||"Unknown Tax",A=k.taxeNumber||"",z=Number(k?.companyTax?.amount??k.amount??0);g[P]||(g[P]={name:P,numberTax:A,amount:z,taxableAmount:0,total:0}),g[P].taxableAmount+=O})});let m=0;return Object.values(g).forEach(h=>{h.total=Number((h.taxableAmount*(h.amount/100)).toFixed(2)),m+=h.total}),{subtotal:Number(v.toFixed(2)),totalTaxes:g,grandTotalTax:Number(m.toFixed(2))}},x=p=>{let v=0,g={};p.forEach(({total:h,taxes:E})=>{h&&(v+=h,E&&0!==E.length)&&(E.some(T=>T.companyTaxId)?E.filter(T=>T.companyTaxId):E.filter(T=>T.selected)).forEach(T=>{const M=T.name||"Unknown Tax",k=T.taxeNumber||"",P=Number(T?.companyTax?.amount??T.amount??0);g[M]||(g[M]={name:M,numberTax:k,amount:P,taxableAmount:0,total:0}),g[M].taxableAmount+=h})});let m=0;return Object.values(g).forEach(h=>{h.total=Number((h.taxableAmount*(h.amount/100)).toFixed(2)),m+=h.total}),{subtotal:Number(v.toFixed(2)),totalTaxes:g,grandTotalTax:Number(m.toFixed(2))}}},3734:(j,y,s)=>{s.r(y),s.d(y,{GenerateInvoiceComponent:()=>we});var D=s(467),l=s(9842),R=s(1110),e=s(4438),I=s(9417),f=s(4006),x=s(9248),p=s(344),v=s(4978),g=s(5599),m=s(6146),h=s(6463),E=s(1970),S=s(687);s(4402),s(7673);let ee=(()=>{var r;class o{constructor(){this._listeners=[]}notify(t,n){for(let a of this._listeners)a(t,n)}listen(t){return this._listeners.push(t),()=>{this._listeners=this._listeners.filter(n=>t!==n)}}ngOnDestroy(){this._listeners=[]}}return(r=o).\u0275fac=function(t){return new(t||r)},r.\u0275prov=e.jDH({token:r,factory:r.\u0275fac,providedIn:"root"}),o})();var N=s(6600),B=s(177);const te=["input"],ne=["formField"],ie=["*"];let X=0;class L{constructor(o,i){this.source=o,this.value=i}}const ae={provide:I.kq,useExisting:(0,e.Rfq)(()=>W),multi:!0},Q=new e.nKC("MatRadioGroup"),oe=new e.nKC("mat-radio-default-options",{providedIn:"root",factory:function re(){return{color:"accent",disabledInteractive:!1}}});let W=(()=>{var r;class o{get name(){return this._name}set name(t){this._name=t,this._updateRadioButtonNames()}get labelPosition(){return this._labelPosition}set labelPosition(t){this._labelPosition="before"===t?"before":"after",this._markRadiosForCheck()}get value(){return this._value}set value(t){this._value!==t&&(this._value=t,this._updateSelectedRadioFromValue(),this._checkSelectedRadioButton())}_checkSelectedRadioButton(){this._selected&&!this._selected.checked&&(this._selected.checked=!0)}get selected(){return this._selected}set selected(t){this._selected=t,this.value=t?t.value:null,this._checkSelectedRadioButton()}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._markRadiosForCheck()}get required(){return this._required}set required(t){this._required=t,this._markRadiosForCheck()}get disabledInteractive(){return this._disabledInteractive}set disabledInteractive(t){this._disabledInteractive=t,this._markRadiosForCheck()}constructor(t){this._changeDetector=t,this._value=null,this._name="mat-radio-group-"+X++,this._selected=null,this._isInitialized=!1,this._labelPosition="after",this._disabled=!1,this._required=!1,this._controlValueAccessorChangeFn=()=>{},this.onTouched=()=>{},this.change=new e.bkB,this._disabledInteractive=!1}ngAfterContentInit(){this._isInitialized=!0,this._buttonChanges=this._radios.changes.subscribe(()=>{this.selected&&!this._radios.find(t=>t===this.selected)&&(this._selected=null)})}ngOnDestroy(){this._buttonChanges?.unsubscribe()}_touch(){this.onTouched&&this.onTouched()}_updateRadioButtonNames(){this._radios&&this._radios.forEach(t=>{t.name=this.name,t._markForCheck()})}_updateSelectedRadioFromValue(){this._radios&&(null===this._selected||this._selected.value!==this._value)&&(this._selected=null,this._radios.forEach(n=>{n.checked=this.value===n.value,n.checked&&(this._selected=n)}))}_emitChangeEvent(){this._isInitialized&&this.change.emit(new L(this._selected,this._value))}_markRadiosForCheck(){this._radios&&this._radios.forEach(t=>t._markForCheck())}writeValue(t){this.value=t,this._changeDetector.markForCheck()}registerOnChange(t){this._controlValueAccessorChangeFn=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t,this._changeDetector.markForCheck()}}return(r=o).\u0275fac=function(t){return new(t||r)(e.rXU(e.gRc))},r.\u0275dir=e.FsC({type:r,selectors:[["mat-radio-group"]],contentQueries:function(t,n,a){if(1&t&&e.wni(a,$,5),2&t){let c;e.mGM(c=e.lsd())&&(n._radios=c)}},hostAttrs:["role","radiogroup",1,"mat-mdc-radio-group"],inputs:{color:"color",name:"name",labelPosition:"labelPosition",value:"value",selected:"selected",disabled:[2,"disabled","disabled",e.L39],required:[2,"required","required",e.L39],disabledInteractive:[2,"disabledInteractive","disabledInteractive",e.L39]},outputs:{change:"change"},exportAs:["matRadioGroup"],standalone:!0,features:[e.Jv_([ae,{provide:Q,useExisting:r}]),e.GFd]}),o})(),$=(()=>{var r;class o{get checked(){return this._checked}set checked(t){this._checked!==t&&(this._checked=t,t&&this.radioGroup&&this.radioGroup.value!==this.value?this.radioGroup.selected=this:!t&&this.radioGroup&&this.radioGroup.value===this.value&&(this.radioGroup.selected=null),t&&this._radioDispatcher.notify(this.id,this.name),this._changeDetector.markForCheck())}get value(){return this._value}set value(t){this._value!==t&&(this._value=t,null!==this.radioGroup&&(this.checked||(this.checked=this.radioGroup.value===t),this.checked&&(this.radioGroup.selected=this)))}get labelPosition(){return this._labelPosition||this.radioGroup&&this.radioGroup.labelPosition||"after"}set labelPosition(t){this._labelPosition=t}get disabled(){return this._disabled||null!==this.radioGroup&&this.radioGroup.disabled}set disabled(t){this._setDisabled(t)}get required(){return this._required||this.radioGroup&&this.radioGroup.required}set required(t){this._required=t}get color(){return this._color||this.radioGroup&&this.radioGroup.color||this._defaultOptions&&this._defaultOptions.color||"accent"}set color(t){this._color=t}get disabledInteractive(){return this._disabledInteractive||null!==this.radioGroup&&this.radioGroup.disabledInteractive}set disabledInteractive(t){this._disabledInteractive=t}get inputId(){return`${this.id||this._uniqueId}-input`}constructor(t,n,a,c,d,u,_,C){this._elementRef=n,this._changeDetector=a,this._focusMonitor=c,this._radioDispatcher=d,this._defaultOptions=_,this._ngZone=(0,e.WQX)(e.SKi),this._uniqueId="mat-radio-"+ ++X,this.id=this._uniqueId,this.disableRipple=!1,this.tabIndex=0,this.change=new e.bkB,this._checked=!1,this._value=null,this._removeUniqueSelectionListener=()=>{},this._injector=(0,e.WQX)(e.zZn),this._onInputClick=b=>{this.disabled&&this.disabledInteractive&&b.preventDefault()},this.radioGroup=t,this._noopAnimations="NoopAnimations"===u,this._disabledInteractive=_?.disabledInteractive??!1,C&&(this.tabIndex=(0,e.Udg)(C,0))}focus(t,n){n?this._focusMonitor.focusVia(this._inputElement,n,t):this._inputElement.nativeElement.focus(t)}_markForCheck(){this._changeDetector.markForCheck()}ngOnInit(){this.radioGroup&&(this.checked=this.radioGroup.value===this._value,this.checked&&(this.radioGroup.selected=this),this.name=this.radioGroup.name),this._removeUniqueSelectionListener=this._radioDispatcher.listen((t,n)=>{t!==this.id&&n===this.name&&(this.checked=!1)})}ngDoCheck(){this._updateTabIndex()}ngAfterViewInit(){this._updateTabIndex(),this._focusMonitor.monitor(this._elementRef,!0).subscribe(t=>{!t&&this.radioGroup&&this.radioGroup._touch()}),this._ngZone.runOutsideAngular(()=>{this._inputElement.nativeElement.addEventListener("click",this._onInputClick)})}ngOnDestroy(){this._inputElement?.nativeElement.removeEventListener("click",this._onInputClick),this._focusMonitor.stopMonitoring(this._elementRef),this._removeUniqueSelectionListener()}_emitChangeEvent(){this.change.emit(new L(this,this._value))}_isRippleDisabled(){return this.disableRipple||this.disabled}_onInputInteraction(t){if(t.stopPropagation(),!this.checked&&!this.disabled){const n=this.radioGroup&&this.value!==this.radioGroup.value;this.checked=!0,this._emitChangeEvent(),this.radioGroup&&(this.radioGroup._controlValueAccessorChangeFn(this.value),n&&this.radioGroup._emitChangeEvent())}}_onTouchTargetClick(t){this._onInputInteraction(t),(!this.disabled||this.disabledInteractive)&&this._inputElement?.nativeElement.focus()}_setDisabled(t){this._disabled!==t&&(this._disabled=t,this._changeDetector.markForCheck())}_updateTabIndex(){const t=this.radioGroup;let n;if(n=t&&t.selected&&!this.disabled?t.selected===this?this.tabIndex:-1:this.tabIndex,n!==this._previousTabIndex){const a=this._inputElement?.nativeElement;a&&(a.setAttribute("tabindex",n+""),this._previousTabIndex=n,(0,e.mal)(()=>{queueMicrotask(()=>{t&&t.selected&&t.selected!==this&&document.activeElement===a&&(t.selected?._inputElement.nativeElement.focus(),document.activeElement===a&&this._inputElement.nativeElement.blur())})},{injector:this._injector}))}}}return(r=o).\u0275fac=function(t){return new(t||r)(e.rXU(Q,8),e.rXU(e.aKT),e.rXU(e.gRc),e.rXU(S.FN),e.rXU(ee),e.rXU(e.bc$,8),e.rXU(oe,8),e.kS0("tabindex"))},r.\u0275cmp=e.VBU({type:r,selectors:[["mat-radio-button"]],viewQuery:function(t,n){if(1&t&&(e.GBs(te,5),e.GBs(ne,7,e.aKT)),2&t){let a;e.mGM(a=e.lsd())&&(n._inputElement=a.first),e.mGM(a=e.lsd())&&(n._rippleTrigger=a.first)}},hostAttrs:[1,"mat-mdc-radio-button"],hostVars:19,hostBindings:function(t,n){1&t&&e.bIt("focus",function(){return n._inputElement.nativeElement.focus()}),2&t&&(e.BMQ("id",n.id)("tabindex",null)("aria-label",null)("aria-labelledby",null)("aria-describedby",null),e.AVh("mat-primary","primary"===n.color)("mat-accent","accent"===n.color)("mat-warn","warn"===n.color)("mat-mdc-radio-checked",n.checked)("mat-mdc-radio-disabled",n.disabled)("mat-mdc-radio-disabled-interactive",n.disabledInteractive)("_mat-animation-noopable",n._noopAnimations))},inputs:{id:"id",name:"name",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],disableRipple:[2,"disableRipple","disableRipple",e.L39],tabIndex:[2,"tabIndex","tabIndex",i=>null==i?0:(0,e.Udg)(i)],checked:[2,"checked","checked",e.L39],value:"value",labelPosition:"labelPosition",disabled:[2,"disabled","disabled",e.L39],required:[2,"required","required",e.L39],color:"color",disabledInteractive:[2,"disabledInteractive","disabledInteractive",e.L39]},outputs:{change:"change"},exportAs:["matRadioButton"],standalone:!0,features:[e.GFd,e.aNF],ngContentSelectors:ie,decls:13,vars:17,consts:[["formField",""],["input",""],["mat-internal-form-field","",3,"labelPosition"],[1,"mdc-radio"],[1,"mat-mdc-radio-touch-target",3,"click"],["type","radio",1,"mdc-radio__native-control",3,"change","id","checked","disabled","required"],[1,"mdc-radio__background"],[1,"mdc-radio__outer-circle"],[1,"mdc-radio__inner-circle"],["mat-ripple","",1,"mat-radio-ripple","mat-mdc-focus-indicator",3,"matRippleTrigger","matRippleDisabled","matRippleCentered"],[1,"mat-ripple-element","mat-radio-persistent-ripple"],[1,"mdc-label",3,"for"]],template:function(t,n){if(1&t){const a=e.RV6();e.NAR(),e.j41(0,"div",2,0)(2,"div",3)(3,"div",4),e.bIt("click",function(d){return e.eBV(a),e.Njj(n._onTouchTargetClick(d))}),e.k0s(),e.j41(4,"input",5,1),e.bIt("change",function(d){return e.eBV(a),e.Njj(n._onInputInteraction(d))}),e.k0s(),e.j41(6,"div",6),e.nrm(7,"div",7)(8,"div",8),e.k0s(),e.j41(9,"div",9),e.nrm(10,"div",10),e.k0s()(),e.j41(11,"label",11),e.SdG(12),e.k0s()()}2&t&&(e.Y8G("labelPosition",n.labelPosition),e.R7$(2),e.AVh("mdc-radio--disabled",n.disabled),e.R7$(2),e.Y8G("id",n.inputId)("checked",n.checked)("disabled",n.disabled&&!n.disabledInteractive)("required",n.required),e.BMQ("name",n.name)("value",n.value)("aria-label",n.ariaLabel)("aria-labelledby",n.ariaLabelledby)("aria-describedby",n.ariaDescribedby)("aria-disabled",n.disabled&&n.disabledInteractive?"true":null),e.R7$(5),e.Y8G("matRippleTrigger",n._rippleTrigger.nativeElement)("matRippleDisabled",n._isRippleDisabled())("matRippleCentered",!0),e.R7$(2),e.Y8G("for",n.inputId))},dependencies:[N.r6,N.tO],styles:['.mat-mdc-radio-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-radio-button .mdc-radio{display:inline-block;position:relative;flex:0 0 auto;box-sizing:content-box;width:20px;height:20px;cursor:pointer;will-change:opacity,transform,border-color,color;padding:calc((var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before{opacity:.04;transform:scale(1)}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled])~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-hover-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-hover-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-pressed-icon-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio:active .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-pressed-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__background{display:inline-block;position:relative;box-sizing:border-box;width:20px;height:20px}.mat-mdc-radio-button .mdc-radio__background::before{position:absolute;transform:scale(0, 0);border-radius:50%;opacity:0;pointer-events:none;content:"";transition:opacity 90ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms cubic-bezier(0.4, 0, 0.6, 1);width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size);top:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2);left:calc(-1*(var(--mdc-radio-state-layer-size) - 20px)/2)}.mat-mdc-radio-button .mdc-radio__outer-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;border-width:2px;border-style:solid;border-radius:50%;transition:border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__inner-circle{position:absolute;top:0;left:0;box-sizing:border-box;width:100%;height:100%;transform:scale(0, 0);border-width:10px;border-style:solid;border-radius:50%;transition:transform 90ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms cubic-bezier(0.4, 0, 0.6, 1)}.mat-mdc-radio-button .mdc-radio__native-control{position:absolute;margin:0;padding:0;opacity:0;top:0;right:0;left:0;cursor:inherit;z-index:1;width:var(--mdc-radio-state-layer-size);height:var(--mdc-radio-state-layer-size)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{transition:border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle{transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:focus+.mdc-radio__background::before{transform:scale(1);opacity:.12;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 1),transform 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background{cursor:default}.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__inner-circle{border-color:var(--mdc-radio-selected-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button .mdc-radio__native-control:enabled:focus:checked+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-selected-focus-icon-color, var(--mat-app-primary))}.mat-mdc-radio-button .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle{transform:scale(0.5);transition:transform 90ms cubic-bezier(0, 0, 0.2, 1),border-color 90ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled{pointer-events:auto}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-unselected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-unselected-icon-opacity)}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled:hover .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control:checked:focus+.mdc-radio__background .mdc-radio__outer-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__inner-circle,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__native-control+.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-disabled-selected-icon-color, var(--mat-app-on-surface));opacity:var(--mdc-radio-disabled-selected-icon-opacity)}.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__background::before,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__outer-circle,.mat-mdc-radio-button._mat-animation-noopable .mdc-radio__inner-circle{transition:none !important}.mat-mdc-radio-button .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before{background-color:var(--mat-radio-checked-ripple-color, var(--mat-app-primary))}.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mat-ripple-element,.mat-mdc-radio-button.mat-mdc-radio-disabled-interactive .mdc-radio--disabled .mdc-radio__background::before{background-color:var(--mat-radio-ripple-color, var(--mat-app-on-surface))}.mat-mdc-radio-button .mat-internal-form-field{color:var(--mat-radio-label-text-color, var(--mat-app-on-surface));font-family:var(--mat-radio-label-text-font, var(--mat-app-body-medium-font));line-height:var(--mat-radio-label-text-line-height, var(--mat-app-body-medium-line-height));font-size:var(--mat-radio-label-text-size, var(--mat-app-body-medium-size));letter-spacing:var(--mat-radio-label-text-tracking, var(--mat-app-body-medium-tracking));font-weight:var(--mat-radio-label-text-weight, var(--mat-app-body-medium-weight))}.mat-mdc-radio-button .mdc-radio--disabled+label{color:var(--mat-radio-disabled-label-color)}.mat-mdc-radio-button .mat-radio-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:50%}.mat-mdc-radio-button .mat-radio-ripple .mat-ripple-element{opacity:.14}.mat-mdc-radio-button .mat-radio-ripple::before{border-radius:50%}.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:focus:enabled:not(:checked)~.mdc-radio__background .mdc-radio__outer-circle{border-color:var(--mdc-radio-unselected-focus-icon-color, black)}.mat-mdc-radio-button.cdk-focused .mat-mdc-focus-indicator::before{content:""}.mat-mdc-radio-disabled{cursor:default;pointer-events:none}.mat-mdc-radio-disabled.mat-mdc-radio-disabled-interactive{pointer-events:auto}.mat-mdc-radio-touch-target{position:absolute;top:50%;left:50%;height:48px;width:48px;transform:translate(-50%, -50%);display:var(--mat-radio-touch-target-display)}[dir=rtl] .mat-mdc-radio-touch-target{left:auto;right:50%;transform:translate(50%, -50%)}'],encapsulation:2,changeDetection:0}),o})(),se=(()=>{var r;class o{}return(r=o).\u0275fac=function(t){return new(t||r)},r.\u0275mod=e.$C({type:r}),r.\u0275inj=e.G2t({imports:[N.yE,B.MD,N.pZ,$,N.yE]}),o})();var de=s(9424),le=s(3202),ce=s(8192),G=s(6473),me=s(3200),Y=s(7656),K=s(6617),H=s(3492),ue=s(6586),pe=s(4805),he=s(4843);let _e=(()=>{var r;class o{constructor(){var t=this;(0,l.A)(this,"timeTrackingService",(0,e.WQX)(pe.y)),(0,l.A)(this,"fetchListProjectByClientAndDate",function(){var n=(0,D.A)(function*(a){const{clientId:c,startDate:d,endDate:u,page:_}=a;if(!c)return null;let C={Page:_,PageSize:20,Search:"",Filter:{clientId:c}};if(d&&u){if(!(0,G.O5)(d,u))return null;C={...C,startDate:(0,G.cn)(d),endDate:(0,G.cn)(u)}}return(yield(0,he._)(t.timeTrackingService.GetAllTimeTracking(C)))??[]});return function(a){return n.apply(this,arguments)}}())}}return r=o,(0,l.A)(o,"\u0275fac",function(t){return new(t||r)}),(0,l.A)(o,"\u0275prov",e.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})),o})();var F=s(2840),V=s(1448),Z=s(1537),ve=s(1456),ge=s(9079),J=s(4433),w=s(5236);function be(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",1)(1,"div",2)(2,"div",3)(3,"app-inno-form-checkbox",4),e.bIt("onChange",function(){const n=e.eBV(i).$index,a=e.XpG();return e.Njj(a.handleToggleCheckedIndex(n))}),e.k0s()(),e.j41(4,"p",10),e.EFF(5),e.k0s()(),e.j41(6,"p",13),e.EFF(7),e.k0s(),e.j41(8,"p",14),e.EFF(9),e.k0s(),e.j41(10,"p",14),e.EFF(11),e.nI1(12,"decimal"),e.nI1(13,"formatNumber"),e.k0s()()}if(2&r){let i,t,n,a;const c=o.$implicit,d=o.$index,u=e.XpG();e.R7$(3),e.Y8G("checked",u.isCheckedIndex(d)),e.R7$(2),e.SpI(" ",null!==(i=c.expensesName)&&void 0!==i?i:""," "),e.R7$(2),e.SpI(" ",null!==(t=c.categoryName)&&void 0!==t?t:""," "),e.R7$(2),e.SpI(" ",null!==(n=c.itemName)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" $",e.bMT(13,8,e.i5U(12,5,null!==(a=c.paidAmount)&&void 0!==a?a:0,2))," ")}}F.is5.Inject(F.Rav);let fe=(()=>{var r;class o{constructor(){(0,l.A)(this,"totalPages",1),(0,l.A)(this,"currentPage",1),(0,l.A)(this,"pageSizes",[10,20,50,100]),(0,l.A)(this,"pageSizesDefault",20),(0,l.A)(this,"clientId",void 0),(0,l.A)(this,"valueRadio",void 0),(0,l.A)(this,"listProjectId",void 0),(0,l.A)(this,"cancel",new e.bkB(!1)),(0,l.A)(this,"listExpensesItem",[]),(0,l.A)(this,"listIndexExpensesSelected",[]),(0,l.A)(this,"_toastService",(0,e.WQX)(H.f)),(0,l.A)(this,"_storeService",(0,e.WQX)(R.n)),(0,l.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,l.A)(this,"expenseService",(0,e.WQX)(ve.D)),(0,l.A)(this,"newInvoiceDialog",(0,e.WQX)(Z.u))}ngOnChanges(t){const n=t?.valueRadio?.currentValue??null;n&&this._GetData(n)}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize),t?.currentPage&&(this.currentPage=t.currentPage)}isCheckedIndex(t){return this.listIndexExpensesSelected.includes(t)}handleToggleCheckedIndex(t){const n=this.isCheckedIndex(t);let a=[...this.listIndexExpensesSelected];n?a=a.filter(c=>c!==t):a.push(t),this.listIndexExpensesSelected=a}get totalAmount(){return this.listExpensesItem.reduce((t,n,a)=>this.listIndexExpensesSelected.includes(a)?t+=n?.paidAmount??0:t,0)}handleCheckedAll(t){this.listIndexExpensesSelected=t?this.listExpensesItem.map((n,a)=>a):[]}_GetData(t){const n={Page:1,PageSize:20,Search:""};2!=t&&(n.clientId=this.clientId),2==t&&(n.projectIds=this.listProjectId),this.expenseService.GetAllExpenses(n).pipe((0,ge.pQ)(this.destroyRef)).subscribe({next:a=>{a&&(this.listExpensesItem=a.data,this.totalPages=a.totalRecords)}})}handleCancel(){this.cancel.emit(!0)}handleSubmit(){this.cancel.emit(!0);const t=this.listExpensesItem.filter((u,_)=>this.listIndexExpensesSelected.includes(_)),n={clientId:this.clientId,isGenrate:!0,itemInvoices:t.map(u=>({ExpensesId:u?.id??"",date:u?.date??null,description:`(Expenses) ${u?.expensesName}\n ${u?.categoryName} ${u?.itemName}`,rate:u?.project?.hourlyRate,qty:0,taxes:[],total:u.paidAmount,metadata:{hours:u?.endTime??"00:00:00",timeTracking:u}}))};n.invoiceDate=new Date,n.dueDate=new Date;const a=this.listExpensesItem.map(u=>new Date(u.date)),c=a.reduce((u,_)=>_<u?_:u,new Date),d=a.reduce((u,_)=>_>u?_:u,new Date);n.invoiceDate=c,n.dueDate=d,this.newInvoiceDialog.open(n)}}return r=o,(0,l.A)(o,"\u0275fac",function(t){return new(t||r)}),(0,l.A)(o,"\u0275cmp",e.VBU({type:r,selectors:[["app-genrate-invoice-expenses"]],inputs:{clientId:"clientId",valueRadio:"valueRadio",listProjectId:"listProjectId"},outputs:{cancel:"cancel"},standalone:!0,features:[e.OA$,e.aNF],decls:30,vars:29,consts:[[1,"overflow-auto","w-full"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","pt-[15px]","pb-[3px]","flex-wrap","justify-end","items-center","gap-[16px]"],[1,"text-text-primary","text-text-sm-regular"],[1,"text-headline-sm-bold","text-text-primary"],[3,"onCancel","onSubmit"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-right"]],template:function(t,n){1&t&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"app-inno-form-checkbox",4),e.bIt("onChange",function(c){return n.handleCheckedAll(c)}),e.k0s()(),e.j41(5,"p",5),e.EFF(6),e.nI1(7,"translate"),e.k0s()(),e.j41(8,"p",6),e.EFF(9),e.nI1(10,"translate"),e.k0s(),e.j41(11,"p",7),e.EFF(12),e.nI1(13,"translate"),e.k0s(),e.j41(14,"p",7),e.EFF(15),e.nI1(16,"translate"),e.k0s()(),e.Z7z(17,be,14,10,"div",1,e.fX1),e.j41(19,"ejs-pager",8),e.bIt("click",function(c){return n.onPageChange(c)}),e.k0s()(),e.j41(20,"div",9)(21,"p",10),e.EFF(22),e.nI1(23,"translate"),e.nI1(24,"async"),e.k0s(),e.j41(25,"p",11),e.EFF(26),e.nI1(27,"decimal"),e.nI1(28,"formatNumber"),e.k0s()(),e.j41(29,"app-inno-modal-footer",12),e.bIt("onCancel",function(){return n.handleCancel()})("onSubmit",function(){return n.handleSubmit()}),e.k0s()),2&t&&(e.R7$(4),e.Y8G("checked",n.listIndexExpensesSelected.length===n.listExpensesItem.length),e.R7$(2),e.SpI(" ",e.bMT(7,12,"GENERATEINVOICE.EXPENSES.ExpensesName")," "),e.R7$(3),e.SpI(" ",e.bMT(10,14,"GENERATEINVOICE.EXPENSES.CategoryName")," "),e.R7$(3),e.SpI(" ",e.bMT(13,16,"GENERATEINVOICE.EXPENSES.ItemName")," "),e.R7$(3),e.SpI(" ",e.bMT(16,18,"GENERATEINVOICE.EXPENSES.PaidAmount")," "),e.R7$(2),e.Dyx(n.listExpensesItem),e.R7$(2),e.Y8G("pageSize",n.pageSizesDefault)("totalRecordsCount",n.totalPages)("currentPage",n.currentPage)("pageSizes",n.pageSizes),e.R7$(3),e.Lme(" ",e.bMT(23,20,"GENERATEINVOICE.AmountDue")," (",e.bMT(24,22,n._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(28,27,e.i5U(27,24,n.totalAmount,2))," "))},dependencies:[m.G,B.Jj,w.D9,V.iov,V.BzB,K.p,J.Q,Y.V,p.k],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(180px,1fr) 180px 100px 120px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),o})();var q=s(5909),Ie=s(97);const Ee=["selectSearchClientElement"],Ce=["rangeDatePopover"],xe=r=>({required:r});function De(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",18),e.bIt("click",function(){const n=e.eBV(i).$implicit,a=e.XpG();return e.Njj(a.handleSelectClient(n))}),e.nrm(1,"ngx-avatars",19),e.j41(2,"div",16)(3,"p",20),e.EFF(4),e.k0s()()()}if(2&r){const i=o.$implicit,t=e.XpG();e.AVh("selected",i.value===t.f.clientId.value),e.R7$(),e.Y8G("size",32)("name",i.label),e.R7$(3),e.SpI(" ",i.label," ")}}function Te(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",24),e.bIt("click",function(){const n=e.eBV(i).$implicit,a=e.XpG(2);return e.Njj(a.handleSelectDate(n))}),e.j41(1,"div",16)(2,"p",20),e.EFF(3),e.k0s()()()}if(2&r){const i=o.$implicit,t=e.XpG(2);e.AVh("selected",i.value===t.rangeDateOptionSelected.value),e.R7$(3),e.SpI(" ",i.label," ")}}function ke(r,o){1&r&&e.nrm(0,"app-inno-error-message",29)}function ye(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",25)(1,"div",26)(2,"app-inno-form-datepicker",27),e.bIt("onChange",function(n){e.eBV(i);const a=e.XpG(2);return e.Njj(a.handleSelectCustomDate("startDate",n))}),e.k0s()(),e.j41(3,"div",26)(4,"app-inno-form-datepicker",28),e.bIt("onChange",function(n){e.eBV(i);const a=e.XpG(2);return e.Njj(a.handleSelectCustomDate("endDate",n))}),e.k0s()()(),e.DNE(5,ke,1,0,"app-inno-error-message",29)}if(2&r){const i=e.XpG(2);e.R7$(2),e.Y8G("value",null==i.rangeDateOptionSelected.metadata?null:i.rangeDateOptionSelected.metadata.startDate),e.R7$(2),e.Y8G("value",null==i.rangeDateOptionSelected.metadata?null:i.rangeDateOptionSelected.metadata.endDate),e.R7$(),e.vxM(i.isShowCustomDateError?5:-1)}}function Re(r,o){if(1&r&&(e.j41(0,"div",21)(1,"div",22),e.Z7z(2,Te,4,3,"div",23,e.fX1),e.DNE(4,ye,6,3),e.k0s()()),2&r){const i=e.XpG();e.R7$(2),e.Dyx(i.rangDateOptions),e.R7$(2),e.vxM(i.rangeDateOptionSelected.value===i.rangDateType.custom?4:-1)}}function Se(r,o){1&r&&(e.j41(0,"mat-radio-button",15),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&r&&(e.Y8G("value",2),e.R7$(),e.SpI(" ",e.bMT(2,2,"GENERATEINVOICE.OnlyUnbilledExpensesProjects")," "))}function Ae(r,o){1&r&&(e.j41(0,"div",16),e.nrm(1,"app-inno-empty-data",30),e.nI1(2,"translate"),e.nI1(3,"translate"),e.k0s()),2&r&&(e.R7$(),e.Y8G("title",e.bMT(2,2,"EMPTY.EmptyProject"))("description",e.bMT(3,4,"COMMON.DescriptionEmptyProject")))}function Ge(r,o){1&r&&(e.j41(0,"div",31),e.nrm(1,"app-inno-spin"),e.k0s())}function Me(r,o){1&r&&(e.j41(0,"div",16),e.nrm(1,"app-inno-empty-data",33),e.k0s())}function Pe(r,o){if(1&r&&(e.j41(0,"div",46),e.nrm(1,"ngx-avatars",49),e.j41(2,"span",50),e.EFF(3),e.k0s()()),2&r){const i=e.XpG().$implicit,t=e.XpG(4);e.R7$(),e.FS9("bgColor",t._storeService.getBgColor(null==i||null==i.inforUser?null:i.inforUser.firstName.slice(0,1))),e.Y8G("size",30)("name",(null==i?null:i.inforUser.firstName.charAt(0))+" "+(null!=i&&null!=i.inforUser&&i.inforUser.lastName?null==i||null==i.inforUser?null:i.inforUser.lastName.charAt(0):"")),e.R7$(2),e.Lme(" ",null==i||null==i.inforUser?null:i.inforUser.firstName," ",null==i||null==i.inforUser?null:i.inforUser.lastName,"")}}function je(r,o){if(1&r&&(e.j41(0,"div",46),e.nrm(1,"ngx-avatars",49),e.j41(2,"span",50),e.EFF(3),e.k0s()()),2&r){const i=e.XpG().$implicit,t=e.XpG(4);e.R7$(),e.FS9("bgColor",t._storeService.getBgColor(null==i||null==i.inforUser?null:i.inforUser.email.slice(0,1))),e.Y8G("size",30)("name",null==i||null==i.inforUser?null:i.inforUser.email.slice(0,1)),e.R7$(2),e.SpI(" ",null==i||null==i.inforUser?null:i.inforUser.email,"")}}function Oe(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",35)(1,"div",36)(2,"div",37)(3,"app-inno-form-checkbox",38),e.bIt("onChange",function(){const n=e.eBV(i).$index,a=e.XpG(4);return e.Njj(a.handleToggleCheckedIndex(n))}),e.k0s()(),e.j41(4,"p",44),e.EFF(5),e.k0s()(),e.j41(6,"div",16)(7,"p",44),e.DNE(8,Pe,4,5,"div",46)(9,je,4,4,"div",46),e.k0s()(),e.j41(10,"p",47),e.EFF(11),e.k0s(),e.j41(12,"p",48),e.EFF(13),e.k0s(),e.j41(14,"p",48),e.EFF(15),e.nI1(16,"decimal"),e.nI1(17,"formatNumber"),e.k0s()()}if(2&r){let i,t,n;const a=o.$implicit,c=o.$index,d=e.XpG(4);e.R7$(3),e.Y8G("checked",d.isCheckedIndex(c)),e.R7$(2),e.SpI(" ",null!==(i=a.description)&&void 0!==i?i:""," "),e.R7$(3),e.vxM(null!=a&&null!=a.inforUser&&a.inforUser.firstName?8:9),e.R7$(3),e.SpI(" ",null!==(t=null==a.metadata||null==a.metadata.timeTracking||null==a.metadata.timeTracking.project?null:a.metadata.timeTracking.project.projectName)&&void 0!==t?t:"-"," "),e.R7$(2),e.SpI(" ",null!==(n=null==a.metadata?null:a.metadata.hours)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" $",e.bMT(17,9,e.i5U(16,6,d.calculateTotalInvoiceItem(null==a?null:a.rate,null==a?null:a.qty),2))," ")}}function Ne(r,o){if(1&r){const i=e.RV6();e.j41(0,"label",9),e.EFF(1),e.nI1(2,"translate"),e.k0s(),e.j41(3,"div",34)(4,"div",35)(5,"div",36)(6,"div",37)(7,"app-inno-form-checkbox",38),e.bIt("onChange",function(n){e.eBV(i);const a=e.XpG(3);return e.Njj(a.handleCheckedAll(n))}),e.k0s()(),e.j41(8,"p",39),e.EFF(9),e.nI1(10,"translate"),e.k0s()(),e.j41(11,"p",40),e.EFF(12),e.nI1(13,"translate"),e.k0s(),e.j41(14,"p",40),e.EFF(15),e.nI1(16,"translate"),e.k0s(),e.j41(17,"p",41),e.EFF(18),e.nI1(19,"translate"),e.k0s(),e.j41(20,"p",41),e.EFF(21),e.nI1(22,"translate"),e.k0s()(),e.Z7z(23,Oe,18,11,"div",35,e.fX1),e.j41(25,"ejs-pager",42),e.bIt("click",function(n){e.eBV(i);const a=e.XpG(3);return e.Njj(a.onPageChange(n))}),e.k0s()(),e.j41(26,"div",43)(27,"p",44),e.EFF(28),e.nI1(29,"translate"),e.nI1(30,"async"),e.k0s(),e.j41(31,"p",45),e.EFF(32),e.nI1(33,"decimal"),e.nI1(34,"formatNumber"),e.k0s()()}if(2&r){const i=e.XpG(3);e.R7$(),e.SpI(" ",e.bMT(2,14,"GENERATEINVOICE.ChooseProject")," "),e.R7$(6),e.Y8G("checked",i.listIndexInvoiceSelected.length===i.listInvoiceItem.length),e.R7$(2),e.SpI(" ",e.bMT(10,16,"GENERATEINVOICE.TableHeaders.Description")," "),e.R7$(3),e.SpI(" ",e.bMT(13,18,"GENERATEINVOICE.TableHeaders.User")," "),e.R7$(3),e.SpI(" ",e.bMT(16,20,"GENERATEINVOICE.TableHeaders.Project")," "),e.R7$(3),e.SpI(" ",e.bMT(19,22,"GENERATEINVOICE.TableHeaders.Hours")," "),e.R7$(3),e.SpI(" ",e.bMT(22,24,"GENERATEINVOICE.TableHeaders.LineTotal")," "),e.R7$(2),e.Dyx(i.listInvoiceItem),e.R7$(2),e.Y8G("pageSize",i.pageSizesDefault)("totalRecordsCount",i.totalPages)("currentPage",i.currentPage)("pageSizes",i.pageSizes),e.R7$(3),e.Lme(" ",e.bMT(29,26,"GENERATEINVOICE.AmountDue")," (",e.bMT(30,28,i._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(34,33,e.i5U(33,30,i.totalAmount(),2))," ")}}function Fe(r,o){if(1&r){const i=e.RV6();e.j41(0,"app-genrate-invoice-expenses",51),e.bIt("cancel",function(){e.eBV(i);const n=e.XpG(3);return e.Njj(n.closeDialog())}),e.k0s()}if(2&r){const i=e.XpG(3);e.Y8G("valueRadio",i.selectedRadio)("clientId",i.f.clientId.value)("listProjectId",i.listProjectId)}}function Ve(r,o){if(1&r&&e.DNE(0,Me,2,0,"div",16)(1,Ne,35,35)(2,Fe,1,3,"app-genrate-invoice-expenses",32),2&r){const i=e.XpG(2);e.vxM(0==i.listInvoiceItem.length?0:i.selectedRadio?2:1)}}function Be(r,o){if(1&r&&(e.j41(0,"div",8)(1,"div",16),e.DNE(2,Ge,2,0,"div",31)(3,Ve,3,1),e.k0s()()),2&r){const i=e.XpG();e.R7$(2),e.vxM(i.isFetchingProject?2:3)}}function $e(r,o){if(1&r){const i=e.RV6();e.j41(0,"div",17)(1,"app-inno-modal-footer",52),e.bIt("onCancel",function(){e.eBV(i);const n=e.XpG();return e.Njj(n.handleCancel())})("onSubmit",function(){e.eBV(i);const n=e.XpG();return e.Njj(n.handleSubmit())}),e.k0s()()}}F.is5.Inject(F.Rav);let we=(()=>{var r;class o{static getComponent(){return o}constructor(t,n,a,c){var d=this;(0,l.A)(this,"dialogRef",void 0),(0,l.A)(this,"modifyInvoiceItemDialog",void 0),(0,l.A)(this,"newInvoiceDialog",void 0),(0,l.A)(this,"data",void 0),(0,l.A)(this,"rangDateType",ue.a),(0,l.A)(this,"listProjectId",[]),(0,l.A)(this,"totalPages",1),(0,l.A)(this,"currentPage",1),(0,l.A)(this,"pageSizes",[10,20,50,100]),(0,l.A)(this,"pageSizesDefault",20),(0,l.A)(this,"selectedRadio",0),(0,l.A)(this,"isFetchingProject",!1),(0,l.A)(this,"clientOptions",[]),(0,l.A)(this,"generateInvoiceForm",void 0),(0,l.A)(this,"isShowCustomDateError",!1),(0,l.A)(this,"listInvoiceItem",[]),(0,l.A)(this,"listIndexInvoiceSelected",[]),(0,l.A)(this,"calculateTotalInvoiceItem",q.R2),(0,l.A)(this,"translate",(0,e.WQX)(w.c$)),(0,l.A)(this,"_toastService",(0,e.WQX)(H.f)),(0,l.A)(this,"_storeService",(0,e.WQX)(R.n)),(0,l.A)(this,"formBuilder",(0,e.WQX)(I.ze)),(0,l.A)(this,"dropdownOptionService",(0,e.WQX)(h.R)),(0,l.A)(this,"generateInvoiceProvider",(0,e.WQX)(_e)),(0,l.A)(this,"selectSearchClientElement",void 0),(0,l.A)(this,"rangeDatePopoverElement",void 0),(0,l.A)(this,"rangDateOptions",[{label:this.translate.instant("GENERATEINVOICE.RangDateOptions.AllTime"),value:this.rangDateType.all},{label:this.translate.instant("GENERATEINVOICE.RangDateOptions.ThisMonth"),value:this.rangDateType.this_month},{label:this.translate.instant("GENERATEINVOICE.RangDateOptions.LastMonth"),value:this.rangDateType.last_month},{label:this.translate.instant("GENERATEINVOICE.RangDateOptions.Custom"),value:this.rangDateType.custom}]),(0,l.A)(this,"rangeDateOptionSelected",{label:this.translate.instant("GENERATEINVOICE.RangDateOptions.AllTime"),value:this.rangDateType.all}),(0,l.A)(this,"resetTimeTrackingData",(0,D.A)(function*(){d.isFetchingProject=!0;const u=d.rangeDateOptionSelected.value===d.rangDateType.all,_=yield d.generateInvoiceProvider.fetchListProjectByClientAndDate({clientId:d.f.clientId?.value,startDate:u?null:d.rangeDateOptionSelected.metadata?.startDate,endDate:u?null:d.rangeDateOptionSelected.metadata?.endDate,page:d.currentPage});d.totalPages=_.totalRecords,Number(d.f.rate?.value??0),d.listInvoiceItem=_.data.map(b=>{const U=(0,G.FA)(b?.endTime??"00:00:00");return{trackingId:b?.id??"",date:b?.date??null,description:b?.description??"",rate:b?.project?.hourlyRate,qty:U,inforUser:b.user,hourlyRate:b?.project?.hourlyRate,taxes:[],total:(b?.project?.hourlyRate??0)*U,metadata:{hours:b?.endTime??"00:00:00",timeTracking:b},dateSelectItem:b.date,projectName:b?.project.projectName,serviceName:b?.service?.serviceName,projectId:b?.project?.id,serviceId:b?.service?.id,service:b?.service}}),d.listIndexInvoiceSelected=[],d.isFetchingProject=!1})),this.dialogRef=t,this.modifyInvoiceItemDialog=n,this.newInvoiceDialog=a,this.data=c,this.generateInvoiceForm=this.formBuilder.group({clientId:["",I.k0.compose([I.k0.required])],tempDate:[""],rate:["0"]}),this.generateInvoiceForm.get("clientId")?.valueChanges.subscribe(()=>this.resetTimeTrackingData()),this.generateInvoiceForm.get("tempDate")?.valueChanges.subscribe(()=>this.resetTimeTrackingData()),this.generateInvoiceForm.get("rate")?.valueChanges.subscribe(u=>{let _=Number(u);(!_||_<0)&&(_=0),this.listInvoiceItem=this.listInvoiceItem.map(C=>({...C,rate:_,total:_*C.qty}))})}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize),t?.currentPage&&(this.currentPage=t.currentPage,this.resetTimeTrackingData())}closeDialog(){this.dialogRef.close()}ngOnInit(){this.dropdownOptionService.getDropdownOptionsProjectAndClient({isOnlyClient:!0}).then(d=>{this.clientOptions=d});const t=new Date,n=new Date((new Date).setMonth(t.getMonth()-1)),a=(0,G.mQ)(t),c=(0,G.mQ)(n);this.rangDateOptions.forEach(d=>{if(this.rangDateType.this_month===d.value){const u=t.toLocaleString("en-US",{month:"long"});d.label+=` (${u})`,d.metadata||(d.metadata={}),d.metadata.startDate=new Date(a.startOfMonth),d.metadata.endDate=new Date(a.endOfMonth)}if(this.rangDateType.last_month===d.value){const u=n.toLocaleString("en-US",{month:"long"});d.label+=` (${u})`,d.metadata||(d.metadata={}),d.metadata.startDate=new Date(c.startOfMonth),d.metadata.endDate=new Date(c.endOfMonth)}})}get getSelectedRangeDateValue(){if(this.rangeDateOptionSelected.value===this.rangDateType.custom){const t=this.rangeDateOptionSelected.metadata?.startDate??"",n=this.rangeDateOptionSelected.metadata?.endDate??"";return[(0,G.cn)(t),(0,G.cn)(n)].join(" - ")}return this.rangDateOptions.find(n=>n.value===this.rangeDateOptionSelected.value)?.label}radioChange(t){if(2==t.value&&0==this.listIndexInvoiceSelected.length)return setTimeout(()=>{this.selectedRadio=null},50),void this._toastService.showWarning("No selected project","Please select a projet.");this.selectedRadio=t.value,this._handleRadioChange(t.value)}_handleRadioChange(t){this.listProjectId=[],2==t&&this.listIndexInvoiceSelected.forEach(n=>{this.listProjectId.push(this.listInvoiceItem[n].metadata.timeTracking.projectId)})}get f(){return this.generateInvoiceForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(t=>{t.markAsTouched()})}totalAmount(){const t=(0,q.yo)(this.listInvoiceItem);return t.subtotal+t.grandTotalTax}isCheckedIndex(t){return this.listIndexInvoiceSelected.includes(t)}handleToggleCheckedIndex(t){const n=this.isCheckedIndex(t);let a=[...this.listIndexInvoiceSelected];n?a=a.filter(c=>c!==t):a.push(t),this.listIndexInvoiceSelected=a}handleCheckedAll(t){this.listIndexInvoiceSelected=t?this.listInvoiceItem.map((n,a)=>a):[]}handleSelectClient(t){this.generateInvoiceForm.controls.clientId.setValue(t.value),this.selectSearchClientElement.handleCloseSearchResult()}handleSelectDate(t){this.rangeDateOptionSelected=t,this.f.tempDate.setValue(t.value),t.value!==this.rangDateType.custom&&this.rangeDatePopoverElement.handleHideContent()}handleSelectCustomDate(t,n){if(this.rangeDateOptionSelected.value!==this.rangDateType.custom)return;const a=this.rangeDateOptionSelected.metadata??{};a[t]=n,a.startDate&&a.endDate&&((0,G.O5)(a.startDate,a.endDate)?this.isShowCustomDateError=!1:(a[t]=null,this.isShowCustomDateError=!0)),this.rangeDateOptionSelected.metadata=a,this.f.tempDate.setValue(this.rangDateType.custom)}handleModifyInvoiceItem(t,n){this.modifyInvoiceItemDialog.open(n).then(c=>{c.afterClosed().subscribe(d=>{d&&(this.listInvoiceItem[t]={...n,...d})})})}handleCancel(){this.dialogRef.close()}handleSubmit(){if(this.generateInvoiceForm.invalid)return void this.markAllControlsAsTouched();if(!this.listIndexInvoiceSelected.length)return this._toastService.showWarning("No project selected","Please select a project to continue creating the invoice.");this.dialogRef.close();const t=this.listInvoiceItem.filter((a,c)=>this.listIndexInvoiceSelected.includes(c)),n={clientId:this.f.clientId.value,itemInvoices:t,isGenrate:!0};if(this.rangeDateOptionSelected.value!==this.rangDateType.all){const a=this.rangeDateOptionSelected.metadata?.startDate,c=this.rangeDateOptionSelected.metadata?.endDate;if(!(0,G.O5)(a,c))return;n.invoiceDate=new Date(a),n.dueDate=new Date(c)}else{const a=this.listInvoiceItem.map(u=>new Date(u.date)),c=a.reduce((u,_)=>_<u?_:u,new Date),d=a.reduce((u,_)=>_>u?_:u,new Date);n.invoiceDate=c,n.dueDate=d}this.newInvoiceDialog.open(n)}}return r=o,(0,l.A)(o,"\u0275fac",function(t){return new(t||r)(e.rXU(f.CP),e.rXU(Ie.r),e.rXU(Z.u),e.rXU(f.Vh))}),(0,l.A)(o,"\u0275cmp",e.VBU({type:r,selectors:[["app-generate-invoice"]],viewQuery:function(t,n){if(1&t&&(e.GBs(Ee,5),e.GBs(Ce,5)),2&t){let a;e.mGM(a=e.lsd())&&(n.selectSearchClientElement=a.first),e.mGM(a=e.lsd())&&(n.rangeDatePopoverElement=a.first)}},standalone:!0,features:[e.aNF],decls:37,vars:38,consts:[["selectSearchClientElement",""],["clientOptionTemplate",""],["rangeDatePopover",""],["rangeDateTemplate",""],[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","gap-[16px]"],[3,"label","options","formControl","value","placeholder","errorMessages","customOptionTemplate"],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],["position","bottom-start",3,"content","isClickOnContentToClose","isClearPadding"],["target","",1,"dropdown-md","w-full"],[1,"w-full","text-left","line-clamp-1","text-text-primary"],["src","../../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"shrink-0"],[1,"flex","flex-col","gap-[8px]",3,"ngModelChange","change","ngModel"],[1,"customRadio",3,"value"],[1,"w-full"],["footer",""],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[3,"size","name"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"w-full","shadow-md","rounded-md","border","border-border-primary-slight","bg-bg-primary"],[1,"w-full","p-[8px]","max-h-[300px]","max-w-[500px]","overflow-auto"],[1,"min-w-[300px]","w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"selected"],[1,"min-w-[300px]","w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click"],[1,"w-full","p-[8px]","flex","flex-col","sm:flex-row","items-center","gap-[16px]"],[1,"w-full","sm:w-[200px]"],["label","Start date","placeholder","Select start date",3,"onChange","value"],["label","End date","placeholder","Select end date",3,"onChange","value"],["message","Invalid date: Start date must be earlier than end date."],[3,"title","description"],[1,"w-full","py-2","flex","justify-center","items-center"],[3,"valueRadio","clientId","listProjectId"],["title","No result"],[1,"overflow-auto","w-full"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","pt-[15px]","pb-[3px]","flex-wrap","justify-end","items-center","gap-[16px]"],[1,"text-text-primary","text-text-sm-regular"],[1,"text-headline-sm-bold","text-text-primary"],[1,"flex","items-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-right"],[3,"size","bgColor","name"],[1,"pl-1","line-clamp-1"],[3,"cancel","valueRadio","clientId","listProjectId"],[3,"onCancel","onSubmit"]],template:function(t,n){if(1&t){const a=e.RV6();e.j41(0,"app-inno-modal-wrapper",4),e.bIt("onClose",function(){return e.eBV(a),e.Njj(n.closeDialog())}),e.j41(1,"div",5)(2,"div",6)(3,"app-inno-form-select-search",7,0),e.nI1(5,"translate"),e.nI1(6,"translate"),e.nI1(7,"translate"),e.DNE(8,De,5,5,"ng-template",null,1,e.C5r),e.k0s(),e.j41(10,"div",8)(11,"label",9),e.EFF(12),e.nI1(13,"translate"),e.k0s(),e.j41(14,"app-inno-popover",10,2)(16,"button",11)(17,"div",12),e.EFF(18),e.k0s(),e.nrm(19,"img",13),e.k0s(),e.DNE(20,Re,5,1,"ng-template",null,3,e.C5r),e.k0s()(),e.j41(22,"div",8)(23,"label",9),e.EFF(24),e.nI1(25,"translate"),e.k0s(),e.j41(26,"mat-radio-group",14),e.mxI("ngModelChange",function(d){return e.eBV(a),e.DH7(n.selectedRadio,d)||(n.selectedRadio=d),e.Njj(d)}),e.bIt("change",function(d){return e.eBV(a),e.Njj(n.radioChange(d))}),e.j41(27,"mat-radio-button",15),e.EFF(28),e.nI1(29,"translate"),e.k0s(),e.DNE(30,Se,3,4,"mat-radio-button",15),e.j41(31,"mat-radio-button",15),e.EFF(32),e.nI1(33,"translate"),e.k0s()()(),e.DNE(34,Ae,4,6,"div",16)(35,Be,4,1,"div",8),e.k0s()(),e.DNE(36,$e,2,0,"div",17),e.k0s()}if(2&t){const a=e.sdS(9),c=e.sdS(21);e.Y8G("title","GENERATEINVOICE.GenerateNewInvoice"),e.R7$(3),e.Y8G("label",e.bMT(5,22,"GENERATEINVOICE.Client"))("options",n.clientOptions)("formControl",n.f.clientId)("value",n.f.clientId.value)("placeholder",e.bMT(6,24,"GENERATEINVOICE.SelectClient"))("errorMessages",e.eq3(36,xe,e.bMT(7,26,"GENERATEINVOICE.ClientRequired")))("customOptionTemplate",a),e.R7$(9),e.SpI("",e.bMT(13,28,"GENERATEINVOICE.DateRange")," "),e.R7$(2),e.Y8G("content",c)("isClickOnContentToClose",!1)("isClearPadding",!0),e.R7$(4),e.SpI(" ",n.getSelectedRangeDateValue," "),e.R7$(6),e.SpI("",e.bMT(25,30,"GENERATEINVOICE.ChooseExpenses")," "),e.R7$(2),e.R50("ngModel",n.selectedRadio),e.R7$(),e.Y8G("value",1),e.R7$(),e.SpI(" ",e.bMT(29,32,"GENERATEINVOICE.AllUnbilledExpensesClient")," "),e.R7$(2),e.vxM(n.selectedRadio?-1:30),e.R7$(),e.Y8G("value",0),e.R7$(),e.SpI(" ",e.bMT(33,34,"GENERATEINVOICE.NoExpenses")," "),e.R7$(2),e.vxM(n.f.clientId.value?35:34),e.R7$(2),e.vxM(n.selectedRadio?-1:36)}},dependencies:[K.p,se,W,$,E.mC,E.fw,m.G,I.BC,I.vS,I.l_,B.Jj,w.D9,V.iov,V.BzB,v.I,p.k,x.M,g.x,de.f,le.k,ce.Y,me.J,Y.V,J.Q,fe],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(200px,1fr) 200px 130px 100px 120px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),o})()}}]);