"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9363],{3200:(N,O,i)=>{i.d(O,{J:()=>c});var n=i(9842),e=i(177),E=i(5236),t=i(4438);function D(I,v){if(1&I&&(t.j41(0,"p",4),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&I){const T=t.XpG();t.R7$(),t.SpI(" ",t.bMT(2,1,T.description)," ")}}let c=(()=>{var I;class v{constructor(){(0,n.A)(this,"title",""),(0,n.A)(this,"description",""),(0,n.A)(this,"icon",""),(0,n.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return I=v,(0,n.A)(v,"\u0275fac",function(C){return new(C||I)}),(0,n.A)(v,"\u0275cmp",t.VBU({type:I,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[t.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(C,b){1&C&&(t.j41(0,"div",0),t.nrm(1,"img",1),t.j41(2,"div",2)(3,"p",3),t.EFF(4),t.nI1(5,"translate"),t.nI1(6,"translate"),t.k0s(),t.DNE(7,D,3,3,"p",4),t.k0s()()),2&C&&(t.R7$(),t.Y8G("src",b.icon||b.defaultIcon,t.B4B),t.R7$(3),t.SpI(" ",b.title?t.bMT(5,3,b.title):t.bMT(6,5,"COMMON.EmptyData")," "),t.R7$(3),t.vxM(b.description?7:-1))},dependencies:[e.MD,E.h,E.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),v})()},9248:(N,O,i)=>{i.d(O,{M:()=>M});var n=i(9842),e=i(4438),E=i(6146),t=i(8192),D=i(3652),c=i(5599),I=i(9417),v=i(177),T=i(5236);const C=h=>({"text-text-primary":h});function b(h,g){if(1&h&&(e.j41(0,"label",10),e.EFF(1),e.k0s()),2&h){const m=e.XpG();e.AVh("required",m.isRequired),e.R7$(),e.JRh(m.label)}}function u(h,g){if(1&h&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&h){const m=e.XpG().$implicit;e.R7$(),e.SpI(" ",m.description," ")}}function f(h,g){if(1&h){const m=e.RV6();e.j41(0,"div",13),e.bIt("click",function(){const o=e.eBV(m).$implicit,l=e.XpG(2);return e.Njj(l.handleChooseOption(o))}),e.j41(1,"div",14)(2,"p",15),e.EFF(3),e.k0s(),e.DNE(4,u,2,1,"p",16),e.k0s()()}if(2&h){const m=g.$implicit,s=e.XpG(2);e.AVh("selected",m.value===s.value),e.R7$(3),e.SpI(" ",m.label," "),e.R7$(),e.vxM(m.description?4:-1)}}function d(h,g){if(1&h){const m=e.RV6();e.j41(0,"button",17),e.bIt("click",function(){e.eBV(m);const o=e.XpG(2);return e.Njj(o.handleCreateNew())}),e.nrm(1,"img",18),e.EFF(2),e.nI1(3,"translate"),e.k0s()}if(2&h){const m=e.XpG(2);e.R7$(2),e.Lme(" ",e.bMT(3,2,"COMMON.CreateNew"),' "',m.textSearch,'" ')}}function A(h,g){if(1&h){const m=e.RV6();e.j41(0,"div",11)(1,"app-inno-input-search-result",12),e.nI1(2,"translate"),e.bIt("onChange",function(o){e.eBV(m);const l=e.XpG();return e.Njj(l.handleSearch(o))}),e.DNE(3,f,5,4,"ng-template",null,1,e.C5r)(5,d,4,4,"ng-template",null,2,e.C5r),e.k0s()()}if(2&h){let m;const s=e.sdS(4),o=e.sdS(6),l=e.XpG();e.R7$(),e.Y8G("placeholder",e.bMT(2,7,"COMMON.Search"))("data",l.listOptionPreview)("isNotFound",!l.listOptionPreview.length)("isEmptyData",!l.listOptionOriginal.length)("isDisableSearch",l.isDisableSearch)("optionTemplate",null!==(m=l.customOptionTemplate)&&void 0!==m?m:s)("footerTemplate",l.isShowCreateButton?o:null)}}let M=(()=>{var h;class g{constructor(){(0,n.A)(this,"isRequired",void 0),(0,n.A)(this,"label",""),(0,n.A)(this,"options",[]),(0,n.A)(this,"placeholder",""),(0,n.A)(this,"value",""),(0,n.A)(this,"projectId",""),(0,n.A)(this,"isProjectClient",!1),(0,n.A)(this,"errorMessages",void 0),(0,n.A)(this,"formControl",void 0),(0,n.A)(this,"customOptionTemplate",null),(0,n.A)(this,"isDisableSearch",!1),(0,n.A)(this,"isForYear",!1),(0,n.A)(this,"onChange",new e.bkB),(0,n.A)(this,"onSelect",new e.bkB),(0,n.A)(this,"onCreateNew",new e.bkB),(0,n.A)(this,"textSearch",""),(0,n.A)(this,"clientName",""),(0,n.A)(this,"labelOfValueSelected",""),(0,n.A)(this,"listOptionPreview",[]),(0,n.A)(this,"listOptionOriginal",[]),(0,n.A)(this,"searchResultComponent",void 0)}registerOnChange(s){}registerOnTouched(s){}setDisabledState(s){}writeValue(s){}ngOnChanges(s){const o=s?.value?.currentValue??null,l=s?.projectId?.currentValue??null;o&&(this.labelOfValueSelected=this.listOptionOriginal.find(_=>_.value===o)?.label,this.clientName=this.listOptionOriginal.find(_=>_.value===o)?.label);const r=s?.options?.currentValue;r?.length&&(this.options=r,this.listOptionOriginal=this.options,this.listOptionPreview=this.listOptionOriginal,this.formControl?.value&&(this.value=this.formControl.value,this.labelOfValueSelected=this.listOptionOriginal.find(_=>_.value===this.value)?.label)),l?(this.clientName=this.listOptionOriginal.find(_=>_.value===l)?.metadata?.objectClient?.clientName,this.labelOfValueSelected=this.clientName+"-"+this.listOptionOriginal.find(_=>_.value===l)?.label):this.labelOfValueSelected=this.listOptionOriginal.find(_=>_.value===o)?.label,this.value&&!this.labelOfValueSelected&&(this.labelOfValueSelected=this.listOptionOriginal.find(_=>_.value===this.value)?.label)}get isShowCreateButton(){return this.onCreateNew?.observed&&this.textSearch.length&&(!this.listOptionOriginal?.length||!this.listOptionPreview?.length)}handleChange(s){this.onChange?.emit&&this.onChange.emit(s)}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const s in this.formControl.errors)if(this.errorMessages[s])return this.errorMessages[s];return""}handleSearch(s){if(this.isProjectClient){if(s=s?.trim()?.toLowerCase(),!s?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(l=>l.label.toLowerCase().indexOf(s)>-1);const o=[];this.listOptionPreview.forEach(l=>{let r=!1,_=!1;this.listOptionOriginal.filter(p=>"project"==p.metadata.type).forEach(p=>{if(l.value==p.metadata.objectClient.id)r||(o.push(l),r=!0),o.push(p);else{let a=this.listOptionPreview.find(P=>"client"==P.metadata?.type);if(!r&&!a){let P=this.listOptionOriginal.find(R=>R.metadata?.client?.id==l.metadata?.objectClient?.id),S=o.find(R=>R.value==l.metadata?.objectClient?.id);S||(_=!0,o.push(P)),(_||S)&&(o.push(l),r=!0)}}})}),this.listOptionPreview=o}else{if(s=s?.trim()?.toLowerCase(),this.textSearch=s,!s?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(l=>l.label.toLowerCase().indexOf(s)>-1);const o=[];this.listOptionPreview.forEach(l=>{o.push(l),this.listOptionOriginal.filter(r=>"project"==r.metadata?.type).forEach(r=>{l.value==r.metadata.objectClient.id&&o.push(r)})})}}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}touchControl(){this.formControl&&(this.formControl.markAsDirty(),this.formControl.markAsTouched())}handleChooseOption(s){s.value!=this.value&&(this.formControl&&this.formControl.setValue(s.value),this.labelOfValueSelected=s.label,this.value=s.value,this.onSelect.emit(s),this.handleCloseSearchResult())}callbackAfterHideSearchResult(){this.listOptionPreview=this.listOptionOriginal}handleCreateNew(){this.onCreateNew.emit(this.textSearch),this.handleCloseSearchResult()}}return h=g,(0,n.A)(g,"\u0275fac",function(s){return new(s||h)}),(0,n.A)(g,"\u0275cmp",e.VBU({type:h,selectors:[["app-inno-form-select-search"]],viewQuery:function(s,o){if(1&s&&e.GBs(c.x,5),2&s){let l;e.mGM(l=e.lsd())&&(o.searchResultComponent=l.first)}},inputs:{isRequired:"isRequired",label:"label",options:"options",placeholder:"placeholder",value:"value",projectId:"projectId",isProjectClient:"isProjectClient",errorMessages:"errorMessages",formControl:"formControl",customOptionTemplate:"customOptionTemplate",isDisableSearch:"isDisableSearch",isForYear:"isForYear"},outputs:{onChange:"onChange",onSelect:"onSelect",onCreateNew:"onCreateNew"},standalone:!0,features:[e.Jv_([{provide:I.kq,useExisting:(0,e.Rfq)(()=>h),multi:!0}]),e.OA$,e.aNF],decls:10,vars:9,consts:[["templateSearchProject",""],["optionTemplate",""],["buttonCreateNew",""],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],["position","bottom-start",3,"onOpen","onClose","content","isClickOnContentToClose","isClearPadding"],["type","button","target","",1,"dropdown-md","w-full"],[1,"w-full","text-left","line-clamp-1","text-text-placeholder-slight",3,"ngClass"],["src","../../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"shrink-0"],[3,"message"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","max-w-[90dvw]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isDisableSearch","optionTemplate","footerTemplate"],[1,"w-full","flex","p-[8px]","my-1","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-teapp-inno-input-search-resultxt-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(s,o){if(1&s){const l=e.RV6();e.j41(0,"div",3),e.DNE(1,b,2,3,"label",4),e.j41(2,"app-inno-popover",5),e.bIt("onOpen",function(){return e.eBV(l),e.Njj(o.touchControl())})("onClose",function(){return e.eBV(l),e.Njj(o.callbackAfterHideSearchResult())}),e.j41(3,"button",6)(4,"div",7),e.EFF(5),e.k0s(),e.nrm(6,"img",8),e.k0s()(),e.DNE(7,A,7,9,"ng-template",null,0,e.C5r),e.nrm(9,"app-inno-error-message",9),e.k0s()}if(2&s){const l=e.sdS(8);e.R7$(),e.vxM(o.label?1:-1),e.R7$(),e.Y8G("content",l)("isClickOnContentToClose",!1)("isClearPadding",!0),e.R7$(2),e.Y8G("ngClass",e.eq3(7,C,o.labelOfValueSelected)),e.R7$(),e.SpI(" ",o.labelOfValueSelected||o.placeholder," "),e.R7$(4),e.Y8G("message",o.getErrorMessage())}},dependencies:[E.G,v.YU,T.D9,c.x,D.t,t.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}']})),g})()},3652:(N,O,i)=>{i.d(O,{t:()=>s});var n=i(9842),e=i(4438),E=i(6146),t=i(9424),D=i(3200);let c=(()=>{var o;class l{constructor(_){(0,n.A)(this,"el",void 0),(0,n.A)(this,"timeoutId",void 0),this.el=_}ngAfterViewInit(){this.timeoutId=setTimeout(()=>{this.el.nativeElement&&this.el.nativeElement.focus()},100)}ngOnDestroy(){this.timeoutId&&clearTimeout(this.timeoutId)}}return o=l,(0,n.A)(l,"\u0275fac",function(_){return new(_||o)(e.rXU(e.aKT))}),(0,n.A)(l,"\u0275dir",e.FsC({type:o,selectors:[["","appAutofocus",""]],standalone:!0})),l})();var I=i(177);const v=o=>({$implicit:o});function T(o,l){if(1&o){const r=e.RV6();e.j41(0,"div",1)(1,"div",3),e.nrm(2,"img",4),e.j41(3,"input",5),e.bIt("keyup",function(p){e.eBV(r);const a=e.XpG();return e.Njj(a.handleOnChange(p))}),e.k0s()()()}if(2&o){const r=e.XpG();e.R7$(3),e.FS9("placeholder",r.placeholder),e.Y8G("value",r.inputSearchValue)}}function C(o,l){1&o&&(e.j41(0,"div",2),e.nrm(1,"app-inno-spin"),e.k0s())}function b(o,l){1&o&&e.nrm(0,"app-inno-empty-data",8),2&o&&e.Y8G("title","COMMON.EmptyData")}function u(o,l){1&o&&e.nrm(0,"app-inno-empty-data",9),2&o&&e.Y8G("title","COMMON.NoResult")("description","COMMON.DifferentKeywords")}function f(o,l){if(1&o&&e.DNE(0,b,1,1,"app-inno-empty-data",8)(1,u,1,2,"app-inno-empty-data",9),2&o){const r=e.XpG(2);e.vxM(r.isEmptyData?0:1)}}function d(o,l){1&o&&e.eu8(0)}function A(o,l){if(1&o&&e.DNE(0,d,1,0,"ng-container",10),2&o){const r=l.$implicit,_=e.XpG(3);e.Y8G("ngTemplateOutlet",_.optionTemplate)("ngTemplateOutletContext",e.eq3(2,v,r))}}function M(o,l){if(1&o&&e.Z7z(0,A,1,4,"ng-container",null,e.fX1),2&o){const r=e.XpG(2);e.Dyx(r.data)}}function h(o,l){1&o&&e.eu8(0)}function g(o,l){if(1&o&&(e.j41(0,"div",7),e.DNE(1,h,1,0,"ng-container",11),e.k0s()),2&o){const r=e.XpG(2);e.R7$(),e.Y8G("ngTemplateOutlet",r.footerTemplate)}}function m(o,l){if(1&o&&(e.j41(0,"div",6),e.DNE(1,f,2,1)(2,M,2,0),e.k0s(),e.DNE(3,g,2,1,"div",7)),2&o){const r=e.XpG();e.R7$(),e.vxM(r.isNotFound?1:2),e.R7$(2),e.vxM(r.footerTemplate?3:-1)}}let s=(()=>{var o;class l{constructor(){(0,n.A)(this,"defaultValue",""),(0,n.A)(this,"placeholder",""),(0,n.A)(this,"data",[]),(0,n.A)(this,"optionTemplate",null),(0,n.A)(this,"footerTemplate",null),(0,n.A)(this,"isNotFound",!1),(0,n.A)(this,"isEmptyData",!1),(0,n.A)(this,"isLoading",!1),(0,n.A)(this,"isDisableSearch",!1),(0,n.A)(this,"inputSearchValue",""),(0,n.A)(this,"onChange",new e.bkB)}ngOnInit(){this.inputSearchValue=this.defaultValue}handleOnChange(_){const p=_?.target?.value??"";this.onChange.emit(p),this.inputSearchValue=p}}return o=l,(0,n.A)(l,"\u0275fac",function(_){return new(_||o)}),(0,n.A)(l,"\u0275cmp",e.VBU({type:o,selectors:[["app-inno-input-search-result"]],inputs:{defaultValue:"defaultValue",placeholder:"placeholder",data:"data",optionTemplate:"optionTemplate",footerTemplate:"footerTemplate",isNotFound:"isNotFound",isEmptyData:"isEmptyData",isLoading:"isLoading",isDisableSearch:"isDisableSearch"},outputs:{onChange:"onChange"},standalone:!0,features:[e.aNF],decls:4,vars:2,consts:[[1,"min-w-[300px]","w-full","shadow-md","rounded-md","border","border-border-primary-slight","bg-bg-primary"],[1,"w-full","p-[16px]","border-b","border-border-primary-slight"],[1,"flex","justify-center","py-3"],[1,"w-full","h-[40px]","flex","items-center","rounded-[8px]","border-[2px]","px-[12px]"],["src","../../../assets/img/icon/ic_search_gray.svg","alt","Icon search",1,"w-[16px]","shrink-0"],["appAutofocus","","type","text",1,"h-full","w-full","pl-[8px]","text-text-md-regular",3,"keyup","placeholder","value"],[1,"w-full","p-[8px]","max-h-[300px]","max-w-[500px]","overflow-auto"],[1,"border-t","border-border-primary","p-[8px]","w-full"],[3,"title"],[3,"title","description"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngTemplateOutlet"]],template:function(_,p){1&_&&(e.j41(0,"div",0),e.DNE(1,T,4,2,"div",1)(2,C,2,0,"div",2)(3,m,4,2),e.k0s()),2&_&&(e.R7$(),e.vxM(p.isDisableSearch?-1:1),e.R7$(),e.vxM(p.isLoading?2:3))},dependencies:[E.G,I.T3,t.f,D.J,c],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),l})()},344:(N,O,i)=>{i.d(O,{k:()=>b});var n=i(9842),e=i(4438),E=i(6146),t=i(177),D=i(5236);function c(u,f){if(1&u&&e.eu8(0,1),2&u){const d=e.XpG();e.Y8G("ngTemplateOutlet",d.leftAction)}}function I(u,f){if(1&u&&e.eu8(0,1),2&u){const d=e.XpG();e.Y8G("ngTemplateOutlet",d.customCancelButton)}}function v(u,f){if(1&u){const d=e.RV6();e.j41(0,"button",5),e.bIt("click",function(){e.eBV(d);const M=e.XpG();return e.Njj(M.handleCancel())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}if(2&u){const d=e.XpG();e.HbH(d.classNameCancelButton),e.R7$(),e.SpI(" ",e.bMT(2,3,d.textCancel||"BUTTON.Cancel")," ")}}function T(u,f){if(1&u&&e.eu8(0,1),2&u){const d=e.XpG();e.Y8G("ngTemplateOutlet",d.customSubmitButton)}}function C(u,f){if(1&u){const d=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(d);const M=e.XpG();return e.Njj(M.handleSubmit())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}if(2&u){const d=e.XpG();e.HbH(d.classNameSubmitButton),e.Y8G("disabled",d.isDisableSubmit),e.R7$(),e.SpI(" ",e.bMT(2,4,d.textSubmit||"BUTTON.Save")," ")}}let b=(()=>{var u;class f{constructor(){(0,n.A)(this,"classNameSubmitButton",""),(0,n.A)(this,"classNameCancelButton",""),(0,n.A)(this,"leftAction",void 0),(0,n.A)(this,"customSubmitButton",void 0),(0,n.A)(this,"customCancelButton",void 0),(0,n.A)(this,"textSubmit",void 0),(0,n.A)(this,"textCancel",void 0),(0,n.A)(this,"idSubmit",""),(0,n.A)(this,"idCancel",""),(0,n.A)(this,"isDisableSubmit",!1),(0,n.A)(this,"onSubmit",new e.bkB),(0,n.A)(this,"onCancel",new e.bkB)}handleSubmit(){this.onSubmit.emit()}handleCancel(){this.onCancel.emit()}}return u=f,(0,n.A)(f,"\u0275fac",function(A){return new(A||u)}),(0,n.A)(f,"\u0275cmp",e.VBU({type:u,selectors:[["app-inno-modal-footer"]],inputs:{classNameSubmitButton:"classNameSubmitButton",classNameCancelButton:"classNameCancelButton",leftAction:"leftAction",customSubmitButton:"customSubmitButton",customCancelButton:"customCancelButton",textSubmit:"textSubmit",textCancel:"textCancel",idSubmit:"idSubmit",idCancel:"idCancel",isDisableSubmit:"isDisableSubmit"},outputs:{onSubmit:"onSubmit",onCancel:"onCancel"},standalone:!0,features:[e.aNF],decls:7,vars:3,consts:[[1,"w-full","p-[16px]","border-t","border-border-primary","flex","items-center","gap-[12px]"],[3,"ngTemplateOutlet"],[1,"ml-auto","flex","gap-[12px]","items-center"],["type","button",1,"button-outline","button-size-md",3,"class"],["type","button",1,"button-primary","button-size-md",3,"class","disabled"],["type","button",1,"button-outline","button-size-md",3,"click"],["type","button",1,"button-primary","button-size-md",3,"click","disabled"]],template:function(A,M){1&A&&(e.j41(0,"div",0),e.DNE(1,c,1,1,"ng-container",1),e.j41(2,"div",2),e.DNE(3,I,1,1,"ng-container",1)(4,v,3,5,"button",3)(5,T,1,1,"ng-container",1)(6,C,3,6,"button",4),e.k0s()()),2&A&&(e.R7$(),e.vxM(M.leftAction?1:-1),e.R7$(2),e.vxM(M.customCancelButton?3:M.onCancel.observers.length?4:-1),e.R7$(2),e.vxM(M.customSubmitButton?5:M.onSubmit.observers.length?6:-1))},dependencies:[E.G,t.T3,D.D9],styles:[".zipplexActionModal[_ngcontent-%COMP%]{width:100%;margin-top:16px}.leftAction[_ngcontent-%COMP%]:empty, .rightAction[_ngcontent-%COMP%]:empty{display:none!important}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%], .zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .leftAction[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%]{flex:1;display:flex;gap:8px}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{width:100%}"]})),f})()},4978:(N,O,i)=>{i.d(O,{I:()=>T});var n=i(9842),e=i(4438),E=i(6146),t=i(5236);const D=["*",[["","footer",""]]],c=["*","[footer]"];function I(C,b){if(1&C){const u=e.RV6();e.j41(0,"button",7),e.bIt("click",function(){e.eBV(u);const d=e.XpG(2);return e.Njj(d.handleClose())}),e.nrm(1,"img",8),e.k0s()}}function v(C,b){if(1&C&&(e.j41(0,"div",4)(1,"p",5),e.EFF(2),e.nI1(3,"translate"),e.k0s()(),e.DNE(4,I,2,0,"button",6)),2&C){const u=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,u.title)),e.R7$(2),e.vxM(u.onClose.observers.length?4:-1)}}let T=(()=>{var C;class b{constructor(){(0,n.A)(this,"title",void 0),(0,n.A)(this,"onClose",new e.bkB)}handleClose(){this.onClose.emit()}}return C=b,(0,n.A)(b,"\u0275fac",function(f){return new(f||C)}),(0,n.A)(b,"\u0275cmp",e.VBU({type:C,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[e.aNF],ngContentSelectors:c,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(f,d){1&f&&(e.NAR(D),e.j41(0,"div",0)(1,"div",1),e.DNE(2,v,5,4),e.k0s(),e.j41(3,"div",2),e.SdG(4),e.k0s(),e.j41(5,"div",3),e.SdG(6,1),e.k0s()()),2&f&&(e.R7$(2),e.vxM(d.title?2:-1))},dependencies:[E.G,t.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),b})()},9363:(N,O,i)=>{i.r(O),i.d(O,{AddClientsFormComponent:()=>o});var n=i(9842),e=i(4982),E=i(6146),t=i(4438),D=i(4006),c=i(9417),I=i(3492),v=i(1342),T=i(6327),C=i(9079),b=i(1110),u=i(4978),f=i(1328),d=i(344),A=i(9248),M=i(5236);const h=l=>({required:l}),g=(l,r)=>({required:l,email:r}),m=(l,r,_)=>({minlength:l,maxlength:r,pattern:_}),s=()=>({required:"Country is required"});let o=(()=>{var l;class r{static getComponent(){return r}constructor(p,a){(0,n.A)(this,"dialogRef",void 0),(0,n.A)(this,"data",void 0),(0,n.A)(this,"newclientForm",void 0),(0,n.A)(this,"countriesOption",T.F.map(P=>({value:P.code,label:P.name}))),(0,n.A)(this,"objectClient",void 0),(0,n.A)(this,"_storeService",(0,t.WQX)(b.n)),(0,n.A)(this,"_spinnerService",(0,t.WQX)(v.D)),(0,n.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,n.A)(this,"_clientService",(0,t.WQX)(e.X)),(0,n.A)(this,"_toastService",(0,t.WQX)(I.f)),(0,n.A)(this,"formBuilder",(0,t.WQX)(c.ze)),this.dialogRef=p,this.data=a,this.newclientForm=this.formBuilder.group({clientname:["",c.k0.compose([c.k0.required])],email:["",c.k0.compose([c.k0.required,c.k0.email])],mobilephone:["",c.k0.compose([c.k0.pattern("[0-9]*"),c.k0.minLength(10),c.k0.maxLength(10)])],businessphone:["",c.k0.compose([c.k0.pattern("[0-9]*"),c.k0.minLength(10),c.k0.maxLength(10)])],phone:["",c.k0.compose([c.k0.pattern("[0-9]*"),c.k0.minLength(10),c.k0.maxLength(10)])],firstname:[""],lastname:[""],country:[""],address1:[""],address2:[""],tow_city:[""],state_province:[""],postal_code:[""],postePhoneNumber:[""],posteBusinessPhoneNumber:[""],posteMobilePhoneNumber:[""]})}ngOnInit(){this.data&&this.GetClientById(this.data)}handleData(p){this.f.clientname.setValue(p.clientName),this.f.firstname.setValue(p.firstName),this.f.lastname.setValue(p.lastName),this.f.email.setValue(p.emailAddress),this.f.phone.setValue(p.phoneNumber),this.f.businessphone.setValue(p.businessPhoneNumber),this.f.mobilephone.setValue(p.mobilePhoneNumber),this.f.address1.setValue(p.addressLine1),this.f.address2.setValue(p.addressLine2),this.f.tow_city.setValue(p.townCity),this.f.state_province.setValue(p.stateProvince),this.f.postal_code.setValue(p.postalCode),this.f.country.setValue(p.country),this.f.postePhoneNumber.setValue(p.postePhoneNumber),this.f.posteBusinessPhoneNumber.setValue(p.posteBusinessPhoneNumber),this.f.posteMobilePhoneNumber.setValue(p.posteMobilePhoneNumber)}GetClientById(p){this._clientService.GetClientById(p).pipe((0,C.pQ)(this.destroyRef)).subscribe(a=>{this.objectClient=a,this.handleData(a)})}handleClose(){this.dialogRef.close()}markAllControlsAsTouched(){Object.values(this.f).forEach(p=>{p.markAsTouched()})}onSubmit(){if(this.newclientForm.invalid)return void this.markAllControlsAsTouched();const p={clientName:this.f.clientname.value,firstName:this.f.firstname.value,lastName:this.f.lastname.value,emailAddress:this.f.email.value,phoneNumber:this.f.phone.value,businessPhoneNumber:this.f.businessphone.value,mobilePhoneNumber:this.f.mobilephone.value,addressLine1:this.f.address1.value,addressLine2:this.f.address2.value,townCity:this.f.tow_city.value,stateProvince:this.f.state_province.value,postalCode:this.f.postal_code.value,country:this.f.country.value,postePhoneNumber:this.f.postePhoneNumber.value,posteBusinessPhoneNumber:this.f.posteBusinessPhoneNumber.value,posteMobilePhoneNumber:this.f.posteMobilePhoneNumber.value};this.data?(this._spinnerService.show(),p.id=this.data,this._clientService.UpdateClient(p).pipe((0,C.pQ)(this.destroyRef)).subscribe(a=>{a?(this._toastService.showSuccess("Save","Create Success"),this.dialogRef.close(a)):this._toastService.showError("Fail","Client name already exists"),this._spinnerService.hide()})):(this._spinnerService.show(),this._clientService.CreateClient(p).pipe((0,C.pQ)(this.destroyRef)).subscribe(a=>{a?(this._toastService.showSuccess("Save","Create Success"),this.dialogRef.close(a)):this._toastService.showError("Fail","Client name already exists"),this._spinnerService.hide()}))}get f(){return this.newclientForm.controls}closeDialog(){this.dialogRef.close()}handleCancel(){this.dialogRef.close()}}return l=r,(0,n.A)(r,"\u0275fac",function(p){return new(p||l)(t.rXU(D.CP),t.rXU(D.Vh))}),(0,n.A)(r,"\u0275cmp",t.VBU({type:l,selectors:[["app-add-clients-form"]],standalone:!0,features:[t.Jv_([]),t.aNF],decls:68,vars:185,consts:[[3,"onClose","title"],[1,"w-full",3,"formGroup"],[1,"w-full","p-[16px]","flex","flex-col","gap-[16px]"],[3,"label","formControl","value","placeholder"],[3,"isRequired","label","placeholder","formControl","value","errorMessages"],["type","email",3,"isRequired","label","placeholder","formControl","value","errorMessages"],[1,"flex","gap-2"],["mask","(*************","autocomplete","phone",1,"w-full",3,"isRequired","label","placeholder","formControl","value","errorMessages"],[1,"w-full",3,"isRequired","label","placeholder","formControl","value"],["mask","(*************","autocomplete","phone",1,"w-full",3,"label","placeholder","formControl","value","errorMessages"],["placeholder","Select country",3,"label","placeholder","options","formControl","value","errorMessages"],[3,"placeholder","label","formControl","value"],[3,"label","placeholder","formControl","value"],["footer",""],[3,"onSubmit","onCancel"]],template:function(p,a){1&p&&(t.j41(0,"app-inno-modal-wrapper",0),t.bIt("onClose",function(){return a.handleClose()}),t.j41(1,"form",1)(2,"div",2),t.nrm(3,"app-inno-form-input",3),t.nI1(4,"translate"),t.nI1(5,"translate"),t.nrm(6,"app-inno-form-input",3),t.nI1(7,"translate"),t.nI1(8,"translate"),t.nrm(9,"app-inno-form-input",4),t.nI1(10,"translate"),t.nI1(11,"translate"),t.nI1(12,"translate"),t.nrm(13,"app-inno-form-input",5),t.nI1(14,"translate"),t.nI1(15,"translate"),t.nI1(16,"translate"),t.nI1(17,"translate"),t.j41(18,"div",6),t.nrm(19,"app-inno-form-input",7),t.nI1(20,"translate"),t.nI1(21,"translate"),t.nI1(22,"translate"),t.nI1(23,"translate"),t.nI1(24,"translate"),t.nrm(25,"app-inno-form-input",8),t.nI1(26,"translate"),t.nI1(27,"translate"),t.k0s(),t.j41(28,"div",6),t.nrm(29,"app-inno-form-input",9),t.nI1(30,"translate"),t.nI1(31,"translate"),t.nI1(32,"translate"),t.nI1(33,"translate"),t.nI1(34,"translate"),t.nrm(35,"app-inno-form-input",8),t.nI1(36,"translate"),t.nI1(37,"translate"),t.k0s(),t.j41(38,"div",6),t.nrm(39,"app-inno-form-input",9),t.nI1(40,"translate"),t.nI1(41,"translate"),t.nI1(42,"translate"),t.nI1(43,"translate"),t.nI1(44,"translate"),t.nrm(45,"app-inno-form-input",8),t.nI1(46,"translate"),t.nI1(47,"translate"),t.k0s(),t.nrm(48,"app-inno-form-select-search",10),t.nI1(49,"translate"),t.nI1(50,"translate"),t.nrm(51,"app-inno-form-input",11),t.nI1(52,"translate"),t.nI1(53,"translate"),t.nrm(54,"app-inno-form-input",11),t.nI1(55,"translate"),t.nI1(56,"translate"),t.nrm(57,"app-inno-form-input",11),t.nI1(58,"translate"),t.nI1(59,"translate"),t.nrm(60,"app-inno-form-input",12),t.nI1(61,"translate"),t.nI1(62,"translate"),t.nrm(63,"app-inno-form-input",11),t.nI1(64,"translate"),t.nI1(65,"translate"),t.k0s()(),t.j41(66,"div",13)(67,"app-inno-modal-footer",14),t.bIt("onSubmit",function(){return a.onSubmit()})("onCancel",function(){return a.handleCancel()}),t.k0s()()()),2&p&&(t.Y8G("title",a.data?"CLIENT.ADD_CLIENT_FORM.TitleEdit":"CLIENT.ADD_CLIENT_FORM.Title"),t.R7$(),t.Y8G("formGroup",a.newclientForm),t.R7$(2),t.Y8G("label",t.bMT(4,79,"CLIENT.ADD_CLIENT_FORM.FirstName"))("formControl",a.f.firstname)("value",a.f.firstname.value)("placeholder",t.bMT(5,81,"CLIENT.ADD_CLIENT_FORM.FirstNamePlaceholder")),t.R7$(3),t.Y8G("label",t.bMT(7,83,"CLIENT.ADD_CLIENT_FORM.LastName"))("formControl",a.f.lastname)("value",a.f.lastname.value)("placeholder",t.bMT(8,85,"CLIENT.ADD_CLIENT_FORM.LastNamePlaceholder")),t.R7$(3),t.Y8G("isRequired",!0)("label",t.bMT(10,87,"CLIENT.ADD_CLIENT_FORM.ClientName"))("placeholder",t.bMT(11,89,"CLIENT.ADD_CLIENT_FORM.ClientNamePlaceholder"))("formControl",a.f.clientname)("value",a.f.clientname.value)("errorMessages",t.eq3(167,h,t.bMT(12,91,"CLIENT.ADD_CLIENT_FORM.ClientNameRequired"))),t.R7$(4),t.Y8G("isRequired",!0)("label",t.bMT(14,93,"CLIENT.ADD_CLIENT_FORM.Email"))("placeholder",t.bMT(15,95,"CLIENT.ADD_CLIENT_FORM.EmailPlaceholder"))("formControl",a.f.email)("value",a.f.email.value)("errorMessages",t.l_i(169,g,t.bMT(16,97,"CLIENT.ADD_CLIENT_FORM.EmailRequired"),t.bMT(17,99,"CLIENT.ADD_CLIENT_FORM.EmailInvalid"))),t.R7$(6),t.Y8G("isRequired",!0)("label",t.bMT(20,101,"CLIENT.ADD_CLIENT_FORM.PhoneNumber"))("placeholder",t.bMT(21,103,"CLIENT.ADD_CLIENT_FORM.PhoneNumberPlaceholder"))("formControl",a.f.phone)("value",a.f.phone.value)("errorMessages",t.sMw(172,m,t.bMT(22,105,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(23,107,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(24,109,"CLIENT.ADD_CLIENT_FORM.PhonePattern"))),t.R7$(6),t.Y8G("isRequired",!1)("label",t.bMT(26,111,"CLIENT.ADD_CLIENT_FORM.Post"))("placeholder",t.bMT(27,113,"CLIENT.ADD_CLIENT_FORM.PostPlaceholder"))("formControl",a.f.postePhoneNumber)("value",a.f.postePhoneNumber.value),t.R7$(4),t.Y8G("label",t.bMT(30,115,"CLIENT.ADD_CLIENT_FORM.BusinessPhone"))("placeholder",t.bMT(31,117,"CLIENT.ADD_CLIENT_FORM.BusinessPhonePlaceholder"))("formControl",a.f.businessphone)("value",a.f.businessphone.value)("errorMessages",t.sMw(176,m,t.bMT(32,119,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(33,121,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(34,123,"CLIENT.ADD_CLIENT_FORM.PhonePattern"))),t.R7$(6),t.Y8G("isRequired",!1)("label",t.bMT(36,125,"CLIENT.ADD_CLIENT_FORM.Post"))("placeholder",t.bMT(37,127,"CLIENT.ADD_CLIENT_FORM.PostPlaceholder"))("formControl",a.f.posteBusinessPhoneNumber)("value",a.f.posteBusinessPhoneNumber.value),t.R7$(4),t.Y8G("label",t.bMT(40,129,"CLIENT.ADD_CLIENT_FORM.MobilePhone"))("placeholder",t.bMT(41,131,"CLIENT.ADD_CLIENT_FORM.MobilePhonePlaceholder"))("formControl",a.f.mobilephone)("value",a.f.mobilephone.value)("errorMessages",t.sMw(180,m,t.bMT(42,133,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(43,135,"CLIENT.ADD_CLIENT_FORM.PhoneLength"),t.bMT(44,137,"CLIENT.ADD_CLIENT_FORM.PhonePattern"))),t.R7$(6),t.Y8G("isRequired",!1)("label",t.bMT(46,139,"CLIENT.ADD_CLIENT_FORM.Post"))("placeholder",t.bMT(47,141,"CLIENT.ADD_CLIENT_FORM.PostPlaceholder"))("formControl",a.f.posteMobilePhoneNumber)("value",a.f.posteMobilePhoneNumber.value),t.R7$(3),t.Y8G("label",t.bMT(49,143,"CLIENT.ADD_CLIENT_FORM.Country"))("placeholder",t.bMT(50,145,"CLIENT.ADD_CLIENT_FORM.CountryPlaceholder"))("options",a.countriesOption)("formControl",a.f.country)("value",a.f.country.value)("errorMessages",t.lJ4(184,s)),t.R7$(3),t.Y8G("placeholder",t.bMT(52,147,"CLIENT.ADD_CLIENT_FORM.Address1Placeholder"))("label",t.bMT(53,149,"CLIENT.ADD_CLIENT_FORM.Address1"))("formControl",a.f.address1)("value",a.f.address1.value),t.R7$(3),t.Y8G("placeholder",t.bMT(55,151,"CLIENT.ADD_CLIENT_FORM.Address2Placeholder"))("label",t.bMT(56,153,"CLIENT.ADD_CLIENT_FORM.Address2"))("formControl",a.f.address2)("value",a.f.address2.value),t.R7$(3),t.Y8G("placeholder",t.bMT(58,155,"CLIENT.ADD_CLIENT_FORM.TownCityPlaceholder"))("label",t.bMT(59,157,"CLIENT.ADD_CLIENT_FORM.TownCity"))("formControl",a.f.tow_city)("value",a.f.tow_city.value),t.R7$(3),t.Y8G("label",t.bMT(61,159,"CLIENT.ADD_CLIENT_FORM.StateProvince"))("placeholder",t.bMT(62,161,"CLIENT.ADD_CLIENT_FORM.StateProvincePlaceholder"))("formControl",a.f.state_province)("value",a.f.state_province.value),t.R7$(3),t.Y8G("placeholder",t.bMT(64,163,"CLIENT.ADD_CLIENT_FORM.PostalCodePlaceholder"))("label",t.bMT(65,165,"CLIENT.ADD_CLIENT_FORM.PostalCode"))("formControl",a.f.postal_code)("value",a.f.postal_code.value))},dependencies:[E.G,c.qT,c.BC,c.cb,c.l_,c.j4,M.D9,u.I,d.k,f.a,A.M],styles:[".customform[_ngcontent-%COMP%]{border:1px solid gainsboro;border-radius:16px}"]})),r})()}}]);