"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[583],{9424:(y,x,a)=>{a.d(x,{f:()=>E});var n=a(9842),t=a(177),_=a(4438);const I=(T,b,p)=>({"w-4 h-4":T,"w-6 h-6":b,"w-10 h-10":p});let E=(()=>{var T;class b{constructor(){(0,n.A)(this,"size","md")}}return T=b,(0,n.A)(b,"\u0275fac",function(s){return new(s||T)}),(0,n.A)(b,"\u0275cmp",_.VBU({type:T,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[_.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(s,d){1&s&&(_.j41(0,"div",0),_.qSk(),_.j41(1,"svg",1),_.nrm(2,"path",2)(3,"path",3),_.k0s(),_.joV(),_.j41(4,"span",4),_.EFF(5,"Loading..."),_.k0s()()),2&s&&(_.R7$(),_.Y8G("ngClass",_.sMw(1,I,"sm"===d.size,"md"===d.size,"lg"===d.size)))},dependencies:[t.MD,t.YU]})),b})()},8556:(y,x,a)=>{a.d(x,{K:()=>M});var n=a(9842),t=a(4438),_=a(5599),I=a(6146),E=a(4823),T=a(5236);function b(v,A){if(1&v){const e=t.RV6();t.j41(0,"button",4),t.bIt("click",function(){t.eBV(e);const g=t.XpG();return t.Njj(g.handleResume())}),t.nrm(1,"img",5),t.k0s()}}function p(v,A){if(1&v){const e=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(e);const g=t.XpG();return t.Njj(g.handleEdit())}),t.nrm(1,"img",7),t.k0s()}}function s(v,A){if(1&v){const e=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(e);const g=t.XpG();return t.Njj(g.handleDowload())}),t.nrm(1,"img",8),t.k0s()}}function d(v,A){if(1&v){const e=t.RV6();t.j41(0,"div",12)(1,"button",13),t.bIt("click",function(){t.eBV(e);const g=t.XpG(2);return t.Njj(g.handleArchive())}),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"button",14),t.bIt("click",function(){t.eBV(e);const g=t.XpG(2);return t.Njj(g.handleDelete())}),t.EFF(5),t.nI1(6,"translate"),t.k0s()()}2&v&&(t.R7$(2),t.SpI(" ",t.bMT(3,2,"COMMON.Archive")," "),t.R7$(3),t.SpI(" ",t.bMT(6,4,"COMMON.Delete")," "))}function l(v,A){if(1&v&&(t.j41(0,"app-inno-popover",9)(1,"button",10),t.nrm(2,"img",11),t.k0s()(),t.DNE(3,d,7,6,"ng-template",null,0,t.C5r)),2&v){const e=t.sdS(4);t.Y8G("content",e)}}function m(v,A){if(1&v){const e=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(e);const g=t.XpG(2);return t.Njj(g.handleArchive())}),t.nrm(1,"img",15),t.k0s()}}function f(v,A){if(1&v){const e=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(e);const g=t.XpG(2);return t.Njj(g.handleDelete())}),t.nrm(1,"img",16),t.k0s()}}function D(v,A){if(1&v&&t.DNE(0,m,2,0,"button",3)(1,f,2,0,"button",3),2&v){const e=t.XpG();t.vxM(e.onArchive.observed?0:-1),t.R7$(),t.vxM(e.onDelete.observed?1:-1)}}let M=(()=>{var v;class A{constructor(){(0,n.A)(this,"onEdit",new t.bkB),(0,n.A)(this,"onResume",new t.bkB),(0,n.A)(this,"onDelete",new t.bkB),(0,n.A)(this,"onArchive",new t.bkB),(0,n.A)(this,"onDowload",new t.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return v=A,(0,n.A)(A,"\u0275fac",function(S){return new(S||v)}),(0,n.A)(A,"\u0275cmp",t.VBU({type:v,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[t.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(S,g){1&S&&(t.j41(0,"div",1),t.DNE(1,b,2,0,"button",2)(2,p,2,0,"button",3)(3,s,2,0,"button",3)(4,l,5,1)(5,D,2,2),t.k0s()),2&S&&(t.R7$(),t.vxM(g.onResume.observed?1:-1),t.R7$(),t.vxM(g.onEdit.observed?2:-1),t.R7$(),t.vxM(g.onDowload.observed?3:-1),t.R7$(),t.vxM(g.onArchive.observed&&g.onDelete.observed?4:5))},dependencies:[I.G,T.D9,E.oV,_.x]})),A})()},1556:(y,x,a)=>{a.d(x,{Z:()=>p});var n=a(9842),t=a(4438),_=a(467),I=a(2716),E=a(7987);let T=(()=>{var s;class d extends I.H{open(m){var f=this;return(0,_.A)(function*(){const D=yield a.e(3190).then(a.bind(a,3190));return f.matDialog.open(D.AlertConfirmComponent.getComponent(),{data:m,width:"440px",panelClass:"custom_dialog",scrollStrategy:new E.t0,disableClose:!0})})()}}return s=d,(0,n.A)(d,"\u0275fac",(()=>{let l;return function(f){return(l||(l=t.xGo(s)))(f||s)}})()),(0,n.A)(d,"\u0275prov",t.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})),d})(),p=(()=>{var s;class d{constructor(m){(0,n.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=m}alertDelete(m){const{title:f,description:D,textSubmit:M="COMMON.Delete",textCancel:v}=m;return new Promise(A=>{this.alertConfirmDialog.open({title:f,description:D,textSubmit:M,textCancel:v,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(S=>{S.afterClosed().subscribe(g=>{A(g??!1)})})})}alertConfirm(m){const{title:f,description:D,textSubmit:M,textCancel:v}=m;return new Promise(A=>{this.alertConfirmDialog.open({title:f,description:D,textSubmit:M,textCancel:v}).then(S=>{S.afterClosed().subscribe(g=>{A(g??!1)})})})}}return s=d,(0,n.A)(d,"\u0275fac",function(m){return new(m||s)(t.KVO(T))}),(0,n.A)(d,"\u0275prov",t.jDH({token:s,factory:s.\u0275fac})),d})()},6586:(y,x,a)=>{a.d(x,{A:()=>n,a:()=>t});var n=function(_){return _.Created_Tab="Created_Tab",_.Sent_To_Me_Tab="Sent_To_Me_Tab",_}(n||{}),t=function(_){return _[_.all=1]="all",_[_.this_month=2]="this_month",_[_.last_month=3]="last_month",_[_.custom=4]="custom",_}(t||{})},5402:(y,x,a)=>{a.d(x,{Q:()=>n});var n=function(t){return t.Item="Item",t.Service="Service",t}(n||{})},1588:(y,x,a)=>{a.d(x,{j:()=>n});var n=function(t){return t.Day="Day",t.Week="Week",t.Month="Month",t.All="All",t}(n||{})},5277:(y,x,a)=>{a.d(x,{u:()=>b});var n=a(9842),t=a(4438),_=a(6586),I=a(1588),E=a(4412),T=a(1413);let b=(()=>{var p;class s{constructor(){(0,n.A)(this,"behaviorTimeTrackingTypeView",new E.t(I.j.Day)),(0,n.A)(this,"behaviorTimeTrackingDate",new E.t(void 0)),(0,n.A)(this,"behaviorTimeTrackingFilter",new E.t({typeView:I.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,n.A)(this,"behaviorTimeTrackingCreateTimer",new E.t(void 0)),(0,n.A)(this,"behaviorisInternalClient",new E.t(!1)),(0,n.A)(this,"behaviorTimeTrackingShowingTimer",new E.t(!1)),(0,n.A)(this,"reloadItem",new T.B),(0,n.A)(this,"reloadService",new T.B),(0,n.A)(this,"isEstimate",(0,t.vPA)(!1)),(0,n.A)(this,"behaviorInvoiceFilter",new E.t({typeView:_.A.Created_Tab,textSearch:""}))}SetisInternalClient(l){this.behaviorisInternalClient.next(l)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(l){this.behaviorTimeTrackingTypeView.next(l)}SetNewTimeTrackingDate(l){this.behaviorTimeTrackingDate.next(l)}SetNewTimeTrackingShowingTimer(l){this.behaviorTimeTrackingShowingTimer.next(l)}SetNewTimeTrackingCreateTimerInfo(l){this.behaviorTimeTrackingCreateTimer.next(l)}SetNewTimeTrackingFilter(l){this.behaviorTimeTrackingFilter.next(l)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(l){localStorage.setItem("ResumeData",JSON.stringify(l))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(l){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...l})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return p=s,(0,n.A)(s,"\u0275fac",function(l){return new(l||p)}),(0,n.A)(s,"\u0275prov",t.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})),s})()},3814:(y,x,a)=>{a.d(x,{v:()=>T});var n=a(467),t=a(9842),_=a(2716),I=a(7987),E=a(4438);let T=(()=>{var b;class p extends _.H{open(d){var l=this;return(0,n.A)(function*(){const m=yield Promise.all([a.e(1328),a.e(2076),a.e(7371)]).then(a.bind(a,2508));return l.matDialog.open(m.DialogModifyItemServiceComponent.getComponent(),{disableClose:!0,panelClass:"custom_dialog",data:d,width:"500px",scrollStrategy:new I.t0})})()}}return b=p,(0,t.A)(p,"\u0275fac",(()=>{let s;return function(l){return(s||(s=E.xGo(b)))(l||b)}})()),(0,t.A)(p,"\u0275prov",E.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})),p})()},5272:(y,x,a)=>{a.d(x,{N:()=>b});var n=a(9842),t=a(5312),_=a(1626),I=a(4438),E=a(6473);const T=t.c.HOST_API+"/api";let b=(()=>{var p;class s{constructor(){(0,n.A)(this,"http",(0,I.WQX)(_.Qq))}GetAllService(l){const m=(0,E.yU)(l,!1);return this.http.get(T+"/Service/GetAllService",{params:m})}CreateService(l){return this.http.post(T+"/Service/CreateService",l)}GetServiceById(l){return this.http.get(T+`/Service/GetServiceById?id=${l}`)}DeleteServices(l){return this.http.post(T+"/Service/DeleteServices",l)}UpdateArchive(l){return this.http.post(T+"/Service/UpdateArchive",l)}Update(l){return this.http.post(T+"/Service/Update",l)}}return p=s,(0,n.A)(s,"\u0275fac",function(l){return new(l||p)}),(0,n.A)(s,"\u0275prov",I.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})),s})()},5909:(y,x,a)=>{a.d(x,{R2:()=>_,Xj:()=>t,az:()=>b,jQ:()=>n,yo:()=>T});const n=(p,s)=>{if(!p||!s)return 0;const d=p.split(":").map(Number);let l=0,m=0,f=0;return 3===d.length?[l,m,f]=d:2===d.length?[l,m]=d:1===d.length&&([l]=d),Number(((l+m/60+f/3600)*s).toFixed(2))},t=(p=[],s=!1)=>(p.some(l=>l.companyTax||l.selected||void 0===l.selected)?p:p.filter(l=>l.selected)).map(l=>{let m,f;return l.companyTax?(m=l.companyTax.name,f=l.companyTax.amount):(m=l.name,f=l.amount),s?`${m} (${f}%)`:m}).filter(Boolean).sort((l,m)=>l.localeCompare(m)).join(", "),_=(p,s)=>p&&s?Number((p*s).toFixed(2)):0,T=p=>{let s=0,d={};p.forEach(({rate:m,qty:f,taxes:D})=>{if(!m||!f)return;const M=Number((m*f).toFixed(2));s+=M,D&&0!==D.length&&(D.some(e=>e.companyTaxId)?D.filter(e=>e.companyTaxId):D.filter(e=>e.selected)).forEach(e=>{const S=e.name||"Unknown Tax",g=e.taxeNumber||"",j=Number(e?.companyTax?.amount??e.amount??0);d[S]||(d[S]={name:S,numberTax:g,amount:j,taxableAmount:0,total:0}),d[S].taxableAmount+=M})});let l=0;return Object.values(d).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),l+=m.total}),{subtotal:Number(s.toFixed(2)),totalTaxes:d,grandTotalTax:Number(l.toFixed(2))}},b=p=>{let s=0,d={};p.forEach(({total:m,taxes:f})=>{m&&(s+=m,f&&0!==f.length)&&(f.some(v=>v.companyTaxId)?f.filter(v=>v.companyTaxId):f.filter(v=>v.selected)).forEach(v=>{const A=v.name||"Unknown Tax",e=v.taxeNumber||"",S=Number(v?.companyTax?.amount??v.amount??0);d[A]||(d[A]={name:A,numberTax:e,amount:S,taxableAmount:0,total:0}),d[A].taxableAmount+=m})});let l=0;return Object.values(d).forEach(m=>{m.total=Number((m.taxableAmount*(m.amount/100)).toFixed(2)),l+=m.total}),{subtotal:Number(s.toFixed(2)),totalTaxes:d,grandTotalTax:Number(l.toFixed(2))}}},583:(y,x,a)=>{a.r(x),a.d(x,{ItemServicesComponent:()=>ve});var n=a(9842),t=a(5599),_=a(8600),I=a(5402),E=a(9424),T=a(1556),b=a(6146),p=a(177),s=a(1448),d=a(1413),l=a(152),m=a(9079),f=a(5900),D=a(8556),M=a(1970),v=a(4823),A=a(5909),e=a(4438),S=a(5236),g=a(33),j=a(5272),G=a(3492),k=a(1110),P=a(5277),V=a(822),N=a(3814);const $=["grid"],L=c=>({"mb-28":c});function X(c,h){1&c&&(e.j41(0,"div",5),e.nrm(1,"app-inno-spin",7),e.k0s())}function z(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){const i=h.$implicit;e.R7$(),e.SpI(" ",i.itemName,"")}}function K(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){const i=h.$implicit;e.R7$(),e.SpI(" ",i.description,"")}}function Q(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){const i=h.$implicit,o=e.XpG(2);e.R7$(),e.SpI(" ",o.getNameSelectedTaxes(null==i?null:i.taxes),"")}}function W(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){const i=h.$implicit;e.R7$(),e.SpI(" ",i.rate,"")}}function Y(c,h){if(1&c&&(e.j41(0,"div",18),e.nrm(1,"ngx-avatars",19),e.j41(2,"span",20),e.EFF(3),e.k0s()()),2&c){const i=e.XpG().$implicit,o=e.XpG(2);e.R7$(),e.FCK("matTooltip","",null==i||null==i.inforUser?null:i.inforUser.firstName," ",null==i||null==i.inforUser?null:i.inforUser.lastName," "),e.FS9("bgColor",o._storeService.getBgColor(null==i||null==i.inforUser?null:i.inforUser.firstName.slice(0,1))),e.Y8G("size",35)("name",(null==i?null:i.inforUser.firstName.charAt(0))+" "+(null!=i&&null!=i.inforUser&&i.inforUser.lastName?null==i||null==i.inforUser?null:i.inforUser.lastName.charAt(0):"")),e.R7$(2),e.Lme(" ",null==i||null==i.inforUser?null:i.inforUser.firstName," ",null==i||null==i.inforUser?null:i.inforUser.lastName,"")}}function H(c,h){if(1&c&&(e.j41(0,"div",18),e.nrm(1,"ngx-avatars",19),e.j41(2,"span",20),e.EFF(3),e.k0s()()),2&c){const i=e.XpG().$implicit,o=e.XpG(2);e.R7$(),e.FS9("matTooltip",null==i||null==i.inforUser?null:i.inforUser.email),e.FS9("bgColor",o._storeService.getBgColor(null==i||null==i.inforUser?null:i.inforUser.email.slice(0,1))),e.Y8G("size",35)("name",null==i||null==i.inforUser?null:i.inforUser.email.slice(0,1)),e.R7$(2),e.SpI(" ",null==i||null==i.inforUser?null:i.inforUser.email,"")}}function Z(c,h){if(1&c&&(e.j41(0,"p",17),e.DNE(1,Y,4,8,"div",18)(2,H,4,5,"div",18),e.k0s()),2&c){const i=h.$implicit;e.R7$(),e.vxM(null!=i&&null!=i.inforUser&&i.inforUser.firstName?1:2)}}function J(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&c){const i=h.$implicit,o=e.XpG(2);e.R7$(),e.SpI(" ",e.i5U(2,1,i.createdAt,o._storeService.getdateFormat()),"")}}function q(c,h){if(1&c){const i=e.RV6();e.j41(0,"app-inno-table-action",21),e.bIt("onEdit",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleEdit(r))})("onDelete",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleDelete(r))})("onArchive",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleArchive(r))}),e.k0s()}}function ee(c,h){if(1&c){const i=e.RV6();e.j41(0,"div",6)(1,"ejs-grid",8,0),e.bIt("actionBegin",function(r){e.eBV(i);const u=e.XpG();return e.Njj(u.onActionBegin(r))}),e.j41(3,"e-columns")(4,"e-column",9),e.nI1(5,"translate"),e.DNE(6,z,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(8,"e-column",10),e.nI1(9,"translate"),e.DNE(10,K,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(12,"e-column",11),e.nI1(13,"translate"),e.DNE(14,Q,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(16,"e-column",12),e.nI1(17,"translate"),e.DNE(18,W,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(20,"e-column",11),e.nI1(21,"translate"),e.DNE(22,Z,3,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(24,"e-column",13),e.nI1(25,"translate"),e.DNE(26,J,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(28,"e-column",14),e.DNE(29,q,1,0,"ng-template",null,1,e.C5r),e.k0s()()(),e.j41(31,"ejs-pager",15),e.bIt("click",function(r){e.eBV(i);const u=e.XpG();return e.Njj(u.onPageChange(r))}),e.k0s()()}if(2&c){const i=e.XpG();e.Y8G("ngClass",e.eq3(28,L,i._storeService.getIsRunning())),e.R7$(),e.Y8G("dataSource",i.dataSource)("allowSelection",!0)("sortSettings",i.sortOptions)("allowSorting",!0)("selectionSettings",i.selectionOptions),e.R7$(3),e.Y8G("headerText",e.bMT(5,16,"ITEMS_SERVICES.GIRD.ItemName")),e.R7$(4),e.Y8G("headerText",e.bMT(9,18,"ITEMS_SERVICES.GIRD.Description")),e.R7$(4),e.Y8G("headerText",e.bMT(13,20,"ITEMS_SERVICES.GIRD.Taxes")),e.R7$(4),e.Y8G("headerText",e.bMT(17,22,"ITEMS_SERVICES.GIRD.Rate")),e.R7$(4),e.Y8G("headerText",e.bMT(21,24,"ITEMS_SERVICES.GIRD.User")),e.R7$(4),e.Y8G("headerText",e.bMT(25,26,"ITEMS_SERVICES.GIRD.Date")),e.R7$(7),e.Y8G("pageSize",i.pageSizesDefault)("totalRecordsCount",i.totalPages)("currentPage",i.currentPage)("pageSizes",i.pageSizes)}}let te=(()=>{var c;class h{constructor(o,r,u,C,R,F,O,U,B,w,ge){(0,n.A)(this,"translate",void 0),(0,n.A)(this,"layoutUtilsService",void 0),(0,n.A)(this,"router",void 0),(0,n.A)(this,"destroyRef",void 0),(0,n.A)(this,"activatedRoute",void 0),(0,n.A)(this,"serviceService",void 0),(0,n.A)(this,"toastService",void 0),(0,n.A)(this,"_storeService",void 0),(0,n.A)(this,"dataService",void 0),(0,n.A)(this,"itemService",void 0),(0,n.A)(this,"modifyItemAndServiceDialog",void 0),(0,n.A)(this,"isLoading",!1),(0,n.A)(this,"sort",void 0),(0,n.A)(this,"sortOptions",{columns:[]}),(0,n.A)(this,"getNameSelectedTaxes",A.Xj),(0,n.A)(this,"_subscriptions",[]),(0,n.A)(this,"search",""),(0,n.A)(this,"searchSubject",new d.B),(0,n.A)(this,"idTime",void 0),(0,n.A)(this,"columnName",void 0),(0,n.A)(this,"direction",void 0),(0,n.A)(this,"selectionOptions",void 0),(0,n.A)(this,"projectId",""),(0,n.A)(this,"listProject",[]),(0,n.A)(this,"dataSource",void 0),(0,n.A)(this,"totalPages",1),(0,n.A)(this,"currentPage",1),(0,n.A)(this,"pageSizes",[10,20,50,100]),(0,n.A)(this,"pageSizesDefault",10),(0,n.A)(this,"grid",void 0),this.translate=o,this.layoutUtilsService=r,this.router=u,this.destroyRef=C,this.activatedRoute=R,this.serviceService=F,this.toastService=O,this._storeService=U,this.dataService=B,this.itemService=w,this.modifyItemAndServiceDialog=ge}onPageChange(o){o?.newProp?.pageSize&&(this.pageSizesDefault=o.newProp.pageSize,this.LoadItem(this.currentPage,"")),o?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:o.currentPage},queryParamsHandling:"merge"})}LoadItem(o,r,u){this.isLoading=!0,this.itemService.GetAllItem({Page:o,PageSize:this.pageSizesDefault,Search:r,Filter:u}).pipe((0,m.pQ)(this.destroyRef)).subscribe({next:R=>{this.totalPages=R.totalRecords,this.dataSource=R.data,this.isLoading=!1,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]})}})}handleSearch(o){this.searchSubject.next(o)}ngOnInit(){const o=this.searchSubject.pipe((0,l.B)(550)).subscribe(C=>{this.search=C??"",this.LoadItem(this.currentPage,this.search)});this._subscriptions.push(o);const r=this.activatedRoute.queryParams.pipe((0,m.pQ)(this.destroyRef)).subscribe(C=>{if("Service"!==C?.tab)if(C?.page){this.currentPage=C.page;const R={Sort:JSON.stringify(this.sort)};this.sort?this.LoadItem(this.currentPage,"",R):this.LoadItem(this.currentPage,"")}else this.LoadItem(this.currentPage,"")});this._subscriptions.push(r);const u=this.dataService.reloadItem.pipe((0,m.pQ)(this.destroyRef)).subscribe(C=>{C&&(this.search="",this.LoadItem(this.currentPage,""))});this._subscriptions.push(u)}creaFormDelete(){}handleEdit(o){this.modifyItemAndServiceDialog.open({mode:I.Q.Item,serviceInfo:o}).then(u=>{u.afterClosed().subscribe(C=>{C&&(this.search="",this.LoadItem(this.currentPage,""))})})}handleDelete(o){const r=this.translate.instant("ITEMS_SERVICES.DeleteItem"),u=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:r,description:u}).then(C=>{C&&this.itemService.DeleteItem([o.id]).pipe((0,m.pQ)(this.destroyRef)).subscribe(R=>{R?(this.LoadItem(this.currentPage,""),this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}handleArchive(o){this.serviceService.UpdateArchive([o.id]).pipe((0,m.pQ)(this.destroyRef)).subscribe(r=>{r?this.toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})}onActionBegin(o){if("sorting"===o.requestType){if(this.columnName=o.columnName,this.direction=o.direction,this.sort={columnName:o.columnName,direction:o.direction},this.columnName){const r={Sort:JSON.stringify(this.sort)};return void this.LoadItem(this.currentPage,"",r)}this.sortOptions={columns:[]},this.sort=null,this.LoadItem(this.currentPage,"")}}ngOnDestroy(){this._subscriptions&&this._subscriptions.forEach(o=>o.unsubscribe())}}return c=h,(0,n.A)(h,"\u0275fac",function(o){return new(o||c)(e.rXU(S.c$),e.rXU(T.Z),e.rXU(g.Ix),e.rXU(e.abz),e.rXU(g.nX),e.rXU(j.N),e.rXU(G.f),e.rXU(k.n),e.rXU(P.u),e.rXU(V.b),e.rXU(N.v))}),(0,n.A)(h,"\u0275cmp",e.VBU({type:c,selectors:[["app-item-management"]],viewQuery:function(o,r){if(1&o&&e.GBs($,5),2&o){let u;e.mGM(u=e.lsd())&&(r.grid=u.first)}},standalone:!0,features:[e.Jv_([T.Z]),e.aNF],decls:5,vars:2,consts:[["grid",""],["template",""],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],[1,"w-full","mt-[12px]",3,"ngClass"],["size","lg"],[1,"customTable",3,"actionBegin","dataSource","allowSelection","sortSettings","allowSorting","selectionSettings"],["width","200","field","itemName",3,"headerText"],["width","200","field","description",3,"headerText"],["width","200",3,"headerText"],["width","140","field","rate",3,"headerText"],["width","200","field","createdAt",3,"headerText"],["headerText","","width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"text-text-md-regular","text-text-primary"],[1,"text-text-primary","text-text-md-semibold"],[1,"flex","items-center"],[3,"matTooltip","size","bgColor","name"],[1,"pl-1","line-clamp-1"],[3,"onEdit","onDelete","onArchive"]],template:function(o,r){1&o&&(e.j41(0,"div",2)(1,"div",3)(2,"app-inno-input-search",4),e.bIt("onChange",function(C){return r.handleSearch(C)}),e.k0s()()(),e.DNE(3,X,2,0,"div",5)(4,ee,32,30,"div",6)),2&o&&(e.R7$(2),e.Y8G("value",r.search),e.R7$(),e.vxM(r.isLoading?3:4))},dependencies:[p.MD,p.YU,p.vh,s.iov,s.BzB,s.gFV,s._ab,s.eeu,s.rFS,s.LGG,s.cvh,b.G,S.D9,M.mC,M.fw,v.uc,v.oV,f.M,D.K,E.f],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),h})();var ne=a(9172);const ie=["grid"],oe=c=>({"mb-28":c});function ae(c,h){1&c&&(e.j41(0,"div",5),e.nrm(1,"app-inno-spin",7),e.k0s())}function re(c,h){if(1&c&&(e.j41(0,"p",15),e.EFF(1),e.k0s()),2&c){const i=h.$implicit;e.R7$(),e.SpI(" ",i.serviceName,"")}}function se(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){let i;const o=h.$implicit;e.R7$(),e.SpI(" ",null!==(i=o.description)&&void 0!==i?i:"","")}}function le(c,h){if(1&c&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&c){const i=h.$implicit,o=e.XpG(2);e.R7$(),e.SpI(" ",o.getNameSelectedTaxes(null==i?null:i.taxes),"")}}function ce(c,h){if(1&c&&(e.j41(0,"p",15),e.EFF(1),e.k0s()),2&c){let i;const o=h.$implicit;e.R7$(),e.SpI(" ",null!==(i=o.rate)&&void 0!==i?i:"","")}}function me(c,h){if(1&c){const i=e.RV6();e.j41(0,"app-inno-table-action",17),e.bIt("onEdit",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleEdit(r))})("onDelete",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleDelete(r))})("onArchive",function(){const r=e.eBV(i).$implicit,u=e.XpG(2);return e.Njj(u.handleArchive(r))}),e.k0s()}}function ue(c,h){if(1&c){const i=e.RV6();e.j41(0,"div",6)(1,"ejs-grid",8,0),e.bIt("actionBegin",function(r){e.eBV(i);const u=e.XpG();return e.Njj(u.onActionBegin(r))}),e.j41(3,"e-columns")(4,"e-column",9),e.nI1(5,"translate"),e.DNE(6,re,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(8,"e-column",10),e.nI1(9,"translate"),e.DNE(10,se,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(12,"e-column",11),e.nI1(13,"translate"),e.DNE(14,le,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(16,"e-column",12),e.nI1(17,"translate"),e.DNE(18,ce,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(20,"e-column",13),e.DNE(21,me,1,0,"ng-template",null,1,e.C5r),e.k0s()()(),e.j41(23,"ejs-pager",14),e.bIt("click",function(r){e.eBV(i);const u=e.XpG();return e.Njj(u.onPageChange(r))}),e.k0s()()}if(2&c){const i=e.XpG();e.Y8G("ngClass",e.eq3(22,oe,i._storeService.getIsRunning())),e.R7$(),e.Y8G("dataSource",i.dataSource)("allowSelection",!0)("sortSettings",i.sortOptions)("allowSorting",!0)("selectionSettings",i.selectionOptions),e.R7$(3),e.Y8G("headerText",e.bMT(5,14,"ITEMS_SERVICES.GIRD.ServiceName")),e.R7$(4),e.Y8G("headerText",e.bMT(9,16,"ITEMS_SERVICES.GIRD.Description")),e.R7$(4),e.Y8G("headerText",e.bMT(13,18,"ITEMS_SERVICES.GIRD.Taxes")),e.R7$(4),e.Y8G("headerText",e.bMT(17,20,"ITEMS_SERVICES.GIRD.Rate")),e.R7$(7),e.Y8G("pageSize",i.pageSizesDefault)("totalRecordsCount",i.totalPages)("currentPage",i.currentPage)("pageSizes",i.pageSizes)}}let de=(()=>{var c;class h{constructor(o,r,u,C,R,F,O,U,B,w){(0,n.A)(this,"translate",void 0),(0,n.A)(this,"layoutUtilsService",void 0),(0,n.A)(this,"router",void 0),(0,n.A)(this,"_storeService",void 0),(0,n.A)(this,"destroyRef",void 0),(0,n.A)(this,"activatedRoute",void 0),(0,n.A)(this,"serviceService",void 0),(0,n.A)(this,"toastService",void 0),(0,n.A)(this,"dataService",void 0),(0,n.A)(this,"modifyItemAndServiceDialog",void 0),(0,n.A)(this,"isLoading",!1),(0,n.A)(this,"getNameSelectedTaxes",A.Xj),(0,n.A)(this,"_subscriptions",[]),(0,n.A)(this,"search",""),(0,n.A)(this,"searchSubject",new d.B),(0,n.A)(this,"sort",void 0),(0,n.A)(this,"sortOptions",{columns:[]}),(0,n.A)(this,"selectionOptions",void 0),(0,n.A)(this,"projectName",void 0),(0,n.A)(this,"listProject",[]),(0,n.A)(this,"dataSource",void 0),(0,n.A)(this,"totalPages",1),(0,n.A)(this,"currentPage",1),(0,n.A)(this,"pageSizes",[10,20,50,100]),(0,n.A)(this,"pageSizesDefault",10),(0,n.A)(this,"grid",void 0),(0,n.A)(this,"columnName",void 0),(0,n.A)(this,"direction",void 0),this.translate=o,this.layoutUtilsService=r,this.router=u,this._storeService=C,this.destroyRef=R,this.activatedRoute=F,this.serviceService=O,this.toastService=U,this.dataService=B,this.modifyItemAndServiceDialog=w}onPageChange(o){o?.newProp?.pageSize&&(this.pageSizesDefault=o.newProp.pageSize,this.LoadAllServiceByProjectAsync()),o?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:o.currentPage},queryParamsHandling:"merge"})}LoadAllServiceByProjectAsync(){this.isLoading=!0;let o={Page:this.currentPage,PageSize:this.pageSizesDefault,Search:this.search,...this.sort};this.serviceService.GetAllService(o).pipe((0,m.pQ)(this.destroyRef)).subscribe({next:r=>{r&&(this.isLoading=!1,this.totalPages=r.totalRecords,this.dataSource=r.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))}})}handleSearch(o){this.searchSubject.next(o)}ngOnInit(){const o=this.searchSubject.pipe((0,ne.Z)("")).subscribe(r=>{this.search=r,this.LoadAllServiceByProjectAsync()});this._subscriptions.push(o),this.activatedRoute.queryParams.pipe((0,m.pQ)(this.destroyRef)).subscribe(r=>{"Item"!==r?.tab&&r?.page&&(this.currentPage=r.page,this.search="",this.LoadAllServiceByProjectAsync())}),this.dataService.reloadService.pipe((0,m.pQ)(this.destroyRef)).subscribe(r=>{1==r&&(this.search="",this.LoadAllServiceByProjectAsync())})}handleEdit(o){this.modifyItemAndServiceDialog.open({mode:I.Q.Service,isShowProject:!0,serviceInfo:o}).then(u=>{u.afterClosed().subscribe(C=>{C&&this.dataService.reloadService.next(!0)})})}handleDelete(o){const r=this.translate.instant("ITEMS_SERVICES.DeleteService"),u=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:r,description:u}).then(C=>{C&&this.serviceService.DeleteServices([o.id]).pipe((0,m.pQ)(this.destroyRef)).subscribe(R=>{R?(this.search="",this.LoadAllServiceByProjectAsync(),this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}handleArchive(o){this.serviceService.UpdateArchive([o.id]).pipe((0,m.pQ)(this.destroyRef)).subscribe(r=>{r?(this.search="",this.LoadAllServiceByProjectAsync(),this.toastService.showError(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})}onActionBegin(o){if("sorting"===o.requestType){if(this.columnName=o.columnName,this.direction=o.direction,this.sort={columnName:o.columnName,direction:o.direction},this.columnName)return void this.LoadAllServiceByProjectAsync();this.sortOptions={columns:[]},this.sort=null,this.LoadAllServiceByProjectAsync()}}ngOnDestroy(){this._subscriptions&&this._subscriptions.forEach(o=>o.unsubscribe())}}return c=h,(0,n.A)(h,"\u0275fac",function(o){return new(o||c)(e.rXU(S.c$),e.rXU(T.Z),e.rXU(g.Ix),e.rXU(k.n),e.rXU(e.abz),e.rXU(g.nX),e.rXU(j.N),e.rXU(G.f),e.rXU(P.u),e.rXU(N.v))}),(0,n.A)(h,"\u0275cmp",e.VBU({type:c,selectors:[["app-service-management"]],viewQuery:function(o,r){if(1&o&&e.GBs(ie,5),2&o){let u;e.mGM(u=e.lsd())&&(r.grid=u.first)}},standalone:!0,features:[e.Jv_([T.Z]),e.aNF],decls:5,vars:2,consts:[["grid",""],["template",""],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],[1,"w-full","mt-[12px]",3,"ngClass"],["size","lg"],[1,"customTable",3,"actionBegin","dataSource","allowSelection","sortSettings","allowSorting","selectionSettings"],["width","200","field","serviceName",3,"headerText"],["width","200","field","description",3,"headerText"],["width","200",3,"headerText"],["width","140","field","rate",3,"headerText"],["headerText","","width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"text-text-md-regular","text-text-primary"],[1,"text-text-md-regular","text-text-primary","whitespace-pre-wrap"],[3,"onEdit","onDelete","onArchive"]],template:function(o,r){1&o&&(e.j41(0,"div",2)(1,"div",3)(2,"app-inno-input-search",4),e.bIt("onChange",function(C){return r.handleSearch(C)}),e.k0s()()(),e.DNE(3,ae,2,0,"div",5)(4,ue,24,24,"div",6)),2&o&&(e.R7$(2),e.Y8G("value",r.search),e.R7$(),e.vxM(r.isLoading?3:4))},dependencies:[p.MD,p.YU,s.iov,s.BzB,s.gFV,s._ab,s.eeu,s.rFS,s.LGG,s.cvh,b.G,S.D9,M.mC,v.uc,f.M,D.K,E.f],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),h})();function pe(c,h){if(1&c){const i=e.RV6();e.j41(0,"div",12)(1,"div",13),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.handleCreateNewItem())}),e.j41(2,"div",14)(3,"span",15),e.EFF(4),e.nI1(5,"translate"),e.k0s(),e.j41(6,"p",16),e.EFF(7),e.nI1(8,"translate"),e.k0s()()(),e.j41(9,"div",13),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.handleCreateNewService())}),e.j41(10,"div",14)(11,"span",15),e.EFF(12),e.nI1(13,"translate"),e.k0s(),e.j41(14,"p",16),e.EFF(15),e.nI1(16,"translate"),e.k0s()()()()}2&c&&(e.R7$(4),e.JRh(e.bMT(5,4,"ITEMS_SERVICES.Tabs.Items")),e.R7$(3),e.SpI(" ",e.bMT(8,6,"ITEMS_SERVICES.Tabs.DescribeItem")," "),e.R7$(5),e.JRh(e.bMT(13,8,"ITEMS_SERVICES.Tabs.Services")),e.R7$(3),e.SpI(" ",e.bMT(16,10,"ITEMS_SERVICES.Tabs.DescribeServices")," "))}function he(c,h){1&c&&e.nrm(0,"app-item-management")}function _e(c,h){1&c&&e.nrm(0,"app-service-management")}let ve=(()=>{var c;class h{constructor(o,r,u,C,R){(0,n.A)(this,"modifyItemAndServiceDialog",void 0),(0,n.A)(this,"dataService",void 0),(0,n.A)(this,"router",void 0),(0,n.A)(this,"activatedRoute",void 0),(0,n.A)(this,"translate",void 0),(0,n.A)(this,"currentTypeView",I.Q.Item),(0,n.A)(this,"itemServiceView",I.Q),(0,n.A)(this,"listTypeView",[]),this.modifyItemAndServiceDialog=o,this.dataService=r,this.router=u,this.activatedRoute=C,this.translate=R,this.listTypeView=[{label:this.translate.instant("ITEMS_SERVICES.Tabs.Items"),value:I.Q.Item},{label:this.translate.instant("ITEMS_SERVICES.Tabs.Services"),value:I.Q.Service}]}ngOnInit(){this.activatedRoute.queryParams.subscribe(o=>{const r=o.tab;r&&(this.currentTypeView=I.Q[r]||I.Q.Item)})}handleChangeTypeView(o){this.currentTypeView!==o&&(this.currentTypeView=o,this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{tab:I.Q[o]},queryParamsHandling:"merge"}))}removeQueryParams(){this.router.navigate([],{queryParams:{},replaceUrl:!0,queryParamsHandling:""})}handleCreateNewItem(){this.modifyItemAndServiceDialog.open({mode:I.Q.Item}).then(r=>{r.afterClosed().subscribe(u=>{u&&this.dataService.reloadItem.next(!0)})})}handleCreateNewService(){this.modifyItemAndServiceDialog.open({mode:I.Q.Service,isShowProject:!0}).then(r=>{r.afterClosed().subscribe(u=>{u&&this.dataService.reloadService.next(!0)})})}}return c=h,(0,n.A)(h,"\u0275fac",function(o){return new(o||c)(e.rXU(N.v),e.rXU(P.u),e.rXU(g.Ix),e.rXU(g.nX),e.rXU(S.c$))}),(0,n.A)(h,"\u0275cmp",e.VBU({type:c,selectors:[["app-item-services"]],standalone:!0,features:[e.aNF],decls:19,vars:10,consts:[["contentCreateNew",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],["position","bottom-start",3,"content"],["target","",1,"button-size-md","button-primary"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"],[1,"container-full","mt-[24px]","flex","items-center","justify-between","flex-wrap","gap-2"],[1,"flex","w-full","items-center","gap-[14px]","flex-wrap","justify-between"],[3,"onChange","tabs","value"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","mt-[24px]"],[1,"w-[300px]"],[1,"button-transparent",3,"click"],[1,"block"],[1,"text-text-md-semibold"],[1,"text-text-secondary","text-text-xs-regular"]],template:function(o,r){if(1&o){const u=e.RV6();e.j41(0,"div",1)(1,"div",2)(2,"p",3),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.j41(5,"app-inno-popover",4)(6,"button",5),e.nrm(7,"img",6),e.EFF(8),e.nI1(9,"translate"),e.k0s(),e.DNE(10,pe,17,12,"ng-template",null,0,e.C5r),e.k0s()()(),e.j41(12,"div",7)(13,"div",8)(14,"app-inno-tabs",9),e.bIt("onChange",function(R){return e.eBV(u),e.Njj(r.handleChangeTypeView(R))}),e.k0s(),e.nrm(15,"div",10),e.k0s()(),e.j41(16,"div",11),e.DNE(17,he,1,0,"app-item-management")(18,_e,1,0,"app-service-management"),e.k0s()}if(2&o){const u=e.sdS(11);e.R7$(3),e.SpI(" ",e.bMT(4,6,"ITEMS_SERVICES.Title")," "),e.R7$(2),e.Y8G("content",u),e.R7$(3),e.SpI(" ",e.bMT(9,8,"ITEMS_SERVICES.Buttons.CreateNew")," "),e.R7$(6),e.Y8G("tabs",r.listTypeView)("value",r.currentTypeView),e.R7$(3),e.vxM(r.currentTypeView===r.itemServiceView.Item?17:18)}},dependencies:[S.h,S.D9,_.k,t.x,te,de],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),h})()}}]);