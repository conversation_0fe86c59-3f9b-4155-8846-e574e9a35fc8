"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9589],{1970:(Q,b,l)=>{l.d(b,{fw:()=>H,mC:()=>Y});var n=l(4438),d=l(177);class u{constructor(){this._dataLength=0,this._bufferLength=0,this._state=new Int32Array(4),this._buffer=new ArrayBuffer(68),this._buffer8=new Uint8Array(this._buffer,0,68),this._buffer32=new Uint32Array(this._buffer,0,17),this.start()}static hashStr(a,i=!1){return this.onePassHasher.start().appendStr(a).end(i)}static hashAsciiStr(a,i=!1){return this.onePassHasher.start().appendAsciiStr(a).end(i)}static _hex(a){const i=u.hexChars,t=u.hexOut;let e,r,s,h;for(h=0;h<4;h+=1)for(r=8*h,e=a[h],s=0;s<8;s+=2)t[r+1+s]=i.charAt(15&e),e>>>=4,t[r+0+s]=i.charAt(15&e),e>>>=4;return t.join("")}static _md5cycle(a,i){let t=a[0],e=a[1],r=a[2],s=a[3];t+=(e&r|~e&s)+i[0]-680876936|0,t=(t<<7|t>>>25)+e|0,s+=(t&e|~t&r)+i[1]-389564586|0,s=(s<<12|s>>>20)+t|0,r+=(s&t|~s&e)+i[2]+606105819|0,r=(r<<17|r>>>15)+s|0,e+=(r&s|~r&t)+i[3]-1044525330|0,e=(e<<22|e>>>10)+r|0,t+=(e&r|~e&s)+i[4]-176418897|0,t=(t<<7|t>>>25)+e|0,s+=(t&e|~t&r)+i[5]+1200080426|0,s=(s<<12|s>>>20)+t|0,r+=(s&t|~s&e)+i[6]-1473231341|0,r=(r<<17|r>>>15)+s|0,e+=(r&s|~r&t)+i[7]-45705983|0,e=(e<<22|e>>>10)+r|0,t+=(e&r|~e&s)+i[8]+1770035416|0,t=(t<<7|t>>>25)+e|0,s+=(t&e|~t&r)+i[9]-1958414417|0,s=(s<<12|s>>>20)+t|0,r+=(s&t|~s&e)+i[10]-42063|0,r=(r<<17|r>>>15)+s|0,e+=(r&s|~r&t)+i[11]-1990404162|0,e=(e<<22|e>>>10)+r|0,t+=(e&r|~e&s)+i[12]+1804603682|0,t=(t<<7|t>>>25)+e|0,s+=(t&e|~t&r)+i[13]-40341101|0,s=(s<<12|s>>>20)+t|0,r+=(s&t|~s&e)+i[14]-1502002290|0,r=(r<<17|r>>>15)+s|0,e+=(r&s|~r&t)+i[15]+1236535329|0,e=(e<<22|e>>>10)+r|0,t+=(e&s|r&~s)+i[1]-165796510|0,t=(t<<5|t>>>27)+e|0,s+=(t&r|e&~r)+i[6]-1069501632|0,s=(s<<9|s>>>23)+t|0,r+=(s&e|t&~e)+i[11]+643717713|0,r=(r<<14|r>>>18)+s|0,e+=(r&t|s&~t)+i[0]-373897302|0,e=(e<<20|e>>>12)+r|0,t+=(e&s|r&~s)+i[5]-701558691|0,t=(t<<5|t>>>27)+e|0,s+=(t&r|e&~r)+i[10]+38016083|0,s=(s<<9|s>>>23)+t|0,r+=(s&e|t&~e)+i[15]-660478335|0,r=(r<<14|r>>>18)+s|0,e+=(r&t|s&~t)+i[4]-405537848|0,e=(e<<20|e>>>12)+r|0,t+=(e&s|r&~s)+i[9]+568446438|0,t=(t<<5|t>>>27)+e|0,s+=(t&r|e&~r)+i[14]-1019803690|0,s=(s<<9|s>>>23)+t|0,r+=(s&e|t&~e)+i[3]-187363961|0,r=(r<<14|r>>>18)+s|0,e+=(r&t|s&~t)+i[8]+1163531501|0,e=(e<<20|e>>>12)+r|0,t+=(e&s|r&~s)+i[13]-1444681467|0,t=(t<<5|t>>>27)+e|0,s+=(t&r|e&~r)+i[2]-51403784|0,s=(s<<9|s>>>23)+t|0,r+=(s&e|t&~e)+i[7]+1735328473|0,r=(r<<14|r>>>18)+s|0,e+=(r&t|s&~t)+i[12]-1926607734|0,e=(e<<20|e>>>12)+r|0,t+=(e^r^s)+i[5]-378558|0,t=(t<<4|t>>>28)+e|0,s+=(t^e^r)+i[8]-2022574463|0,s=(s<<11|s>>>21)+t|0,r+=(s^t^e)+i[11]+1839030562|0,r=(r<<16|r>>>16)+s|0,e+=(r^s^t)+i[14]-35309556|0,e=(e<<23|e>>>9)+r|0,t+=(e^r^s)+i[1]-1530992060|0,t=(t<<4|t>>>28)+e|0,s+=(t^e^r)+i[4]+1272893353|0,s=(s<<11|s>>>21)+t|0,r+=(s^t^e)+i[7]-155497632|0,r=(r<<16|r>>>16)+s|0,e+=(r^s^t)+i[10]-1094730640|0,e=(e<<23|e>>>9)+r|0,t+=(e^r^s)+i[13]+681279174|0,t=(t<<4|t>>>28)+e|0,s+=(t^e^r)+i[0]-358537222|0,s=(s<<11|s>>>21)+t|0,r+=(s^t^e)+i[3]-722521979|0,r=(r<<16|r>>>16)+s|0,e+=(r^s^t)+i[6]+76029189|0,e=(e<<23|e>>>9)+r|0,t+=(e^r^s)+i[9]-640364487|0,t=(t<<4|t>>>28)+e|0,s+=(t^e^r)+i[12]-421815835|0,s=(s<<11|s>>>21)+t|0,r+=(s^t^e)+i[15]+530742520|0,r=(r<<16|r>>>16)+s|0,e+=(r^s^t)+i[2]-995338651|0,e=(e<<23|e>>>9)+r|0,t+=(r^(e|~s))+i[0]-198630844|0,t=(t<<6|t>>>26)+e|0,s+=(e^(t|~r))+i[7]+1126891415|0,s=(s<<10|s>>>22)+t|0,r+=(t^(s|~e))+i[14]-1416354905|0,r=(r<<15|r>>>17)+s|0,e+=(s^(r|~t))+i[5]-57434055|0,e=(e<<21|e>>>11)+r|0,t+=(r^(e|~s))+i[12]+1700485571|0,t=(t<<6|t>>>26)+e|0,s+=(e^(t|~r))+i[3]-1894986606|0,s=(s<<10|s>>>22)+t|0,r+=(t^(s|~e))+i[10]-1051523|0,r=(r<<15|r>>>17)+s|0,e+=(s^(r|~t))+i[1]-2054922799|0,e=(e<<21|e>>>11)+r|0,t+=(r^(e|~s))+i[8]+1873313359|0,t=(t<<6|t>>>26)+e|0,s+=(e^(t|~r))+i[15]-30611744|0,s=(s<<10|s>>>22)+t|0,r+=(t^(s|~e))+i[6]-1560198380|0,r=(r<<15|r>>>17)+s|0,e+=(s^(r|~t))+i[13]+1309151649|0,e=(e<<21|e>>>11)+r|0,t+=(r^(e|~s))+i[4]-145523070|0,t=(t<<6|t>>>26)+e|0,s+=(e^(t|~r))+i[11]-1120210379|0,s=(s<<10|s>>>22)+t|0,r+=(t^(s|~e))+i[2]+718787259|0,r=(r<<15|r>>>17)+s|0,e+=(s^(r|~t))+i[9]-343485551|0,e=(e<<21|e>>>11)+r|0,a[0]=t+a[0]|0,a[1]=e+a[1]|0,a[2]=r+a[2]|0,a[3]=s+a[3]|0}start(){return this._dataLength=0,this._bufferLength=0,this._state.set(u.stateIdentity),this}appendStr(a){const i=this._buffer8,t=this._buffer32;let r,s,e=this._bufferLength;for(s=0;s<a.length;s+=1){if(r=a.charCodeAt(s),r<128)i[e++]=r;else if(r<2048)i[e++]=192+(r>>>6),i[e++]=63&r|128;else if(r<55296||r>56319)i[e++]=224+(r>>>12),i[e++]=r>>>6&63|128,i[e++]=63&r|128;else{if(r=1024*(r-55296)+(a.charCodeAt(++s)-56320)+65536,r>1114111)throw new Error("Unicode standard supports code points up to U+10FFFF");i[e++]=240+(r>>>18),i[e++]=r>>>12&63|128,i[e++]=r>>>6&63|128,i[e++]=63&r|128}e>=64&&(this._dataLength+=64,u._md5cycle(this._state,t),e-=64,t[0]=t[16])}return this._bufferLength=e,this}appendAsciiStr(a){const i=this._buffer8,t=this._buffer32;let r,e=this._bufferLength,s=0;for(;;){for(r=Math.min(a.length-s,64-e);r--;)i[e++]=a.charCodeAt(s++);if(e<64)break;this._dataLength+=64,u._md5cycle(this._state,t),e=0}return this._bufferLength=e,this}appendByteArray(a){const i=this._buffer8,t=this._buffer32;let r,e=this._bufferLength,s=0;for(;;){for(r=Math.min(a.length-s,64-e);r--;)i[e++]=a[s++];if(e<64)break;this._dataLength+=64,u._md5cycle(this._state,t),e=0}return this._bufferLength=e,this}getState(){const a=this._state;return{buffer:String.fromCharCode.apply(null,Array.from(this._buffer8)),buflen:this._bufferLength,length:this._dataLength,state:[a[0],a[1],a[2],a[3]]}}setState(a){const i=a.buffer,t=a.state,e=this._state;let r;for(this._dataLength=a.length,this._bufferLength=a.buflen,e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],r=0;r<i.length;r+=1)this._buffer8[r]=i.charCodeAt(r)}end(a=!1){const i=this._bufferLength,t=this._buffer8,e=this._buffer32,r=1+(i>>2);this._dataLength+=i;const s=8*this._dataLength;if(t[i]=128,t[i+1]=t[i+2]=t[i+3]=0,e.set(u.buffer32Identity.subarray(r),r),i>55&&(u._md5cycle(this._state,e),e.set(u.buffer32Identity)),s<=4294967295)e[14]=s;else{const h=s.toString(16).match(/(.*?)(.{0,8})$/);if(null===h)return;const W=parseInt(h[2],16),X=parseInt(h[1],16)||0;e[14]=W,e[15]=X}return u._md5cycle(this._state,e),a?this._state:u._hex(this._state)}}if(u.stateIdentity=new Int32Array([1732584193,-271733879,-1732584194,271733878]),u.buffer32Identity=new Int32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),u.hexChars="0123456789abcdef",u.hexOut=[],u.onePassHasher=new u,"5d41402abc4b2a76b9719d911017c592"!==u.hashStr("hello"))throw new Error("Md5 self test failed.");var _=l(1626),I=l(9974),m=l(4360),T=l(6354),x=l(345);function w(o,a){if(1&o){const i=n.RV6();n.j41(0,"img",3),n.bIt("error",function(){n.eBV(i);const e=n.XpG();return n.Njj(e.fetchAvatarSource())}),n.k0s()}if(2&o){const i=n.XpG();n.Y8G("src",i.avatarSrc,n.B4B)("alt",i.customAlt?i.customAlt:i.avatarAlt)("width",i.size)("height",i.size)("ngStyle",i.avatarStyle)("referrerPolicy",i.referrerpolicy)}}function F(o,a){if(1&o&&(n.j41(0,"div",5),n.EFF(1),n.k0s()),2&o){const i=n.XpG(2);n.Y8G("ngStyle",i.avatarStyle),n.R7$(),n.SpI(" ",i.avatarText," ")}}function O(o,a){if(1&o&&n.DNE(0,F,2,2,"div",4),2&o){const i=n.XpG();n.Y8G("ngIf",i.avatarText)}}class f{constructor(a){this.sourceId=a}}var c=function(o){return o.FACEBOOK="facebook",o.GOOGLE="google",o.TWITTER="twitter",o.INSTAGRAM="instagram",o.VKONTAKTE="vkontakte",o.SKYPE="skype",o.GRAVATAR="gravatar",o.GITHUB="github",o.CUSTOM="custom",o.INITIALS="initials",o.VALUE="value",o}(c||{});class R{constructor(a){this.sourceId=a,this.sourceType=c.FACEBOOK}getAvatar(a){return`https://graph.facebook.com/${this.sourceId}/picture?width=${a}&height=${a}`}}class z{constructor(a){this.sourceId=a,this.sourceType=c.TWITTER}getAvatar(a){const i=this.getImageSize(a);return`https://twitter.com/${this.sourceId}/profile_image?size=${i}`}getImageSize(a){return a<=24?"mini":a<=48?"normal":a<=73?"bigger":"original"}}class L extends f{constructor(a){super(a),this.sourceType=c.GOOGLE}getAvatar(){return`https://picasaweb.google.com/data/entry/api/user/${this.sourceId}?alt=json`}processResponse(a,i){const t=a.entry.gphoto$thumbnail.$t;return t?t.replace("s64","s"+i):null}}class G extends f{constructor(a){super(a),this.sourceType=c.INSTAGRAM}getAvatar(){return`https://www.instagram.com/${this.sourceId}/?__a=1`}processResponse(a,i){return`${a.graphql.user.profile_pic_url_hd}&s=${i}`}}class E{constructor(a){this.sourceId=a,this.sourceType=c.CUSTOM}getAvatar(){return this.sourceId}}class ${constructor(a){this.sourceId=a,this.sourceType=c.INITIALS}getAvatar(a){return this.getInitials(this.sourceId,a)}getInitials(a,i){if(!(a=a.trim()))return"";const t=a.split(" ");return this.constructInitials(i&&i<t.length?t.slice(0,i):t)}constructInitials(a){return a&&a.length?a.filter(i=>i&&i.length>0).map(i=>i[0].toUpperCase()).join(""):""}}class D{constructor(a){this.value=a,this.sourceType=c.GRAVATAR,this.sourceId=a.match("^[a-f0-9]{32}$")?a:u.hashStr(a).toString()}getAvatar(a){const i=function V(){return!!(typeof window<"u"&&null!==window&&(window.devicePixelRatio>1.25||window.matchMedia&&window.matchMedia("(-webkit-min-device-pixel-ratio: 1.25), (min--moz-device-pixel-ratio: 1.25), (-o-min-device-pixel-ratio: 5/4), (min-resolution: 1.25dppx)").matches))}()?2*a:a;return`https://secure.gravatar.com/avatar/${this.sourceId}?s=${i}&d=404`}}class U{constructor(a){this.sourceId=a,this.sourceType=c.SKYPE}getAvatar(){return`https://api.skype.com/users/${this.sourceId}/profile/avatar`}}class B{constructor(a){this.sourceId=a,this.sourceType=c.VALUE}getAvatar(){return this.sourceId}}class P extends f{constructor(a){super(a),this.sourceType=c.VKONTAKTE}getAvatar(a){const i=this.getImageSize(a);return`https://api.vk.com/method/users.get?user_id=${this.sourceId}&v=5.8&fields=${i}`}processResponse(a){const i=Object.keys(a.response[0]).pop();return i&&a.response[0][i]||null}getImageSize(a){return a<=50?"photo_50":a<=100?"photo_100":a<=200?"photo_200":"photo_max"}}class K extends f{constructor(a){super(a),this.sourceType=c.GITHUB}getAvatar(){return`https://api.github.com/users/${this.sourceId}`}processResponse(a,i){return i?`${a.avatar_url}&s=${i}`:a.avatar_url}}const p=new n.nKC("avatar.config");let g=(()=>{var o;class a{constructor(t){this.userConfig=t}getAvatarSources(t){if(this.userConfig&&this.userConfig.sourcePriorityOrder&&this.userConfig.sourcePriorityOrder.length){const r=[...new Set(this.userConfig.sourcePriorityOrder)].filter(s=>t.includes(s));return[...r,...t.filter(s=>!r.includes(s))]}return t}getAvatarColors(t){return this.userConfig&&this.userConfig.colors&&this.userConfig.colors.length&&this.userConfig.colors||t}getDisableSrcCache(t){return null==this.userConfig||null==this.userConfig.disableSrcCache?t:this.userConfig.disableSrcCache}}return(o=a).\u0275fac=function(t){return new(t||o)(n.KVO(p,8))},o.\u0275prov=n.jDH({token:o,factory:o.\u0275fac,providedIn:"root"}),a})();const v=[c.FACEBOOK,c.GOOGLE,c.TWITTER,c.INSTAGRAM,c.VKONTAKTE,c.SKYPE,c.GRAVATAR,c.GITHUB,c.CUSTOM,c.INITIALS,c.VALUE],A=["#1abc9c","#3498db","#f1c40f","#8e44ad","#e74c3c","#d35400","#2c3e50","#7f8c8d"];let S=(()=>{var o;class a{constructor(t,e){this.http=t,this.avatarConfigService=e,this.avatarSources=v,this.avatarColors=A,this.failedSources=new Map,this.overrideAvatarSources(),this.overrideAvatarColors()}fetchAvatar(t){return this.http.get(t)}getRandomColor(t){if(!t)return"transparent";const e=this.calculateAsciiCode(t);return this.avatarColors[e%this.avatarColors.length]}compareSources(t,e){return this.getSourcePriority(t)-this.getSourcePriority(e)}isSource(t){return this.avatarSources.includes(t)}isTextAvatar(t){return[c.INITIALS,c.VALUE].includes(t)}buildSourceKey(t){return t.sourceType+"-"+t.sourceId}sourceHasFailedBefore(t){return this.failedSources.has(this.buildSourceKey(t))}markSourceAsFailed(t){this.failedSources.set(this.buildSourceKey(t),t)}overrideAvatarSources(){this.avatarSources=this.avatarConfigService.getAvatarSources(v)}overrideAvatarColors(){this.avatarColors=this.avatarConfigService.getAvatarColors(A)}calculateAsciiCode(t){return t.split("").map(e=>e.charCodeAt(0)).reduce((e,r)=>e+r)}getSourcePriority(t){return this.avatarSources.indexOf(t)}}return(o=a).\u0275fac=function(t){return new(t||o)(n.KVO(_.Qq),n.KVO(g))},o.\u0275prov=n.jDH({token:o,factory:o.\u0275fac,providedIn:"root"}),a})();class j{constructor(a){this.sourceId=a,this.sourceType=c.CUSTOM}getAvatar(){const a=Math.random();return`${this.sourceId}${this.sourceId.indexOf("?")>-1?"&":"?"}_=${a}`}}let y=(()=>{var o;class a{constructor(t){this.sources={};const e=t.getDisableSrcCache(!1);this.sources[c.FACEBOOK]=R,this.sources[c.TWITTER]=z,this.sources[c.GOOGLE]=L,this.sources[c.INSTAGRAM]=G,this.sources[c.SKYPE]=U,this.sources[c.GRAVATAR]=D,this.sources[c.CUSTOM]=e?j:E,this.sources[c.INITIALS]=$,this.sources[c.VALUE]=B,this.sources[c.VKONTAKTE]=P,this.sources[c.GITHUB]=K}newInstance(t,e){return new this.sources[t](e)}}return(o=a).\u0275fac=function(t){return new(t||o)(n.KVO(g))},o.\u0275prov=n.jDH({token:o,factory:o.\u0275fac,providedIn:"root"}),a})(),H=(()=>{var o;class a{constructor(t,e,r){this.sourceFactory=t,this.avatarService=e,this.sanitizer=r,this.round=!0,this.size=50,this.textSizeRatio=3,this.fgColor="#FFF",this.style={},this.cornerRadius=0,this.initialsSize=0,this.clickOnAvatar=new n.bkB,this.isAlive=!0,this.avatarSrc=null,this.avatarAlt=null,this.avatarText=null,this.avatarStyle={},this.hostStyle={},this.currentIndex=-1,this.sources=[]}onAvatarClicked(){this.clickOnAvatar.emit(this.sources[this.currentIndex])}ngOnChanges(t){for(const e in t)if(this.avatarService.isSource(e)){const r=c[e.toUpperCase()],s=t[e].currentValue;if(s&&"string"==typeof s)this.addSource(r,s);else{const h=this.sanitizer.sanitize(n.WPN.URL,s);h?this.addSource(r,h):this.removeSource(r)}}this.initializeAvatar()}fetchAvatarSource(){const t=this.sources[this.currentIndex];t&&this.avatarService.markSourceAsFailed(t);const e=this.findNextSource();e&&(this.avatarService.isTextAvatar(e.sourceType)?(this.buildTextAvatar(e),this.avatarSrc=null):this.buildImageAvatar(e))}findNextSource(){for(;++this.currentIndex<this.sources.length;){const t=this.sources[this.currentIndex];if(t&&!this.avatarService.sourceHasFailedBefore(t))return t}return null}ngOnDestroy(){this.isAlive=!1}initializeAvatar(){this.currentIndex=-1,this.sources.length>0&&(this.sortAvatarSources(),this.fetchAvatarSource(),this.hostStyle={width:this.size+"px",height:this.size+"px"})}sortAvatarSources(){this.sources.sort((t,e)=>this.avatarService.compareSources(t.sourceType,e.sourceType))}buildTextAvatar(t){this.avatarText=t.getAvatar(+this.initialsSize),this.avatarStyle=this.getInitialsStyle(t.sourceId)}buildImageAvatar(t){this.avatarStyle=this.getImageStyle(),t instanceof f?this.fetchAndProcessAsyncAvatar(t):(this.avatarSrc=this.sanitizer.bypassSecurityTrustUrl(t.getAvatar(+this.size)),this.avatarAlt=t.getAvatar(+this.size))}getInitialsStyle(t){return{textAlign:"center",borderRadius:this.round?"100%":this.cornerRadius+"px",border:this.borderColor?"1px solid "+this.borderColor:"",textTransform:"uppercase",color:this.fgColor,backgroundColor:this.bgColor?this.bgColor:this.avatarService.getRandomColor(t),font:Math.floor(+this.size/this.textSizeRatio)+"px Helvetica, Arial, sans-serif",lineHeight:this.size+"px",...this.style}}getImageStyle(){return{maxWidth:"100%",borderRadius:this.round?"50%":this.cornerRadius+"px",border:this.borderColor?"1px solid "+this.borderColor:"",width:this.size+"px",height:this.size+"px",...this.style}}fetchAndProcessAsyncAvatar(t){this.avatarService.sourceHasFailedBefore(t)||this.avatarService.fetchAvatar(t.getAvatar(+this.size)).pipe(function C(o,a=!1){return(0,I.N)((i,t)=>{let e=0;i.subscribe((0,m._)(t,r=>{const s=o(r,e++);(s||a)&&t.next(r),!s&&t.complete()}))})}(()=>this.isAlive),(0,T.T)(e=>t.processResponse(e,+this.size))).subscribe({next:e=>this.avatarSrc=e,error:()=>{this.fetchAvatarSource()}})}addSource(t,e){const r=this.sources.find(s=>s.sourceType===t);r?r.sourceId=e:this.sources.push(this.sourceFactory.newInstance(t,e))}removeSource(t){this.sources=this.sources.filter(e=>e.sourceType!==t)}}return(o=a).\u0275fac=function(t){return new(t||o)(n.rXU(y),n.rXU(S),n.rXU(x.up))},o.\u0275cmp=n.VBU({type:o,selectors:[["ngx-avatars"]],inputs:{round:"round",size:"size",textSizeRatio:"textSizeRatio",bgColor:"bgColor",fgColor:"fgColor",borderColor:"borderColor",style:"style",cornerRadius:"cornerRadius",facebook:[0,"facebookId","facebook"],twitter:[0,"twitterId","twitter"],google:[0,"googleId","google"],instagram:[0,"instagramId","instagram"],vkontakte:[0,"vkontakteId","vkontakte"],skype:[0,"skypeId","skype"],gravatar:[0,"gravatarId","gravatar"],github:[0,"githubId","github"],custom:[0,"src","custom"],customAlt:[0,"alt","customAlt"],initials:[0,"name","initials"],value:"value",referrerpolicy:"referrerpolicy",placeholder:"placeholder",initialsSize:"initialsSize"},outputs:{clickOnAvatar:"clickOnAvatar"},features:[n.OA$],decls:4,vars:3,consts:[["textAvatar",""],[1,"avatar-container",3,"click","ngStyle"],["class","avatar-content","loading","lazy",3,"src","alt","width","height","ngStyle","referrerPolicy","error",4,"ngIf","ngIfElse"],["loading","lazy",1,"avatar-content",3,"error","src","alt","width","height","ngStyle","referrerPolicy"],["class","avatar-content",3,"ngStyle",4,"ngIf"],[1,"avatar-content",3,"ngStyle"]],template:function(t,e){if(1&t){const r=n.RV6();n.j41(0,"div",1),n.bIt("click",function(){return n.eBV(r),n.Njj(e.onAvatarClicked())}),n.DNE(1,w,1,6,"img",2)(2,O,1,1,"ng-template",null,0,n.C5r),n.k0s()}if(2&t){const r=n.sdS(3);n.Y8G("ngStyle",e.hostStyle),n.R7$(),n.Y8G("ngIf",e.avatarSrc)("ngIfElse",r)}},dependencies:[d.bT,d.B3],styles:["[_nghost-%COMP%]{border-radius:50%}"]}),a})(),Y=(()=>{var o;class a{static forRoot(t){return{ngModule:a,providers:[{provide:p,useValue:t||{}}]}}}return(o=a).\u0275fac=function(t){return new(t||o)},o.\u0275mod=n.$C({type:o}),o.\u0275inj=n.G2t({providers:[y,S,g],imports:[d.MD]}),a})()}}]);