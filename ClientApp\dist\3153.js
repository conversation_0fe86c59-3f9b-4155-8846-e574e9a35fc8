"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3153],{6586:(D,v,s)=>{s.d(v,{A:()=>n,a:()=>c});var n=function(l){return l.Created_Tab="Created_Tab",l.<PERSON>t_To_Me_Tab="Sent_To_Me_Tab",l}(n||{}),c=function(l){return l[l.all=1]="all",l[l.this_month=2]="this_month",l[l.last_month=3]="last_month",l[l.custom=4]="custom",l}(c||{})},1588:(D,v,s)=>{s.d(v,{j:()=>n});var n=function(c){return c.Day="Day",c.Week="Week",c.Month="Month",c.All="All",c}(n||{})},5936:(D,v,s)=>{s.d(v,{H:()=>g});var n=s(9842),c=s(1626),l=s(4438);const h=s(5312).c.HOST_API+"/api";let g=(()=>{var p;class m{constructor(){(0,n.A)(this,"http",(0,l.WQX)(c.Qq))}GetFile(I){return this.http.get(h+`/Images/GetFile?nameFile=${I}`,{responseType:"blob"})}GetFileURL(I){return this.http.get(h+`/Images/GetFileURL?nameFile=${I}`,{responseType:"blob"})}}return p=m,(0,n.A)(m,"\u0275fac",function(I){return new(I||p)}),(0,n.A)(m,"\u0275prov",l.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})),m})()},5277:(D,v,s)=>{s.d(v,{u:()=>p});var n=s(9842),c=s(4438),l=s(6586),b=s(1588),h=s(4412),g=s(1413);let p=(()=>{var m;class f{constructor(){(0,n.A)(this,"behaviorTimeTrackingTypeView",new h.t(b.j.Day)),(0,n.A)(this,"behaviorTimeTrackingDate",new h.t(void 0)),(0,n.A)(this,"behaviorTimeTrackingFilter",new h.t({typeView:b.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,n.A)(this,"behaviorTimeTrackingCreateTimer",new h.t(void 0)),(0,n.A)(this,"behaviorisInternalClient",new h.t(!1)),(0,n.A)(this,"behaviorTimeTrackingShowingTimer",new h.t(!1)),(0,n.A)(this,"reloadItem",new g.B),(0,n.A)(this,"reloadService",new g.B),(0,n.A)(this,"isEstimate",(0,c.vPA)(!1)),(0,n.A)(this,"behaviorInvoiceFilter",new h.t({typeView:l.A.Created_Tab,textSearch:""}))}SetisInternalClient(e){this.behaviorisInternalClient.next(e)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(e){this.behaviorTimeTrackingTypeView.next(e)}SetNewTimeTrackingDate(e){this.behaviorTimeTrackingDate.next(e)}SetNewTimeTrackingShowingTimer(e){this.behaviorTimeTrackingShowingTimer.next(e)}SetNewTimeTrackingCreateTimerInfo(e){this.behaviorTimeTrackingCreateTimer.next(e)}SetNewTimeTrackingFilter(e){this.behaviorTimeTrackingFilter.next(e)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(e){localStorage.setItem("ResumeData",JSON.stringify(e))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(e){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...e})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return m=f,(0,n.A)(f,"\u0275fac",function(e){return new(e||m)}),(0,n.A)(f,"\u0275prov",c.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})),f})()},3153:(D,v,s)=>{s.r(v),s.d(v,{DetailInvoiceComponent:()=>ie});var n=s(9842),c=s(5936),l=s(5277),b=s(2928),h=s(4433),g=s(1556),p=s(8897),m=s(6146),f=s(5644),I=s(1110),e=s(4438),u=s(9079),A=s(33),F=s(3719),R=s(9115),k=s(3492),C=s(2840),y=s(1448),O=s(5236),N=s(1970),j=s(6196),B=s(5599),P=s(6473),L=s(6617),U=s(402),$=s(335),W=s(3570),G=s(2953),S=s(5909),x=s(177),w=s(1342),K=s(4193),V=s(1537),X=s(584);const Q=["actiontMenuTrigger"];function z(o,_){if(1&o){const r=e.RV6();e.j41(0,"button",39),e.bIt("click",function(){const i=e.eBV(r).$implicit,a=e.XpG(2);return e.Njj(a.handleAction(i.action))}),e.nrm(1,"img",40),e.EFF(2),e.nI1(3,"translate"),e.k0s()}if(2&o){const r=_.$implicit;e.R7$(),e.FS9("src",r.icon,e.B4B),e.R7$(),e.SpI(" ",e.bMT(3,2,r.label)," ")}}function H(o,_){if(1&o&&e.Z7z(0,z,4,4,"button",38,e.fX1),2&o){const r=e.XpG();e.Dyx(r.filteredMenu)}}function J(o,_){if(1&o){const r=e.RV6();e.j41(0,"button",41),e.bIt("click",function(){e.eBV(r);const i=e.XpG();return e.Njj(i.EditInvoice())}),e.nrm(1,"img",42),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"button",41),e.bIt("click",function(){e.eBV(r);const i=e.XpG();return e.Njj(i.ShareLink())}),e.nrm(5,"img",43),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.j41(8,"button",44),e.bIt("click",function(){e.eBV(r);const i=e.XpG();return e.Njj(i.handleFunctionInDevelopment())}),e.nrm(9,"img",45),e.EFF(10),e.nI1(11,"translate"),e.k0s()}2&o&&(e.R7$(2),e.SpI(" ",e.bMT(3,3,"INVOICES.Summary.Edit")," "),e.R7$(4),e.SpI(" ",e.bMT(7,5,"INVOICES.Summary.ByLink")," "),e.R7$(4),e.SpI(" ",e.bMT(11,7,"INVOICES.Summary.SendMail")," "))}function Z(o,_){1&o&&(e.j41(0,"p",19),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&o&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"COMMON.Loading")," "))}function Y(o,_){if(1&o&&(e.j41(0,"div",3)(1,"p",46),e.EFF(2),e.nI1(3,"phoneMask"),e.k0s()()),2&o){const r=e.XpG();e.R7$(2),e.SpI(" ",e.bMT(3,1,r.businessInfo.businessPhoneNumber)," ")}}function q(o,_){if(1&o&&(e.j41(0,"p",21),e.EFF(1),e.k0s()),2&o){const r=e.XpG();e.R7$(),e.SpI(" ",r.businessInfo.businessAddress," ")}}function ee(o,_){if(1&o&&(e.j41(0,"div",28)(1,"div",47)(2,"div",48)(3,"span",49),e.EFF(4," fiber_manual_record "),e.k0s(),e.j41(5,"p",50),e.EFF(6),e.k0s()()(),e.j41(7,"p",51),e.EFF(8),e.nI1(9,"formatNumber"),e.k0s(),e.j41(10,"div",52)(11,"p"),e.EFF(12),e.nI1(13,"decimal"),e.nI1(14,"formatNumber"),e.k0s(),e.j41(15,"p"),e.EFF(16),e.k0s()(),e.j41(17,"p",53),e.EFF(18),e.nI1(19,"decimal"),e.nI1(20,"formatNumber"),e.k0s()()),2&o){let r;const t=_.$implicit,i=e.XpG();e.R7$(6),e.SpI(" ",null!==(r=null==t?null:t.description)&&void 0!==r?r:""," "),e.R7$(2),e.SpI(" $",e.bMT(9,5,t.rate)," "),e.R7$(4),e.SpI(" ",e.bMT(14,10,e.i5U(13,7,t.qty,2))," "),e.R7$(4),e.SpI(" ",i.getNameTaxes(null==t?null:t.taxes,!0),""),e.R7$(2),e.SpI(" $",e.bMT(20,15,e.i5U(19,12,i.calculateTotal(t),2))," ")}}function te(o,_){if(1&o&&(e.j41(0,"div",35)(1,"div",54)(2,"p",55),e.EFF(3),e.k0s(),e.j41(4,"p",55),e.EFF(5),e.k0s()(),e.j41(6,"p",34),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&o){const r=_.$implicit;e.R7$(3),e.Lme(" ",r.name," (",r.amount,"%) "),e.R7$(2),e.SpI(" #",r.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,r.total,2))," ")}}C.is5.Inject(C.Rav);let ie=(()=>{var o;class _{constructor(t,i,a,d,T){(0,n.A)(this,"location",void 0),(0,n.A)(this,"spinnerService",void 0),(0,n.A)(this,"addPaymentDialog",void 0),(0,n.A)(this,"editInvoiceDialog",void 0),(0,n.A)(this,"shareLinkDialog",void 0),(0,n.A)(this,"titleStatus","Draft"),(0,n.A)(this,"dataSource",void 0),(0,n.A)(this,"today",new Date),(0,n.A)(this,"imageUrl",void 0),(0,n.A)(this,"totalPages",1),(0,n.A)(this,"currentPage",1),(0,n.A)(this,"pageSizes",[10,20,50,100]),(0,n.A)(this,"pageSizesDefault",10),(0,n.A)(this,"selectionOptions",{type:"Multiple",checkboxOnly:!0}),(0,n.A)(this,"InforInvoice",void 0),(0,n.A)(this,"listChoosePayment",[]),(0,n.A)(this,"InforBussiness",void 0),(0,n.A)(this,"isErrorTotal",!1),(0,n.A)(this,"isErrorClient",!1),(0,n.A)(this,"clientId",void 0),(0,n.A)(this,"reference",void 0),(0,n.A)(this,"note",void 0),(0,n.A)(this,"nameTax",""),(0,n.A)(this,"sumtax",0),(0,n.A)(this,"subtotal",0),(0,n.A)(this,"qty",void 0),(0,n.A)(this,"invoiceNumber","0000001"),(0,n.A)(this,"total",void 0),(0,n.A)(this,"rate",void 0),(0,n.A)(this,"listTax",[]),(0,n.A)(this,"taxArray",[]),(0,n.A)(this,"selectedDate",void 0),(0,n.A)(this,"selectedDueDate",void 0),(0,n.A)(this,"calculateGroupedTaxes",S.yo),(0,n.A)(this,"getNameTaxes",S.Xj),(0,n.A)(this,"listClient",[]),(0,n.A)(this,"actiontMenuTrigger",void 0),(0,n.A)(this,"clientName",void 0),(0,n.A)(this,"_id",void 0),(0,n.A)(this,"isAccountant",!1),(0,n.A)(this,"layoutUtilsService",(0,e.WQX)(g.Z)),(0,n.A)(this,"activatedRoute",(0,e.WQX)(A.nX)),(0,n.A)(this,"router",(0,e.WQX)(A.Ix)),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"_paymentService",(0,e.WQX)(p.W)),(0,n.A)(this,"translate",(0,e.WQX)(O.c$)),(0,n.A)(this,"_toastService",(0,e.WQX)(k.f)),(0,n.A)(this,"_storeService",(0,e.WQX)(I.n)),(0,n.A)(this,"_invoiceService",(0,e.WQX)(f.p)),(0,n.A)(this,"authenticationService",(0,e.WQX)(b.k)),(0,n.A)(this,"duplicateInvoiceDialog",(0,e.WQX)(U.x)),(0,n.A)(this,"dataService",(0,e.WQX)(l.u)),(0,n.A)(this,"cdnService",(0,e.WQX)(c.H)),this.location=t,this.spinnerService=i,this.addPaymentDialog=a,this.editInvoiceDialog=d,this.shareLinkDialog=T,this.InforBussiness=this._storeService.get_UserBusiness()}formatDate(t){return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}convertToHours(t){const[i,a]=t.split(":").map(Number);return i+a/60}GetImg(t){this.cdnService.GetFile(t).pipe((0,u.pQ)(this.destroyRef)).subscribe(i=>{if(i){const a=new FileReader;a.onload=()=>{this.imageUrl=a.result},a.readAsDataURL(i)}})}get businessInfo(){return{businessName:this.InforInvoice?.company?.businessName??"",businessPhoneNumber:this.InforInvoice?.company?.phone??"",businessAddress:(0,P.Aw)({addressLine1:this.InforInvoice?.company?.adress??"",addressLine2:this.InforInvoice?.company?.adress2??"",city:this.InforInvoice?.company?.city??"",stateProvince:this.InforInvoice?.company?.province??"",postalCode:this.InforInvoice?.company?.postalCode??""})}}get businessInfoClient(){return{businessAddress:(0,P.lq)({addressLine1:this.InforInvoice?.client?.addressLine1??"",addressLine2:this.InforInvoice?.client?.addressLine2??"",townCity:this.InforInvoice?.client?.townCity??"",stateProvince:this.InforInvoice?.client?.stateProvince??"",country:this.InforInvoice?.client?.country??""})}}handleAction(t){switch(t){case"paid":this.handleMarkAsPaid();break;case"sent":this.handleMarkAsSent();break;case"duplicate":this.handleDuplicate();break;case"download":this.handleDownloadPDF();break;case"print":default:this.handleFunctionInDevelopment();break;case"archive":this.handleArchive();break;case"delete":this.handleDelete()}}handleDuplicate(){this.duplicateInvoiceDialog.open({id:this._id,isInvoice:!0}).then(i=>{i.afterClosed().subscribe(a=>{})})}handleMarkAsPaid(){this._invoiceService.MarkAsPaid(this._id).pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._toastService.showSuccess("Mark As Paid","Success"),this.GetInvoiceById(this._id))})}handleMarkAsSent(){this._invoiceService.MarkAsSent(this._id).pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._toastService.showSuccess("Mark As Sent","Success"),this.GetInvoiceById(this._id))})}handleBack(){this.location.back()}get filteredMenu(){return W.l.filter(t=>t.permissions.includes(this.authenticationService.getBusinessRole()))}ngOnInit(){this.activatedRoute.params.pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{t?.id&&(this._id=t?.id,this.GetInvoiceById(t?.id))}),this.activatedRoute.queryParams.pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{if(t?.page)return this.currentPage=t.page,void this.GetAllPaymentAsync(this.currentPage);this.GetAllPaymentAsync(this.currentPage)}),this._storeService.getRoleBusinessAsObservable().subscribe(t=>{this.isAccountant=t===G.X.Accountant})}_handleData(t){let i=this.listClient.findIndex(a=>a.clientId.toString()==this.InforInvoice?.clientId);i>=0&&(this.clientName=this.listClient[i].clientName),this.selectedDueDate=this.formatDate(new Date(this.InforInvoice?.dueDate??0)),this.selectedDate=this.formatDate(new Date(this.InforInvoice?.invoiceDate??0)),this.note=this.InforInvoice?.notes,this.rate=this.InforInvoice?.rate??0,this.qty=this.InforInvoice?.timeAmount??0,this.total=this.InforInvoice?.paidAmount??0,this.reference=this.InforInvoice?.reference}GetInvoiceById(t){this._invoiceService.GetInvoiceById(t).pipe((0,u.pQ)(this.destroyRef)).subscribe(i=>{i&&(this.InforInvoice=i,this.GetImg(this.InforInvoice.img?this.InforInvoice.img:this.InforInvoice?.company?.companyImage),this._handleData(i),this.calculateAllTax())})}handleDownloadPDF(){this.spinnerService.show(),this._invoiceService.PrintInvoiceById(this._id,this.InforInvoice.invoiceNumber)}stopPropagation(t){t.stopPropagation()}AddPayment(){this.addPaymentDialog.open(this.InforInvoice).then(i=>{i.afterClosed().subscribe(a=>{a&&this.GetAllPaymentAsync(this.currentPage)})})}_formatTotal(t){return Math.floor(1e3*t)/1e3}calculateAllTax(){this.taxArray=[],this.sumtax=0;const t=(0,S.yo)(this.InforInvoice?.itemInvoices);this.taxArray=Object.values(t.totalTaxes),this.sumtax=t.grandTotalTax,this.subtotal=this._formatTotal((this.InforInvoice?.totalAmount??0)-this.CheckIsNaN(this.sumtax))}CheckIsNaN(t){return isNaN(t)?0:t}calculateTotal(t){let i=0,d=t.qty;return i=Math.floor(100*t.rate)/100*(Math.floor(100*d)/100),this._formatTotal(this.CheckIsNaN(i))}EditInvoice(){this.dataService.isEstimate.set(!1),this.editInvoiceDialog.open(this.InforInvoice).then(i=>{i.afterClosed().subscribe(a=>{a&&this.GetInvoiceById(this._id)})})}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize),t?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:t.currentPage},queryParamsHandling:"merge"})}GetAllPaymentAsync(t){this._paymentService.GetAllPayment({Page:t,PageSize:this.pageSizesDefault,Search:"",InvoiceId:this._id}).pipe((0,u.pQ)(this.destroyRef)).subscribe(a=>{a&&(this.totalPages=a.totalRecords,this.dataSource=a.data)})}onRowSelecting(t){t?.data?.length>0?t?.data.forEach(i=>{this.listChoosePayment.findIndex(d=>d==i?.id)<0&&this.listChoosePayment.push(i?.id)}):this.listChoosePayment.findIndex(a=>a==t?.data?.id)<0&&this.listChoosePayment.push(t?.data?.id)}onRowDeselecting(t){if(t?.data?.length>0)this.listChoosePayment=[];else{let i=this.listChoosePayment.findIndex(a=>a==t.data?.id);i>=0&&this.listChoosePayment.splice(i,1)}}handleArchive(){this._invoiceService.UpdateArchive(this._id,!0).pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{t?(this.GetAllPaymentAsync(this.currentPage),this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this.listChoosePayment=[]):this._toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})}handleDelete(){const t=this.translate.instant("INVOICES.DeleteInvoice"),i=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:t,description:i}).then(a=>{a&&this._invoiceService.DeleteInvoice([this._id],!1).pipe((0,u.pQ)(this.destroyRef)).subscribe(d=>{d?(this._toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success")),this.router.navigate(["/invoices"])):this._toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}CloseTax(t){this.actiontMenuTrigger.closeMenu()}ShareLink(){this.shareLinkDialog.open(this._id)}getFullName(t){return t?.firstName&&t?.lastName?t?.firstName+" "+t?.lastName:t?.email??""}handleFunctionInDevelopment(){this._toastService.showInfo(this.translate.instant("TOAST.TheFeature"))}}return o=_,(0,n.A)(_,"\u0275fac",function(t){return new(t||o)(e.rXU(x.aZ),e.rXU(w.D),e.rXU(K.y),e.rXU(V.u),e.rXU(X.y))}),(0,n.A)(_,"\u0275cmp",e.VBU({type:o,selectors:[["app-detail-invoice"]],viewQuery:function(t,i){if(1&t&&e.GBs(Q,5),2&t){let a;e.mGM(a=e.lsd())&&(i.actiontMenuTrigger=a.first)}},standalone:!0,features:[e.Jv_([g.Z]),e.aNF],decls:114,vars:80,consts:[["templateSearchProject",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"flex","items-center","gap-[8px]"],[1,"button-icon","button-size-md",3,"click"],["src","../../../../assets/img/icon/ic_arrow_left.svg","alt","Icon"],[1,"text-text-primary","text-headline-lg-bold"],[3,"status"],[1,"flex","items-center","gap-[12px]","flex-wrap"],["position","bottom-end",3,"content"],["target","",1,"button-size-md","button-outline"],["src","../../../../assets/img/icon/ic_three_dot_horizontal.svg","alt","icon"],[1,"container-full","mt-[24px]","pb-[90px]"],[1,"w-full","w-fit","mx-auto","bg-bg-primary","p-[32px]","relative"],[1,"flex","w-full","gap-[18px]","md:flex-row","flex-col"],[1,"w-[160px]","h-[100px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'","alt","image",1,"rounded-md","w-full","h-full","object-cover",3,"src"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]","font-bold"],[1,"w-full","text-text-sm-regular","text-text-secondary"],[1,"w-full","grid","sm:grid-cols-2","lg:grid-cols-4","gap-[16px]"],[1,"text-text-brand-primary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-secondary","text-text-md-regular"],[1,"text-text-secondary","text-text-md-regular","whitespace-pre-line"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],[1,"overflow-auto","w-full"],[1,"invoiceTableLayout"],[1,"text-text-brand-primary","text-text-sm-semibold","font-bold"],[1,"text-text-brand-primary","text-text-sm-semibold","text-right","font-bold"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["src","../../../../assets/img/bg_footer_invoice_details.png","alt","Footer",1,"invoiceDetailsFooter"],[1,"button-size-md","button-transparent"],[1,"button-size-md","button-transparent",3,"click"],["alt","Icon",3,"src"],[1,"button-size-md","button-outline",3,"click"],["src","../../../../assets/img/icon/ic_edit.svg","alt","icon"],["src","../../../../assets/img/icon/ic_link_black.svg","alt","icon"],[1,"button-size-md","button-primary",3,"click"],["src","../../../../assets/img/icon/ic_email_sending.svg","alt","icon"],[1,"text-text-sm-regular","text-text-secondary"],[1,"flex","flex-col"],[1,"flex","items-center"],[1,"material-icons","cursor-pointer","mr-3","!text-[8px]"],[1,"text-text-primary","text-text-md-regular","break-words","w-full","max-w-md","pr-5","whitespace-pre-line"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","flex","flex-col"],[1,"text-text-primary","text-text-md-bold","text-right"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"]],template:function(t,i){if(1&t){const a=e.RV6();e.j41(0,"div",1)(1,"div",2)(2,"div",3)(3,"button",4),e.bIt("click",function(){return e.eBV(a),e.Njj(i.handleBack())}),e.nrm(4,"img",5),e.k0s(),e.j41(5,"p",6),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"app-inno-status",7),e.k0s(),e.j41(9,"div",8)(10,"app-inno-popover",9)(11,"button",10),e.nrm(12,"img",11),e.EFF(13),e.nI1(14,"translate"),e.k0s()(),e.DNE(15,H,2,0,"ng-template",null,0,e.C5r)(17,J,12,9),e.k0s()()(),e.j41(18,"div",12)(19,"div",13)(20,"div",14)(21,"div",15),e.nrm(22,"img",16),e.k0s(),e.j41(23,"div",17)(24,"div",18),e.DNE(25,Z,3,3,"p",19),e.j41(26,"p",20),e.EFF(27),e.k0s(),e.DNE(28,Y,4,3,"div",3),e.j41(29,"p",21),e.EFF(30),e.k0s(),e.DNE(31,q,2,1,"p",21),e.j41(32,"p",21),e.EFF(33),e.k0s()(),e.j41(34,"div",22)(35,"div",18)(36,"p",23),e.EFF(37),e.nI1(38,"translate"),e.k0s(),e.j41(39,"p",24),e.EFF(40),e.k0s(),e.j41(41,"p",24),e.EFF(42),e.k0s()(),e.j41(43,"div",18)(44,"p",23),e.EFF(45),e.nI1(46,"translate"),e.k0s(),e.j41(47,"p",24),e.EFF(48),e.k0s()(),e.j41(49,"div",18)(50,"p",23),e.EFF(51),e.nI1(52,"translate"),e.k0s(),e.j41(53,"p",24),e.EFF(54),e.nI1(55,"date"),e.k0s()(),e.j41(56,"div",18)(57,"p",23),e.EFF(58),e.nI1(59,"translate"),e.k0s(),e.j41(60,"p",24),e.EFF(61),e.nI1(62,"date"),e.k0s()()(),e.j41(63,"div",18)(64,"p",23),e.EFF(65),e.nI1(66,"translate"),e.k0s(),e.j41(67,"p",25),e.EFF(68),e.k0s()()()(),e.j41(69,"div",26)(70,"div",27)(71,"div",28)(72,"p",29),e.EFF(73),e.nI1(74,"translate"),e.k0s(),e.j41(75,"p",29),e.EFF(76),e.nI1(77,"translate"),e.k0s(),e.j41(78,"p",29),e.EFF(79),e.nI1(80,"translate"),e.k0s(),e.j41(81,"p",30),e.EFF(82),e.nI1(83,"translate"),e.k0s()(),e.j41(84,"div",18),e.Z7z(85,ee,21,17,"div",28,e.fX1),e.k0s()()(),e.j41(87,"div",31)(88,"div",32)(89,"p",33),e.EFF(90),e.nI1(91,"translate"),e.k0s(),e.j41(92,"p",34),e.EFF(93),e.nI1(94,"decimal"),e.nI1(95,"formatNumber"),e.k0s()(),e.Z7z(96,te,10,9,"div",35,e.fX1),e.j41(98,"div",32)(99,"p",33),e.EFF(100),e.nI1(101,"translate"),e.k0s(),e.j41(102,"p",34),e.EFF(103," $0 "),e.k0s()(),e.j41(104,"div",32)(105,"p",33),e.EFF(106),e.nI1(107,"translate"),e.nI1(108,"async"),e.k0s(),e.j41(109,"p",36),e.EFF(110),e.nI1(111,"decimal"),e.nI1(112,"formatNumber"),e.k0s()()(),e.nrm(113,"img",37),e.k0s()()}if(2&t){let a,d,T,E,M;const ne=e.sdS(16);e.R7$(6),e.Lme(" ",e.bMT(7,34,"INVOICES.Title")," ",null!==(a=null==i.InforInvoice?null:i.InforInvoice.invoiceNumber)&&void 0!==a?a:""," "),e.R7$(2),e.Y8G("status",null==i.InforInvoice?null:i.InforInvoice.status),e.R7$(2),e.Y8G("content",ne),e.R7$(3),e.SpI(" ",e.bMT(14,36,"INVOICES.Summary.More")," "),e.R7$(4),e.vxM(i.isAccountant?-1:17),e.R7$(5),e.Y8G("src",i.imageUrl,e.B4B),e.R7$(3),e.vxM(i.businessInfo.businessName?-1:25),e.R7$(2),e.SpI("",null==i.InforInvoice||null==i.InforInvoice.company?null:i.InforInvoice.company.businessName," "),e.R7$(),e.vxM(i.businessInfo.businessPhoneNumber?28:-1),e.R7$(2),e.SpI(" ",null==i.InforInvoice||null==i.InforInvoice.company?null:i.InforInvoice.company.email," "),e.R7$(),e.vxM(i.businessInfo.businessAddress?31:-1),e.R7$(2),e.SpI(" ",null==i.InforInvoice||null==i.InforInvoice.company?null:i.InforInvoice.company.country," "),e.R7$(4),e.SpI(" ",e.bMT(38,38,"INVOICES.Summary.BilledTo"),""),e.R7$(3),e.SpI(" ",null!==(d=null==i.InforInvoice||null==i.InforInvoice.client?null:i.InforInvoice.client.clientName)&&void 0!==d?d:""," "),e.R7$(2),e.SpI(" ",null!==(T=null==i.businessInfoClient?null:i.businessInfoClient.businessAddress)&&void 0!==T?T:""," "),e.R7$(3),e.SpI("",e.bMT(46,40,"INVOICES.GIRD.InvoiceNumber")," "),e.R7$(3),e.SpI(" ",null!==(E=null==i.InforInvoice?null:i.InforInvoice.invoiceNumber)&&void 0!==E?E:""," "),e.R7$(3),e.JRh(e.bMT(52,42,"INVOICES.Summary.DatefIssue")),e.R7$(3),e.SpI(" ",e.i5U(55,44,i.selectedDate,i._storeService.getdateFormat())," "),e.R7$(4),e.JRh(e.bMT(59,47,"INVOICES.Summary.DueDate")),e.R7$(3),e.SpI(" ",e.i5U(62,49,i.selectedDueDate,i._storeService.getdateFormat())," "),e.R7$(4),e.JRh(e.bMT(66,52,"INVOICES.Summary.Description")),e.R7$(3),e.SpI(" ",null!==(M=null==i.InforInvoice?null:i.InforInvoice.notes)&&void 0!==M?M:""," "),e.R7$(5),e.SpI(" ",e.bMT(74,54,"INVOICES.INVOICE_FORM.TableHeaders.InvoiceItem")," "),e.R7$(3),e.SpI(" ",e.bMT(77,56,"INVOICES.INVOICE_FORM.TableHeaders.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(80,58,"INVOICES.INVOICE_FORM.TableHeaders.Quantity")," "),e.R7$(3),e.SpI(" ",e.bMT(83,60,"INVOICES.INVOICE_FORM.TableHeaders.LineTotal")," "),e.R7$(3),e.Dyx(null==i.InforInvoice?null:i.InforInvoice.itemInvoices),e.R7$(5),e.SpI(" ",e.bMT(91,62,"INVOICES.INVOICE_FORM.Subtotal")," "),e.R7$(3),e.SpI(" $",e.bMT(95,67,e.i5U(94,64,i.subtotal,2))," "),e.R7$(3),e.Dyx(i.taxArray),e.R7$(4),e.SpI(" ",e.bMT(101,69,"INVOICES.INVOICE_FORM.Discount")," "),e.R7$(6),e.Lme(" ",e.bMT(107,71,"INVOICES.INVOICE_FORM.AmountDue")," (",e.bMT(108,73,i._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(112,78,e.i5U(111,75,null==i.InforInvoice?null:i.InforInvoice.totalAmount,2))," ")}},dependencies:[m.G,x.Jj,x.vh,O.D9,F.RG,N.mC,A.iI,R.Cn,y.gFV,y.iov,j.A,B.x,L.p,$.L,h.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceDetailsFooter[_ngcontent-%COMP%]{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;bottom:5px;left:0;transform:translateY(100%);width:100%;height:auto;object-fit:contain}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(200px,1fr) 200px 200px 200px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),_})()}}]);