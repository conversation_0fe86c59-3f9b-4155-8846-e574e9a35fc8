"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5305],{5305:(P,d,a)=>{a.r(d),a.d(d,{BillingComponent:()=>F});var n=a(9842),e=a(4438),m=a(6146),p=a(8600),u=a(7152),_=a(3114),c=a(33),l=a(9079),h=a(1342),b=a(3492),v=a(5236);function g(s,o){if(1&s&&(e.j41(0,"div",11)(1,"div",55),e.nrm(2,"img",56),e.k0s(),e.j41(3,"p",17),e.EFF(4),e.k0s()()),2&s){const i=e.XpG(2);e.R7$(4),e.Lme("Your next ",1===i.planType?"monthly":"yearly"," invoice will be issued ",i.nextStatementDate,"")}}function y(s,o){if(1&s&&(e.j41(0,"p",21),e.EFF(1),e.k0s()),2&s){const i=e.XpG(2);e.R7$(),e.SpI("$",i.yearlyAmount," /year")}}function x(s,o){if(1&s){const i=e.RV6();e.j41(0,"div",22)(1,"button",57),e.bIt("click",function(){e.eBV(i);const r=e.XpG(2);return e.Njj(r.cancelAccount())}),e.EFF(2," Cancel Plan "),e.k0s()()}}function f(s,o){if(1&s){const i=e.RV6();e.j41(0,"div",7)(1,"div",8)(2,"div",9)(3,"div",10),e.DNE(4,g,5,2,"div",11),e.j41(5,"div",12)(6,"p",13),e.EFF(7,"Current plan"),e.k0s(),e.j41(8,"p",14),e.EFF(9),e.k0s()(),e.j41(10,"div",15)(11,"div",16)(12,"p",17),e.EFF(13,"Total"),e.k0s(),e.j41(14,"p",18),e.EFF(15),e.k0s()(),e.j41(16,"div",16)(17,"p",17),e.EFF(18),e.k0s(),e.j41(19,"p",17),e.EFF(20),e.k0s()(),e.j41(21,"div",16)(22,"p",17),e.EFF(23,"Customers available"),e.k0s(),e.j41(24,"p",17),e.EFF(25),e.k0s()()(),e.j41(26,"div",19)(27,"div",20)(28,"p",14),e.EFF(29,"Total"),e.k0s(),e.j41(30,"div")(31,"p",21),e.EFF(32),e.k0s(),e.DNE(33,y,2,1,"p",21),e.k0s()()(),e.DNE(34,x,3,0,"div",22),e.k0s()(),e.j41(35,"div",9)(36,"div",10)(37,"div",12)(38,"h2",23),e.EFF(39),e.k0s(),e.j41(40,"p",13),e.EFF(41,"View and manage team members included in your paid plan."),e.k0s()(),e.j41(42,"div",12)(43,"div",24)(44,"div",25)(45,"div",26)(46,"span",27),e.EFF(47,"Ad"),e.k0s()(),e.j41(48,"div")(49,"p",17),e.EFF(50,"Andou Designer"),e.k0s(),e.j41(51,"p",13),e.EFF(52),e.k0s()()(),e.j41(53,"button",28),e.nrm(54,"img",29),e.k0s()(),e.j41(55,"div",24)(56,"div",25)(57,"div",30)(58,"span",31),e.EFF(59,"Fc"),e.k0s()(),e.j41(60,"div")(61,"p",17),e.EFF(62,"FredCanadaviet"),e.k0s(),e.j41(63,"p",13),e.EFF(64),e.k0s()()(),e.j41(65,"button",28),e.nrm(66,"img",29),e.k0s()()(),e.j41(67,"button",32),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.addTeamMember())}),e.j41(68,"span",33),e.EFF(69,"+"),e.k0s(),e.EFF(70," Add Member "),e.k0s()(),e.j41(71,"div",34)(72,"div",12)(73,"h2",23),e.EFF(74,"Payment Method"),e.k0s(),e.j41(75,"p",13),e.EFF(76,"Manage and update your stored payment options."),e.k0s()(),e.j41(77,"div",24)(78,"div",25)(79,"div",35),e.nrm(80,"img",36),e.k0s(),e.j41(81,"div")(82,"p",17),e.EFF(83),e.k0s(),e.j41(84,"p",13),e.EFF(85),e.k0s()()(),e.j41(86,"p",18),e.EFF(87),e.k0s()(),e.j41(88,"div",22)(89,"button",37),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.updatePaymentMethod())}),e.nrm(90,"img",38),e.EFF(91," Update Payment Method "),e.k0s()()()()(),e.nrm(92,"div",39),e.j41(93,"div",9)(94,"h2",40),e.EFF(95,"Other Offers"),e.k0s(),e.j41(96,"div",41)(97,"div",10)(98,"div",42)(99,"div",43),e.nrm(100,"img",44),e.k0s(),e.j41(101,"div")(102,"h3",45),e.EFF(103,"Refer and Earn"),e.k0s(),e.j41(104,"p",46),e.EFF(105,"Earn a $100 service credit for each qualified referral you complete!"),e.k0s(),e.j41(106,"p",47)(107,"a",48),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.managePlan())}),e.EFF(108,"Refer a Friend"),e.k0s(),e.EFF(109," today."),e.k0s()()()(),e.j41(110,"div",10)(111,"div",42)(112,"div",49),e.nrm(113,"img",50),e.k0s(),e.j41(114,"div")(115,"h3",45),e.EFF(116,"Pay Yearly "),e.j41(117,"span",51),e.EFF(118,"Save 10%"),e.k0s()(),e.j41(119,"p",46),e.EFF(120,"Switch to one easy payment per year."),e.k0s(),e.j41(121,"button",52),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.switchToYearly())}),e.EFF(122," Switch to Yearly "),e.k0s()()()()()(),e.j41(123,"div",53)(124,"h3",45),e.EFF(125,"Need to bill more than 50 clients? "),e.j41(126,"a",48),e.bIt("click",function(){e.eBV(i);const r=e.XpG();return e.Njj(r.upgradeToPremium())}),e.EFF(127," Upgrade to Premium "),e.k0s()(),e.j41(128,"p",54),e.EFF(129," You'll still have access to all the features you love on the Plus package, with a little more room for your business to grow. "),e.k0s()()()}if(2&s){const i=e.XpG();e.R7$(4),e.vxM(0!==i.planType?4:-1),e.R7$(5),e.JRh(i.planName),e.R7$(6),e.SpI("$",i.planAmount," /mo"),e.R7$(3),e.SpI("Team members x",i.teamMembersCount,""),e.R7$(2),e.SpI("$",i.addOnsAmount," /mo"),e.R7$(5),e.JRh(0===i.clientProfilesTotal?"Unlimited":i.clientProfilesUsed+" of "+i.clientProfilesTotal),e.R7$(7),e.SpI("$",i.totalAmount," /mo"),e.R7$(),e.vxM(2===i.renewType?33:-1),e.R7$(),e.vxM(0!==i.planType?34:-1),e.R7$(5),e.SpI("Team Members: $",i.addOnsAmount," /mo"),e.R7$(13),e.JRh(i.planName),e.R7$(12),e.JRh(i.planName),e.R7$(19),e.SpI("Visa **** **** ",i.cardLastFour,""),e.R7$(2),e.SpI("Expires ",i.cardExpiryDate,""),e.R7$(2),e.SpI("$",i.totalAmount," /mo")}}function E(s,o){1&s&&(e.j41(0,"div",7)(1,"div",10)(2,"h2",40),e.EFF(3,"Receipts"),e.k0s(),e.j41(4,"p",58),e.EFF(5," You don't have any receipts yet. They will appear here once you've been billed. "),e.k0s()()())}let F=(()=>{var s;class o{constructor(t){(0,n.A)(this,"translate",void 0),(0,n.A)(this,"TYPE_TAB",{YOUR_PLAN:1,RECEIPTS:2}),(0,n.A)(this,"tabs",[{label:"Your Plan",value:this.TYPE_TAB.YOUR_PLAN},{label:"Receipts",value:this.TYPE_TAB.RECEIPTS}]),(0,n.A)(this,"currentTab",this.TYPE_TAB.YOUR_PLAN),(0,n.A)(this,"isLoading",!1),(0,n.A)(this,"planName","Plus Plan"),(0,n.A)(this,"planAmount",0),(0,n.A)(this,"yearlyAmount",0),(0,n.A)(this,"addOnsAmount",0),(0,n.A)(this,"totalAmount",0),(0,n.A)(this,"planType",0),(0,n.A)(this,"renewType",1),(0,n.A)(this,"teamMembersCount",2),(0,n.A)(this,"clientProfilesUsed",0),(0,n.A)(this,"clientProfilesTotal",0),(0,n.A)(this,"teamMembersUsed",0),(0,n.A)(this,"teamMembersTotal",1),(0,n.A)(this,"nextStatementDate","28/02/2025"),(0,n.A)(this,"cardLastFour","7013"),(0,n.A)(this,"cardHolderName","frederic coutu"),(0,n.A)(this,"cardExpiryDate","7/2026"),(0,n.A)(this,"stripeService",(0,e.WQX)(u.d)),(0,n.A)(this,"planService",(0,e.WQX)(_.J)),(0,n.A)(this,"route",(0,e.WQX)(c.nX)),(0,n.A)(this,"router",(0,e.WQX)(c.Ix)),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"spinnerService",(0,e.WQX)(h.D)),(0,n.A)(this,"toastService",(0,e.WQX)(b.f)),this.translate=t}ngOnInit(){this.route.queryParams.pipe((0,l.pQ)(this.destroyRef)).subscribe(t=>{t.session_id&&"success"===t.status?this.verifyStripePayment(t.session_id):"cancelled"===t.status&&this.toastService.showError("Payment Cancelled","Your payment was cancelled.")}),this.planService.getCurrentPlan().pipe((0,l.pQ)(this.destroyRef)).subscribe({next:t=>{this.planName=t.plan.name,this.clientProfilesTotal=t.plan.maxClients,this.planType=t.plan.planType,t.subscriptionId?(this.renewType=t.renewType,this.planAmount=1==this.renewType?t.plan.monthlyPrice:t.plan.yearlyPrice/12,this.clientProfilesUsed=0,this.addOnsAmount=t.plan.additionalUserPrice*t.additionalUsers,this.totalAmount=this.planAmount+this.addOnsAmount,this.yearlyAmount=t.totalPrice):(this.planAmount=0,this.addOnsAmount=0,this.clientProfilesUsed=0,this.totalAmount=0),this.nextStatementDate=this.calculateNextStatementDate()},error:t=>{console.error("Error fetching current plan:",t)}})}handleChangeTab(t){this.currentTab=t}managePlan(){this.router.navigate(["billing/plan-selection"])}updatePaymentMethod(){}referFriend(){console.log("Refer a friend clicked")}switchToYearly(){this.router.navigate(["billing/plan-selection"])}upgradeToPremium(){this.router.navigate(["billing/plan-selection"])}cancelAccount(){console.log("Cancel account clicked")}addTeamMember(){console.log("Add team member clicked")}verifyStripePayment(t){this.spinnerService.show(),this.stripeService.verifyPayment(t).pipe((0,l.pQ)(this.destroyRef)).subscribe({next:r=>{this.spinnerService.hide(),r&&"complete"===r.status?(this.toastService.showSuccess("Payment Successful","Your payment was processed successfully."),this.router.navigate([],{relativeTo:this.route,queryParams:{session_id:null,status:null},queryParamsHandling:"merge"})):this.toastService.showError("Payment Failed","Your payment could not be processed.")},error:r=>{this.spinnerService.hide(),this.toastService.showError("Error","Failed to verify payment: "+r.message)}})}calculateNextStatementDate(){const t=new Date;return 1==this.renewType?t.setMonth(t.getMonth()+1):t.setFullYear(t.getFullYear()+1),`${t.getDate().toString().padStart(2,"0")}/${(t.getMonth()+1).toString().padStart(2,"0")}/${t.getFullYear()}`}}return s=o,(0,n.A)(o,"\u0275fac",function(t){return new(t||s)(e.rXU(v.c$))}),(0,n.A)(o,"\u0275cmp",e.VBU({type:s,selectors:[["app-billing"]],standalone:!0,features:[e.aNF],decls:11,vars:4,consts:[[1,"w-full","pb-3"],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"button-size-md","button-primary",3,"click"],[1,"container-full","mt-[24px]","flex","items-center","justify-between","flex-wrap","gap-2"],[3,"onChange","tabs","value"],[1,"container-full","mt-[24px]"],[1,"grid","grid-cols-1","md:grid-cols-2"],[1,"p-6"],[1,"border","border-border-primary","rounded-md","p-6"],[1,"flex","items-center","mb-4","warning-text-bg","p-2","rounded-md"],[1,"mb-4"],[1,"text-text-secondary","text-text-sm-regular"],[1,"text-text-primary","text-headline-sm-bold"],[1,"border-t","border-border-primary","pt-4","mb-4"],[1,"flex","justify-between","items-center","mb-2"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold"],[1,"border-t","border-border-primary","pt-4"],[1,"flex","justify-between","items-center"],[1,"text-text-primary","text-headline-sm-bold","text-right"],[1,"mt-4"],[1,"text-text-primary","text-headline-sm-bold","mb-2"],[1,"flex","justify-between","items-center","py-3","border-b","border-border-primary"],[1,"flex","items-center"],[1,"w-8","h-8","rounded-full","bg-purple-100","flex","items-center","justify-center","mr-3"],[1,"text-purple-700"],[1,"text-text-secondary"],["src","assets/img/icon/ic_three_dots_verticel.svg","alt","More",1,"w-5","h-5"],[1,"w-8","h-8","rounded-full","bg-blue-100","flex","items-center","justify-center","mr-3"],[1,"text-blue-700"],[1,"button-size-md","button-outline-primary","flex","items-center",3,"click"],[1,"mr-1"],[1,"border","border-border-primary","rounded-md","p-6","mt-6"],[1,"flex-shrink-0","mr-3"],["src","assets/img/icon/visa.svg","alt","Visa",1,"w-8","h-8"],[1,"button-link-primary","flex","items-center",3,"click"],["src","assets/img/icon/ic_edit.svg","alt","Edit",1,"w-4","h-4","mr-1"],[1,"border-t","border-border-primary","my-8"],[1,"text-text-primary","text-headline-sm-bold","mb-4"],[1,"grid","grid-cols-1","md:grid-cols-2","gap-6","mb-8"],[1,"flex","items-start","gap-4"],[1,"flex-shrink-0","bg-amber-100","rounded-full","p-3"],["src","assets/img/icon/gift.svg","alt","Gift",1,"w-6","h-6"],[1,"text-text-primary","text-text-md-bold","mb-2"],[1,"text-text-primary","text-text-md-regular","mb-1"],[1,"text-text-primary","text-text-sm-regular"],["href","javascript:void(0)",1,"button-link-primary","d-inline-block",3,"click"],[1,"flex-shrink-0","bg-blue-100","rounded-full","p-3"],["src","assets/img/icon/calculator.svg","alt","Calculator",1,"w-6","h-6"],[1,"text-green-500","text-text-sm-regular"],[1,"button-link-primary",3,"click"],[1,"border-t","border-border-primary","pt-6","mt-6","text-center"],[1,"text-text-primary","text-text-sm-regular","mb-4"],[1,"flex-shrink-0","mr-2"],["src","assets/img/icon/ic_warning.svg","alt","Clock",1,"w-5","h-5"],[1,"button-link-primary","text-red-500",3,"click"],[1,"text-text-primary","text-text-md-regular","text-center","py-8"]],template:function(t,r){1&t&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"p",3),e.EFF(4," Billing & Plans "),e.k0s(),e.j41(5,"button",4),e.bIt("click",function(){return r.managePlan()}),e.EFF(6," Manager Your Plan "),e.k0s()()(),e.j41(7,"div",5)(8,"app-inno-tabs",6),e.bIt("onChange",function(A){return r.handleChangeTab(A)}),e.k0s()(),e.DNE(9,f,130,15,"div",7)(10,E,6,0,"div",7),e.k0s()),2&t&&(e.R7$(8),e.Y8G("tabs",r.tabs)("value",r.currentTab),e.R7$(),e.vxM(r.currentTab===r.TYPE_TAB.YOUR_PLAN?9:-1),e.R7$(),e.vxM(r.currentTab===r.TYPE_TAB.RECEIPTS?10:-1))},dependencies:[m.G,p.k],styles:[".warning-text-bg[_ngcontent-%COMP%]{background-color:#fdf5ec}"]})),o})()}}]);