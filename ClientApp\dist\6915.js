"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6915],{6408:(x,v,i)=>{i.d(v,{G:()=>c});var o=i(9842),_=i(4438),h=i(3924);let c=(()=>{var f;class C{constructor(){(0,o.A)(this,"mode","day"),(0,o.A)(this,"id",void 0),(0,o.A)(this,"enableMask",void 0),(0,o.A)(this,"name",void 0),(0,o.A)(this,"format","dd-MM-yyyy"),(0,o.A)(this,"placeholder",void 0),(0,o.A)(this,"value",void 0),(0,o.A)(this,"onChange",new _.bkB),(0,o.A)(this,"datePickerValue",void 0),(0,o.A)(this,"fields",{text:"label",value:"value"}),(0,o.A)(this,"start","Month"),(0,o.A)(this,"depth","Month"),(0,o.A)(this,"showTodayButton",!0),(0,o.A)(this,"weekNumber",!1)}ngOnChanges(p){const r=p.mode?.currentValue??p.value?.currentValue;r&&this.updateModeSettings(r)}updateModeSettings(p){switch(p){case"day":this.start="Month",this.depth="Month",this.showTodayButton=!0,this.weekNumber=!1;break;case"week":this.start="Month",this.depth="Month",this.showTodayButton=!1,this.weekNumber=!0;break;case"month":this.start="Year",this.depth="Year",this.showTodayButton=!1,this.weekNumber=!1}}handleChangeValue(p){this.onChange.emit(p?.value??void 0)}}return f=C,(0,o.A)(C,"\u0275fac",function(p){return new(p||f)}),(0,o.A)(C,"\u0275cmp",_.VBU({type:f,selectors:[["app-inno-datepicker"]],inputs:{mode:"mode",id:"id",enableMask:"enableMask",name:"name",format:"format",placeholder:"placeholder",value:"value"},outputs:{onChange:"onChange"},standalone:!0,features:[_.OA$,_.aNF],decls:1,vars:10,consts:[[1,"customDatePickerV2",3,"change","name","id","format","enableMask","placeholder","value","start","depth","showTodayButton","weekNumber"]],template:function(p,r){1&p&&(_.j41(0,"ejs-datepicker",0),_.bIt("change",function(g){return r.handleChangeValue(g)}),_.k0s()),2&p&&(_.FS9("name",r.name||""),_.FS9("id",r.id||""),_.Y8G("format",r.format)("enableMask",r.enableMask)("placeholder",r.placeholder)("value",r.value)("start",r.start)("depth",r.depth)("showTodayButton",r.showTodayButton)("weekNumber",r.weekNumber))},dependencies:[h.tZ,h.I],styles:[".customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{margin:0!important;padding:8px 12px;display:flex;min-width:125px!important;height:40px!important;border-radius:8px}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{background-color:var(--bg-primary)}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%] > input[_ngcontent-%COMP%]{padding:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:after{display:none}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group-icon.e-date-icon[_ngcontent-%COMP%]{margin:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons[_ngcontent-%COMP%]{min-height:unset!important}.customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%]::selection, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:after, .e-datepicker[_ngcontent-%COMP%]   .e-focused-date.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-cell.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%]{background-color:#0f182e!important}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons.e-active[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:not(.e-focused-date)   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]{color:#0f182e!important}.e-datepicker[_ngcontent-%COMP%]   .e-model-header[_ngcontent-%COMP%]{background-color:#fff!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{border:2px solid #E9EAEB!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:hover{background-color:#24242433!important}"]})),C})()},3200:(x,v,i)=>{i.d(v,{J:()=>C});var o=i(9842),_=i(177),h=i(5236),c=i(4438);function f(u,p){if(1&u&&(c.j41(0,"p",4),c.EFF(1),c.nI1(2,"translate"),c.k0s()),2&u){const r=c.XpG();c.R7$(),c.SpI(" ",c.bMT(2,1,r.description)," ")}}let C=(()=>{var u;class p{constructor(){(0,o.A)(this,"title",""),(0,o.A)(this,"description",""),(0,o.A)(this,"icon",""),(0,o.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return u=p,(0,o.A)(p,"\u0275fac",function(m){return new(m||u)}),(0,o.A)(p,"\u0275cmp",c.VBU({type:u,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[c.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(m,g){1&m&&(c.j41(0,"div",0),c.nrm(1,"img",1),c.j41(2,"div",2)(3,"p",3),c.EFF(4),c.nI1(5,"translate"),c.nI1(6,"translate"),c.k0s(),c.DNE(7,f,3,3,"p",4),c.k0s()()),2&m&&(c.R7$(),c.Y8G("src",g.icon||g.defaultIcon,c.B4B),c.R7$(3),c.SpI(" ",g.title?c.bMT(5,3,g.title):c.bMT(6,5,"COMMON.EmptyData")," "),c.R7$(3),c.vxM(g.description?7:-1))},dependencies:[_.MD,h.h,h.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),p})()},9424:(x,v,i)=>{i.d(v,{f:()=>f});var o=i(9842),_=i(177),h=i(4438);const c=(C,u,p)=>({"w-4 h-4":C,"w-6 h-6":u,"w-10 h-10":p});let f=(()=>{var C;class u{constructor(){(0,o.A)(this,"size","md")}}return C=u,(0,o.A)(u,"\u0275fac",function(r){return new(r||C)}),(0,o.A)(u,"\u0275cmp",h.VBU({type:C,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[h.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(r,m){1&r&&(h.j41(0,"div",0),h.qSk(),h.j41(1,"svg",1),h.nrm(2,"path",2)(3,"path",3),h.k0s(),h.joV(),h.j41(4,"span",4),h.EFF(5,"Loading..."),h.k0s()()),2&r&&(h.R7$(),h.Y8G("ngClass",h.sMw(1,c,"sm"===m.size,"md"===m.size,"lg"===m.size)))},dependencies:[_.MD,_.YU]})),u})()},1556:(x,v,i)=>{i.d(v,{Z:()=>p});var o=i(9842),_=i(4438),h=i(467),c=i(2716),f=i(7987);let C=(()=>{var r;class m extends c.H{open(P){var D=this;return(0,h.A)(function*(){const M=yield i.e(3190).then(i.bind(i,3190));return D.matDialog.open(M.AlertConfirmComponent.getComponent(),{data:P,width:"440px",panelClass:"custom_dialog",scrollStrategy:new f.t0,disableClose:!0})})()}}return r=m,(0,o.A)(m,"\u0275fac",(()=>{let g;return function(D){return(g||(g=_.xGo(r)))(D||r)}})()),(0,o.A)(m,"\u0275prov",_.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})),m})(),p=(()=>{var r;class m{constructor(P){(0,o.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=P}alertDelete(P){const{title:D,description:M,textSubmit:R="COMMON.Delete",textCancel:F}=P;return new Promise(k=>{this.alertConfirmDialog.open({title:D,description:M,textSubmit:R,textCancel:F,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(E=>{E.afterClosed().subscribe(S=>{k(S??!1)})})})}alertConfirm(P){const{title:D,description:M,textSubmit:R,textCancel:F}=P;return new Promise(k=>{this.alertConfirmDialog.open({title:D,description:M,textSubmit:R,textCancel:F}).then(E=>{E.afterClosed().subscribe(S=>{k(S??!1)})})})}}return r=m,(0,o.A)(m,"\u0275fac",function(P){return new(P||r)(_.KVO(C))}),(0,o.A)(m,"\u0275prov",_.jDH({token:r,factory:r.\u0275fac})),m})()},4433:(x,v,i)=>{i.d(v,{Q:()=>h});var o=i(9842),_=i(4438);let h=(()=>{var c;class f{transform(u,p=2){const r=Math.pow(10,p);return(Math.trunc(Number((u*r).toFixed(p+5)))/r).toFixed(p)}}return c=f,(0,o.A)(f,"\u0275fac",function(u){return new(u||c)}),(0,o.A)(f,"\u0275pipe",_.EJ8({name:"decimal",type:c,pure:!0,standalone:!0})),f})()},6617:(x,v,i)=>{i.d(v,{p:()=>c});var o=i(9842),_=i(6473),h=i(4438);let c=(()=>{var f;class C{transform(p){return(0,_.ZV)(p)}}return f=C,(0,o.A)(C,"\u0275fac",function(p){return new(p||f)}),(0,o.A)(C,"\u0275pipe",h.EJ8({name:"formatNumber",type:f,pure:!0,standalone:!0})),C})()},6812:(x,v,i)=>{i.d(v,{J:()=>C});var o=i(467),_=i(9842),h=i(2716),c=i(7987),f=i(4438);let C=(()=>{var u;class p extends h.H{open(m){var g=this;return(0,o.A)(function*(){const P=yield Promise.all([i.e(1328),i.e(4823),i.e(6217),i.e(9717),i.e(5475),i.e(2076),i.e(2456)]).then(i.bind(i,2456));return g.matDialog.open(P.AddProjectFormComponent.getComponent(),{panelClass:"custom_dialog",data:m,maxWidth:"700px",width:"100%",scrollStrategy:new c.t0,disableClose:!0})})()}}return u=p,(0,_.A)(p,"\u0275fac",(()=>{let r;return function(g){return(r||(r=f.xGo(u)))(g||u)}})()),(0,_.A)(p,"\u0275prov",f.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})),p})()},6915:(x,v,i)=>{i.r(v),i.d(v,{ProjectsComponent:()=>Mt});var o=i(9842),_=i(3200),h=i(4433),c=i(6617),f=i(9424),C=i(5599),u=i(1556),p=i(6674),r=i(2840),m=i(1448),g=i(9079),P=i(9115),D=i(1413),M=i(152),R=i(6146),F=i(5900),k=i(6408),t=i(4438),E=i(1110),S=i(1970),B=i(177);const X=n=>({width:n}),L=(n,l,e)=>({width:n,height:l,left:e});function z(n,l){if(1&n&&t.nrm(0,"ngx-avatars",4),2&n){const e=t.XpG().$implicit,a=t.XpG();t.FS9("bgColor",a._storeService.getBgColor(null==e.user?null:e.user.firstName.slice(0,1))),t.Y8G("size",35)("name",e.user.firstName.slice(0,1))}}function J(n,l){if(1&n&&t.nrm(0,"ngx-avatars",4),2&n){const e=t.XpG().$implicit,a=t.XpG();t.FS9("bgColor",a._storeService.getBgColor(e.user.email.slice(0,1))),t.Y8G("size",35)("name",e.user.email.slice(0,1))}}function Y(n,l){if(1&n&&(t.j41(0,"div",3),t.DNE(1,z,1,3,"ngx-avatars",4)(2,J,1,3,"ngx-avatars",4),t.k0s()),2&n){const e=l.$implicit,a=l.index,s=t.XpG();t.Y8G("ngStyle",t.sMw(2,L,s.avatarSize+"px",s.avatarSize+"px",a>0?a*s.realAvatarSize+"px":"0px")),t.R7$(),t.vxM(null!=e.user&&e.user.firstName&&null!=e.user&&e.user.lastName?1:2)}}function W(n,l){if(1&n&&(t.j41(0,"div",2),t.EFF(1),t.k0s()),2&n){const e=t.XpG();t.Y8G("ngStyle",t.sMw(2,L,e.avatarSize+"px",e.avatarSize+"px",e.avatars.length*e.realAvatarSize+"px")),t.R7$(),t.SpI(" +",e.overLength," ")}}let K=(()=>{var n;class l{constructor(){(0,o.A)(this,"avatars",[]),(0,o.A)(this,"overLength",0),(0,o.A)(this,"_storeService",(0,t.WQX)(E.n)),(0,o.A)(this,"avatarSize",40),(0,o.A)(this,"space",10),(0,o.A)(this,"realAvatarSize",this.avatarSize-this.space),(0,o.A)(this,"maxWidth","")}ngOnInit(){this.calculateMaxWidth()}calculateMaxWidth(){let a=this.avatars.length*this.realAvatarSize;this.overLength>0&&(a+=this.avatarSize),this.maxWidth=`${a}px`}}return n=l,(0,o.A)(l,"\u0275fac",function(a){return new(a||n)}),(0,o.A)(l,"\u0275cmp",t.VBU({type:n,selectors:[["app-inno-user-list-overlap"]],inputs:{avatars:"avatars",overLength:"overLength"},standalone:!0,features:[t.aNF],decls:3,vars:5,consts:[[1,"flex","relative","h-[24px]",3,"ngStyle"],["class","border-2 border-white rounded-full absolute",3,"ngStyle",4,"ngFor","ngForOf"],[1,"rounded-full","flex","justify-center","items-center","absolute","bg-bg-secondary","text-text-tertiary","text-text-sm-semibold","border-2","border-border-white",3,"ngStyle"],[1,"border-2","border-white","rounded-full","absolute",3,"ngStyle"],[3,"size","bgColor","name"]],template:function(a,s){1&a&&(t.j41(0,"div",0),t.DNE(1,Y,3,6,"div",1)(2,W,2,6,"div",2),t.k0s()),2&a&&(t.Y8G("ngStyle",t.eq3(3,X,s.maxWidth)),t.R7$(),t.Y8G("ngForOf",s.avatars),t.R7$(),t.vxM(s.overLength>0?2:-1))},dependencies:[R.G,B.Sq,B.B3,S.mC,S.fw]})),l})();var Q=i(2953),H=i(6473),V=i(5236),w=i(33),Z=i(1433),q=i(3492),tt=i(6812),N=i(9417);const et=["grid"],nt=n=>({"mb-28":n});function ot(n,l){if(1&n){const e=t.RV6();t.j41(0,"button",9),t.bIt("click",function(){t.eBV(e);const s=t.XpG();return t.Njj(s.OpenDialog())}),t.nrm(1,"img",10),t.EFF(2),t.nI1(3,"translate"),t.k0s()}2&n&&(t.R7$(2),t.SpI(" ",t.bMT(3,1,"PROJECT.NewProject")," "))}function it(n,l){1&n&&(t.j41(0,"div",7),t.nrm(1,"app-inno-empty-data",11),t.k0s()),2&n&&(t.R7$(),t.Y8G("title","EMPTY.NoResult"))}function at(n,l){1&n&&(t.j41(0,"div",8),t.nrm(1,"app-inno-spin"),t.k0s())}function st(n,l){if(1&n&&(t.j41(0,"div",13)(1,"p",14),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"p",5),t.EFF(5),t.nI1(6,"decimal"),t.nI1(7,"formatNumber"),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(2),t.JRh(t.bMT(3,2,"PROJECT.TotalAmount")),t.R7$(3),t.SpI("$",t.bMT(7,7,t.i5U(6,4,null==e.ProjectResponse?null:e.ProjectResponse.totalAmount,2)),"")}}function rt(n,l){1&n&&(t.j41(0,"option",20),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&n&&(t.R7$(),t.JRh(t.bMT(2,1,"PROJECT.UnActiveProject")))}function lt(n,l){if(1&n&&(t.j41(0,"p",34),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(),t.SpI(" ",e.description," ")}}function ct(n,l){if(1&n&&(t.j41(0,"div",7)(1,"p",33),t.EFF(2),t.k0s(),t.DNE(3,lt,2,1,"p",34),t.k0s()),2&n){let e;const a=l.$implicit;t.R7$(2),t.SpI(" ",null!==(e=a.projectName)&&void 0!==e?e:""," "),t.R7$(),t.vxM(a.description?3:-1)}}function dt(n,l){if(1&n&&(t.j41(0,"p",35),t.EFF(1),t.k0s()),2&n){let e;const a=l.$implicit;t.R7$(),t.SpI(" ",null!==(e=null==a?null:a.clientName)&&void 0!==e?e:""," ")}}function pt(n,l){if(1&n&&(t.j41(0,"p",36),t.EFF(1),t.nI1(2,"date"),t.k0s()),2&n){const e=l.$implicit;t.R7$(),t.SpI(" ",t.i5U(2,1,e.endDate,"MM/dd/yyyy"),"")}}function ut(n,l){if(1&n&&t.nrm(0,"app-inno-user-list-overlap",37),2&n){const e=l.$implicit;t.Y8G("overLength",(null==e.members?null:e.members.length)-2)("avatars",e.members.slice(0,2))}}function mt(n,l){if(1&n&&(t.j41(0,"p",36),t.EFF(1),t.k0s()),2&n){const e=l.$implicit;t.R7$(),t.SpI(" ",e.totalLogger," ")}}function ht(n,l){if(1&n&&(t.j41(0,"p",36),t.EFF(1),t.k0s()),2&n){const e=l.$implicit;t.R7$(),t.SpI(" ",e.totalunbilled," ")}}function _t(n,l){if(1&n&&(t.j41(0,"div",38)(1,"p",39),t.EFF(2),t.nI1(3,"decimal"),t.nI1(4,"formatNumber"),t.k0s()()),2&n){const e=l.$implicit;t.R7$(2),t.SpI(" $",t.bMT(4,4,t.i5U(3,1,e.totalbilled,2))," ")}}function Ct(n,l){1&n&&(t.j41(0,"e-column",29),t.nI1(1,"translate"),t.DNE(2,_t,5,6,"ng-template",null,1,t.C5r),t.k0s()),2&n&&t.Y8G("headerText",t.bMT(1,1,"PROJECT.GIRD.Amount"))}function gt(n,l){if(1&n){const e=t.RV6();t.j41(0,"button",45),t.bIt("click",function(){t.eBV(e);const s=t.XpG().$implicit,d=t.XpG(3);return t.Njj(d.handleEdit(s.id))}),t.nrm(1,"img",46),t.k0s()}}function ft(n,l){if(1&n){const e=t.RV6();t.j41(0,"div",47)(1,"button",48),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2).$implicit,d=t.XpG(3);return t.Njj(d.handleArchive(s,!0))}),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"button",49),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2).$implicit,d=t.XpG(3);return t.Njj(d.handleDelete(s,!1))}),t.EFF(5),t.nI1(6,"translate"),t.k0s()()}2&n&&(t.R7$(2),t.SpI(" ",t.bMT(3,2,"COMMON.Archive")," "),t.R7$(3),t.SpI(" ",t.bMT(6,4,"COMMON.Delete")," "))}function vt(n,l){if(1&n){const e=t.RV6();t.j41(0,"div",47)(1,"button",48),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2).$implicit,d=t.XpG(3);return t.Njj(d.handleArchive(s,!1))}),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}2&n&&(t.R7$(2),t.SpI(" ",t.bMT(3,1,"COMMON.Activate")," "))}function Pt(n,l){if(1&n){const e=t.RV6();t.j41(0,"div",47)(1,"button",48),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2).$implicit,d=t.XpG(3);return t.Njj(d.handleDelete(s,!0))}),t.EFF(2),t.nI1(3,"translate"),t.k0s()()}2&n&&(t.R7$(2),t.SpI(" ",t.bMT(3,1,"COMMON.Activate")," "))}function jt(n,l){if(1&n&&t.DNE(0,ft,7,6,"div",47)(1,vt,4,3,"div",47)(2,Pt,4,3,"div",47),2&n){const e=t.XpG().$implicit;t.vxM(e.isActive&&!e.isArchive?0:e.isActive&&e.isArchive?1:e.isActive?-1:2)}}function At(n,l){if(1&n&&(t.j41(0,"div",40),t.DNE(1,gt,2,0,"button",41),t.j41(2,"app-inno-popover",42)(3,"button",43),t.nrm(4,"img",44),t.k0s()(),t.DNE(5,jt,3,1,"ng-template",null,2,t.C5r),t.k0s()),2&n){const e=l.$implicit,a=t.sdS(6);t.R7$(),t.vxM(e.isArchive?-1:1),t.R7$(),t.Y8G("content",a)}}function Dt(n,l){1&n&&(t.j41(0,"e-column",31),t.DNE(1,At,7,2,"ng-template",null,1,t.C5r),t.k0s())}function xt(n,l){if(1&n){const e=t.RV6();t.j41(0,"div",12)(1,"div",13)(2,"p",14),t.EFF(3),t.nI1(4,"translate"),t.k0s(),t.j41(5,"p",5),t.EFF(6),t.k0s()(),t.j41(7,"div",13)(8,"p",14),t.EFF(9),t.nI1(10,"translate"),t.k0s(),t.j41(11,"p",5),t.EFF(12),t.k0s()(),t.j41(13,"div",13)(14,"p",14),t.EFF(15),t.nI1(16,"translate"),t.k0s(),t.j41(17,"p",5),t.EFF(18),t.k0s()(),t.DNE(19,st,8,9,"div",13),t.k0s(),t.j41(20,"div",15)(21,"div",16)(22,"app-inno-input-search",17),t.bIt("onChange",function(s){t.eBV(e);const d=t.XpG();return t.Njj(d.handleSearch(s))}),t.k0s()(),t.j41(23,"select",18),t.bIt("change",function(s){t.eBV(e);const d=t.XpG();return t.Njj(d.handleSelectStatusProject(s))}),t.mxI("ngModelChange",function(s){t.eBV(e);const d=t.XpG();return t.DH7(d.filterProjectDefault,s)||(d.filterProjectDefault=s),t.Njj(s)}),t.j41(24,"option",19),t.EFF(25),t.nI1(26,"translate"),t.k0s(),t.DNE(27,rt,3,3,"option",20),t.j41(28,"option",21),t.EFF(29),t.nI1(30,"translate"),t.k0s()(),t.j41(31,"div",22)(32,"app-inno-datepicker",23),t.nI1(33,"translate"),t.bIt("onChange",function(s){t.eBV(e);const d=t.XpG();return t.Njj(d.handleChangeDateFilter(s))}),t.k0s()()(),t.j41(34,"div",24)(35,"ejs-grid",25,0),t.bIt("actionBegin",function(s){t.eBV(e);const d=t.XpG();return t.Njj(d.onActionBegin(s))}),t.j41(37,"e-columns")(38,"e-column",26),t.nI1(39,"translate"),t.DNE(40,ct,4,2,"ng-template",null,1,t.C5r),t.k0s(),t.j41(42,"e-column",27),t.nI1(43,"translate"),t.DNE(44,dt,2,1,"ng-template",null,1,t.C5r),t.k0s(),t.j41(46,"e-column",28),t.nI1(47,"translate"),t.DNE(48,pt,3,4,"ng-template",null,1,t.C5r),t.k0s(),t.j41(50,"e-column",29),t.nI1(51,"translate"),t.DNE(52,ut,1,2,"ng-template",null,1,t.C5r),t.k0s(),t.j41(54,"e-column",30),t.nI1(55,"translate"),t.DNE(56,mt,2,1,"ng-template",null,1,t.C5r),t.k0s(),t.j41(58,"e-column",30),t.nI1(59,"translate"),t.DNE(60,ht,2,1,"ng-template",null,1,t.C5r),t.k0s(),t.DNE(62,Ct,4,3,"e-column",29)(63,Dt,3,0,"e-column",31),t.k0s()(),t.j41(64,"ejs-pager",32),t.bIt("click",function(s){t.eBV(e);const d=t.XpG();return t.Njj(d.onPageChange(s))}),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(3),t.JRh(t.bMT(4,31,"PROJECT.ActiveProject")),t.R7$(3),t.JRh(null==e.ProjectResponse?null:e.ProjectResponse.totalActive),t.R7$(3),t.JRh(t.bMT(10,33,"PROJECT.TotalLogged")),t.R7$(3),t.JRh(null==e.ProjectResponse?null:e.ProjectResponse.totalLoggedFormatted),t.R7$(3),t.JRh(t.bMT(16,35,"PROJECT.TotalUnbilled")),t.R7$(3),t.JRh(null==e.ProjectResponse?null:e.ProjectResponse.totalUnbilled),t.R7$(),t.vxM(e.role!=e.Role.Accountant?19:-1),t.R7$(3),t.Y8G("value",e.search),t.R7$(),t.R50("ngModel",e.filterProjectDefault),t.R7$(2),t.JRh(t.bMT(26,37,"PROJECT.ActiveProject")),t.R7$(2),t.vxM(e.role===e.Role.Admin?27:-1),t.R7$(2),t.JRh(t.bMT(30,39,"PROJECT.ArchiveProjects")),t.R7$(3),t.Y8G("placeholder",t.bMT(33,41,"COMMON.DatePlaceholder"))("value",e.dateSelected),t.R7$(2),t.Y8G("ngClass",t.eq3(55,nt,e.storeService.getIsRunning())),t.R7$(),t.Y8G("dataSource",e.dataSource)("allowSelection",!0)("allowSorting",!0)("sortSettings",e.sortOptions),t.R7$(3),t.Y8G("headerText",t.bMT(39,43,"PROJECT.GIRD.ProjectName")),t.R7$(4),t.Y8G("headerText",t.bMT(43,45,"PROJECT.GIRD.Client")),t.R7$(4),t.Y8G("headerText",t.bMT(47,47,"PROJECT.GIRD.EndDate")),t.R7$(4),t.Y8G("headerText",t.bMT(51,49,"PROJECT.GIRD.Members")),t.R7$(4),t.Y8G("headerText",t.bMT(55,51,"PROJECT.GIRD.Logged")),t.R7$(4),t.Y8G("headerText",t.bMT(59,53,"PROJECT.GIRD.Unbilled")),t.R7$(4),t.vxM(e.role!=e.Role.Employee?62:-1),t.R7$(),t.vxM(e.role!=e.Role.Employee&&e.role!=e.Role.Accountant&&e.role!=e.Role.Contractor?63:-1),t.R7$(),t.Y8G("pageSize",e.pageSizesDefault)("totalRecordsCount",e.totalPages)("currentPage",e.currentPage)("pageSizes",e.pageSizes)}}r.is5.Inject(r.Rav);let Mt=(()=>{var n;class l{constructor(a,s,d,j,A,y,I,U,b){(0,o.A)(this,"translate",void 0),(0,o.A)(this,"layoutUtilsService",void 0),(0,o.A)(this,"router",void 0),(0,o.A)(this,"destroyRef",void 0),(0,o.A)(this,"activatedRoute",void 0),(0,o.A)(this,"storeService",void 0),(0,o.A)(this,"projectService",void 0),(0,o.A)(this,"toastService",void 0),(0,o.A)(this,"addProjectDialog",void 0),(0,o.A)(this,"dateSelected",void 0),(0,o.A)(this,"sort",void 0),(0,o.A)(this,"Role",Q.X),(0,o.A)(this,"isLoading",!1),(0,o.A)(this,"timeout",void 0),(0,o.A)(this,"_subscriptions",[]),(0,o.A)(this,"search",""),(0,o.A)(this,"role",void 0),(0,o.A)(this,"searchSubject",new D.B),(0,o.A)(this,"idTime",void 0),(0,o.A)(this,"columnName",void 0),(0,o.A)(this,"direction",void 0),(0,o.A)(this,"sortOptions",{columns:[]}),(0,o.A)(this,"selectionOptions",{type:"Multiple",checkboxOnly:!0}),(0,o.A)(this,"dataSource",void 0),(0,o.A)(this,"totalPages",1),(0,o.A)(this,"ProjectResponse",null),(0,o.A)(this,"filterProjectDefault",1),(0,o.A)(this,"currentPage",1),(0,o.A)(this,"pageSizes",[10,20,50,100]),(0,o.A)(this,"pageSizesDefault",10),(0,o.A)(this,"listAvatar",["https://picsum.photos/id/237/200/300","https://picsum.photos/id/238/200/300","https://picsum.photos/id/239/200/300"]),(0,o.A)(this,"grid",void 0),this.translate=a,this.layoutUtilsService=s,this.router=d,this.destroyRef=j,this.activatedRoute=A,this.storeService=y,this.projectService=I,this.toastService=U,this.addProjectDialog=b}onPageChange(a){a?.newProp?.pageSize&&(this.pageSizesDefault=a.newProp.pageSize,this.handleCase(this.filterProjectDefault)),a?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:a.currentPage},queryParamsHandling:"merge"})}GetAllProject(a){let s={Page:this.currentPage,PageSize:this.pageSizesDefault,Search:this.search,filterDate:(0,H.cn)(this.dateSelected),...a};this.isLoading=!0,this.projectService.GetAllProjectAsync(s).pipe((0,g.pQ)(this.destroyRef)).subscribe({next:d=>{this.totalPages=d.totalRecords;const j=d.data.map(A=>{let y="",I="",U=0,b=0,T=0,G=0,O=0;return A.timeTrackings.forEach($=>{if(1==$.billable){const[Ot,Rt]=$.endTime.split(":").map(Number);G+=Ot,O+=Rt,O>=60&&(G+=Math.floor(O/60),O=0),I=(G<10?"0"+G:G)+":"+(O<10?"0"+O:O)}const[bt,Tt]=$.endTime.split(":").map(Number);b+=bt,T+=Tt,T>=60&&(b+=Math.floor(T/60),T=0),y=(b<10?"0"+b:b)+":"+(T<10?"0"+T:T)}),A.invoices.forEach($=>{U+=$.totalAmount}),{...A,totalLogger:y,totalunbilled:I,totalbilled:U}});this.dataSource=j,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]})},complete:()=>{this.isLoading=!1}})}handleSelectStatusProject(a){this.currentPage=1,this.filterProjectDefault=Number.parseInt(a.target.value),this.handleCase(Number.parseInt(a.target.value))}handleCase(a,s){switch(a){case 3:let d={isArchive:!0,...this.sort};this.GetAllProject(d);break;case 2:let j={isActive:!1,...this.sort};this.GetAllProject(j);break;default:let A={isActive:!0,...this.sort};this.GetAllProject(A)}}handleSearch(a){this.searchSubject.next(a)}CalculationProject(){this.projectService.CalculationProject().subscribe({next:a=>{this.ProjectResponse=a}})}ngOnInit(){this.CalculationProject();const a=this.searchSubject.pipe((0,M.B)(550)).subscribe(s=>{s?(this.search=s,this.handleCase(this.filterProjectDefault)):(this.search="",this.handleCase(this.filterProjectDefault))});this._subscriptions.push(a),this.activatedRoute.queryParams.pipe((0,g.pQ)(this.destroyRef)).subscribe(s=>{s?.page?(this.currentPage=s.page,this.sort?this.handleCase(this.filterProjectDefault,this.sort):this.handleCase(this.filterProjectDefault)):this.handleCase(this.filterProjectDefault)}),this.storeService.getRoleBusinessAsObservable().pipe((0,g.pQ)(this.destroyRef)).subscribe(s=>{this.role=s})}creaFormDelete(a){const s=this.translate.instant("PROJECT.DeleteProject"),d=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:s,description:d}).then(j=>{j&&this.projectService.DeleteProject(a,!1).pipe((0,g.pQ)(this.destroyRef)).subscribe(A=>{A?(this.CalculationProject(),this.handleCase(this.filterProjectDefault),this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}OpenDialog(){this.addProjectDialog.open(null).then(s=>{s.afterClosed().subscribe(d=>{let j={isActive:!0,...this.sort};d&&(this.CalculationProject(),this.GetAllProject(j))})})}handleEdit(a){this.addProjectDialog.open(a).then(d=>{d.afterClosed().subscribe(j=>{j&&(this.sort?this.handleCase(this.filterProjectDefault,this.sort):this.handleCase(this.filterProjectDefault))})})}handleDelete(a,s){if(!a)return;const d=this.translate.instant(s?"PROJECT.ActiveProject":"PROJECT.DeleteProject"),j=this.translate.instant(s?"PROJECT.DescriptionActive":"COMMON.ConfirmDelete");let A;A=s?this.layoutUtilsService.alertConfirm({title:d,description:j,textSubmit:"Active"}):this.layoutUtilsService.alertDelete({title:d,description:j}),A.then(y=>{y&&this.projectService.DeleteProject([a.id],s).pipe((0,g.pQ)(this.destroyRef)).subscribe(I=>{I?(this.CalculationProject(),this.handleCase(this.filterProjectDefault),this.toastService.showSuccess(this.translate.instant(s?"TOAST.Active":"TOAST.Delete"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}handleArchive(a,s){this.projectService.UpdateArchive([a.id],s).pipe((0,g.pQ)(this.destroyRef)).subscribe(d=>{d?(this.CalculationProject(),this.handleCase(this.filterProjectDefault),this.toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})}onActionBegin(a){if("sorting"===a.requestType){if(this.columnName=a.columnName,this.direction=a.direction,this.sort={columnName:a.columnName,direction:a.direction},this.columnName)return void this.handleCase(this.filterProjectDefault,this.sort);this.sortOptions={columns:[]},this.sort=null,this.handleCase(this.filterProjectDefault)}}handleChangeDateFilter(a){let s={isActive:!0,...this.sort};this.dateSelected=a,this.GetAllProject(s)}ngOnDestroy(){this._subscriptions&&this._subscriptions.forEach(a=>a.unsubscribe()),this.timeout&&clearTimeout(this.timeout),this.idTime&&clearTimeout(this.idTime)}}return n=l,(0,o.A)(l,"\u0275fac",function(a){return new(a||n)(t.rXU(V.c$),t.rXU(u.Z),t.rXU(w.Ix),t.rXU(t.abz),t.rXU(w.nX),t.rXU(E.n),t.rXU(Z.T),t.rXU(q.f),t.rXU(tt.J))}),(0,o.A)(l,"\u0275cmp",t.VBU({type:n,selectors:[["app-projects"]],viewQuery:function(a,s){if(1&a&&t.GBs(et,5),2&a){let d;t.mGM(d=t.lsd())&&(s.grid=d.first)}},standalone:!0,features:[t.Jv_([u.Z]),t.aNF],decls:8,vars:3,consts:[["grid",""],["template",""],["contentPopover",""],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"button-size-md","button-primary"],[1,"w-full"],[1,"flex","justify-center","items-center","grow","py-3"],[1,"button-size-md","button-primary",3,"click"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"],[3,"title"],[1,"container-full","mt-[24px]","grid","grid-cols-4","mxw1100:grid-cols-2","mxw600:grid-cols-1"],[1,"w-full","min-h-[112px]","border-[4px]","border-border-primary","flex","flex-col","justify-end","gap-[8px]","pb-[16px]","px-[24px]","bg-bg-primary"],[1,"text-text-tertiary","text-text-md-semibold"],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"dropdown-md",3,"change","ngModelChange","ngModel"],["value","1"],["value","2"],["value","3"],[1,"w-[200px]"],[3,"onChange","placeholder","value"],[1,"w-full","mt-[12px]",3,"ngClass"],[1,"customTable",3,"actionBegin","dataSource","allowSelection","allowSorting","sortSettings"],["width","200","field","ProjectName",3,"headerText"],["width","180","field","ClientName",3,"headerText"],["width","120","field","EndDate",3,"headerText"],["width","150",3,"headerText"],["width","100",3,"headerText"],["headerText","","width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"text-text-primary","text-text-md-semibold","line-clamp-1"],[1,"text-text-xs-regular","text-text-tertiary","line-clamp-1"],[1,"text-text-md-regular","text-text-primary"],[1,"text-text-primary","text-text-md-regular"],[3,"overLength","avatars"],[1,"w-full","flex","gap-[8px]","flex-wrap"],[1,"text-text-primary","text-text-md-bold"],[1,"flex","items-center"],[1,"button-icon"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"]],template:function(a,s){1&a&&(t.j41(0,"div",3)(1,"div",4)(2,"p",5),t.EFF(3," Projects "),t.k0s(),t.DNE(4,ot,4,3,"button",6),t.k0s()(),t.DNE(5,it,2,1,"div",7)(6,at,2,0,"div",8)(7,xt,65,57)),2&a&&(t.R7$(4),t.vxM(s.role!=s.Role.Employee?4:-1),t.R7$(),t.vxM(0==(null==s.dataSource?null:s.dataSource.length)?5:-1),t.R7$(),t.vxM(s.isLoading?6:7))},dependencies:[m.iov,m.BzB,_.J,m.gFV,m._ab,m.eeu,m.rFS,m.LGG,m.cvh,R.G,B.YU,N.xH,N.y7,N.wz,N.BC,N.vS,B.vh,V.D9,p.Ph,P.Cn,F.M,k.G,K,C.x,f.f,c.p,h.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.mat-mdc-menu-content[_ngcontent-%COMP%]{background:#fff!important}"]})),l})()}}]);