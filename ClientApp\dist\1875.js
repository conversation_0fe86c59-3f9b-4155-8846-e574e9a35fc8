"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1875],{1875:(ne,It,p)=>{p.d(It,{T1:()=>bt,Fb:()=>Dt,O7:()=>St,ad:()=>ie,HD:()=>z,eg:()=>Vt});var l=p(4438),N=p(177),F=p(7333),Z=p(687),w=p(4085),D=p(6860),_=p(1413),y=p(8359),Ct=p(3236),xt=p(1584),kt=p(536),Q=p(1985),A=p(7786),Lt=p(4412),x=p(6977),Ot=p(6354),Mt=p(6697),Nt=p(8141),Ft=p(5558),$=p(9172),j=p(8203);function J(r){const t=r.cloneNode(!0),s=t.querySelectorAll("[id]"),e=r.nodeName.toLowerCase();t.removeAttribute("id");for(let i=0;i<s.length;i++)s[i].removeAttribute("id");return"canvas"===e?tt(r,t):("input"===e||"select"===e||"textarea"===e)&&q(r,t),Y("canvas",r,t,tt),Y("input, textarea, select",r,t,q),t}function Y(r,t,s,e){const i=t.querySelectorAll(r);if(i.length){const n=s.querySelectorAll(r);for(let o=0;o<i.length;o++)e(i[o],n[o])}}let At=0;function q(r,t){"file"!==t.type&&(t.value=r.value),"radio"===t.type&&t.name&&(t.name=`mat-clone-${t.name}-${At++}`)}function tt(r,t){const s=t.getContext("2d");if(s)try{s.drawImage(r,0,0)}catch{}}function B(r){const t=r.getBoundingClientRect();return{top:t.top,right:t.right,bottom:t.bottom,left:t.left,width:t.width,height:t.height,x:t.x,y:t.y}}function H(r,t,s){const{top:e,bottom:i,left:n,right:o}=r;return s>=e&&s<=i&&t>=n&&t<=o}function E(r,t,s){r.top+=t,r.bottom=r.top+r.height,r.left+=s,r.right=r.left+r.width}function et(r,t,s,e){const{top:i,right:n,bottom:o,left:a,width:h,height:c}=r,d=h*t,g=c*t;return e>i-g&&e<o+g&&s>a-d&&s<n+d}class it{constructor(t){this._document=t,this.positions=new Map}clear(){this.positions.clear()}cache(t){this.clear(),this.positions.set(this._document,{scrollPosition:this.getViewportScrollPosition()}),t.forEach(s=>{this.positions.set(s,{scrollPosition:{top:s.scrollTop,left:s.scrollLeft},clientRect:B(s)})})}handleScroll(t){const s=(0,D.Fb)(t),e=this.positions.get(s);if(!e)return null;const i=e.scrollPosition;let n,o;if(s===this._document){const c=this.getViewportScrollPosition();n=c.top,o=c.left}else n=s.scrollTop,o=s.scrollLeft;const a=i.top-n,h=i.left-o;return this.positions.forEach((c,d)=>{c.clientRect&&s!==d&&s.contains(d)&&E(c.clientRect,a,h)}),i.top=n,i.left=o,{top:a,left:h}}getViewportScrollPosition(){return{top:window.scrollY,left:window.scrollX}}}function st(r,t){const s=r.rootNodes;if(1===s.length&&s[0].nodeType===t.ELEMENT_NODE)return s[0];const e=t.createElement("div");return s.forEach(i=>e.appendChild(i)),e}function G(r,t,s){for(let e in t)if(t.hasOwnProperty(e)){const i=t[e];i?r.setProperty(e,i,s?.has(e)?"important":""):r.removeProperty(e)}return r}function S(r,t){const s=t?"":"none";G(r.style,{"touch-action":t?"":"none","-webkit-user-drag":t?"":"none","-webkit-tap-highlight-color":t?"":"transparent","user-select":s,"-ms-user-select":s,"-webkit-user-select":s,"-moz-user-select":s})}function nt(r,t,s){G(r.style,{position:t?"":"fixed",top:t?"":"0",opacity:t?"":"0",left:t?"":"-999em"},s)}function T(r,t){return t&&"none"!=t?r+" "+t:r}function rt(r,t){r.style.width=`${t.width}px`,r.style.height=`${t.height}px`,r.style.transform=k(t.left,t.top)}function k(r,t){return`translate3d(${Math.round(r)}px, ${Math.round(t)}px, 0)`}function ot(r){const t=r.toLowerCase().indexOf("ms")>-1?1:1e3;return parseFloat(r)*t}function U(r,t){return r.getPropertyValue(t).split(",").map(e=>e.trim())}const Ht=new Set(["position"]);class Gt{get element(){return this._preview}constructor(t,s,e,i,n,o,a,h,c){this._document=t,this._rootElement=s,this._direction=e,this._initialDomRect=i,this._previewTemplate=n,this._previewClass=o,this._pickupPositionOnPage=a,this._initialTransform=h,this._zIndex=c}attach(t){this._preview=this._createPreview(),t.appendChild(this._preview),at(this._preview)&&this._preview.showPopover()}destroy(){this._preview.remove(),this._previewEmbeddedView?.destroy(),this._preview=this._previewEmbeddedView=null}setTransform(t){this._preview.style.transform=t}getBoundingClientRect(){return this._preview.getBoundingClientRect()}addClass(t){this._preview.classList.add(t)}getTransitionDuration(){return function Bt(r){const t=getComputedStyle(r),s=U(t,"transition-property"),e=s.find(a=>"transform"===a||"all"===a);if(!e)return 0;const i=s.indexOf(e),n=U(t,"transition-duration"),o=U(t,"transition-delay");return ot(n[i])+ot(o[i])}(this._preview)}addEventListener(t,s){this._preview.addEventListener(t,s)}removeEventListener(t,s){this._preview.removeEventListener(t,s)}_createPreview(){const t=this._previewTemplate,s=this._previewClass,e=t?t.template:null;let i;if(e&&t){const n=t.matchSize?this._initialDomRect:null,o=t.viewContainer.createEmbeddedView(e,t.context);o.detectChanges(),i=st(o,this._document),this._previewEmbeddedView=o,t.matchSize?rt(i,n):i.style.transform=k(this._pickupPositionOnPage.x,this._pickupPositionOnPage.y)}else i=J(this._rootElement),rt(i,this._initialDomRect),this._initialTransform&&(i.style.transform=this._initialTransform);return G(i.style,{"pointer-events":"none",margin:at(i)?"0 auto 0 0":"0",position:"fixed",top:"0",left:"0","z-index":this._zIndex+""},Ht),S(i,!1),i.classList.add("cdk-drag-preview"),i.setAttribute("popover","manual"),i.setAttribute("dir",this._direction),s&&(Array.isArray(s)?s.forEach(n=>i.classList.add(n)):i.classList.add(s)),i}}function at(r){return"showPopover"in r}const lt=(0,D.BQ)({passive:!0}),L=(0,D.BQ)({passive:!1}),ht=(0,D.BQ)({passive:!1,capture:!0}),ct=new Set(["position"]);class zt{get disabled(){return this._disabled||!(!this._dropContainer||!this._dropContainer.disabled)}set disabled(t){t!==this._disabled&&(this._disabled=t,this._toggleNativeDragInteractions(),this._handles.forEach(s=>S(s,t)))}constructor(t,s,e,i,n,o){this._config=s,this._document=e,this._ngZone=i,this._viewportRuler=n,this._dragDropRegistry=o,this._passiveTransform={x:0,y:0},this._activeTransform={x:0,y:0},this._hasStartedDragging=(0,l.vPA)(!1),this._moveEvents=new _.B,this._pointerMoveSubscription=y.yU.EMPTY,this._pointerUpSubscription=y.yU.EMPTY,this._scrollSubscription=y.yU.EMPTY,this._resizeSubscription=y.yU.EMPTY,this._boundaryElement=null,this._nativeInteractionsEnabled=!0,this._handles=[],this._disabledHandles=new Set,this._direction="ltr",this.dragStartDelay=0,this.scale=1,this._disabled=!1,this.beforeStarted=new _.B,this.started=new _.B,this.released=new _.B,this.ended=new _.B,this.entered=new _.B,this.exited=new _.B,this.dropped=new _.B,this.moved=this._moveEvents,this._pointerDown=a=>{if(this.beforeStarted.next(),this._handles.length){const h=this._getTargetHandle(a);h&&!this._disabledHandles.has(h)&&!this.disabled&&this._initializeDragSequence(h,a)}else this.disabled||this._initializeDragSequence(this._rootElement,a)},this._pointerMove=a=>{const h=this._getPointerPositionOnPage(a);if(!this._hasStartedDragging()){if(Math.abs(h.x-this._pickupPositionOnPage.x)+Math.abs(h.y-this._pickupPositionOnPage.y)>=this._config.dragStartThreshold){const P=Date.now()>=this._dragStartTime+this._getDragStartDelay(a),b=this._dropContainer;if(!P)return void this._endDragSequence(a);(!b||!b.isDragging()&&!b.isReceiving())&&(a.cancelable&&a.preventDefault(),this._hasStartedDragging.set(!0),this._ngZone.run(()=>this._startDragSequence(a)))}return}a.cancelable&&a.preventDefault();const c=this._getConstrainedPointerPosition(h);if(this._hasMoved=!0,this._lastKnownPointerPosition=h,this._updatePointerDirectionDelta(c),this._dropContainer)this._updateActiveDropContainer(c,h);else{const d=this.constrainPosition?this._initialDomRect:this._pickupPositionOnPage,g=this._activeTransform;g.x=c.x-d.x+this._passiveTransform.x,g.y=c.y-d.y+this._passiveTransform.y,this._applyRootElementTransform(g.x,g.y)}this._moveEvents.observers.length&&this._ngZone.run(()=>{this._moveEvents.next({source:this,pointerPosition:c,event:a,distance:this._getDragDistance(c),delta:this._pointerDirectionDelta})})},this._pointerUp=a=>{this._endDragSequence(a)},this._nativeDragStart=a=>{if(this._handles.length){const h=this._getTargetHandle(a);h&&!this._disabledHandles.has(h)&&!this.disabled&&a.preventDefault()}else this.disabled||a.preventDefault()},this.withRootElement(t).withParent(s.parentDragRef||null),this._parentPositions=new it(e),o.registerDragItem(this)}getPlaceholderElement(){return this._placeholder}getRootElement(){return this._rootElement}getVisibleElement(){return this.isDragging()?this.getPlaceholderElement():this.getRootElement()}withHandles(t){this._handles=t.map(e=>(0,w.i8)(e)),this._handles.forEach(e=>S(e,this.disabled)),this._toggleNativeDragInteractions();const s=new Set;return this._disabledHandles.forEach(e=>{this._handles.indexOf(e)>-1&&s.add(e)}),this._disabledHandles=s,this}withPreviewTemplate(t){return this._previewTemplate=t,this}withPlaceholderTemplate(t){return this._placeholderTemplate=t,this}withRootElement(t){const s=(0,w.i8)(t);return s!==this._rootElement&&(this._rootElement&&this._removeRootElementListeners(this._rootElement),this._ngZone.runOutsideAngular(()=>{s.addEventListener("mousedown",this._pointerDown,L),s.addEventListener("touchstart",this._pointerDown,lt),s.addEventListener("dragstart",this._nativeDragStart,L)}),this._initialTransform=void 0,this._rootElement=s),typeof SVGElement<"u"&&this._rootElement instanceof SVGElement&&(this._ownerSVGElement=this._rootElement.ownerSVGElement),this}withBoundaryElement(t){return this._boundaryElement=t?(0,w.i8)(t):null,this._resizeSubscription.unsubscribe(),t&&(this._resizeSubscription=this._viewportRuler.change(10).subscribe(()=>this._containInsideBoundaryOnResize())),this}withParent(t){return this._parentDragRef=t,this}dispose(){this._removeRootElementListeners(this._rootElement),this.isDragging()&&this._rootElement?.remove(),this._anchor?.remove(),this._destroyPreview(),this._destroyPlaceholder(),this._dragDropRegistry.removeDragItem(this),this._removeListeners(),this.beforeStarted.complete(),this.started.complete(),this.released.complete(),this.ended.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this._moveEvents.complete(),this._handles=[],this._disabledHandles.clear(),this._dropContainer=void 0,this._resizeSubscription.unsubscribe(),this._parentPositions.clear(),this._boundaryElement=this._rootElement=this._ownerSVGElement=this._placeholderTemplate=this._previewTemplate=this._anchor=this._parentDragRef=null}isDragging(){return this._hasStartedDragging()&&this._dragDropRegistry.isDragging(this)}reset(){this._rootElement.style.transform=this._initialTransform||"",this._activeTransform={x:0,y:0},this._passiveTransform={x:0,y:0}}disableHandle(t){!this._disabledHandles.has(t)&&this._handles.indexOf(t)>-1&&(this._disabledHandles.add(t),S(t,!0))}enableHandle(t){this._disabledHandles.has(t)&&(this._disabledHandles.delete(t),S(t,this.disabled))}withDirection(t){return this._direction=t,this}_withDropContainer(t){this._dropContainer=t}getFreeDragPosition(){const t=this.isDragging()?this._activeTransform:this._passiveTransform;return{x:t.x,y:t.y}}setFreeDragPosition(t){return this._activeTransform={x:0,y:0},this._passiveTransform.x=t.x,this._passiveTransform.y=t.y,this._dropContainer||this._applyRootElementTransform(t.x,t.y),this}withPreviewContainer(t){return this._previewContainer=t,this}_sortFromLastPointerPosition(){const t=this._lastKnownPointerPosition;t&&this._dropContainer&&this._updateActiveDropContainer(this._getConstrainedPointerPosition(t),t)}_removeListeners(){this._pointerMoveSubscription.unsubscribe(),this._pointerUpSubscription.unsubscribe(),this._scrollSubscription.unsubscribe(),this._getShadowRoot()?.removeEventListener("selectstart",gt,ht)}_destroyPreview(){this._preview?.destroy(),this._preview=null}_destroyPlaceholder(){this._placeholder?.remove(),this._placeholderRef?.destroy(),this._placeholder=this._placeholderRef=null}_endDragSequence(t){if(this._dragDropRegistry.isDragging(this)&&(this._removeListeners(),this._dragDropRegistry.stopDragging(this),this._toggleNativeDragInteractions(),this._handles&&(this._rootElement.style.webkitTapHighlightColor=this._rootElementTapHighlight),this._hasStartedDragging()))if(this.released.next({source:this,event:t}),this._dropContainer)this._dropContainer._stopScrolling(),this._animatePreviewToPlaceholder().then(()=>{this._cleanupDragArtifacts(t),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)});else{this._passiveTransform.x=this._activeTransform.x;const s=this._getPointerPositionOnPage(t);this._passiveTransform.y=this._activeTransform.y,this._ngZone.run(()=>{this.ended.next({source:this,distance:this._getDragDistance(s),dropPoint:s,event:t})}),this._cleanupCachedDimensions(),this._dragDropRegistry.stopDragging(this)}}_startDragSequence(t){R(t)&&(this._lastTouchEventTime=Date.now()),this._toggleNativeDragInteractions();const s=this._getShadowRoot(),e=this._dropContainer;if(s&&this._ngZone.runOutsideAngular(()=>{s.addEventListener("selectstart",gt,ht)}),e){const i=this._rootElement,n=i.parentNode,o=this._placeholder=this._createPlaceholderElement(),a=this._anchor=this._anchor||this._document.createComment("");n.insertBefore(a,i),this._initialTransform=i.style.transform||"",this._preview=new Gt(this._document,this._rootElement,this._direction,this._initialDomRect,this._previewTemplate||null,this.previewClass||null,this._pickupPositionOnPage,this._initialTransform,this._config.zIndex||1e3),this._preview.attach(this._getPreviewInsertionPoint(n,s)),nt(i,!1,ct),this._document.body.appendChild(n.replaceChild(o,i)),this.started.next({source:this,event:t}),e.start(),this._initialContainer=e,this._initialIndex=e.getItemIndex(this)}else this.started.next({source:this,event:t}),this._initialContainer=this._initialIndex=void 0;this._parentPositions.cache(e?e.getScrollableParents():[])}_initializeDragSequence(t,s){this._parentDragRef&&s.stopPropagation();const e=this.isDragging(),i=R(s),n=!i&&0!==s.button,o=this._rootElement,a=(0,D.Fb)(s),h=!i&&this._lastTouchEventTime&&this._lastTouchEventTime+800>Date.now(),c=i?(0,Z.w6)(s):(0,Z._G)(s);if(a&&a.draggable&&"mousedown"===s.type&&s.preventDefault(),e||n||h||c)return;if(this._handles.length){const m=o.style;this._rootElementTapHighlight=m.webkitTapHighlightColor||"",m.webkitTapHighlightColor="transparent"}this._hasMoved=!1,this._hasStartedDragging.set(this._hasMoved),this._removeListeners(),this._initialDomRect=this._rootElement.getBoundingClientRect(),this._pointerMoveSubscription=this._dragDropRegistry.pointerMove.subscribe(this._pointerMove),this._pointerUpSubscription=this._dragDropRegistry.pointerUp.subscribe(this._pointerUp),this._scrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(m=>this._updateOnScroll(m)),this._boundaryElement&&(this._boundaryRect=B(this._boundaryElement));const d=this._previewTemplate;this._pickupPositionInElement=d&&d.template&&!d.matchSize?{x:0,y:0}:this._getPointerPositionInElement(this._initialDomRect,t,s);const g=this._pickupPositionOnPage=this._lastKnownPointerPosition=this._getPointerPositionOnPage(s);this._pointerDirectionDelta={x:0,y:0},this._pointerPositionAtLastDirectionChange={x:g.x,y:g.y},this._dragStartTime=Date.now(),this._dragDropRegistry.startDragging(this,s)}_cleanupDragArtifacts(t){nt(this._rootElement,!0,ct),this._anchor.parentNode.replaceChild(this._rootElement,this._anchor),this._destroyPreview(),this._destroyPlaceholder(),this._initialDomRect=this._boundaryRect=this._previewRect=this._initialTransform=void 0,this._ngZone.run(()=>{const s=this._dropContainer,e=s.getItemIndex(this),i=this._getPointerPositionOnPage(t),n=this._getDragDistance(i),o=s._isOverContainer(i.x,i.y);this.ended.next({source:this,distance:n,dropPoint:i,event:t}),this.dropped.next({item:this,currentIndex:e,previousIndex:this._initialIndex,container:s,previousContainer:this._initialContainer,isPointerOverContainer:o,distance:n,dropPoint:i,event:t}),s.drop(this,e,this._initialIndex,this._initialContainer,o,n,i,t),this._dropContainer=this._initialContainer})}_updateActiveDropContainer({x:t,y:s},{x:e,y:i}){let n=this._initialContainer._getSiblingContainerFromPosition(this,t,s);!n&&this._dropContainer!==this._initialContainer&&this._initialContainer._isOverContainer(t,s)&&(n=this._initialContainer),n&&n!==this._dropContainer&&this._ngZone.run(()=>{this.exited.next({item:this,container:this._dropContainer}),this._dropContainer.exit(this),this._dropContainer=n,this._dropContainer.enter(this,t,s,n===this._initialContainer&&n.sortingDisabled?this._initialIndex:void 0),this.entered.next({item:this,container:n,currentIndex:n.getItemIndex(this)})}),this.isDragging()&&(this._dropContainer._startScrollingIfNecessary(e,i),this._dropContainer._sortItem(this,t,s,this._pointerDirectionDelta),this.constrainPosition?this._applyPreviewTransform(t,s):this._applyPreviewTransform(t-this._pickupPositionInElement.x,s-this._pickupPositionInElement.y))}_animatePreviewToPlaceholder(){if(!this._hasMoved)return Promise.resolve();const t=this._placeholder.getBoundingClientRect();this._preview.addClass("cdk-drag-animating"),this._applyPreviewTransform(t.left,t.top);const s=this._preview.getTransitionDuration();return 0===s?Promise.resolve():this._ngZone.runOutsideAngular(()=>new Promise(e=>{const i=o=>{(!o||this._preview&&(0,D.Fb)(o)===this._preview.element&&"transform"===o.propertyName)&&(this._preview?.removeEventListener("transitionend",i),e(),clearTimeout(n))},n=setTimeout(i,1.5*s);this._preview.addEventListener("transitionend",i)}))}_createPlaceholderElement(){const t=this._placeholderTemplate,s=t?t.template:null;let e;return s?(this._placeholderRef=t.viewContainer.createEmbeddedView(s,t.context),this._placeholderRef.detectChanges(),e=st(this._placeholderRef,this._document)):e=J(this._rootElement),e.style.pointerEvents="none",e.classList.add("cdk-drag-placeholder"),e}_getPointerPositionInElement(t,s,e){const i=s===this._rootElement?null:s,n=i?i.getBoundingClientRect():t,o=R(e)?e.targetTouches[0]:e,a=this._getViewportScrollPosition();return{x:n.left-t.left+(o.pageX-n.left-a.left),y:n.top-t.top+(o.pageY-n.top-a.top)}}_getPointerPositionOnPage(t){const s=this._getViewportScrollPosition(),e=R(t)?t.touches[0]||t.changedTouches[0]||{pageX:0,pageY:0}:t,i=e.pageX-s.left,n=e.pageY-s.top;if(this._ownerSVGElement){const o=this._ownerSVGElement.getScreenCTM();if(o){const a=this._ownerSVGElement.createSVGPoint();return a.x=i,a.y=n,a.matrixTransform(o.inverse())}}return{x:i,y:n}}_getConstrainedPointerPosition(t){const s=this._dropContainer?this._dropContainer.lockAxis:null;let{x:e,y:i}=this.constrainPosition?this.constrainPosition(t,this,this._initialDomRect,this._pickupPositionInElement):t;if("x"===this.lockAxis||"x"===s?i=this._pickupPositionOnPage.y-(this.constrainPosition?this._pickupPositionInElement.y:0):("y"===this.lockAxis||"y"===s)&&(e=this._pickupPositionOnPage.x-(this.constrainPosition?this._pickupPositionInElement.x:0)),this._boundaryRect){const{x:n,y:o}=this.constrainPosition?{x:0,y:0}:this._pickupPositionInElement,a=this._boundaryRect,{width:h,height:c}=this._getPreviewRect(),d=a.top+o,g=a.bottom-(c-o);e=dt(e,a.left+n,a.right-(h-n)),i=dt(i,d,g)}return{x:e,y:i}}_updatePointerDirectionDelta(t){const{x:s,y:e}=t,i=this._pointerDirectionDelta,n=this._pointerPositionAtLastDirectionChange,o=Math.abs(s-n.x),a=Math.abs(e-n.y);return o>this._config.pointerDirectionChangeThreshold&&(i.x=s>n.x?1:-1,n.x=s),a>this._config.pointerDirectionChangeThreshold&&(i.y=e>n.y?1:-1,n.y=e),i}_toggleNativeDragInteractions(){if(!this._rootElement||!this._handles)return;const t=this._handles.length>0||!this.isDragging();t!==this._nativeInteractionsEnabled&&(this._nativeInteractionsEnabled=t,S(this._rootElement,t))}_removeRootElementListeners(t){t.removeEventListener("mousedown",this._pointerDown,L),t.removeEventListener("touchstart",this._pointerDown,lt),t.removeEventListener("dragstart",this._nativeDragStart,L)}_applyRootElementTransform(t,s){const e=1/this.scale,i=k(t*e,s*e),n=this._rootElement.style;null==this._initialTransform&&(this._initialTransform=n.transform&&"none"!=n.transform?n.transform:""),n.transform=T(i,this._initialTransform)}_applyPreviewTransform(t,s){const e=this._previewTemplate?.template?void 0:this._initialTransform,i=k(t,s);this._preview.setTransform(T(i,e))}_getDragDistance(t){const s=this._pickupPositionOnPage;return s?{x:t.x-s.x,y:t.y-s.y}:{x:0,y:0}}_cleanupCachedDimensions(){this._boundaryRect=this._previewRect=void 0,this._parentPositions.clear()}_containInsideBoundaryOnResize(){let{x:t,y:s}=this._passiveTransform;if(0===t&&0===s||this.isDragging()||!this._boundaryElement)return;const e=this._rootElement.getBoundingClientRect(),i=this._boundaryElement.getBoundingClientRect();if(0===i.width&&0===i.height||0===e.width&&0===e.height)return;const n=i.left-e.left,o=e.right-i.right,a=i.top-e.top,h=e.bottom-i.bottom;i.width>e.width?(n>0&&(t+=n),o>0&&(t-=o)):t=0,i.height>e.height?(a>0&&(s+=a),h>0&&(s-=h)):s=0,(t!==this._passiveTransform.x||s!==this._passiveTransform.y)&&this.setFreeDragPosition({y:s,x:t})}_getDragStartDelay(t){const s=this.dragStartDelay;return"number"==typeof s?s:R(t)?s.touch:s?s.mouse:0}_updateOnScroll(t){const s=this._parentPositions.handleScroll(t);if(s){const e=(0,D.Fb)(t);this._boundaryRect&&e!==this._boundaryElement&&e.contains(this._boundaryElement)&&E(this._boundaryRect,s.top,s.left),this._pickupPositionOnPage.x+=s.left,this._pickupPositionOnPage.y+=s.top,this._dropContainer||(this._activeTransform.x-=s.left,this._activeTransform.y-=s.top,this._applyRootElementTransform(this._activeTransform.x,this._activeTransform.y))}}_getViewportScrollPosition(){return this._parentPositions.positions.get(this._document)?.scrollPosition||this._parentPositions.getViewportScrollPosition()}_getShadowRoot(){return void 0===this._cachedShadowRoot&&(this._cachedShadowRoot=(0,D.KT)(this._rootElement)),this._cachedShadowRoot}_getPreviewInsertionPoint(t,s){const e=this._previewContainer||"global";if("parent"===e)return t;if("global"===e){const i=this._document;return s||i.fullscreenElement||i.webkitFullscreenElement||i.mozFullScreenElement||i.msFullscreenElement||i.body}return(0,w.i8)(e)}_getPreviewRect(){return(!this._previewRect||!this._previewRect.width&&!this._previewRect.height)&&(this._previewRect=this._preview?this._preview.getBoundingClientRect():this._initialDomRect),this._previewRect}_getTargetHandle(t){return this._handles.find(s=>t.target&&(t.target===s||s.contains(t.target)))}}function dt(r,t,s){return Math.max(t,Math.min(s,r))}function R(r){return"t"===r.type[0]}function gt(r){r.preventDefault()}function z(r,t,s){const e=I(t,r.length-1),i=I(s,r.length-1);if(e===i)return;const n=r[e],o=i<e?-1:1;for(let a=e;a!==i;a+=o)r[a]=r[a+o];r[i]=n}function Vt(r,t,s,e){const i=I(s,r.length-1),n=I(e,t.length);r.length&&t.splice(n,0,r.splice(i,1)[0])}function I(r,t){return Math.max(0,Math.min(t,r))}class pt{constructor(t){this._dragDropRegistry=t,this._itemPositions=[],this.orientation="vertical",this._previousSwap={drag:null,delta:0,overlaps:!1}}start(t){this.withItems(t)}sort(t,s,e,i){const n=this._itemPositions,o=this._getItemIndexFromPointerPosition(t,s,e,i);if(-1===o&&n.length>0)return null;const a="horizontal"===this.orientation,h=n.findIndex(u=>u.drag===t),c=n[o],g=c.clientRect,m=h>o?1:-1,P=this._getItemOffsetPx(n[h].clientRect,g,m),b=this._getSiblingOffsetPx(h,n,m),X=n.slice();return z(n,h,o),n.forEach((u,se)=>{if(X[se]===u)return;const yt=u.drag===t,W=yt?P:b,Et=yt?t.getPlaceholderElement():u.drag.getRootElement();u.offset+=W;const Rt=Math.round(u.offset*(1/u.drag.scale));a?(Et.style.transform=T(`translate3d(${Rt}px, 0, 0)`,u.initialTransform),E(u.clientRect,0,W)):(Et.style.transform=T(`translate3d(0, ${Rt}px, 0)`,u.initialTransform),E(u.clientRect,W,0))}),this._previousSwap.overlaps=H(g,s,e),this._previousSwap.drag=c.drag,this._previousSwap.delta=a?i.x:i.y,{previousIndex:h,currentIndex:o}}enter(t,s,e,i){const n=null==i||i<0?this._getItemIndexFromPointerPosition(t,s,e):i,o=this._activeDraggables,a=o.indexOf(t),h=t.getPlaceholderElement();let c=o[n];if(c===t&&(c=o[n+1]),!c&&(null==n||-1===n||n<o.length-1)&&this._shouldEnterAsFirstChild(s,e)&&(c=o[0]),a>-1&&o.splice(a,1),c&&!this._dragDropRegistry.isDragging(c)){const d=c.getRootElement();d.parentElement.insertBefore(h,d),o.splice(n,0,t)}else this._element.appendChild(h),o.push(t);h.style.transform="",this._cacheItemPositions()}withItems(t){this._activeDraggables=t.slice(),this._cacheItemPositions()}withSortPredicate(t){this._sortPredicate=t}reset(){this._activeDraggables?.forEach(t=>{const s=t.getRootElement();if(s){const e=this._itemPositions.find(i=>i.drag===t)?.initialTransform;s.style.transform=e||""}}),this._itemPositions=[],this._activeDraggables=[],this._previousSwap.drag=null,this._previousSwap.delta=0,this._previousSwap.overlaps=!1}getActiveItemsSnapshot(){return this._activeDraggables}getItemIndex(t){return("horizontal"===this.orientation&&"rtl"===this.direction?this._itemPositions.slice().reverse():this._itemPositions).findIndex(e=>e.drag===t)}updateOnScroll(t,s){this._itemPositions.forEach(({clientRect:e})=>{E(e,t,s)}),this._itemPositions.forEach(({drag:e})=>{this._dragDropRegistry.isDragging(e)&&e._sortFromLastPointerPosition()})}withElementContainer(t){this._element=t}_cacheItemPositions(){const t="horizontal"===this.orientation;this._itemPositions=this._activeDraggables.map(s=>{const e=s.getVisibleElement();return{drag:s,offset:0,initialTransform:e.style.transform||"",clientRect:B(e)}}).sort((s,e)=>t?s.clientRect.left-e.clientRect.left:s.clientRect.top-e.clientRect.top)}_getItemOffsetPx(t,s,e){const i="horizontal"===this.orientation;let n=i?s.left-t.left:s.top-t.top;return-1===e&&(n+=i?s.width-t.width:s.height-t.height),n}_getSiblingOffsetPx(t,s,e){const i="horizontal"===this.orientation,n=s[t].clientRect,o=s[t+-1*e];let a=n[i?"width":"height"]*e;if(o){const h=i?"left":"top",c=i?"right":"bottom";-1===e?a-=o.clientRect[h]-n[c]:a+=n[h]-o.clientRect[c]}return a}_shouldEnterAsFirstChild(t,s){if(!this._activeDraggables.length)return!1;const e=this._itemPositions,i="horizontal"===this.orientation;if(e[0].drag!==this._activeDraggables[0]){const o=e[e.length-1].clientRect;return i?t>=o.right:s>=o.bottom}{const o=e[0].clientRect;return i?t<=o.left:s<=o.top}}_getItemIndexFromPointerPosition(t,s,e,i){const n="horizontal"===this.orientation,o=this._itemPositions.findIndex(({drag:a,clientRect:h})=>a!==t&&((!i||a!==this._previousSwap.drag||!this._previousSwap.overlaps||(n?i.x:i.y)!==this._previousSwap.delta)&&(n?s>=Math.floor(h.left)&&s<Math.floor(h.right):e>=Math.floor(h.top)&&e<Math.floor(h.bottom))));return-1!==o&&this._sortPredicate(o,t)?o:-1}}class Kt{constructor(t,s){this._document=t,this._dragDropRegistry=s,this._previousSwap={drag:null,deltaX:0,deltaY:0,overlaps:!1},this._relatedNodes=[]}start(t){const s=this._element.childNodes;this._relatedNodes=[];for(let e=0;e<s.length;e++){const i=s[e];this._relatedNodes.push([i,i.nextSibling])}this.withItems(t)}sort(t,s,e,i){const n=this._getItemIndexFromPointerPosition(t,s,e),o=this._previousSwap;if(-1===n||this._activeItems[n]===t)return null;const a=this._activeItems[n];if(o.drag===a&&o.overlaps&&o.deltaX===i.x&&o.deltaY===i.y)return null;const h=this.getItemIndex(t),c=t.getPlaceholderElement(),d=a.getRootElement();n>h?d.after(c):d.before(c),z(this._activeItems,h,n);const g=this._getRootNode().elementFromPoint(s,e);return o.deltaX=i.x,o.deltaY=i.y,o.drag=a,o.overlaps=d===g||d.contains(g),{previousIndex:h,currentIndex:n}}enter(t,s,e,i){let n=null==i||i<0?this._getItemIndexFromPointerPosition(t,s,e):i;-1===n&&(n=this._getClosestItemIndexToPointer(t,s,e));const o=this._activeItems[n],a=this._activeItems.indexOf(t);a>-1&&this._activeItems.splice(a,1),o&&!this._dragDropRegistry.isDragging(o)?(this._activeItems.splice(n,0,t),o.getRootElement().before(t.getPlaceholderElement())):(this._activeItems.push(t),this._element.appendChild(t.getPlaceholderElement()))}withItems(t){this._activeItems=t.slice()}withSortPredicate(t){this._sortPredicate=t}reset(){const t=this._element,s=this._previousSwap;for(let e=this._relatedNodes.length-1;e>-1;e--){const[i,n]=this._relatedNodes[e];i.parentNode===t&&i.nextSibling!==n&&(null===n?t.appendChild(i):n.parentNode===t&&t.insertBefore(i,n))}this._relatedNodes=[],this._activeItems=[],s.drag=null,s.deltaX=s.deltaY=0,s.overlaps=!1}getActiveItemsSnapshot(){return this._activeItems}getItemIndex(t){return this._activeItems.indexOf(t)}updateOnScroll(){this._activeItems.forEach(t=>{this._dragDropRegistry.isDragging(t)&&t._sortFromLastPointerPosition()})}withElementContainer(t){t!==this._element&&(this._element=t,this._rootNode=void 0)}_getItemIndexFromPointerPosition(t,s,e){const i=this._getRootNode().elementFromPoint(Math.floor(s),Math.floor(e)),n=i?this._activeItems.findIndex(o=>{const a=o.getRootElement();return i===a||a.contains(i)}):-1;return-1!==n&&this._sortPredicate(n,t)?n:-1}_getRootNode(){return this._rootNode||(this._rootNode=(0,D.KT)(this._element)||this._document),this._rootNode}_getClosestItemIndexToPointer(t,s,e){if(0===this._activeItems.length)return-1;if(1===this._activeItems.length)return 0;let i=1/0,n=-1;for(let o=0;o<this._activeItems.length;o++){const a=this._activeItems[o];if(a!==t){const{x:h,y:c}=a.getRootElement().getBoundingClientRect(),d=Math.hypot(s-h,e-c);d<i&&(i=d,n=o)}}return n}}var v=function(r){return r[r.NONE=0]="NONE",r[r.UP=1]="UP",r[r.DOWN=2]="DOWN",r}(v||{}),f=function(r){return r[r.NONE=0]="NONE",r[r.LEFT=1]="LEFT",r[r.RIGHT=2]="RIGHT",r}(f||{});class Xt{constructor(t,s,e,i,n){this._dragDropRegistry=s,this._ngZone=i,this._viewportRuler=n,this.disabled=!1,this.sortingDisabled=!1,this.autoScrollDisabled=!1,this.autoScrollStep=2,this.enterPredicate=()=>!0,this.sortPredicate=()=>!0,this.beforeStarted=new _.B,this.entered=new _.B,this.exited=new _.B,this.dropped=new _.B,this.sorted=new _.B,this.receivingStarted=new _.B,this.receivingStopped=new _.B,this._isDragging=!1,this._draggables=[],this._siblings=[],this._activeSiblings=new Set,this._viewportScrollSubscription=y.yU.EMPTY,this._verticalScrollDirection=v.NONE,this._horizontalScrollDirection=f.NONE,this._stopScrollTimers=new _.B,this._cachedShadowRoot=null,this._scrollableElements=[],this._direction="ltr",this._startScrollInterval=()=>{this._stopScrolling(),function Tt(r=0,t=Ct.E){return r<0&&(r=0),(0,xt.O)(r,r,t)}(0,kt.X).pipe((0,x.Q)(this._stopScrollTimers)).subscribe(()=>{const a=this._scrollNode,h=this.autoScrollStep;this._verticalScrollDirection===v.UP?a.scrollBy(0,-h):this._verticalScrollDirection===v.DOWN&&a.scrollBy(0,h),this._horizontalScrollDirection===f.LEFT?a.scrollBy(-h,0):this._horizontalScrollDirection===f.RIGHT&&a.scrollBy(h,0)})};const o=this.element=(0,w.i8)(t);this._document=e,this.withOrientation("vertical").withElementContainer(o),s.registerDropContainer(this),this._parentPositions=new it(e)}dispose(){this._stopScrolling(),this._stopScrollTimers.complete(),this._viewportScrollSubscription.unsubscribe(),this.beforeStarted.complete(),this.entered.complete(),this.exited.complete(),this.dropped.complete(),this.sorted.complete(),this.receivingStarted.complete(),this.receivingStopped.complete(),this._activeSiblings.clear(),this._scrollNode=null,this._parentPositions.clear(),this._dragDropRegistry.removeDropContainer(this)}isDragging(){return this._isDragging}start(){this._draggingStarted(),this._notifyReceivingSiblings()}enter(t,s,e,i){this._draggingStarted(),null==i&&this.sortingDisabled&&(i=this._draggables.indexOf(t)),this._sortStrategy.enter(t,s,e,i),this._cacheParentPositions(),this._notifyReceivingSiblings(),this.entered.next({item:t,container:this,currentIndex:this.getItemIndex(t)})}exit(t){this._reset(),this.exited.next({item:t,container:this})}drop(t,s,e,i,n,o,a,h={}){this._reset(),this.dropped.next({item:t,currentIndex:s,previousIndex:e,container:this,previousContainer:i,isPointerOverContainer:n,distance:o,dropPoint:a,event:h})}withItems(t){const s=this._draggables;return this._draggables=t,t.forEach(e=>e._withDropContainer(this)),this.isDragging()&&(s.filter(i=>i.isDragging()).every(i=>-1===t.indexOf(i))?this._reset():this._sortStrategy.withItems(this._draggables)),this}withDirection(t){return this._direction=t,this._sortStrategy instanceof pt&&(this._sortStrategy.direction=t),this}connectedTo(t){return this._siblings=t.slice(),this}withOrientation(t){if("mixed"===t)this._sortStrategy=new Kt(this._document,this._dragDropRegistry);else{const s=new pt(this._dragDropRegistry);s.direction=this._direction,s.orientation=t,this._sortStrategy=s}return this._sortStrategy.withElementContainer(this._container),this._sortStrategy.withSortPredicate((s,e)=>this.sortPredicate(s,e,this)),this}withScrollableParents(t){const s=this._container;return this._scrollableElements=-1===t.indexOf(s)?[s,...t]:t.slice(),this}withElementContainer(t){if(t===this._container)return this;(0,w.i8)(this.element);const e=this._scrollableElements.indexOf(this._container),i=this._scrollableElements.indexOf(t);return e>-1&&this._scrollableElements.splice(e,1),i>-1&&this._scrollableElements.splice(i,1),this._sortStrategy&&this._sortStrategy.withElementContainer(t),this._cachedShadowRoot=null,this._scrollableElements.unshift(t),this._container=t,this}getScrollableParents(){return this._scrollableElements}getItemIndex(t){return this._isDragging?this._sortStrategy.getItemIndex(t):this._draggables.indexOf(t)}isReceiving(){return this._activeSiblings.size>0}_sortItem(t,s,e,i){if(this.sortingDisabled||!this._domRect||!et(this._domRect,.05,s,e))return;const n=this._sortStrategy.sort(t,s,e,i);n&&this.sorted.next({previousIndex:n.previousIndex,currentIndex:n.currentIndex,container:this,item:t})}_startScrollingIfNecessary(t,s){if(this.autoScrollDisabled)return;let e,i=v.NONE,n=f.NONE;if(this._parentPositions.positions.forEach((o,a)=>{a===this._document||!o.clientRect||e||et(o.clientRect,.05,t,s)&&([i,n]=function Wt(r,t,s,e,i){const n=ft(t,i),o=mt(t,e);let a=v.NONE,h=f.NONE;if(n){const c=r.scrollTop;n===v.UP?c>0&&(a=v.UP):r.scrollHeight-c>r.clientHeight&&(a=v.DOWN)}if(o){const c=r.scrollLeft;"rtl"===s?o===f.RIGHT?c<0&&(h=f.RIGHT):r.scrollWidth+c>r.clientWidth&&(h=f.LEFT):o===f.LEFT?c>0&&(h=f.LEFT):r.scrollWidth-c>r.clientWidth&&(h=f.RIGHT)}return[a,h]}(a,o.clientRect,this._direction,t,s),(i||n)&&(e=a))}),!i&&!n){const{width:o,height:a}=this._viewportRuler.getViewportSize(),h={width:o,height:a,top:0,right:o,bottom:a,left:0};i=ft(h,s),n=mt(h,t),e=window}e&&(i!==this._verticalScrollDirection||n!==this._horizontalScrollDirection||e!==this._scrollNode)&&(this._verticalScrollDirection=i,this._horizontalScrollDirection=n,this._scrollNode=e,(i||n)&&e?this._ngZone.runOutsideAngular(this._startScrollInterval):this._stopScrolling())}_stopScrolling(){this._stopScrollTimers.next()}_draggingStarted(){const t=this._container.style;this.beforeStarted.next(),this._isDragging=!0,this._initialScrollSnap=t.msScrollSnapType||t.scrollSnapType||"",t.scrollSnapType=t.msScrollSnapType="none",this._sortStrategy.start(this._draggables),this._cacheParentPositions(),this._viewportScrollSubscription.unsubscribe(),this._listenToScrollEvents()}_cacheParentPositions(){this._parentPositions.cache(this._scrollableElements),this._domRect=this._parentPositions.positions.get(this._container).clientRect}_reset(){this._isDragging=!1;const t=this._container.style;t.scrollSnapType=t.msScrollSnapType=this._initialScrollSnap,this._siblings.forEach(s=>s._stopReceiving(this)),this._sortStrategy.reset(),this._stopScrolling(),this._viewportScrollSubscription.unsubscribe(),this._parentPositions.clear()}_isOverContainer(t,s){return null!=this._domRect&&H(this._domRect,t,s)}_getSiblingContainerFromPosition(t,s,e){return this._siblings.find(i=>i._canReceive(t,s,e))}_canReceive(t,s,e){if(!this._domRect||!H(this._domRect,s,e)||!this.enterPredicate(t,this))return!1;const i=this._getShadowRoot().elementFromPoint(s,e);return!!i&&(i===this._container||this._container.contains(i))}_startReceiving(t,s){const e=this._activeSiblings;!e.has(t)&&s.every(i=>this.enterPredicate(i,this)||this._draggables.indexOf(i)>-1)&&(e.add(t),this._cacheParentPositions(),this._listenToScrollEvents(),this.receivingStarted.next({initiator:t,receiver:this,items:s}))}_stopReceiving(t){this._activeSiblings.delete(t),this._viewportScrollSubscription.unsubscribe(),this.receivingStopped.next({initiator:t,receiver:this})}_listenToScrollEvents(){this._viewportScrollSubscription=this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(t=>{if(this.isDragging()){const s=this._parentPositions.handleScroll(t);s&&this._sortStrategy.updateOnScroll(s.top,s.left)}else this.isReceiving()&&this._cacheParentPositions()})}_getShadowRoot(){if(!this._cachedShadowRoot){const t=(0,D.KT)(this._container);this._cachedShadowRoot=t||this._document}return this._cachedShadowRoot}_notifyReceivingSiblings(){const t=this._sortStrategy.getActiveItemsSnapshot().filter(s=>s.isDragging());this._siblings.forEach(s=>s._startReceiving(this,t))}}function ft(r,t){const{top:s,bottom:e,height:i}=r,n=.05*i;return t>=s-n&&t<=s+n?v.UP:t>=e-n&&t<=e+n?v.DOWN:v.NONE}function mt(r,t){const{left:s,right:e,width:i}=r,n=.05*i;return t>=s-n&&t<=s+n?f.LEFT:t>=e-n&&t<=e+n?f.RIGHT:f.NONE}const O=(0,D.BQ)({passive:!1,capture:!0}),M=new Set;let Zt=(()=>{var r;class t{}return(r=t).\u0275fac=function(e){return new(e||r)},r.\u0275cmp=l.VBU({type:r,selectors:[["ng-component"]],hostAttrs:["cdk-drag-resets-container",""],standalone:!0,features:[l.aNF],decls:0,vars:0,template:function(e,i){},styles:["@layer cdk-resets{.cdk-drag-preview{background:none;border:none;padding:0;color:inherit;inset:auto}}.cdk-drag-placeholder *,.cdk-drag-preview *{pointer-events:none !important}"],encapsulation:2,changeDetection:0}),t})(),Qt=(()=>{var r;class t{constructor(e,i){this._ngZone=e,this._appRef=(0,l.WQX)(l.o8S),this._environmentInjector=(0,l.WQX)(l.uvJ),this._dropInstances=new Set,this._dragInstances=new Set,this._activeDragInstances=(0,l.vPA)([]),this._globalListeners=new Map,this._draggingPredicate=n=>n.isDragging(),this.pointerMove=new _.B,this.pointerUp=new _.B,this.scroll=new _.B,this._preventDefaultWhileDragging=n=>{this._activeDragInstances().length>0&&n.preventDefault()},this._persistentTouchmoveListener=n=>{this._activeDragInstances().length>0&&(this._activeDragInstances().some(this._draggingPredicate)&&n.preventDefault(),this.pointerMove.next(n))},this._document=i}registerDropContainer(e){this._dropInstances.has(e)||this._dropInstances.add(e)}registerDragItem(e){this._dragInstances.add(e),1===this._dragInstances.size&&this._ngZone.runOutsideAngular(()=>{this._document.addEventListener("touchmove",this._persistentTouchmoveListener,O)})}removeDropContainer(e){this._dropInstances.delete(e)}removeDragItem(e){this._dragInstances.delete(e),this.stopDragging(e),0===this._dragInstances.size&&this._document.removeEventListener("touchmove",this._persistentTouchmoveListener,O)}startDragging(e,i){if(!(this._activeDragInstances().indexOf(e)>-1)&&(this._loadResets(),this._activeDragInstances.update(n=>[...n,e]),1===this._activeDragInstances().length)){const n=i.type.startsWith("touch");this._globalListeners.set(n?"touchend":"mouseup",{handler:o=>this.pointerUp.next(o),options:!0}).set("scroll",{handler:o=>this.scroll.next(o),options:!0}).set("selectstart",{handler:this._preventDefaultWhileDragging,options:O}),n||this._globalListeners.set("mousemove",{handler:o=>this.pointerMove.next(o),options:O}),this._ngZone.runOutsideAngular(()=>{this._globalListeners.forEach((o,a)=>{this._document.addEventListener(a,o.handler,o.options)})})}}stopDragging(e){this._activeDragInstances.update(i=>{const n=i.indexOf(e);return n>-1?(i.splice(n,1),[...i]):i}),0===this._activeDragInstances().length&&this._clearGlobalListeners()}isDragging(e){return this._activeDragInstances().indexOf(e)>-1}scrolled(e){const i=[this.scroll];return e&&e!==this._document&&i.push(new Q.c(n=>this._ngZone.runOutsideAngular(()=>{const a=h=>{this._activeDragInstances().length&&n.next(h)};return e.addEventListener("scroll",a,!0),()=>{e.removeEventListener("scroll",a,!0)}}))),(0,A.h)(...i)}ngOnDestroy(){this._dragInstances.forEach(e=>this.removeDragItem(e)),this._dropInstances.forEach(e=>this.removeDropContainer(e)),this._clearGlobalListeners(),this.pointerMove.complete(),this.pointerUp.complete()}_clearGlobalListeners(){this._globalListeners.forEach((e,i)=>{this._document.removeEventListener(i,e.handler,e.options)}),this._globalListeners.clear()}_loadResets(){if(!M.has(this._appRef)){M.add(this._appRef);const e=(0,l.a0P)(Zt,{environmentInjector:this._environmentInjector});this._appRef.onDestroy(()=>{M.delete(this._appRef),0===M.size&&e.destroy()})}}}return(r=t).\u0275fac=function(e){return new(e||r)(l.KVO(l.SKi),l.KVO(N.qQ))},r.\u0275prov=l.jDH({token:r,factory:r.\u0275fac,providedIn:"root"}),t})();const $t={dragStartThreshold:5,pointerDirectionChangeThreshold:5};let V=(()=>{var r;class t{constructor(e,i,n,o){this._document=e,this._ngZone=i,this._viewportRuler=n,this._dragDropRegistry=o}createDrag(e,i=$t){return new zt(e,i,this._document,this._ngZone,this._viewportRuler,this._dragDropRegistry)}createDropList(e){return new Xt(e,this._dragDropRegistry,this._document,this._ngZone,this._viewportRuler)}}return(r=t).\u0275fac=function(e){return new(e||r)(l.KVO(N.qQ),l.KVO(l.SKi),l.KVO(F.Xj),l.KVO(Qt))},r.\u0275prov=l.jDH({token:r,factory:r.\u0275fac,providedIn:"root"}),t})();const C=new l.nKC("CDK_DRAG_PARENT"),vt=new l.nKC("CdkDragHandle");let Dt=(()=>{var r;class t{get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._stateChanges.next(this)}constructor(e,i){this.element=e,this._parentDrag=i,this._stateChanges=new _.B,this._disabled=!1,i?._addHandle(this)}ngOnDestroy(){this._parentDrag?._removeHandle(this),this._stateChanges.complete()}}return(r=t).\u0275fac=function(e){return new(e||r)(l.rXU(l.aKT),l.rXU(C,12))},r.\u0275dir=l.FsC({type:r,selectors:[["","cdkDragHandle",""]],hostAttrs:[1,"cdk-drag-handle"],inputs:{disabled:[2,"cdkDragHandleDisabled","disabled",l.L39]},standalone:!0,features:[l.Jv_([{provide:vt,useExisting:r}]),l.GFd]}),t})();const wt=new l.nKC("CDK_DRAG_CONFIG"),Pt=new l.nKC("CdkDropList");let bt=(()=>{var r;class t{get disabled(){return this._disabled||this.dropContainer&&this.dropContainer.disabled}set disabled(e){this._disabled=e,this._dragRef.disabled=this._disabled}constructor(e,i,n,o,a,h,c,d,g,m,P){this.element=e,this.dropContainer=i,this._ngZone=o,this._viewContainerRef=a,this._dir=c,this._changeDetectorRef=g,this._selfHandle=m,this._parentDrag=P,this._destroyed=new _.B,this._handles=new Lt.t([]),this.scale=1,this.started=new l.bkB,this.released=new l.bkB,this.ended=new l.bkB,this.entered=new l.bkB,this.exited=new l.bkB,this.dropped=new l.bkB,this.moved=new Q.c(b=>{const X=this._dragRef.moved.pipe((0,Ot.T)(u=>({source:this,pointerPosition:u.pointerPosition,event:u.event,delta:u.delta,distance:u.distance}))).subscribe(b);return()=>{X.unsubscribe()}}),this._injector=(0,l.WQX)(l.zZn),this._dragRef=d.createDrag(e,{dragStartThreshold:h&&null!=h.dragStartThreshold?h.dragStartThreshold:5,pointerDirectionChangeThreshold:h&&null!=h.pointerDirectionChangeThreshold?h.pointerDirectionChangeThreshold:5,zIndex:h?.zIndex}),this._dragRef.data=this,t._dragInstances.push(this),h&&this._assignDefaults(h),i&&(this._dragRef._withDropContainer(i._dropListRef),i.addItem(this),i._dropListRef.beforeStarted.pipe((0,x.Q)(this._destroyed)).subscribe(()=>{this._dragRef.scale=this.scale})),this._syncInputs(this._dragRef),this._handleEvents(this._dragRef)}getPlaceholderElement(){return this._dragRef.getPlaceholderElement()}getRootElement(){return this._dragRef.getRootElement()}reset(){this._dragRef.reset()}getFreeDragPosition(){return this._dragRef.getFreeDragPosition()}setFreeDragPosition(e){this._dragRef.setFreeDragPosition(e)}ngAfterViewInit(){(0,l.mal)(()=>{this._updateRootElement(),this._setupHandlesListener(),this._dragRef.scale=this.scale,this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)},{injector:this._injector})}ngOnChanges(e){const i=e.rootElementSelector,n=e.freeDragPosition;i&&!i.firstChange&&this._updateRootElement(),this._dragRef.scale=this.scale,n&&!n.firstChange&&this.freeDragPosition&&this._dragRef.setFreeDragPosition(this.freeDragPosition)}ngOnDestroy(){this.dropContainer&&this.dropContainer.removeItem(this);const e=t._dragInstances.indexOf(this);e>-1&&t._dragInstances.splice(e,1),this._ngZone.runOutsideAngular(()=>{this._handles.complete(),this._destroyed.next(),this._destroyed.complete(),this._dragRef.dispose()})}_addHandle(e){const i=this._handles.getValue();i.push(e),this._handles.next(i)}_removeHandle(e){const i=this._handles.getValue(),n=i.indexOf(e);n>-1&&(i.splice(n,1),this._handles.next(i))}_setPreviewTemplate(e){this._previewTemplate=e}_resetPreviewTemplate(e){e===this._previewTemplate&&(this._previewTemplate=null)}_setPlaceholderTemplate(e){this._placeholderTemplate=e}_resetPlaceholderTemplate(e){e===this._placeholderTemplate&&(this._placeholderTemplate=null)}_updateRootElement(){const e=this.element.nativeElement;let i=e;this.rootElementSelector&&(i=void 0!==e.closest?e.closest(this.rootElementSelector):e.parentElement?.closest(this.rootElementSelector)),this._dragRef.withRootElement(i||e)}_getBoundaryElement(){const e=this.boundaryElement;return e?"string"==typeof e?this.element.nativeElement.closest(e):(0,w.i8)(e):null}_syncInputs(e){e.beforeStarted.subscribe(()=>{if(!e.isDragging()){const i=this._dir,n=this.dragStartDelay,o=this._placeholderTemplate?{template:this._placeholderTemplate.templateRef,context:this._placeholderTemplate.data,viewContainer:this._viewContainerRef}:null,a=this._previewTemplate?{template:this._previewTemplate.templateRef,context:this._previewTemplate.data,matchSize:this._previewTemplate.matchSize,viewContainer:this._viewContainerRef}:null;e.disabled=this.disabled,e.lockAxis=this.lockAxis,e.scale=this.scale,e.dragStartDelay="object"==typeof n&&n?n:(0,w.OE)(n),e.constrainPosition=this.constrainPosition,e.previewClass=this.previewClass,e.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(o).withPreviewTemplate(a).withPreviewContainer(this.previewContainer||"global"),i&&e.withDirection(i.value)}}),e.beforeStarted.pipe((0,Mt.s)(1)).subscribe(()=>{if(this._parentDrag)return void e.withParent(this._parentDrag._dragRef);let i=this.element.nativeElement.parentElement;for(;i;){if(i.classList.contains("cdk-drag")){e.withParent(t._dragInstances.find(n=>n.element.nativeElement===i)?._dragRef||null);break}i=i.parentElement}})}_handleEvents(e){e.started.subscribe(i=>{this.started.emit({source:this,event:i.event}),this._changeDetectorRef.markForCheck()}),e.released.subscribe(i=>{this.released.emit({source:this,event:i.event})}),e.ended.subscribe(i=>{this.ended.emit({source:this,distance:i.distance,dropPoint:i.dropPoint,event:i.event}),this._changeDetectorRef.markForCheck()}),e.entered.subscribe(i=>{this.entered.emit({container:i.container.data,item:this,currentIndex:i.currentIndex})}),e.exited.subscribe(i=>{this.exited.emit({container:i.container.data,item:this})}),e.dropped.subscribe(i=>{this.dropped.emit({previousIndex:i.previousIndex,currentIndex:i.currentIndex,previousContainer:i.previousContainer.data,container:i.container.data,isPointerOverContainer:i.isPointerOverContainer,item:this,distance:i.distance,dropPoint:i.dropPoint,event:i.event})})}_assignDefaults(e){const{lockAxis:i,dragStartDelay:n,constrainPosition:o,previewClass:a,boundaryElement:h,draggingDisabled:c,rootElementSelector:d,previewContainer:g}=e;this.disabled=c??!1,this.dragStartDelay=n||0,i&&(this.lockAxis=i),o&&(this.constrainPosition=o),a&&(this.previewClass=a),h&&(this.boundaryElement=h),d&&(this.rootElementSelector=d),g&&(this.previewContainer=g)}_setupHandlesListener(){this._handles.pipe((0,Nt.M)(e=>{const i=e.map(n=>n.element);this._selfHandle&&this.rootElementSelector&&i.push(this.element),this._dragRef.withHandles(i)}),(0,Ft.n)(e=>(0,A.h)(...e.map(i=>i._stateChanges.pipe((0,$.Z)(i))))),(0,x.Q)(this._destroyed)).subscribe(e=>{const i=this._dragRef,n=e.element.nativeElement;e.disabled?i.disableHandle(n):i.enableHandle(n)})}}return(r=t)._dragInstances=[],r.\u0275fac=function(e){return new(e||r)(l.rXU(l.aKT),l.rXU(Pt,12),l.rXU(N.qQ),l.rXU(l.SKi),l.rXU(l.c1b),l.rXU(wt,8),l.rXU(j.dS,8),l.rXU(V),l.rXU(l.gRc),l.rXU(vt,10),l.rXU(C,12))},r.\u0275dir=l.FsC({type:r,selectors:[["","cdkDrag",""]],hostAttrs:[1,"cdk-drag"],hostVars:4,hostBindings:function(e,i){2&e&&l.AVh("cdk-drag-disabled",i.disabled)("cdk-drag-dragging",i._dragRef.isDragging())},inputs:{data:[0,"cdkDragData","data"],lockAxis:[0,"cdkDragLockAxis","lockAxis"],rootElementSelector:[0,"cdkDragRootElement","rootElementSelector"],boundaryElement:[0,"cdkDragBoundary","boundaryElement"],dragStartDelay:[0,"cdkDragStartDelay","dragStartDelay"],freeDragPosition:[0,"cdkDragFreeDragPosition","freeDragPosition"],disabled:[2,"cdkDragDisabled","disabled",l.L39],constrainPosition:[0,"cdkDragConstrainPosition","constrainPosition"],previewClass:[0,"cdkDragPreviewClass","previewClass"],previewContainer:[0,"cdkDragPreviewContainer","previewContainer"],scale:[2,"cdkDragScale","scale",l.Udg]},outputs:{started:"cdkDragStarted",released:"cdkDragReleased",ended:"cdkDragEnded",entered:"cdkDragEntered",exited:"cdkDragExited",dropped:"cdkDragDropped",moved:"cdkDragMoved"},exportAs:["cdkDrag"],standalone:!0,features:[l.Jv_([{provide:C,useExisting:r}]),l.GFd,l.OA$]}),t})();const K=new l.nKC("CdkDropListGroup");let Yt=0,St=(()=>{var r;class t{get disabled(){return this._disabled||!!this._group&&this._group.disabled}set disabled(e){this._dropListRef.disabled=this._disabled=e}constructor(e,i,n,o,a,h,c){this.element=e,this._changeDetectorRef=n,this._scrollDispatcher=o,this._dir=a,this._group=h,this._destroyed=new _.B,this.connectedTo=[],this.id="cdk-drop-list-"+Yt++,this.enterPredicate=()=>!0,this.sortPredicate=()=>!0,this.dropped=new l.bkB,this.entered=new l.bkB,this.exited=new l.bkB,this.sorted=new l.bkB,this._unsortedItems=new Set,this._dropListRef=i.createDropList(e),this._dropListRef.data=this,c&&this._assignDefaults(c),this._dropListRef.enterPredicate=(d,g)=>this.enterPredicate(d.data,g.data),this._dropListRef.sortPredicate=(d,g,m)=>this.sortPredicate(d,g.data,m.data),this._setupInputSyncSubscription(this._dropListRef),this._handleEvents(this._dropListRef),t._dropLists.push(this),h&&h._items.add(this)}addItem(e){this._unsortedItems.add(e),this._dropListRef.isDragging()&&this._syncItemsWithRef()}removeItem(e){this._unsortedItems.delete(e),this._dropListRef.isDragging()&&this._syncItemsWithRef()}getSortedItems(){return Array.from(this._unsortedItems).sort((e,i)=>e._dragRef.getVisibleElement().compareDocumentPosition(i._dragRef.getVisibleElement())&Node.DOCUMENT_POSITION_FOLLOWING?-1:1)}ngOnDestroy(){const e=t._dropLists.indexOf(this);e>-1&&t._dropLists.splice(e,1),this._group&&this._group._items.delete(this),this._unsortedItems.clear(),this._dropListRef.dispose(),this._destroyed.next(),this._destroyed.complete()}_setupInputSyncSubscription(e){this._dir&&this._dir.change.pipe((0,$.Z)(this._dir.value),(0,x.Q)(this._destroyed)).subscribe(i=>e.withDirection(i)),e.beforeStarted.subscribe(()=>{const i=(0,w.FG)(this.connectedTo).map(n=>"string"==typeof n?t._dropLists.find(a=>a.id===n):n);if(this._group&&this._group._items.forEach(n=>{-1===i.indexOf(n)&&i.push(n)}),!this._scrollableParentsResolved){const n=this._scrollDispatcher.getAncestorScrollContainers(this.element).map(o=>o.getElementRef().nativeElement);this._dropListRef.withScrollableParents(n),this._scrollableParentsResolved=!0}if(this.elementContainerSelector){const n=this.element.nativeElement.querySelector(this.elementContainerSelector);e.withElementContainer(n)}e.disabled=this.disabled,e.lockAxis=this.lockAxis,e.sortingDisabled=this.sortingDisabled,e.autoScrollDisabled=this.autoScrollDisabled,e.autoScrollStep=(0,w.OE)(this.autoScrollStep,2),e.connectedTo(i.filter(n=>n&&n!==this).map(n=>n._dropListRef)).withOrientation(this.orientation)})}_handleEvents(e){e.beforeStarted.subscribe(()=>{this._syncItemsWithRef(),this._changeDetectorRef.markForCheck()}),e.entered.subscribe(i=>{this.entered.emit({container:this,item:i.item.data,currentIndex:i.currentIndex})}),e.exited.subscribe(i=>{this.exited.emit({container:this,item:i.item.data}),this._changeDetectorRef.markForCheck()}),e.sorted.subscribe(i=>{this.sorted.emit({previousIndex:i.previousIndex,currentIndex:i.currentIndex,container:this,item:i.item.data})}),e.dropped.subscribe(i=>{this.dropped.emit({previousIndex:i.previousIndex,currentIndex:i.currentIndex,previousContainer:i.previousContainer.data,container:i.container.data,item:i.item.data,isPointerOverContainer:i.isPointerOverContainer,distance:i.distance,dropPoint:i.dropPoint,event:i.event}),this._changeDetectorRef.markForCheck()}),(0,A.h)(e.receivingStarted,e.receivingStopped).subscribe(()=>this._changeDetectorRef.markForCheck())}_assignDefaults(e){const{lockAxis:i,draggingDisabled:n,sortingDisabled:o,listAutoScrollDisabled:a,listOrientation:h}=e;this.disabled=n??!1,this.sortingDisabled=o??!1,this.autoScrollDisabled=a??!1,this.orientation=h||"vertical",i&&(this.lockAxis=i)}_syncItemsWithRef(){this._dropListRef.withItems(this.getSortedItems().map(e=>e._dragRef))}}return(r=t)._dropLists=[],r.\u0275fac=function(e){return new(e||r)(l.rXU(l.aKT),l.rXU(V),l.rXU(l.gRc),l.rXU(F.R),l.rXU(j.dS,8),l.rXU(K,12),l.rXU(wt,8))},r.\u0275dir=l.FsC({type:r,selectors:[["","cdkDropList",""],["cdk-drop-list"]],hostAttrs:[1,"cdk-drop-list"],hostVars:7,hostBindings:function(e,i){2&e&&(l.BMQ("id",i.id),l.AVh("cdk-drop-list-disabled",i.disabled)("cdk-drop-list-dragging",i._dropListRef.isDragging())("cdk-drop-list-receiving",i._dropListRef.isReceiving()))},inputs:{connectedTo:[0,"cdkDropListConnectedTo","connectedTo"],data:[0,"cdkDropListData","data"],orientation:[0,"cdkDropListOrientation","orientation"],id:"id",lockAxis:[0,"cdkDropListLockAxis","lockAxis"],disabled:[2,"cdkDropListDisabled","disabled",l.L39],sortingDisabled:[2,"cdkDropListSortingDisabled","sortingDisabled",l.L39],enterPredicate:[0,"cdkDropListEnterPredicate","enterPredicate"],sortPredicate:[0,"cdkDropListSortPredicate","sortPredicate"],autoScrollDisabled:[2,"cdkDropListAutoScrollDisabled","autoScrollDisabled",l.L39],autoScrollStep:[0,"cdkDropListAutoScrollStep","autoScrollStep"],elementContainerSelector:[0,"cdkDropListElementContainer","elementContainerSelector"]},outputs:{dropped:"cdkDropListDropped",entered:"cdkDropListEntered",exited:"cdkDropListExited",sorted:"cdkDropListSorted"},exportAs:["cdkDropList"],standalone:!0,features:[l.Jv_([{provide:K,useValue:void 0},{provide:Pt,useExisting:r}]),l.GFd]}),t})(),ie=(()=>{var r;class t{}return(r=t).\u0275fac=function(e){return new(e||r)},r.\u0275mod=l.$C({type:r}),r.\u0275inj=l.G2t({providers:[V],imports:[F.Gj]}),t})()}}]);