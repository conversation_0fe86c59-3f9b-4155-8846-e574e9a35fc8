import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { Service } from '../../../dto/interface/service.interface';
import { ChooseUserInProjectComponent } from '../choose-user-in-project/choose-user-in-project.component';
import { ChooseProjectTimeTrackingComponent } from '../choose-project-time-tracking/choose-project-time-tracking.component';
import { InforUser } from 'app/dto/interface/inforUser.interface';
import { Project } from 'app/dto/interface/project.interface';
import { TimetrackingService } from '../../../service/timetracking.service';
import { ChangeDetectorRef, Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { StoreService } from 'app/service/store.service';
import { BehaviorSubject, Subscription } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SharedModule } from 'app/module/shared.module';
import { DatePickerModule, DateRangePickerModule, DateTimePickerModule } from '@syncfusion/ej2-angular-calendars';
import { AutoCompleteModule } from '@syncfusion/ej2-angular-dropdowns';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import moment from 'moment-timezone';
import { InnoSelectSearchServiceComponent } from 'app/component/inno-select-search-service/inno-select-search-service.component';
import { Client } from '../../../dto/interface/client.interface';
import { TimeTracking } from '../../../dto/interface/timeTracking.interface';
@Component({
  selector: 'app-dialog-add-time',
  standalone: true,
  imports: [DatePickerModule,
    SharedModule,
    MatMenuModule,
    AutoCompleteModule,
    DatePickerModule,
    MatMenuModule,
    DateRangePickerModule,
    DateTimePickerModule,
    MatFormFieldModule,
    // component
    ChooseProjectTimeTrackingComponent,
    InnoSelectSearchServiceComponent,
    InnoModalWrapperComponent,
    InnoSelectSearchProjectComponent,
    ChooseUserInProjectComponent,],
  templateUrl: './dialog-add-time.component.html',
  styleUrl: './dialog-add-time.component.scss'
})
export class DialogAddTimeComponent implements OnInit {
  _id: string = '';
  memberId!: string;
  isCloseMenu: boolean = false
  checked: boolean = true
  objectClient!: Client | null;
  objectProject!: Project | null;
  objectServices!: Service | null;
  searchServices = ''
  clientName: string = ''
  projectName: string = ''
  servicesName: string = ''
  nameService!: string;
  projectId!: string;
  timeTrackingId!: string
  serviceId: string = "";
  public previewWorkingInfo?: IFilterDropdownOption
  private _subscriptions: Subscription[] = [];
  localMoment = moment.tz(new Date(), moment.tz.guess());
  searchSubject = new BehaviorSubject<string>('');
  searchSubjectName = new BehaviorSubject<string>('');
  search = '';
  timeEnd!: string;
  description!: string;
  searchMember = ''
  name!: string;
  isrequiredProject: boolean = false
  isrequiredTime: boolean = false
  public selectedDate: any = new Date();
  project!: string;

  destroyRef = inject(DestroyRef);
  changeDetectorRefs = inject(ChangeDetectorRef);
  public _storeService = inject(StoreService)
  private _timeTrackingService = inject(TimetrackingService)

  static getComponent(): typeof DialogAddTimeComponent {
    return DialogAddTimeComponent;
  }
  constructor(public dialogRef: MatDialogRef<DialogAddTimeComponent>, @Inject(MAT_DIALOG_DATA) public data: any,) {

  }
  closeDialog() {
    this.dialogRef.close();
  }
  getNowUTC() {
    const now = new Date();
    return new Date(now.getTime() + (now.getTimezoneOffset() * 60000));
  }
  handleSearch(search: string) {
    this.searchSubject.next(search);
  }

  handleSearchName(search: string) {
    this.searchSubjectName.next(search);
  }

  onBlur(event: any) {
    if (this.timeEnd == "") {
      return;
    }
    else {

      let value = this.timeEnd;
      // Remove any non-numeric characters except the colon
      value = value.replace(/[^0-9:]/g, '');

      // Format to HH:MM
      if (value.indexOf(':') !== -1) {
        const parts = value.split(':');
        if (parts.length === 2 && parts[1] === '') {
          // Case where input is like "4:" (missing minutes)
          value = parts[0] + ':00';  // Default minutes to '00'
        } else if (parts.length > 2) {
          // Case where input contains more than one colon (e.g. "4::5")
          value = parts[0] + ':' + (parts[1] || '00');
        }
      }
      // Split into hours and minutes
      let [hours, minutes] = value.split(':');
      // If no minutes are entered, set it to '00'
      if (!minutes) {
        minutes = '00';
      }

      // Ensure hours are within 00-23 and minutes within 00-59
      if (hours && Number(hours) > 23) {
        hours = '23';
      } else if (hours && Number(hours) < 10 && hours.length === 1) {
        // If hours is a single digit, pad it with '0'
        hours = '0' + hours;
      }

      if (minutes && Number(minutes) > 59) {
        minutes = '59';
      } else if (minutes && Number(minutes) < 10 && minutes.length === 1) {
        // If minutes is a single digit, pad it with '0'
        minutes = '0' + minutes;
      }
      this.timeEnd = `${hours}:${minutes}`
    }

  }
  Update() {


    if (!this.timeEnd || this.timeEnd == "") {
      this.isrequiredTime = true
    }
    if (this.timeEnd) {
      let payload: TimeTracking = {
        id: this.timeTrackingId,
        memberId: this.memberId,
        date: this.selectedDate,
        serviceId: this.serviceId,
        clientId: this.objectClient?.id,
        service: null,
        description: this.description,
        projectId: this.projectId,
        userId: this._id,
        startTime: null,
        endTime: this.timeEnd,
        billable: this.checked

      }

      this._timeTrackingService.UpdateTimeTracking(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this.dialogRef.close(res)
        }
      }
      )
    }

  }

  confirm() {

    if (!this.objectProject) {
      this.isrequiredProject = true
    }
    if (!this.timeEnd || this.timeEnd == "") {
      this.isrequiredTime = true
    }
    if (this.objectProject && this.timeEnd) {
      let payload: TimeTracking = {
        memberId: null,
        date: this.selectedDate,
        serviceId: this.serviceId,
        clientId: this.objectClient?.id,
        service: null,
        description: this.description,
        projectId: this.objectProject.id,
        userId: this._id,
        startTime: null,
        endTime: this.timeEnd,
        billable: this.checked
      }
      this._timeTrackingService.CreateTimeTracking(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this.dialogRef.close(res)
        }
      }
      )
    }

  }
  handleSelectServices(item: IFilterDropdownOption) {
    this.serviceId = item.value
    this.servicesName = item.label
  }
  ngOnInit(): void {
    if (!this.data?.new) {
      const offsetInHours = this.localMoment.utcOffset() / 60;
      const utcDate = new Date(this.data);
      const hoursInUTC = utcDate.getHours();
      utcDate.setHours(hoursInUTC + offsetInHours);

      this.selectedDate = utcDate;
    }
    else {
      this.memberId = this.data.data.memberId
      this.timeTrackingId = this.data.data.id
      this.selectedDate = this.data.data.date
      this.timeEnd = this.data.data.endTime
      this.description = this.data.data.description
      this.objectClient = this.data.data?.client;
      this.projectId = this.data.data?.projectId;
      this.serviceId = this.data.data?.serviceId
      this.clientName = this.data.data?.client?.clientName
      this.projectName = this.data.data?.project?.projectName
      this.checked = this.data.data.billable
      this.servicesName = this.data.data.service ? this.data.data.service.serviceName : ""
      this.project = this.clientName + "/" + this.projectName + "/" + this.servicesName
      this.changeDetectorRefs.detectChanges();


    }
    this.name = this._storeService._InforUser.firstName + ' ' + this._storeService._InforUser.lastName
    this._id = this._storeService._InforUser.id

  }

  ngOnDestroy(): void {
    if (this._subscriptions) {

      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }

  cancel() {
    this.dialogRef.close();
  }


  stopPropagation(event: any) {
    event.stopPropagation();
  }
  ItemSelected(item: any) {
    this.objectProject = item.data;
    setTimeout(() => {
      this.project = (this.project + "/" + item.data.projectName);
      if (item.isClose == true) {
      }
      this.projectName = item.data.projectName
      this.project = this.clientName + "/" + this.projectName
      this.changeDetectorRefs.detectChanges();
    }, 100);
    this.projectId = this.objectProject!.id;
  }

  ItemSelectedMember(item: InforUser) {
    this.name = item.firstName + " " + item.lastName
    this._id = item.id

  }

  ItemSelectedClient(item: Client) {
    this.objectClient = item;
    this.clientName = item.clientName
    this.project = this.clientName

  }
  onSelectedDateChange(date: any) {
    this.selectedDate = date;

  }
  handleSelectProject(item: IFilterDropdownOption) {
    this.previewWorkingInfo = item
    this.projectName = item.label
    this.clientName = item.metadata.objectClient.clientName
    this.projectId = item.value
    this.objectClient = item.metadata.objectClient

    // Clear service selection when project changes
    this.servicesName = ""
    this.serviceId = ""
  }

  menuOpened() {
    this.isCloseMenu = false;
  }
  menuClosed() {
    this.isCloseMenu = true;
  }
  clearInput() {
    this.project = "";
    this.objectClient = null
    this.objectProject = null
    this.objectServices = null
  }
}
