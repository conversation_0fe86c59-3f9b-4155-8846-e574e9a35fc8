"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3653],{3653:(m,i,o)=>{o.r(i),o.d(i,{AddPermissionComponent:()=>l});var t=o(9842),r=o(1470),n=o(4438),_=o(4006);let l=(()=>{var a;class e{static getComponent(){return e}constructor(s){(0,t.A)(this,"dialogRef",void 0),this.dialogRef=s}closeDialog(){this.dialogRef.close()}}return a=e,(0,t.A)(e,"\u0275fac",function(s){return new(s||a)(n.rXU(_.CP))}),(0,t.A)(e,"\u0275cmp",n.VBU({type:a,selectors:[["app-add-permission"]],standalone:!0,features:[n.aNF],decls:1,vars:0,consts:[[3,"onClose"]],template:function(s,p){1&s&&(n.j41(0,"app-innobook-modal-wrapper",0),n.bIt("onClose",function(){return p.closeDialog()}),n.k0s())},dependencies:[r.j]})),e})()}}]);