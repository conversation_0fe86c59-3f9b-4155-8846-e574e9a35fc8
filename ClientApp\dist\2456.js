"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2456],{7656:(E,v,n)=>{n.d(v,{V:()=>h});var a=n(9842),p=n(4438),P=n(6146),b=n(9417);const T=["*"];function e(u,d){if(1&u){const s=p.RV6();p.j41(0,"input",5),p.bIt("change",function(_){p.eBV(s);const g=p.XpG();return p.Njj(g.handleChange(_))}),p.k0s()}if(2&u){const s=p.XpG();p.Y8G("checked",s.checked)("formControl",s.formControl)}}function C(u,d){if(1&u){const s=p.RV6();p.j41(0,"input",6),p.bIt("change",function(_){p.eBV(s);const g=p.XpG();return p.Njj(g.handle<PERSON>hange(_))}),p.k0s()}if(2&u){const s=p.XpG();p.Y8G("checked",s.checked)}}let h=(()=>{var u;class d{constructor(){(0,a.A)(this,"checked",void 0),(0,a.A)(this,"onChange",new p.bkB),(0,a.A)(this,"formControl",void 0),(0,a.A)(this,"errorMessages",void 0)}registerOnChange(c){}registerOnTouched(c){}setDisabledState(c){}writeValue(c){}handleChange(c){this.onChange.emit(c?.target?.checked??!1)}}return u=d,(0,a.A)(d,"\u0275fac",function(c){return new(c||u)}),(0,a.A)(d,"\u0275cmp",p.VBU({type:u,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[p.Jv_([{provide:b.kq,useExisting:(0,p.Rfq)(()=>u),multi:!0}]),p.aNF],ngContentSelectors:T,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(c,_){1&c&&(p.NAR(),p.j41(0,"div",0)(1,"label",1),p.DNE(2,e,1,2,"input",2)(3,C,1,1,"input",3),p.j41(4,"div",4),p.SdG(5),p.k0s()()()),2&c&&(p.R7$(2),p.vxM(_.formControl?2:3))},dependencies:[P.G,b.Zm,b.BC,b.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),d})()},5402:(E,v,n)=>{n.d(v,{Q:()=>a});var a=function(p){return p.Item="Item",p.Service="Service",p}(a||{})},6617:(E,v,n)=>{n.d(v,{p:()=>b});var a=n(9842),p=n(6473),P=n(4438);let b=(()=>{var T;class e{transform(h){return(0,p.ZV)(h)}}return T=e,(0,a.A)(e,"\u0275fac",function(h){return new(h||T)}),(0,a.A)(e,"\u0275pipe",P.EJ8({name:"formatNumber",type:T,pure:!0,standalone:!0})),e})()},3814:(E,v,n)=>{n.d(v,{v:()=>e});var a=n(467),p=n(9842),P=n(2716),b=n(7987),T=n(4438);let e=(()=>{var C;class h extends P.H{open(d){var s=this;return(0,a.A)(function*(){const c=yield Promise.all([n.e(1328),n.e(2076),n.e(7371)]).then(n.bind(n,2508));return s.matDialog.open(c.DialogModifyItemServiceComponent.getComponent(),{disableClose:!0,panelClass:"custom_dialog",data:d,width:"500px",scrollStrategy:new b.t0})})()}}return C=h,(0,p.A)(h,"\u0275fac",(()=>{let u;return function(s){return(u||(u=T.xGo(C)))(s||C)}})()),(0,p.A)(h,"\u0275prov",T.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})),h})()},2387:(E,v,n)=>{n.d(v,{I:()=>e});var a=n(467),p=n(9842),P=n(2716),b=n(7987),T=n(4438);let e=(()=>{var C;class h extends P.H{open(d){var s=this;return(0,a.A)(function*(){const c=yield Promise.all([n.e(1328),n.e(2076),n.e(4592)]).then(n.bind(n,4592));return s.matDialog.open(c.ModifyTaxesComponent.getComponent(),{panelClass:"custom_dialog",data:d,width:"500px",scrollStrategy:new b.t0})})()}}return C=h,(0,p.A)(h,"\u0275fac",(()=>{let u;return function(s){return(u||(u=T.xGo(C)))(s||C)}})()),(0,p.A)(h,"\u0275prov",T.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})),h})()},5909:(E,v,n)=>{n.d(v,{R2:()=>P,Xj:()=>p,az:()=>C,jQ:()=>a,yo:()=>e});const a=(h,u)=>{if(!h||!u)return 0;const d=h.split(":").map(Number);let s=0,c=0,_=0;return 3===d.length?[s,c,_]=d:2===d.length?[s,c]=d:1===d.length&&([s]=d),Number(((s+c/60+_/3600)*u).toFixed(2))},p=(h=[],u=!1)=>(h.some(s=>s.companyTax||s.selected||void 0===s.selected)?h:h.filter(s=>s.selected)).map(s=>{let c,_;return s.companyTax?(c=s.companyTax.name,_=s.companyTax.amount):(c=s.name,_=s.amount),u?`${c} (${_}%)`:c}).filter(Boolean).sort((s,c)=>s.localeCompare(c)).join(", "),P=(h,u)=>h&&u?Number((h*u).toFixed(2)):0,e=h=>{let u=0,d={};h.forEach(({rate:c,qty:_,taxes:g})=>{if(!c||!_)return;const D=Number((c*_).toFixed(2));u+=D,g&&0!==g.length&&(g.some(x=>x.companyTaxId)?g.filter(x=>x.companyTaxId):g.filter(x=>x.selected)).forEach(x=>{const O=x.name||"Unknown Tax",k=x.taxeNumber||"",B=Number(x?.companyTax?.amount??x.amount??0);d[O]||(d[O]={name:O,numberTax:k,amount:B,taxableAmount:0,total:0}),d[O].taxableAmount+=D})});let s=0;return Object.values(d).forEach(c=>{c.total=Number((c.taxableAmount*(c.amount/100)).toFixed(2)),s+=c.total}),{subtotal:Number(u.toFixed(2)),totalTaxes:d,grandTotalTax:Number(s.toFixed(2))}},C=h=>{let u=0,d={};h.forEach(({total:c,taxes:_})=>{c&&(u+=c,_&&0!==_.length)&&(_.some(j=>j.companyTaxId)?_.filter(j=>j.companyTaxId):_.filter(j=>j.selected)).forEach(j=>{const R=j.name||"Unknown Tax",x=j.taxeNumber||"",O=Number(j?.companyTax?.amount??j.amount??0);d[R]||(d[R]={name:R,numberTax:x,amount:O,taxableAmount:0,total:0}),d[R].taxableAmount+=c})});let s=0;return Object.values(d).forEach(c=>{c.total=Number((c.taxableAmount*(c.amount/100)).toFixed(2)),s+=c.total}),{subtotal:Number(u.toFixed(2)),totalTaxes:d,grandTotalTax:Number(s.toFixed(2))}}},2456:(E,v,n)=>{n.r(v),n.d(v,{AddProjectFormComponent:()=>Fe});var a=n(9842),p=n(7656),P=n(2387),b=n(6617),T=n(8556),e=n(4438),C=n(6146),h=n(8192),u=n(3652),d=n(5599),s=n(9417),c=n(177);const _=l=>({"text-text-primary":l});function g(l,m){if(1&l&&(e.j41(0,"label",10),e.EFF(1),e.k0s()),2&l){const i=e.XpG();e.AVh("required",i.isRequired),e.R7$(),e.JRh(i.label)}}function D(l,m){if(1&l&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&l){const i=e.XpG().$implicit;e.R7$(),e.SpI(" ",i.description," ")}}function j(l,m){if(1&l){const i=e.RV6();e.j41(0,"div",13),e.bIt("click",function(){const o=e.eBV(i).$implicit,r=e.XpG(2);return e.Njj(r.handleChooseOption(o))}),e.j41(1,"div",14)(2,"p",15),e.EFF(3),e.k0s(),e.DNE(4,D,2,1,"p",16),e.k0s()()}if(2&l){const i=m.$implicit,t=e.XpG(2);e.AVh("selected",i.value===t.value),e.R7$(3),e.SpI(" ",i.label," "),e.R7$(),e.vxM(i.description?4:-1)}}function R(l,m){}function x(l,m){if(1&l){const i=e.RV6();e.j41(0,"div",11)(1,"app-inno-input-search-result",12),e.bIt("onChange",function(o){e.eBV(i);const r=e.XpG();return e.Njj(r.handleSearch(o))}),e.DNE(2,j,5,4,"ng-template",null,1,e.C5r)(4,R,0,0,"ng-template",null,2,e.C5r),e.k0s()()}if(2&l){let i;const t=e.sdS(3),o=e.sdS(5),r=e.XpG();e.R7$(),e.Y8G("data",r.listOptionPreview)("isNotFound",!r.listOptionPreview.length)("isEmptyData",!r.listOptionOriginal.length)("optionTemplate",null!==(i=r.customOptionTemplate)&&void 0!==i?i:t)("footerTemplate",r.isShowCreateButton?o:null)}}let O=(()=>{var l;class m{constructor(){(0,a.A)(this,"isRequired",void 0),(0,a.A)(this,"label",""),(0,a.A)(this,"options",[]),(0,a.A)(this,"placeholder",""),(0,a.A)(this,"value",""),(0,a.A)(this,"projectId",""),(0,a.A)(this,"errorMessages",void 0),(0,a.A)(this,"formControl",void 0),(0,a.A)(this,"customOptionTemplate",null),(0,a.A)(this,"onChange",new e.bkB),(0,a.A)(this,"onSelect",new e.bkB),(0,a.A)(this,"onCreateNew",new e.bkB),(0,a.A)(this,"textSearch",""),(0,a.A)(this,"clientName",""),(0,a.A)(this,"labelOfValueSelected",""),(0,a.A)(this,"listOptionPreview",[]),(0,a.A)(this,"listOptionOriginal",[]),(0,a.A)(this,"searchResultComponent",void 0)}registerOnChange(t){}registerOnTouched(t){}setDisabledState(t){}writeValue(t){}ngOnChanges(t){const o=t?.options?.currentValue,r=t?.value?.currentValue??"";o?.length&&(this.options=o,this.listOptionOriginal=this.options,this.listOptionPreview=this.listOptionOriginal,this.formControl?.value&&(this.value=this.formControl.value,this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===this.value)?.name)),r&&(this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===r)?.name)}get isShowCreateButton(){return this.onCreateNew?.observed&&this.textSearch.length&&(!this.listOptionOriginal?.length||!this.listOptionPreview?.length)}handleChange(t){this.onChange?.emit&&this.onChange.emit(t)}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const t in this.formControl.errors)if(this.errorMessages[t])return this.errorMessages[t];return""}handleSearch(t){t=t?.trim()?.toLowerCase(),this.textSearch=t,this.listOptionPreview=t?.length?this.listOptionOriginal.filter(o=>o.name.toLowerCase().indexOf(t)>-1):this.listOptionOriginal}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}touchControl(){this.formControl&&(this.formControl.markAsDirty(),this.formControl.markAsTouched())}handleChooseOption(t){t.value!=this.value&&(this.formControl&&this.formControl.setValue(t.value),this.labelOfValueSelected=t.name,this.value=t.value,this.onSelect.emit(t),this.handleCloseSearchResult())}callbackAfterHideSearchResult(){this.listOptionPreview=this.listOptionOriginal}handleCreateNew(){this.onCreateNew.emit(this.textSearch),this.handleCloseSearchResult()}}return l=m,(0,a.A)(m,"\u0275fac",function(t){return new(t||l)}),(0,a.A)(m,"\u0275cmp",e.VBU({type:l,selectors:[["app-inno-form-project-type"]],viewQuery:function(t,o){if(1&t&&e.GBs(d.x,5),2&t){let r;e.mGM(r=e.lsd())&&(o.searchResultComponent=r.first)}},inputs:{isRequired:"isRequired",label:"label",options:"options",placeholder:"placeholder",value:"value",projectId:"projectId",errorMessages:"errorMessages",formControl:"formControl",customOptionTemplate:"customOptionTemplate"},outputs:{onChange:"onChange",onSelect:"onSelect",onCreateNew:"onCreateNew"},standalone:!0,features:[e.Jv_([{provide:s.kq,useExisting:(0,e.Rfq)(()=>l),multi:!0}]),e.OA$,e.aNF],decls:10,vars:9,consts:[["templateSearchProject",""],["optionTemplate",""],["buttonCreateNew",""],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],["position","bottom-start",3,"onOpen","onClose","content","isClickOnContentToClose","isClearPadding"],["target","",1,"dropdown-md","w-full"],[1,"w-full","text-left","line-clamp-1","text-text-placeholder-slight",3,"ngClass"],["src","../../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"shrink-0"],[3,"message"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","max-w-[90dvw]"],["placeholder","Search...",3,"onChange","data","isNotFound","isEmptyData","optionTemplate","footerTemplate"],[1,"w-full","flex","p-[8px]","my-1","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-teapp-inno-input-search-resultxt-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"]],template:function(t,o){if(1&t){const r=e.RV6();e.j41(0,"div",3),e.DNE(1,g,2,3,"label",4),e.j41(2,"app-inno-popover",5),e.bIt("onOpen",function(){return e.eBV(r),e.Njj(o.touchControl())})("onClose",function(){return e.eBV(r),e.Njj(o.callbackAfterHideSearchResult())}),e.j41(3,"button",6)(4,"div",7),e.EFF(5),e.k0s(),e.nrm(6,"img",8),e.k0s()(),e.DNE(7,x,6,5,"ng-template",null,0,e.C5r),e.nrm(9,"app-inno-error-message",9),e.k0s()}if(2&t){const r=e.sdS(8);e.R7$(),e.vxM(o.label?1:-1),e.R7$(),e.Y8G("content",r)("isClickOnContentToClose",!1)("isClearPadding",!0),e.R7$(2),e.Y8G("ngClass",e.eq3(7,_,o.labelOfValueSelected)),e.R7$(),e.SpI(" ",o.labelOfValueSelected||o.placeholder," "),e.R7$(4),e.Y8G("message",o.getErrorMessage())}},dependencies:[C.G,c.YU,d.x,u.t,h.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}']})),m})();var k=n(3492),B=n(1433),W=n(1110),H=n(2928),M=n(9079),V=n(4006),J=n(1970),Y=n(4978),Q=n(344),K=n(1328),z=n(7086),Z=n(4800),q=n(9248),ee=n(6463),te=n(3202),oe=n(4823),ne=n(4805);const re=[{name:"Fixed rate project",value:2},{name:"Hourly project",value:1}];var U=function(l){return l[l.Fixed=2]="Fixed",l[l.Hourly=1]="Hourly",l}(U||{}),le=n(467),ie=n(2716),ae=n(7987);let se=(()=>{var l;class m extends ie.H{open(t){var o=this;return(0,le.A)(function*(){const r=yield Promise.all([n.e(2591),n.e(1448),n.e(1515)]).then(n.bind(n,1515));return o.matDialog.open(r.AddServiceForProjectComponent.getComponent(),{panelClass:"custom_dialog",width:"100%",maxWidth:"700px",data:t,disableClose:!0,scrollStrategy:new ae.t0})})()}}return l=m,(0,a.A)(m,"\u0275fac",(()=>{let i;return function(o){return(i||(i=e.xGo(l)))(o||l)}})()),(0,a.A)(m,"\u0275prov",e.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})),m})();var ce=n(5402),X=n(5909),pe=n(3814),w=n(5236);const me=["selectProjectTypeElement"],de=["selectSearchClientElement"],L=l=>({required:l});function ue(l,m){if(1&l){const i=e.RV6();e.j41(0,"div",25),e.bIt("click",function(){const o=e.eBV(i).$implicit,r=e.XpG();return e.Njj(r.handleSelectProjectType(o))}),e.j41(1,"div",19)(2,"p",26),e.EFF(3),e.k0s()()()}if(2&l){const i=m.$implicit;e.R7$(3),e.SpI(" ",i.name," ")}}function he(l,m){if(1&l&&(e.j41(0,"p",29),e.EFF(1),e.k0s()),2&l){const i=e.XpG().$implicit;e.R7$(),e.SpI(" Internal ( ",i.label,") ")}}function fe(l,m){if(1&l){const i=e.RV6();e.j41(0,"div",25),e.bIt("click",function(){const o=e.eBV(i).$implicit,r=e.XpG();return e.Njj(r.handleSelectClient(o))}),e.nrm(1,"ngx-avatars",27),e.j41(2,"div",28)(3,"p",26),e.EFF(4),e.k0s(),e.DNE(5,he,2,1,"p",29),e.k0s()()}if(2&l){const i=m.$implicit;e.R7$(),e.Y8G("size",32)("name",i.label),e.R7$(3),e.SpI(" ",i.label," "),e.R7$(),e.vxM(null!=i&&null!=i.metadata&&null!=i.metadata.client&&i.metadata.client.isInternal?5:-1)}}function _e(l,m){if(1&l&&(e.nrm(0,"app-inno-form-input",13),e.nI1(1,"translate"),e.nI1(2,"translate")),2&l){const i=e.XpG();e.Y8G("label",e.bMT(1,4,"PROJECT.ADD_PROJECT_FORM.HourlyRate"))("formControl",i.f.hourly)("value",i.f.hourly.value)("placeholder",e.bMT(2,6,"PROJECT.ADD_PROJECT_FORM.HourlyRatePlaceholder"))}}function Ce(l,m){if(1&l){const i=e.RV6();e.j41(0,"span",33),e.bIt("click",function(){e.eBV(i);const o=e.XpG(2).$index,r=e.XpG();return e.Njj(r.Remove(o))}),e.EFF(1," close "),e.k0s()}}function ve(l,m){if(1&l&&e.nrm(0,"ngx-avatars",32),2&l){const i=e.XpG(2).$implicit,t=e.XpG();e.FCK("matTooltip","",i.firstName," ",i.lastName," "),e.FS9("bgColor",t._storeService.getBgColor(i.firstName.slice(0,1))),e.Y8G("size",40)("name",i.firstName.charAt(0)+" "+(i.lastName?i.lastName.charAt(0):""))}}function Te(l,m){if(1&l&&e.nrm(0,"ngx-avatars",32),2&l){const i=e.XpG(2).$implicit,t=e.XpG();e.FS9("matTooltip",i.email),e.FS9("bgColor",t._storeService.getBgColor(i.email.slice(0,1))),e.Y8G("size",40)("name",i.email.slice(0,1))}}function ge(l,m){if(1&l&&(e.j41(0,"div",30),e.DNE(1,Ce,2,0,"span",31)(2,ve,1,6,"ngx-avatars",32)(3,Te,1,4,"ngx-avatars",32),e.k0s()),2&l){const i=e.XpG().$implicit,t=e.XpG();e.R7$(),e.vxM(t.auth_services.getIdUser()!=i.userId?1:-1),e.R7$(),e.vxM(i.firstName?2:3)}}function xe(l,m){1&l&&e.DNE(0,ge,4,2,"div",30),2&l&&e.vxM(0==m.$implicit.status?0:-1)}function be(l,m){1&l&&(e.j41(0,"button",34),e.nrm(1,"img",35),e.k0s())}function je(l,m){if(1&l&&(e.j41(0,"p",40),e.EFF(1),e.k0s()),2&l){const i=e.XpG().$implicit,t=e.XpG(2);e.R7$(),e.SpI(" ",t.getNameSelectedTaxes(null==i?null:i.taxes)," ")}}function Pe(l,m){1&l&&(e.j41(0,"p",40),e.EFF(1," - "),e.k0s())}function Ae(l,m){if(1&l){const i=e.RV6();e.j41(0,"div",36)(1,"p",38),e.EFF(2),e.k0s(),e.j41(3,"p",39),e.EFF(4),e.nI1(5,"formatNumber"),e.k0s(),e.j41(6,"p",39),e.EFF(7),e.nI1(8,"formatNumber"),e.k0s(),e.DNE(9,je,2,1,"p",40)(10,Pe,2,0,"p",40),e.j41(11,"p",41),e.EFF(12),e.nI1(13,"formatNumber"),e.k0s(),e.j41(14,"app-inno-table-action",42),e.bIt("onEdit",function(){const o=e.eBV(i).$implicit,r=e.XpG(2);return e.Njj(r.handleEditService(o))})("onDelete",function(){const o=e.eBV(i).$index,r=e.XpG(2);return e.Njj(r.handleDeleteInvoiceItem(o))}),e.k0s()()}if(2&l){let i,t,o;const r=m.$implicit,f=e.XpG(2);e.R7$(2),e.SpI(" ",null!==(i=null==r?null:r.serviceName)&&void 0!==i?i:""," "),e.R7$(2),e.SpI(" $",e.bMT(5,5,null!==(t=null==r?null:r.rate)&&void 0!==t?t:0)," "),e.R7$(3),e.SpI(" ",e.bMT(8,7,null!==(o=null==r?null:r.qty)&&void 0!==o?o:0)," "),e.R7$(2),e.vxM(null!=r&&r.taxes&&(null==r?null:r.taxes.length)>0&&""!=f.getNameSelectedTaxes(null==r?null:r.taxes)?9:10),e.R7$(3),e.SpI(" $",e.bMT(13,9,f.calculateTotalInvoiceItem(null==r?null:r.rate,null==r?null:r.qty))," ")}}function Oe(l,m){if(1&l&&(e.j41(0,"div",20)(1,"div",36)(2,"p",37),e.EFF(3," Service Name "),e.k0s(),e.j41(4,"p",37),e.EFF(5," Rate "),e.k0s(),e.j41(6,"p",37),e.EFF(7," Quantity "),e.k0s(),e.j41(8,"p",37),e.EFF(9," Tax "),e.k0s(),e.j41(10,"p",37),e.EFF(11," Line Total "),e.k0s()(),e.Z7z(12,Ae,15,11,"div",36,e.fX1),e.k0s()),2&l){const i=e.XpG();e.R7$(12),e.Dyx(i.listService)}}let Fe=(()=>{var l;class m{static getComponent(){return m}constructor(t,o){(0,a.A)(this,"dialogRef",void 0),(0,a.A)(this,"data",void 0),(0,a.A)(this,"clientOptions",[]),(0,a.A)(this,"projectType",re),(0,a.A)(this,"selectProjectTypeElement",void 0),(0,a.A)(this,"selectSearchClientElement",void 0),(0,a.A)(this,"listService",[]),(0,a.A)(this,"listChooseUser",[]),(0,a.A)(this,"listRemoveMember",[]),(0,a.A)(this,"showErrorMember",!1),(0,a.A)(this,"hourly",U.Hourly),(0,a.A)(this,"previewBillable",!0),(0,a.A)(this,"createdAt",void 0),(0,a.A)(this,"calculateTotalInvoiceItem",X.R2),(0,a.A)(this,"getNameSelectedTaxes",X.Xj),(0,a.A)(this,"newprojectForm",void 0),(0,a.A)(this,"formBuilder",(0,e.WQX)(s.ze)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"_storeService",(0,e.WQX)(W.n)),(0,a.A)(this,"_toastService",(0,e.WQX)(k.f)),(0,a.A)(this,"_projectService",(0,e.WQX)(B.T)),(0,a.A)(this,"auth_services",(0,e.WQX)(H.k)),(0,a.A)(this,"timetrackingService",(0,e.WQX)(ne.y)),(0,a.A)(this,"dropdownOptionService",(0,e.WQX)(ee.R)),(0,a.A)(this,"modifyTaxesDialog",(0,e.WQX)(P.I)),(0,a.A)(this,"translate",(0,e.WQX)(w.c$)),(0,a.A)(this,"addServiceForProjectDialog",(0,e.WQX)(se)),(0,a.A)(this,"modifyEditItemAndServiceDialog",(0,e.WQX)(pe.v)),this.dialogRef=t,this.data=o,this.newprojectForm=this.formBuilder.group({option:[null==this.data||0==this.data?.option?1:this.data?.option],projectname:["",s.k0.compose([s.k0.required])],clientId:[null,s.k0.compose([s.k0.required])],endDate:[null],description:[""],total:[""],flat:[""],hourly:[""],service:[""]})}ngOnInit(){this.dropdownOptionService.getDropdownOptionsProjectAndClient({isOnlyClient:!0}).then(t=>{this.clientOptions=t}),this.data?this.GetProjectById():this.auth_services.GetUser().pipe((0,M.pQ)(this.destroyRef)).subscribe({next:t=>{t&&this.listChooseUser.push({userId:t.id,status:0,lastName:t.lastName,email:t.email,firstName:t.firstName})}})}handleDataEdit(t){this.f.projectname.setValue(t.projectName),this.f.clientId.setValue(t.clientId),this.f.description.setValue(t.description),this.f.endDate.setValue(t.endDate),this.f.flat.setValue(t.flatRate||0),this.f.total.setValue(t.totalHours||0),this.f.hourly.setValue(t.hourlyRate||0),this.f.option.setValue(t.option||1)}GetProjectById(){this.listChooseUser=[],this._projectService.GetProjectById(this.data).pipe((0,M.pQ)(this.destroyRef)).subscribe(t=>{t&&(this.handleDataEdit(t),t?.members?.forEach(o=>{this.listChooseUser.push({id:o.id,status:0,userId:o.user.id,email:o.user.email,lastName:o.user.lastName,firstName:o.user.firstName})}),t?.services&&(this.listService=t?.services),this.previewBillable=t?.billable??!1,this.createdAt=t.createdAt)})}handleClose(){this.dialogRef.close()}handleSubmitForm(){if(this.newprojectForm.invalid)return void this.markAllControlsAsTouched();if(this.f.endDate.value){const o=new Date(this.f.endDate.value);if(isNaN(o.getTime()))return void this.markAllControlsAsTouched()}const t={projectName:this.f.projectname.value,clientId:this.f.clientId.value,description:this.f.description.value,endDate:this.f.endDate.value,flatRate:this.f.flat.value||0,totalHours:this.f.total.value||0,hourlyRate:this.f.hourly.value||0,members:this.listChooseUser,option:this.f.option.value||1,services:this.listService.map(o=>({...o,taxes:o.taxes.some(r=>r.companyTax)?o.taxes.map(({companyTax:r,...f})=>f):o.taxes.filter(r=>r.selected)})),billable:this.previewBillable};this.data?(t.id=this.data,t.createdAt=this.createdAt,this._projectService.UpdateProject(t).pipe((0,M.pQ)(this.destroyRef)).subscribe(o=>{o?(this._toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.Success")),this.dialogRef.close(o)):this._toastService.showError(this.translate.instant("TOAST.Fail"))})):this._projectService.CreateProject(t).pipe((0,M.pQ)(this.destroyRef)).subscribe({next:o=>{o&&(this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this.dialogRef.close(o))}})}handleSelectClient(t){this.newprojectForm.controls.clientId.setValue(t.value),this.selectSearchClientElement.handleCloseSearchResult()}handleSelectProjectType(t){this.newprojectForm.controls.option.setValue(t.value),this.selectProjectTypeElement.handleCloseSearchResult()}handleCancel(){this.dialogRef.close()}handleDeleteInvoiceItem(t){this.listService.splice(t,1)}get f(){return this.newprojectForm.controls}Remove(t){this.data?this.listChooseUser[t].status=1:this.listChooseUser.splice(t,1)}markAllControlsAsTouched(){Object.values(this.f).forEach(t=>{t.markAsTouched()})}handleSelectUser(t){this.listChooseUser.find(f=>f.userId==t.value)?this._toastService.showInfo(this.translate.instant("COMMON.UserExist")," "):this.listChooseUser.push({userId:t.value,status:0,lastName:t.metadata?.lastName,firstName:t.metadata?.firstName,email:t.metadata?.email,projectId:this.data})}handleAddService(){this.addServiceForProjectDialog.open({projectId:this.data,listService:this.listService.filter(o=>o.isNewItem)}).then(o=>{o.afterClosed().subscribe(r=>{r&&r&&(this.listService=this.listService.concat(r))})})}handleModifyTaxes(t,o){const r=structuredClone(this.listService);let f=[];t.forEach(F=>{F.companyTax?(F.companyTax.selected=!0,f.push(F.companyTax)):f.push(F)}),this.modifyTaxesDialog.open(f.filter(F=>F.selected)).then(F=>{F.afterClosed().subscribe(S=>{if(!S)return void(this.listService=r);const G=this.listService;r.forEach((N,y)=>{o!=y&&(G[y].taxes=N.taxes.filter(A=>A.selected))}),G[o].taxes=S.taxes.filter(N=>N.selected),this.listService=G.slice(),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.listService.forEach(y=>{y.taxes.length>0?S.taxes.filter(A=>1==A.selected).forEach(A=>{y.taxes.some($=>$.companyTax?$?.companyTax.name===A?.name:$.name===A.name)||y.taxes.push(A)}):S.taxes.forEach(A=>{y.taxes.push(A)})}))})})}handleEditService(t){this.modifyEditItemAndServiceDialog.open({mode:ce.Q.Service,isShowProject:!0,serviceInfo:t}).then(r=>{r.afterClosed().subscribe(f=>{f&&this.GetProjectById()})})}handleChangeBillable(t){this.previewBillable=t}ngOnDestroy(){this.listChooseUser=[]}}return l=m,(0,a.A)(m,"\u0275fac",function(t){return new(t||l)(e.rXU(V.CP),e.rXU(V.Vh))}),(0,a.A)(m,"\u0275cmp",e.VBU({type:l,selectors:[["app-add-project-form"]],viewQuery:function(t,o){if(1&t&&(e.GBs(me,5),e.GBs(de,5)),2&t){let r;e.mGM(r=e.lsd())&&(o.selectProjectTypeElement=r.first),e.mGM(r=e.lsd())&&(o.selectSearchClientElement=r.first)}},standalone:!0,features:[e.aNF],decls:59,vars:91,consts:[["selectProjectTypeElement",""],["projectTypeOptionTemplate",""],["selectSearchClientElement",""],["projectOptionTemplate",""],["templateTriggerSelectUser",""],[3,"onClose","title"],[3,"formGroup"],[1,"w-full","p-[16px]","flex","flex-col","gap-[16px]"],[3,"label","options","formControl","value","placeholder","customOptionTemplate"],[3,"label","placeholder","formControl","value","errorMessages"],[3,"label","isAbleResize","formControl","value","placeholder"],[3,"label","options","formControl","value","placeholder","errorMessages","customOptionTemplate"],[3,"label","placeholder","value","formControl"],["type","number",3,"label","formControl","value","placeholder"],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"flex","flex-wrap","gap-2","w-full"],[3,"onSelect","isAll","templateTrigger"],[3,"onChange","checked"],[1,"w-full"],[1,"overflow-auto","w-full"],["type","button",1,"btn","btn-outline-secondary","btn-dashed",3,"click"],[1,"material-icons"],["footer",""],[3,"onSubmit","onCancel"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular"],[3,"size","name"],[1,"w-full","flex","flex-col"],[1,"line-clamp-1","text-text-sm-regular","text-text-tertiary"],[1,"w-[40px]","h-[40px]","rounded-full","relative"],[1,"material-icons","icon_clear","cursor-pointer","click","view-hover-inline-block","text-dark","bg-white"],[3,"matTooltip","size","bgColor","name"],[1,"material-icons","icon_clear","cursor-pointer","click","view-hover-inline-block","text-dark","bg-white",3,"click"],[1,"cursor-pointer","w-[40px]","h-[40px]","rounded-full","border","border-dashed","flex","justify-center","items-center","border-border-in-tertiary"],["src","../../../../assets/img/icon/ic_add.svg","alt","Icon",1,"w-[20px]"],[1,"invoiceTableLayout"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"text-text-primary","text-text-md-semibold"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","cursor-pointer"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"]],template:function(t,o){if(1&t){const r=e.RV6();e.j41(0,"app-inno-modal-wrapper",5),e.nI1(1,"translate"),e.nI1(2,"translate"),e.bIt("onClose",function(){return e.eBV(r),e.Njj(o.handleClose())}),e.j41(3,"form",6)(4,"div",7)(5,"app-inno-form-project-type",8,0),e.nI1(7,"translate"),e.nI1(8,"translate"),e.DNE(9,ue,4,1,"ng-template",null,1,e.C5r),e.k0s(),e.nrm(11,"app-inno-form-input",9),e.nI1(12,"translate"),e.nI1(13,"translate"),e.nI1(14,"translate"),e.nrm(15,"app-inno-form-textarea",10),e.nI1(16,"translate"),e.nI1(17,"translate"),e.j41(18,"app-inno-form-select-search",11,2),e.nI1(20,"translate"),e.nI1(21,"translate"),e.nI1(22,"translate"),e.DNE(23,fe,6,4,"ng-template",null,3,e.C5r),e.k0s(),e.nrm(25,"app-inno-form-datepicker",12),e.nI1(26,"translate"),e.nI1(27,"translate"),e.DNE(28,_e,3,8,"app-inno-form-input",13),e.nrm(29,"app-inno-form-input",13),e.nI1(30,"translate"),e.nI1(31,"translate"),e.nrm(32,"app-inno-form-input",13),e.nI1(33,"translate"),e.nI1(34,"translate"),e.j41(35,"div",14)(36,"label",15),e.EFF(37),e.nI1(38,"translate"),e.k0s(),e.j41(39,"div",16),e.Z7z(40,xe,1,1,null,null,e.fX1),e.DNE(42,be,2,0,"ng-template",null,4,e.C5r),e.j41(44,"app-inno-select-search-user",17),e.bIt("onSelect",function(I){return e.eBV(r),e.Njj(o.handleSelectUser(I))}),e.k0s()()(),e.j41(45,"app-inno-form-checkbox",18),e.bIt("onChange",function(I){return e.eBV(r),e.Njj(o.handleChangeBillable(I))}),e.EFF(46),e.nI1(47,"translate"),e.k0s(),e.j41(48,"div",14)(49,"div",19),e.DNE(50,Oe,14,0,"div",20),e.k0s(),e.j41(51,"div")(52,"button",21),e.bIt("click",function(){return e.eBV(r),e.Njj(o.handleAddService())}),e.j41(53,"span",22),e.EFF(54,"add"),e.k0s(),e.EFF(55),e.nI1(56,"translate"),e.k0s()()()()(),e.j41(57,"div",23)(58,"app-inno-modal-footer",24),e.bIt("onSubmit",function(){return e.eBV(r),e.Njj(o.handleSubmitForm())})("onCancel",function(){return e.eBV(r),e.Njj(o.handleCancel())}),e.k0s()()()}if(2&t){const r=e.sdS(10),f=e.sdS(24),I=e.sdS(43);e.Y8G("title",o.data?e.bMT(2,47,"PROJECT.ADD_PROJECT_FORM.TitleEdit"):e.bMT(1,45,"PROJECT.ADD_PROJECT_FORM.Title")),e.R7$(3),e.Y8G("formGroup",o.newprojectForm),e.R7$(2),e.Y8G("label",e.bMT(7,49,"PROJECT.ADD_PROJECT_FORM.ProjectType"))("options",o.projectType)("formControl",o.f.option)("value",o.f.option.value)("placeholder",e.bMT(8,51,"PROJECT.ADD_PROJECT_FORM.ProjectTypePlaceholder"))("customOptionTemplate",r),e.R7$(6),e.Y8G("label",e.bMT(12,53,"PROJECT.ADD_PROJECT_FORM.ProjectName"))("placeholder",e.bMT(13,55,"PROJECT.ADD_PROJECT_FORM.ProjectNamePlaceholder"))("formControl",o.f.projectname)("value",o.f.projectname.value)("errorMessages",e.eq3(87,L,e.bMT(14,57,"PROJECT.ADD_PROJECT_FORM.ProjectName"))),e.R7$(4),e.Y8G("label",e.bMT(16,59,"PROJECT.ADD_PROJECT_FORM.Description"))("isAbleResize",!0)("formControl",o.f.description)("value",o.f.description.value)("placeholder",e.bMT(17,61,"PROJECT.ADD_PROJECT_FORM.DescriptionPlaceholder")),e.R7$(3),e.Y8G("label",e.bMT(20,63,"PROJECT.ADD_PROJECT_FORM.AssignClient"))("options",o.clientOptions)("formControl",o.f.clientId)("value",o.f.clientId.value)("placeholder",e.bMT(21,65,"PROJECT.ADD_PROJECT_FORM.AssignClientPlaceholder"))("errorMessages",e.eq3(89,L,e.bMT(22,67,"PROJECT.ADD_PROJECT_FORM.ClientRequired")))("customOptionTemplate",f),e.R7$(7),e.Y8G("label",e.bMT(26,69,"PROJECT.ADD_PROJECT_FORM.EndDate"))("placeholder",e.bMT(27,71,"PROJECT.ADD_PROJECT_FORM.EndDatePlaceholder"))("value",o.f.endDate.value)("formControl",o.f.endDate),e.R7$(3),e.vxM(o.f.option.value==o.hourly?28:-1),e.R7$(),e.Y8G("label",e.bMT(30,73,"PROJECT.ADD_PROJECT_FORM.FlatRate"))("formControl",o.f.flat)("value",o.f.flat.value)("placeholder",e.bMT(31,75,"PROJECT.ADD_PROJECT_FORM.FlatRatePlaceholder")),e.R7$(3),e.Y8G("label",e.bMT(33,77,"PROJECT.ADD_PROJECT_FORM.TotalHours"))("formControl",o.f.total)("value",o.f.total.value)("placeholder",e.bMT(34,79,"PROJECT.ADD_PROJECT_FORM.TotalHoursPlaceholder")),e.R7$(5),e.SpI(" ",e.bMT(38,81,"PROJECT.ADD_PROJECT_FORM.Member")," "),e.R7$(3),e.Dyx(o.listChooseUser),e.R7$(4),e.Y8G("isAll",!1)("templateTrigger",I),e.R7$(),e.Y8G("checked",o.previewBillable),e.R7$(),e.SpI(" ",e.bMT(47,83,"PROJECT.ADD_PROJECT_FORM.Billable")," "),e.R7$(4),e.vxM(o.listService.length>0?50:-1),e.R7$(5),e.SpI(" ",e.bMT(56,85,"PROJECT.ADD_PROJECT_FORM.AddService")," ")}},dependencies:[oe.oV,C.G,s.qT,s.BC,s.cb,s.l_,s.j4,w.D9,J.mC,J.fw,Y.I,Q.k,K.a,z.C,Z.j,q.M,te.k,O,T.K,p.V,b.p],styles:[".icon_clear[_ngcontent-%COMP%]{position:absolute;margin-left:25px;border-radius:50%;font-size:10pt}p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:110px 100px 100px 110px 100px 100px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),m})()}}]);