"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6677],{6677:(X,_,o)=>{o.r(_),o.d(_,{LoadLinkInvoiceComponent:()=>w});var n=o(9842),x=o(4433),E=o(1556),g=o(6146),y=o(5644),b=o(1110),e=o(4438),m=o(9079),u=o(33),D=o(3719),F=o(9115),A=o(3492),M=o(5236),C=o(1970),P=o(6196),R=o(5599),h=o(6473),T=o(6617),k=o(402),L=o(335),I=o(5909),f=o(177),j=o(1342);const S=["actiontMenuTrigger"];function O(a,l){if(1&a){const r=e.RV6();e.j41(0,"div",36)(1,"div",37)(2,"button",38),e.bIt("click",function(){e.eBV(r);const t=e.XpG();return e.Njj(t.handleDownloadPDF())}),e.nrm(3,"img",39),e.EFF(4," Download PDF "),e.k0s()()()}}function N(a,l){1&a&&(e.j41(0,"p",17),e.EFF(1," Loading... "),e.k0s())}function $(a,l){if(1&a&&(e.j41(0,"div",3)(1,"p",40),e.EFF(2),e.nI1(3,"phoneMask"),e.k0s()()),2&a){const r=e.XpG();e.R7$(2),e.SpI(" ",e.bMT(3,1,r.businessInfo.businessPhoneNumber)," ")}}function B(a,l){if(1&a&&(e.j41(0,"p",19),e.EFF(1),e.k0s()),2&a){const r=e.XpG();e.R7$(),e.SpI(" ",r.businessInfo.businessAddress," ")}}function U(a,l){if(1&a&&(e.j41(0,"div",26)(1,"div",41)(2,"ul",42)(3,"span",43),e.EFF(4," fiber_manual_record "),e.k0s(),e.j41(5,"p",44),e.EFF(6),e.k0s()()(),e.j41(7,"p",45),e.EFF(8),e.nI1(9,"formatNumber"),e.k0s(),e.j41(10,"div",46)(11,"p"),e.EFF(12),e.nI1(13,"decimal"),e.nI1(14,"formatNumber"),e.k0s(),e.j41(15,"p"),e.EFF(16),e.k0s()(),e.j41(17,"p",47),e.EFF(18),e.nI1(19,"decimal"),e.nI1(20,"formatNumber"),e.k0s()()),2&a){let r;const i=l.$implicit,t=e.XpG();e.R7$(6),e.SpI(" ",null!==(r=null==i?null:i.description)&&void 0!==r?r:""," "),e.R7$(2),e.SpI(" $",e.bMT(9,5,i.rate)," "),e.R7$(4),e.SpI(" ",e.bMT(14,10,e.i5U(13,7,i.qty,2))," "),e.R7$(4),e.SpI(" ",t.getNameTaxes(null==i?null:i.taxes,!0),""),e.R7$(2),e.SpI(" $",e.bMT(20,15,e.i5U(19,12,t.calculateTotal(i),2))," ")}}function W(a,l){if(1&a&&(e.j41(0,"div",33)(1,"div",48)(2,"p",49),e.EFF(3),e.k0s(),e.j41(4,"p",49),e.EFF(5),e.k0s()(),e.j41(6,"p",32),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&a){const r=l.$implicit;e.R7$(3),e.Lme(" ",r.name," (",r.amount,"%) "),e.R7$(2),e.SpI(" #",r.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,r.total,2))," ")}}let w=(()=>{var a;class l{constructor(i,t){(0,n.A)(this,"location",void 0),(0,n.A)(this,"spinnerService",void 0),(0,n.A)(this,"titleStatus","Draft"),(0,n.A)(this,"today",new Date),(0,n.A)(this,"imageUrl",void 0),(0,n.A)(this,"totalPages",1),(0,n.A)(this,"currentPage",1),(0,n.A)(this,"pageSizes",[10,20,50,100]),(0,n.A)(this,"pageSizesDefault",10),(0,n.A)(this,"selectionOptions",{type:"Multiple",checkboxOnly:!0}),(0,n.A)(this,"InforInvoice",void 0),(0,n.A)(this,"listChoosePayment",[]),(0,n.A)(this,"InforBussiness",void 0),(0,n.A)(this,"isErrorTotal",!1),(0,n.A)(this,"isErrorClient",!1),(0,n.A)(this,"clientId",void 0),(0,n.A)(this,"reference",void 0),(0,n.A)(this,"note",void 0),(0,n.A)(this,"nameTax",""),(0,n.A)(this,"sumtax",0),(0,n.A)(this,"subtotal",0),(0,n.A)(this,"qty",void 0),(0,n.A)(this,"total",void 0),(0,n.A)(this,"rate",void 0),(0,n.A)(this,"listTax",[]),(0,n.A)(this,"taxArray",[]),(0,n.A)(this,"selectedDate",void 0),(0,n.A)(this,"selectedDueDate",void 0),(0,n.A)(this,"calculateGroupedTaxes",I.yo),(0,n.A)(this,"getNameTaxes",I.Xj),(0,n.A)(this,"router",(0,e.WQX)(u.Ix)),(0,n.A)(this,"actiontMenuTrigger",void 0),(0,n.A)(this,"clientName",void 0),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"_id",void 0),(0,n.A)(this,"activatedRoute",(0,e.WQX)(u.nX)),(0,n.A)(this,"listClient",[]),(0,n.A)(this,"translate",(0,e.WQX)(M.c$)),(0,n.A)(this,"_toastService",(0,e.WQX)(A.f)),(0,n.A)(this,"_storeService",(0,e.WQX)(b.n)),(0,n.A)(this,"_invoiceService",(0,e.WQX)(y.p)),(0,n.A)(this,"duplicateInvoiceDialog",(0,e.WQX)(k.x)),this.location=i,this.spinnerService=t,this.InforBussiness=this._storeService.get_UserBusiness()}formatDate(i){return`${i.getFullYear()}-${String(i.getMonth()+1).padStart(2,"0")}-${String(i.getDate()).padStart(2,"0")}`}convertToHours(i){const[t,s]=i.split(":").map(Number);return t+s/60}get businessInfo(){return{businessName:this.InforInvoice?.company?.businessName??"",businessPhoneNumber:this.InforInvoice?.company?.phone??"",businessAddress:(0,h.Aw)({addressLine1:this.InforInvoice?.company?.adress??"",addressLine2:this.InforInvoice?.company?.adress2??"",city:this.InforInvoice?.company?.city??"",stateProvince:this.InforInvoice?.company?.province??"",postalCode:this.InforInvoice?.company?.postalCode??""})}}get businessInfoClient(){return{businessAddress:(0,h.lq)({addressLine1:this.InforInvoice?.client?.addressLine1??"",addressLine2:this.InforInvoice?.client?.addressLine2??"",townCity:this.InforInvoice?.client?.townCity??"",stateProvince:this.InforInvoice?.client?.stateProvince??"",country:this.InforInvoice?.client?.country??""})}}handleDuplicate(){this.duplicateInvoiceDialog.open({id:this._id,isInvoice:!0}).then(t=>{t.afterClosed().subscribe(s=>{})})}handleMarkAsPaid(){this._invoiceService.MarkAsPaid(this._id).pipe((0,m.pQ)(this.destroyRef)).subscribe(i=>{i&&(this._toastService.showSuccess("Mark As Paid","Success"),this.GetInvoiceById(this._id))})}handleMarkAsSent(){this._invoiceService.MarkAsSent(this._id).pipe((0,m.pQ)(this.destroyRef)).subscribe(i=>{i&&(this._toastService.showSuccess("Mark As Sent","Success"),this.GetInvoiceById(this._id))})}handleBack(){this.location.back()}ngOnInit(){this.activatedRoute.params.pipe((0,m.pQ)(this.destroyRef)).subscribe(i=>{i?.id&&(this._id=i?.id,this.GetInvoiceById(i?.id))})}_handleData(i){let t=this.listClient.findIndex(s=>s.clientId.toString()==this.InforInvoice?.clientId);t>=0&&(this.clientName=this.listClient[t].clientName),this.selectedDueDate=this.formatDate(new Date(this.InforInvoice?.dueDate??0)),this.selectedDate=this.formatDate(new Date(this.InforInvoice?.invoiceDate??0)),this.note=this.InforInvoice?.notes,this.rate=this.InforInvoice?.rate??0,this.qty=this.InforInvoice?.timeAmount??0,this.total=this.InforInvoice?.paidAmount??0,this.reference=this.InforInvoice?.reference,this.imageUrl=this.InforInvoice?.company?.companyImage}GetInvoiceById(i){this._invoiceService.GetInvoiceByIdLink(i).pipe((0,m.pQ)(this.destroyRef)).subscribe(t=>{t&&(this.InforInvoice=t,this._handleData(t),this.calculateAllTax())})}handleDownloadPDF(){this.spinnerService.show(),this._invoiceService.PrintInvoiceById(this._id,this.InforInvoice.invoiceNumber)}_formatTotal(i){return Math.floor(1e3*i)/1e3}calculateAllTax(){this.taxArray=[],this.sumtax=0;const t=(0,I.yo)(this.InforInvoice?.itemInvoices);this.taxArray=Object.values(t.totalTaxes),this.sumtax=t.grandTotalTax,this.subtotal=this._formatTotal((this.InforInvoice?.totalAmount??0)-this.CheckIsNaN(this.sumtax))}CheckIsNaN(i){return isNaN(i)?0:i}calculateTotal(i){let t=0,d=i.qty;return t=Math.floor(100*i.rate)/100*(Math.floor(100*d)/100),this._formatTotal(this.CheckIsNaN(t))}onRowSelecting(i){i?.data?.length>0?i?.data.forEach(t=>{this.listChoosePayment.findIndex(d=>d==t?.id)<0&&this.listChoosePayment.push(t?.id)}):this.listChoosePayment.findIndex(s=>s==i?.data?.id)<0&&this.listChoosePayment.push(i?.data?.id)}onRowDeselecting(i){if(i?.data?.length>0)this.listChoosePayment=[];else{let t=this.listChoosePayment.findIndex(s=>s==i.data?.id);t>=0&&this.listChoosePayment.splice(t,1)}}getFullName(i){return i?.firstName&&i?.lastName?i?.firstName+" "+i?.lastName:i?.email??""}handleFunctionInDevelopment(){this._toastService.showInfo("The feature is in development.")}}return a=l,(0,n.A)(l,"\u0275fac",function(i){return new(i||a)(e.rXU(f.aZ),e.rXU(j.D))}),(0,n.A)(l,"\u0275cmp",e.VBU({type:a,selectors:[["app-load-link-invoice"]],viewQuery:function(i,t){if(1&i&&e.GBs(S,5),2&i){let s;e.mGM(s=e.lsd())&&(t.actiontMenuTrigger=s.first)}},standalone:!0,features:[e.Jv_([E.Z]),e.aNF],decls:102,vars:37,consts:[["templateSearchProject",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"flex","items-center","gap-[8px]"],[1,"text-text-primary","text-headline-lg-bold"],[3,"status"],[1,"flex","items-center","gap-[12px]","flex-wrap"],["position","bottom-end",3,"content"],["target","",1,"button-size-md","button-outline"],["src","../../../../assets/img/icon/ic_three_dot_horizontal.svg","alt","icon"],[1,"container-full","mt-[24px]"],[1,"w-full","w-fit","mx-auto","bg-bg-primary","p-[32px]","relative"],[1,"flex","w-full","gap-[18px]","md:flex-row","flex-col"],[1,"w-[160px]","h-[100px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'","alt","image",1,"rounded-md","w-full","h-full","object-cover",3,"src"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]","font-bold"],[1,"w-full","text-text-sm-regular","text-text-secondary"],[1,"w-full","grid","sm:grid-cols-2","lg:grid-cols-4","gap-[16px]"],[1,"text-text-brand-primary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-secondary","text-text-md-regular"],[1,"text-text-secondary","text-text-md-regular","whitespace-pre-line"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],[1,"overflow-auto","w-full"],[1,"invoiceTableLayout"],[1,"text-text-brand-primary","text-text-sm-semibold","font-bold"],[1,"text-text-brand-primary","text-text-sm-semibold","text-right","font-bold"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["src","../../../../assets/img/bg_footer_invoice_details.png","alt","Footer",1,"invoiceDetailsFooter"],[1,"block"],[1,"flex","flex-col","gap-[2px]"],[1,"button-size-md","button-transparent",3,"click"],["src","../../../../assets/img/icon/ic_download.svg","alt","icon"],[1,"text-text-sm-regular","text-text-secondary"],[1,"flex","flex-col"],[1,"flex","items-center"],[1,"material-icons","cursor-pointer","mr-3","!text-[8px]"],[1,"text-text-primary","text-text-md-regular","whitespace-pre-line"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","flex","flex-col"],[1,"text-text-primary","text-text-md-bold","text-right"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"]],template:function(i,t){if(1&i&&(e.j41(0,"div",1)(1,"div",2)(2,"div",3)(3,"p",4),e.EFF(4),e.k0s(),e.nrm(5,"app-inno-status",5),e.k0s(),e.j41(6,"div",6)(7,"app-inno-popover",7)(8,"button",8),e.nrm(9,"img",9),e.EFF(10," More "),e.k0s()(),e.DNE(11,O,5,0,"ng-template",null,0,e.C5r),e.k0s()()(),e.j41(13,"div",10)(14,"div",11)(15,"div",12)(16,"div",13),e.nrm(17,"img",14),e.k0s(),e.j41(18,"div",15)(19,"div",16),e.DNE(20,N,2,0,"p",17),e.j41(21,"p",18),e.EFF(22),e.k0s(),e.DNE(23,$,4,3,"div",3),e.j41(24,"p",19),e.EFF(25),e.k0s(),e.DNE(26,B,2,1,"p",19),e.j41(27,"p",19),e.EFF(28),e.k0s()(),e.j41(29,"div",20)(30,"div",16)(31,"p",21),e.EFF(32,"Billed to"),e.k0s(),e.j41(33,"p",22),e.EFF(34),e.k0s(),e.j41(35,"p",22),e.EFF(36),e.k0s()(),e.j41(37,"div",16)(38,"p",21),e.EFF(39,"Invoice Number"),e.k0s(),e.j41(40,"p",22),e.EFF(41),e.k0s()(),e.j41(42,"div",16)(43,"p",21),e.EFF(44,"Date of Issue"),e.k0s(),e.j41(45,"p",22),e.EFF(46),e.k0s()(),e.j41(47,"div",16)(48,"p",21),e.EFF(49,"Due Date"),e.k0s(),e.j41(50,"p",22),e.EFF(51),e.k0s()()(),e.j41(52,"div",16)(53,"p",21),e.EFF(54,"Description"),e.k0s(),e.j41(55,"p",23),e.EFF(56),e.k0s()()()(),e.j41(57,"div",24)(58,"div",25)(59,"div",26)(60,"p",27),e.EFF(61," Invoice Item "),e.k0s(),e.j41(62,"p",27),e.EFF(63," Rate "),e.k0s(),e.j41(64,"p",27),e.EFF(65," Quantity "),e.k0s(),e.j41(66,"p",28),e.EFF(67," Line Total "),e.k0s()(),e.j41(68,"div",25),e.Z7z(69,U,21,17,"div",26,e.fX1),e.k0s()()(),e.j41(71,"div",29)(72,"div",30)(73,"p",31),e.EFF(74," Subtotal "),e.k0s(),e.j41(75,"p",32),e.EFF(76),e.nI1(77,"decimal"),e.nI1(78,"formatNumber"),e.k0s()(),e.Z7z(79,W,10,9,"div",33,e.fX1),e.j41(81,"div",30)(82,"p",31),e.EFF(83," Tax "),e.k0s(),e.j41(84,"p",32),e.EFF(85),e.nI1(86,"decimal"),e.nI1(87,"formatNumber"),e.k0s()(),e.j41(88,"div",30)(89,"p",31),e.EFF(90," Discount "),e.k0s(),e.j41(91,"p",32),e.EFF(92," $0 "),e.k0s()(),e.j41(93,"div",30)(94,"p",31),e.EFF(95),e.nI1(96,"async"),e.k0s(),e.j41(97,"p",34),e.EFF(98),e.nI1(99,"decimal"),e.nI1(100,"formatNumber"),e.k0s()()(),e.nrm(101,"img",35),e.k0s()()),2&i){let s,d,c,p,v;const K=e.sdS(12);e.R7$(4),e.SpI(" Invoice ",null!==(s=null==t.InforInvoice?null:t.InforInvoice.invoiceNumber)&&void 0!==s?s:""," "),e.R7$(),e.Y8G("status",null==t.InforInvoice?null:t.InforInvoice.status),e.R7$(2),e.Y8G("content",K),e.R7$(10),e.Y8G("src",t.imageUrl,e.B4B),e.R7$(3),e.vxM(t.businessInfo.businessName?-1:20),e.R7$(2),e.SpI("",null==t.InforInvoice||null==t.InforInvoice.company?null:t.InforInvoice.company.businessName," "),e.R7$(),e.vxM(t.businessInfo.businessPhoneNumber?23:-1),e.R7$(2),e.SpI(" ",null==t.InforInvoice||null==t.InforInvoice.company?null:t.InforInvoice.company.email," "),e.R7$(),e.vxM(t.businessInfo.businessAddress?26:-1),e.R7$(2),e.SpI(" ",null==t.InforInvoice||null==t.InforInvoice.company?null:t.InforInvoice.company.country," "),e.R7$(6),e.SpI(" ",null!==(d=null==t.InforInvoice||null==t.InforInvoice.client?null:t.InforInvoice.client.clientName)&&void 0!==d?d:""," "),e.R7$(2),e.SpI(" ",null!==(c=null==t.businessInfoClient?null:t.businessInfoClient.businessAddress)&&void 0!==c?c:""," "),e.R7$(5),e.SpI(" ",null!==(p=null==t.InforInvoice?null:t.InforInvoice.invoiceNumber)&&void 0!==p?p:""," "),e.R7$(5),e.SpI(" ",t.selectedDate," "),e.R7$(5),e.SpI(" ",t.selectedDueDate," "),e.R7$(5),e.SpI(" ",null!==(v=null==t.InforInvoice?null:t.InforInvoice.notes)&&void 0!==v?v:""," "),e.R7$(13),e.Dyx(null==t.InforInvoice?null:t.InforInvoice.itemInvoices),e.R7$(7),e.SpI(" $",e.bMT(78,23,e.i5U(77,20,t.subtotal,2))," "),e.R7$(3),e.Dyx(t.taxArray),e.R7$(6),e.SpI(" $",e.bMT(87,28,e.i5U(86,25,t.CheckIsNaN(t.sumtax),2))," "),e.R7$(10),e.SpI(" Amount Due (",e.bMT(96,30,t._storeService.curencyCompany),") "),e.R7$(3),e.SpI(" $",e.bMT(100,35,e.i5U(99,32,null==t.InforInvoice?null:t.InforInvoice.totalAmount,2))," ")}},dependencies:[g.G,f.Jj,D.RG,C.mC,u.iI,F.Cn,P.A,R.x,T.p,L.L,x.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceDetailsFooter[_ngcontent-%COMP%]{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;bottom:5px;left:0;transform:translateY(100%);width:100%;height:auto;object-fit:contain}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(100px,1fr) 200px 200px 200px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),l})()}}]);