"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6600],{152:(q,L,f)=>{f.d(L,{B:()=>F});var m=f(3236),u=f(9974),M=f(4360);function F(w,T=m.E){return(0,u.N)((y,I)=>{let _=null,v=null,C=null;const x=()=>{if(_){_.unsubscribe(),_=null;const D=v;v=null,I.next(D)}};function A(){const D=C+w,R=T.now();if(R<D)return _=this.schedule(void 0,D-R),void I.add(_);x()}y.subscribe((0,M._)(I,D=>{v=D,C=T.now(),_||(_=T.schedule(A,w),I.add(_))},()=>{x(),I.complete()},void 0,()=>{v=_=null}))})}},687:(q,L,f)=>{f.d(L,{Pd:()=>Te,vr:()=>Fe,vR:()=>Ae,kB:()=>ct,Bu:()=>S,FN:()=>Ie,GX:()=>We,Q_:()=>oe,Z7:()=>W,_G:()=>me,w6:()=>Q});var m=f(177),u=f(4438),M=f(6860),F=f(1413),w=f(8359),y=(f(4402),f(7673)),I=f(4412),_=f(7336),v=f(8141),C=f(152),x=f(5964),A=f(6354),D=f(6697),R=f(5245),ee=f(3669),$=f(9974),te=f(4360);function z(s,t){return s===t}var re=f(6977),g=f(2318),O=f(4085),P=f(4572),K=f(8793),Qe=f(1985),Ye=f(9172);const Ce=new Set;let j,Xe=(()=>{var s;class t{constructor(e,n){this._platform=e,this._nonce=n,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):xe}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&function Oe(s,t){if(!Ce.has(s))try{j||(j=document.createElement("style"),t&&j.setAttribute("nonce",t),j.setAttribute("type","text/css"),document.head.appendChild(j)),j.sheet&&(j.sheet.insertRule(`@media ${s} {body{ }}`,0),Ce.add(s))}catch(r){console.error(r)}}(e,this._nonce),this._matchMedia(e)}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(M.OD),u.KVO(u.BIS,8))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();function xe(s){return{matches:"all"===s||""===s,media:s,addListener:()=>{},removeListener:()=>{}}}let Je=(()=>{var s;class t{constructor(e,n){this._mediaMatcher=e,this._zone=n,this._queries=new Map,this._destroySubject=new F.B}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return ce((0,O.FG)(e)).some(l=>this._registerQuery(l).mql.matches)}observe(e){const l=ce((0,O.FG)(e)).map(b=>this._registerQuery(b).observable);let h=(0,P.z)(l);return h=(0,K.x)(h.pipe((0,D.s)(1)),h.pipe((0,R.i)(1),(0,C.B)(0))),h.pipe((0,A.T)(b=>{const k={matches:!1,breakpoints:{}};return b.forEach(({matches:G,query:J})=>{k.matches=k.matches||G,k.breakpoints[J]=G}),k}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);const n=this._mediaMatcher.matchMedia(e),h={observable:new Qe.c(b=>{const k=G=>this._zone.run(()=>b.next(G));return n.addListener(k),()=>{n.removeListener(k)}}).pipe((0,Ye.Z)(n),(0,A.T)(({matches:b})=>({query:e,matches:b})),(0,re.Q)(this._destroySubject)),mql:n};return this._queries.set(e,h),h}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(Xe),u.KVO(u.SKi))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();function ce(s){return s.map(t=>t.split(",")).reduce((t,r)=>t.concat(r)).map(t=>t.trim())}function H(s,t){return s.getAttribute(t)?.match(/\S+/g)??[]}const pe="cdk-describedby-message",de="cdk-describedby-host";let fe=0,Fe=(()=>{var s;class t{constructor(e,n){this._platform=n,this._messageRegistry=new Map,this._messagesContainer=null,this._id=""+fe++,this._document=e,this._id=(0,u.WQX)(u.sZ2)+"-"+fe++}describe(e,n,l){if(!this._canBeDescribed(e,n))return;const h=ge(n,l);"string"!=typeof n?(we(n,this._id),this._messageRegistry.set(h,{messageElement:n,referenceCount:0})):this._messageRegistry.has(h)||this._createMessageElement(n,l),this._isElementDescribedByMessage(e,h)||this._addMessageReference(e,h)}removeDescription(e,n,l){if(!n||!this._isElementNode(e))return;const h=ge(n,l);if(this._isElementDescribedByMessage(e,h)&&this._removeMessageReference(e,h),"string"==typeof n){const b=this._messageRegistry.get(h);b&&0===b.referenceCount&&this._deleteMessageElement(h)}0===this._messagesContainer?.childNodes.length&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){const e=this._document.querySelectorAll(`[${de}="${this._id}"]`);for(let n=0;n<e.length;n++)this._removeCdkDescribedByReferenceIds(e[n]),e[n].removeAttribute(de);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(e,n){const l=this._document.createElement("div");we(l,this._id),l.textContent=e,n&&l.setAttribute("role",n),this._createMessagesContainer(),this._messagesContainer.appendChild(l),this._messageRegistry.set(ge(e,n),{messageElement:l,referenceCount:0})}_deleteMessageElement(e){this._messageRegistry.get(e)?.messageElement?.remove(),this._messageRegistry.delete(e)}_createMessagesContainer(){if(this._messagesContainer)return;const e="cdk-describedby-message-container",n=this._document.querySelectorAll(`.${e}[platform="server"]`);for(let h=0;h<n.length;h++)n[h].remove();const l=this._document.createElement("div");l.style.visibility="hidden",l.classList.add(e),l.classList.add("cdk-visually-hidden"),this._platform&&!this._platform.isBrowser&&l.setAttribute("platform","server"),this._document.body.appendChild(l),this._messagesContainer=l}_removeCdkDescribedByReferenceIds(e){const n=H(e,"aria-describedby").filter(l=>0!=l.indexOf(pe));e.setAttribute("aria-describedby",n.join(" "))}_addMessageReference(e,n){const l=this._messageRegistry.get(n);(function le(s,t,r){const e=H(s,t);r=r.trim(),!e.some(n=>n.trim()===r)&&(e.push(r),s.setAttribute(t,e.join(" ")))})(e,"aria-describedby",l.messageElement.id),e.setAttribute(de,this._id),l.referenceCount++}_removeMessageReference(e,n){const l=this._messageRegistry.get(n);l.referenceCount--,function ke(s,t,r){const e=H(s,t);r=r.trim();const n=e.filter(l=>l!==r);n.length?s.setAttribute(t,n.join(" ")):s.removeAttribute(t)}(e,"aria-describedby",l.messageElement.id),e.removeAttribute(de)}_isElementDescribedByMessage(e,n){const l=H(e,"aria-describedby"),h=this._messageRegistry.get(n),b=h&&h.messageElement.id;return!!b&&-1!=l.indexOf(b)}_canBeDescribed(e,n){if(!this._isElementNode(e))return!1;if(n&&"object"==typeof n)return!0;const l=null==n?"":`${n}`.trim(),h=e.getAttribute("aria-label");return!(!l||h&&h.trim()===l)}_isElementNode(e){return e.nodeType===this._document.ELEMENT_NODE}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(m.qQ),u.KVO(M.OD))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();function ge(s,t){return"string"==typeof s?`${t||""}/${s}`:s}function we(s,t){s.id||(s.id=`${pe}-${t}-${fe++}`)}class Ne{constructor(t,r){this._letterKeyStream=new F.B,this._items=[],this._selectedItemIndex=-1,this._pressedLetters=[],this._selectedItem=new F.B,this.selectedItem=this._selectedItem;const e="number"==typeof r?.debounceInterval?r.debounceInterval:200;r?.skipPredicate&&(this._skipPredicateFn=r.skipPredicate),this.setItems(t),this._setupKeyHandler(e)}destroy(){this._pressedLetters=[],this._letterKeyStream.complete(),this._selectedItem.complete()}setCurrentSelectedItemIndex(t){this._selectedItemIndex=t}setItems(t){this._items=t}handleKey(t){const r=t.keyCode;t.key&&1===t.key.length?this._letterKeyStream.next(t.key.toLocaleUpperCase()):(r>=_.A&&r<=_.Z||r>=_.f2&&r<=_.bn)&&this._letterKeyStream.next(String.fromCharCode(r))}isTyping(){return this._pressedLetters.length>0}reset(){this._pressedLetters=[]}_setupKeyHandler(t){this._letterKeyStream.pipe((0,v.M)(r=>this._pressedLetters.push(r)),(0,C.B)(t),(0,x.p)(()=>this._pressedLetters.length>0),(0,A.T)(()=>this._pressedLetters.join("").toLocaleUpperCase())).subscribe(r=>{for(let e=1;e<this._items.length+1;e++){const l=this._items[(this._selectedItemIndex+e)%this._items.length];if(!this._skipPredicateFn?.(l)&&0===l.getLabel?.().toLocaleUpperCase().trim().indexOf(r)){this._selectedItem.next(l);break}}this._pressedLetters=[]})}}class Z{constructor(t,r){this._items=t,this._activeItemIndex=-1,this._activeItem=null,this._wrap=!1,this._typeaheadSubscription=w.yU.EMPTY,this._vertical=!0,this._allowedModifierKeys=[],this._homeAndEnd=!1,this._pageUpAndDown={enabled:!1,delta:10},this._skipPredicateFn=e=>e.disabled,this.tabOut=new F.B,this.change=new F.B,t instanceof u.rOR?this._itemChangesSubscription=t.changes.subscribe(e=>this._itemsChanged(e.toArray())):(0,u.Hps)(t)&&(this._effectRef=(0,u.QZP)(()=>this._itemsChanged(t()),{injector:r}))}skipPredicate(t){return this._skipPredicateFn=t,this}withWrap(t=!0){return this._wrap=t,this}withVerticalOrientation(t=!0){return this._vertical=t,this}withHorizontalOrientation(t){return this._horizontal=t,this}withAllowedModifierKeys(t){return this._allowedModifierKeys=t,this}withTypeAhead(t=200){this._typeaheadSubscription.unsubscribe();const r=this._getItemsArray();return this._typeahead=new Ne(r,{debounceInterval:"number"==typeof t?t:void 0,skipPredicate:e=>this._skipPredicateFn(e)}),this._typeaheadSubscription=this._typeahead.selectedItem.subscribe(e=>{this.setActiveItem(e)}),this}cancelTypeahead(){return this._typeahead?.reset(),this}withHomeAndEnd(t=!0){return this._homeAndEnd=t,this}withPageUpDown(t=!0,r=10){return this._pageUpAndDown={enabled:t,delta:r},this}setActiveItem(t){const r=this._activeItem;this.updateActiveItem(t),this._activeItem!==r&&this.change.next(this._activeItemIndex)}onKeydown(t){const r=t.keyCode,n=["altKey","ctrlKey","metaKey","shiftKey"].every(l=>!t[l]||this._allowedModifierKeys.indexOf(l)>-1);switch(r){case _.wn:return void this.tabOut.next();case _.n6:if(this._vertical&&n){this.setNextItemActive();break}return;case _.i7:if(this._vertical&&n){this.setPreviousItemActive();break}return;case _.LE:if(this._horizontal&&n){"rtl"===this._horizontal?this.setPreviousItemActive():this.setNextItemActive();break}return;case _.UQ:if(this._horizontal&&n){"rtl"===this._horizontal?this.setNextItemActive():this.setPreviousItemActive();break}return;case _.yZ:if(this._homeAndEnd&&n){this.setFirstItemActive();break}return;case _.Kp:if(this._homeAndEnd&&n){this.setLastItemActive();break}return;case _.w_:if(this._pageUpAndDown.enabled&&n){const l=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(l>0?l:0,1);break}return;case _.dB:if(this._pageUpAndDown.enabled&&n){const l=this._activeItemIndex+this._pageUpAndDown.delta,h=this._getItemsArray().length;this._setActiveItemByIndex(l<h?l:h-1,-1);break}return;default:return void((n||(0,_.rp)(t,"shiftKey"))&&this._typeahead?.handleKey(t))}this._typeahead?.reset(),t.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem}isTyping(){return!!this._typeahead&&this._typeahead.isTyping()}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._getItemsArray().length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(t){const r=this._getItemsArray(),e="number"==typeof t?t:r.indexOf(t);this._activeItem=r[e]??null,this._activeItemIndex=e,this._typeahead?.setCurrentSelectedItemIndex(e)}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._effectRef?.destroy(),this._typeahead?.destroy(),this.tabOut.complete(),this.change.complete()}_setActiveItemByDelta(t){this._wrap?this._setActiveInWrapMode(t):this._setActiveInDefaultMode(t)}_setActiveInWrapMode(t){const r=this._getItemsArray();for(let e=1;e<=r.length;e++){const n=(this._activeItemIndex+t*e+r.length)%r.length;if(!this._skipPredicateFn(r[n]))return void this.setActiveItem(n)}}_setActiveInDefaultMode(t){this._setActiveItemByIndex(this._activeItemIndex+t,t)}_setActiveItemByIndex(t,r){const e=this._getItemsArray();if(e[t]){for(;this._skipPredicateFn(e[t]);)if(!e[t+=r])return;this.setActiveItem(t)}}_getItemsArray(){return(0,u.Hps)(this._items)?this._items():this._items instanceof u.rOR?this._items.toArray():this._items}_itemsChanged(t){if(this._typeahead?.setItems(t),this._activeItem){const r=t.indexOf(this._activeItem);r>-1&&r!==this._activeItemIndex&&(this._activeItemIndex=r,this._typeahead?.setCurrentSelectedItemIndex(r))}}}class S extends Z{constructor(){super(...arguments),this._origin="program"}setFocusOrigin(t){return this._origin=t,this}setActiveItem(t){super.setActiveItem(t),this.activeItem&&this.activeItem.focus(this._origin)}}let W=(()=>{var s;class t{constructor(e){this._platform=e}isDisabled(e){return e.hasAttribute("disabled")}isVisible(e){return function he(s){return!!(s.offsetWidth||s.offsetHeight||"function"==typeof s.getClientRects&&s.getClientRects().length)}(e)&&"visible"===getComputedStyle(e).visibility}isTabbable(e){if(!this._platform.isBrowser)return!1;const n=function Ke(s){try{return s.frameElement}catch{return null}}(function at(s){return s.ownerDocument&&s.ownerDocument.defaultView||window}(e));if(n&&(-1===je(n)||!this.isVisible(n)))return!1;let l=e.nodeName.toLowerCase(),h=je(e);return e.hasAttribute("contenteditable")?-1!==h:!("iframe"===l||"object"===l||this._platform.WEBKIT&&this._platform.IOS&&!function ot(s){let t=s.nodeName.toLowerCase(),r="input"===t&&s.type;return"text"===r||"password"===r||"select"===t||"textarea"===t}(e))&&("audio"===l?!!e.hasAttribute("controls")&&-1!==h:"video"===l?-1!==h&&(null!==h||this._platform.FIREFOX||e.hasAttribute("controls")):e.tabIndex>=0)}isFocusable(e,n){return function rt(s){return!function Ue(s){return function nt(s){return"input"==s.nodeName.toLowerCase()}(s)&&"hidden"==s.type}(s)&&(function _e(s){let t=s.nodeName.toLowerCase();return"input"===t||"select"===t||"button"===t||"textarea"===t}(s)||function it(s){return function st(s){return"a"==s.nodeName.toLowerCase()}(s)&&s.hasAttribute("href")}(s)||s.hasAttribute("contenteditable")||Ve(s))}(e)&&!this.isDisabled(e)&&(n?.ignoreVisibility||this.isVisible(e))}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(M.OD))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();function Ve(s){if(!s.hasAttribute("tabindex")||void 0===s.tabIndex)return!1;let t=s.getAttribute("tabindex");return!(!t||isNaN(parseInt(t,10)))}function je(s){if(!Ve(s))return null;const t=parseInt(s.getAttribute("tabindex")||"",10);return isNaN(t)?-1:t}class He{get enabled(){return this._enabled}set enabled(t){this._enabled=t,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}constructor(t,r,e,n,l=!1,h){this._element=t,this._checker=r,this._ngZone=e,this._document=n,this._injector=h,this._hasAttached=!1,this.startAnchorListener=()=>this.focusLastTabbableElement(),this.endAnchorListener=()=>this.focusFirstTabbableElement(),this._enabled=!0,l||this.attachAnchors()}destroy(){const t=this._startAnchor,r=this._endAnchor;t&&(t.removeEventListener("focus",this.startAnchorListener),t.remove()),r&&(r.removeEventListener("focus",this.endAnchorListener),r.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return!!this._hasAttached||(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(t){return new Promise(r=>{this._executeOnStable(()=>r(this.focusInitialElement(t)))})}focusFirstTabbableElementWhenReady(t){return new Promise(r=>{this._executeOnStable(()=>r(this.focusFirstTabbableElement(t)))})}focusLastTabbableElementWhenReady(t){return new Promise(r=>{this._executeOnStable(()=>r(this.focusLastTabbableElement(t)))})}_getRegionBoundary(t){const r=this._element.querySelectorAll(`[cdk-focus-region-${t}], [cdkFocusRegion${t}], [cdk-focus-${t}]`);return"start"==t?r.length?r[0]:this._getFirstTabbableElement(this._element):r.length?r[r.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(t){const r=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(r){if(!this._checker.isFocusable(r)){const e=this._getFirstTabbableElement(r);return e?.focus(t),!!e}return r.focus(t),!0}return this.focusFirstTabbableElement(t)}focusFirstTabbableElement(t){const r=this._getRegionBoundary("start");return r&&r.focus(t),!!r}focusLastTabbableElement(t){const r=this._getRegionBoundary("end");return r&&r.focus(t),!!r}hasAttached(){return this._hasAttached}_getFirstTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;const r=t.children;for(let e=0;e<r.length;e++){const n=r[e].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(r[e]):null;if(n)return n}return null}_getLastTabbableElement(t){if(this._checker.isFocusable(t)&&this._checker.isTabbable(t))return t;const r=t.children;for(let e=r.length-1;e>=0;e--){const n=r[e].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(r[e]):null;if(n)return n}return null}_createAnchor(){const t=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,t),t.classList.add("cdk-visually-hidden"),t.classList.add("cdk-focus-trap-anchor"),t.setAttribute("aria-hidden","true"),t}_toggleAnchorTabIndex(t,r){t?r.setAttribute("tabindex","0"):r.removeAttribute("tabindex")}toggleAnchors(t){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(t,this._startAnchor),this._toggleAnchorTabIndex(t,this._endAnchor))}_executeOnStable(t){this._injector?(0,u.mal)(t,{injector:this._injector}):setTimeout(t)}}let We=(()=>{var s;class t{constructor(e,n,l){this._checker=e,this._ngZone=n,this._injector=(0,u.WQX)(u.zZn),this._document=l}create(e,n=!1){return new He(e,this._checker,this._ngZone,this._document,n,this._injector)}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(W),u.KVO(u.SKi),u.KVO(m.qQ))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),ct=(()=>{var s;class t{get enabled(){return this.focusTrap?.enabled||!1}set enabled(e){this.focusTrap&&(this.focusTrap.enabled=e)}constructor(e,n,l){this._elementRef=e,this._focusTrapFactory=n,this._previouslyFocusedElement=null,(0,u.WQX)(M.OD).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(e){const n=e.autoCapture;n&&!n.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=(0,M.vc)(),this.focusTrap?.focusInitialElementWhenReady()}}return(s=t).\u0275fac=function(e){return new(e||s)(u.rXU(u.aKT),u.rXU(We),u.rXU(m.qQ))},s.\u0275dir=u.FsC({type:s,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[2,"cdkTrapFocus","enabled",u.L39],autoCapture:[2,"cdkTrapFocusAutoCapture","autoCapture",u.L39]},exportAs:["cdkTrapFocus"],standalone:!0,features:[u.GFd,u.OA$]}),t})();function me(s){return 0===s.buttons||0===s.detail}function Q(s){const t=s.touches&&s.touches[0]||s.changedTouches&&s.changedTouches[0];return!(!t||-1!==t.identifier||null!=t.radiusX&&1!==t.radiusX||null!=t.radiusY&&1!==t.radiusY)}const ze=new u.nKC("cdk-input-modality-detector-options"),Ze={ignoreKeys:[_.A$,_.W3,_.eg,_.Ge,_.FX]},o=(0,M.BQ)({passive:!0,capture:!0});let a=(()=>{var s;class t{get mostRecentModality(){return this._modality.value}constructor(e,n,l,h){this._platform=e,this._mostRecentTarget=null,this._modality=new I.t(null),this._lastTouchMs=0,this._onKeydown=b=>{this._options?.ignoreKeys?.some(k=>k===b.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=(0,M.Fb)(b))},this._onMousedown=b=>{Date.now()-this._lastTouchMs<650||(this._modality.next(me(b)?"keyboard":"mouse"),this._mostRecentTarget=(0,M.Fb)(b))},this._onTouchstart=b=>{Q(b)?this._modality.next("keyboard"):(this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=(0,M.Fb)(b))},this._options={...Ze,...h},this.modalityDetected=this._modality.pipe((0,R.i)(1)),this.modalityChanged=this.modalityDetected.pipe(function ie(s,t=ee.D){return s=s??z,(0,$.N)((r,e)=>{let n,l=!0;r.subscribe((0,te._)(e,h=>{const b=t(h);(l||!s(n,b))&&(l=!1,n=b,e.next(h))}))})}()),e.isBrowser&&n.runOutsideAngular(()=>{l.addEventListener("keydown",this._onKeydown,o),l.addEventListener("mousedown",this._onMousedown,o),l.addEventListener("touchstart",this._onTouchstart,o)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,o),document.removeEventListener("mousedown",this._onMousedown,o),document.removeEventListener("touchstart",this._onTouchstart,o))}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(M.OD),u.KVO(u.SKi),u.KVO(m.qQ),u.KVO(ze,8))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();var V=function(s){return s[s.IMMEDIATE=0]="IMMEDIATE",s[s.EVENTUAL=1]="EVENTUAL",s}(V||{});const N=new u.nKC("cdk-focus-monitor-default-options"),Y=(0,M.BQ)({passive:!0,capture:!0});let Ie=(()=>{var s;class t{constructor(e,n,l,h,b){this._ngZone=e,this._platform=n,this._inputModalityDetector=l,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new F.B,this._rootNodeFocusAndBlurListener=k=>{for(let J=(0,M.Fb)(k);J;J=J.parentElement)"focus"===k.type?this._onFocus(k,J):this._onBlur(k,J)},this._document=h,this._detectionMode=b?.detectionMode||V.IMMEDIATE}monitor(e,n=!1){const l=(0,O.i8)(e);if(!this._platform.isBrowser||1!==l.nodeType)return(0,y.of)();const h=(0,M.KT)(l)||this._getDocument(),b=this._elementInfo.get(l);if(b)return n&&(b.checkChildren=!0),b.subject;const k={checkChildren:n,subject:new F.B,rootNode:h};return this._elementInfo.set(l,k),this._registerGlobalListeners(k),k.subject}stopMonitoring(e){const n=(0,O.i8)(e),l=this._elementInfo.get(n);l&&(l.subject.complete(),this._setClasses(n),this._elementInfo.delete(n),this._removeGlobalListeners(l))}focusVia(e,n,l){const h=(0,O.i8)(e);h===this._getDocument().activeElement?this._getClosestElementsInfo(h).forEach(([k,G])=>this._originChanged(k,n,G)):(this._setOrigin(n),"function"==typeof h.focus&&h.focus(l))}ngOnDestroy(){this._elementInfo.forEach((e,n)=>this.stopMonitoring(n))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===V.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,n){e.classList.toggle("cdk-focused",!!n),e.classList.toggle("cdk-touch-focused","touch"===n),e.classList.toggle("cdk-keyboard-focused","keyboard"===n),e.classList.toggle("cdk-mouse-focused","mouse"===n),e.classList.toggle("cdk-program-focused","program"===n)}_setOrigin(e,n=!1){this._ngZone.runOutsideAngular(()=>{this._origin=e,this._originFromTouchInteraction="touch"===e&&n,this._detectionMode===V.IMMEDIATE&&(clearTimeout(this._originTimeoutId),this._originTimeoutId=setTimeout(()=>this._origin=null,this._originFromTouchInteraction?650:1))})}_onFocus(e,n){const l=this._elementInfo.get(n),h=(0,M.Fb)(e);!l||!l.checkChildren&&n!==h||this._originChanged(n,this._getFocusOrigin(h),l)}_onBlur(e,n){const l=this._elementInfo.get(n);!l||l.checkChildren&&e.relatedTarget instanceof Node&&n.contains(e.relatedTarget)||(this._setClasses(n),this._emitOrigin(l,null))}_emitOrigin(e,n){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(n))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;const n=e.rootNode,l=this._rootNodeFocusListenerCount.get(n)||0;l||this._ngZone.runOutsideAngular(()=>{n.addEventListener("focus",this._rootNodeFocusAndBlurListener,Y),n.addEventListener("blur",this._rootNodeFocusAndBlurListener,Y)}),this._rootNodeFocusListenerCount.set(n,l+1),1==++this._monitoredElementCount&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe((0,re.Q)(this._stopInputModalityDetector)).subscribe(h=>{this._setOrigin(h,!0)}))}_removeGlobalListeners(e){const n=e.rootNode;if(this._rootNodeFocusListenerCount.has(n)){const l=this._rootNodeFocusListenerCount.get(n);l>1?this._rootNodeFocusListenerCount.set(n,l-1):(n.removeEventListener("focus",this._rootNodeFocusAndBlurListener,Y),n.removeEventListener("blur",this._rootNodeFocusAndBlurListener,Y),this._rootNodeFocusListenerCount.delete(n))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,n,l){this._setClasses(e,n),this._emitOrigin(l,n),this._lastFocusOrigin=n}_getClosestElementsInfo(e){const n=[];return this._elementInfo.forEach((l,h)=>{(h===e||l.checkChildren&&h.contains(e))&&n.push([h,l])}),n}_isLastInteractionFromInputLabel(e){const{_mostRecentTarget:n,mostRecentModality:l}=this._inputModalityDetector;if("mouse"!==l||!n||n===e||"INPUT"!==e.nodeName&&"TEXTAREA"!==e.nodeName||e.disabled)return!1;const h=e.labels;if(h)for(let b=0;b<h.length;b++)if(h[b].contains(n))return!0;return!1}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(u.SKi),u.KVO(M.OD),u.KVO(a),u.KVO(m.qQ,8),u.KVO(N,8))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),Ae=(()=>{var s;class t{constructor(e,n){this._elementRef=e,this._focusMonitor=n,this._focusOrigin=null,this.cdkFocusChange=new u.bkB}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){const e=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(e,1===e.nodeType&&e.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(n=>{this._focusOrigin=n,this.cdkFocusChange.emit(n)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}}return(s=t).\u0275fac=function(e){return new(e||s)(u.rXU(u.aKT),u.rXU(Ie))},s.\u0275dir=u.FsC({type:s,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"],standalone:!0}),t})();var B=function(s){return s[s.NONE=0]="NONE",s[s.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",s[s.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",s}(B||{});const U="cdk-high-contrast-black-on-white",X="cdk-high-contrast-white-on-black",se="cdk-high-contrast-active";let oe=(()=>{var s;class t{constructor(e,n){this._platform=e,this._document=n,this._breakpointSubscription=(0,u.WQX)(Je).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return B.NONE;const e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);const n=this._document.defaultView||window,l=n&&n.getComputedStyle?n.getComputedStyle(e):null,h=(l&&l.backgroundColor||"").replace(/ /g,"");switch(e.remove(),h){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return B.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return B.BLACK_ON_WHITE}return B.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){const e=this._document.body.classList;e.remove(se,U,X),this._hasCheckedHighContrastMode=!0;const n=this.getHighContrastMode();n===B.BLACK_ON_WHITE?e.add(se,U):n===B.WHITE_ON_BLACK&&e.add(se,X)}}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(M.OD),u.KVO(m.qQ))},s.\u0275prov=u.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),Te=(()=>{var s;class t{constructor(e){e._applyBodyHighContrastModeCssClasses()}}return(s=t).\u0275fac=function(e){return new(e||s)(u.KVO(oe))},s.\u0275mod=u.$C({type:s}),s.\u0275inj=u.G2t({imports:[g.w5]}),t})()},8203:(q,L,f)=>{f.d(L,{dS:()=>y,jI:()=>_});var m=f(4438),u=f(177);const M=new m.nKC("cdk-dir-doc",{providedIn:"root",factory:function F(){return(0,m.WQX)(u.qQ)}}),w=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;let y=(()=>{var v;class C{constructor(A){this.value="ltr",this.change=new m.bkB,A&&(this.value=function T(v){const C=v?.toLowerCase()||"";return"auto"===C&&typeof navigator<"u"&&navigator?.language?w.test(navigator.language)?"rtl":"ltr":"rtl"===C?"rtl":"ltr"}((A.body?A.body.dir:null)||(A.documentElement?A.documentElement.dir:null)||"ltr"))}ngOnDestroy(){this.change.complete()}}return(v=C).\u0275fac=function(A){return new(A||v)(m.KVO(M,8))},v.\u0275prov=m.jDH({token:v,factory:v.\u0275fac,providedIn:"root"}),C})(),_=(()=>{var v;class C{}return(v=C).\u0275fac=function(A){return new(A||v)},v.\u0275mod=m.$C({type:v}),v.\u0275inj=m.G2t({}),C})()},4085:(q,L,f)=>{f.d(L,{FG:()=>w,OE:()=>M,a1:()=>T,he:()=>u,i8:()=>y});var m=f(4438);function u(_){return null!=_&&"false"!=`${_}`}function M(_,v=0){return function F(_){return!isNaN(parseFloat(_))&&!isNaN(Number(_))}(_)?Number(_):2===arguments.length?v:0}function w(_){return Array.isArray(_)?_:[_]}function T(_){return null==_?"":"string"==typeof _?_:`${_}px`}function y(_){return _ instanceof m.aKT?_.nativeElement:_}},7336:(q,L,f)=>{f.d(L,{A:()=>le,A$:()=>I,FX:()=>T,Fm:()=>w,Ge:()=>_e,Kp:()=>R,LE:()=>ie,UQ:()=>$,W3:()=>y,Z:()=>he,_f:()=>C,bn:()=>xe,dB:()=>D,eg:()=>mt,f2:()=>P,i7:()=>te,n6:()=>z,rp:()=>pt,t6:()=>x,w_:()=>A,wn:()=>M,yZ:()=>ee});const M=9,w=13,T=16,y=17,I=18,C=27,x=32,A=33,D=34,R=35,ee=36,$=37,te=38,ie=39,z=40,P=48,xe=57,le=65,he=90,_e=91,mt=224;function pt(De,..._t){return _t.length?_t.some(ft=>De[ft]):De.altKey||De.shiftKey||De.ctrlKey||De.metaKey}},2318:(q,L,f)=>{f.d(L,{w5:()=>T});var m=f(4438);let M=(()=>{var y;class I{create(v){return typeof MutationObserver>"u"?null:new MutationObserver(v)}}return(y=I).\u0275fac=function(v){return new(v||y)},y.\u0275prov=m.jDH({token:y,factory:y.\u0275fac,providedIn:"root"}),I})(),T=(()=>{var y;class I{}return(y=I).\u0275fac=function(v){return new(v||y)},y.\u0275mod=m.$C({type:y}),y.\u0275inj=m.G2t({providers:[M]}),I})()},6860:(q,L,f)=>{f.d(L,{BD:()=>ee,BQ:()=>C,CZ:()=>R,Fb:()=>re,KT:()=>ie,OD:()=>F,r5:()=>x,v8:()=>ae,vc:()=>z});var m=f(4438),u=f(177);let M;try{M=typeof Intl<"u"&&Intl.v8BreakIterator}catch{M=!1}let _,F=(()=>{var g;class O{constructor(K){this._platformId=K,this.isBrowser=this._platformId?(0,u.UE)(this._platformId):"object"==typeof document&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!(!window.chrome&&!M)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}}return(g=O).\u0275fac=function(K){return new(K||g)(m.KVO(m.Agw))},g.\u0275prov=m.jDH({token:g,factory:g.\u0275fac,providedIn:"root"}),O})();function C(g){return function v(){if(null==_&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>_=!0}))}finally{_=_||!1}return _}()?g:!!g.capture}var x=function(g){return g[g.NORMAL=0]="NORMAL",g[g.NEGATED=1]="NEGATED",g[g.INVERTED=2]="INVERTED",g}(x||{});let A,D,$;function R(){if(null==D){if("object"!=typeof document||!document||"function"!=typeof Element||!Element)return D=!1,D;if("scrollBehavior"in document.documentElement.style)D=!0;else{const g=Element.prototype.scrollTo;D=!!g&&!/\{\s*\[native code\]\s*\}/.test(g.toString())}}return D}function ee(){if("object"!=typeof document||!document)return x.NORMAL;if(null==A){const g=document.createElement("div"),O=g.style;g.dir="rtl",O.width="1px",O.overflow="auto",O.visibility="hidden",O.pointerEvents="none",O.position="absolute";const P=document.createElement("div"),K=P.style;K.width="2px",K.height="1px",g.appendChild(P),document.body.appendChild(g),A=x.NORMAL,0===g.scrollLeft&&(g.scrollLeft=1,A=0===g.scrollLeft?x.NEGATED:x.INVERTED),g.remove()}return A}function ie(g){if(function te(){if(null==$){const g=typeof document<"u"?document.head:null;$=!(!g||!g.createShadowRoot&&!g.attachShadow)}return $}()){const O=g.getRootNode?g.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&O instanceof ShadowRoot)return O}return null}function z(){let g=typeof document<"u"&&document?document.activeElement:null;for(;g&&g.shadowRoot;){const O=g.shadowRoot.activeElement;if(O===g)break;g=O}return g}function re(g){return g.composedPath?g.composedPath()[0]:g.target}function ae(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}},6600:(q,L,f)=>{f.d(L,{MJ:()=>ne,de:()=>le,yE:()=>P,r6:()=>_e,Ej:()=>ze,pZ:()=>Ue,tO:()=>Ze,aw:()=>Fe});var m=f(4438),u=f(687),M=f(8203),w=f(177),T=f(6860),y=f(4085),I=f(1413);const te=["mat-internal-form-field",""],ie=["*"],O=new m.nKC("mat-sanity-checks",{providedIn:"root",factory:function g(){return!0}});let P=(()=>{var d;class o{constructor(i,c,p){this._sanityChecks=c,this._document=p,this._hasDoneGlobalChecks=!1,i._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(i){return!(0,T.v8)()&&("boolean"==typeof this._sanityChecks?this._sanityChecks:!!this._sanityChecks[i])}}return(d=o).\u0275fac=function(i){return new(i||d)(m.KVO(u.Q_),m.KVO(O,8),m.KVO(w.qQ))},d.\u0275mod=m.$C({type:d}),d.\u0275inj=m.G2t({imports:[M.jI,M.jI]}),o})();const ce=new m.nKC("MAT_DATE_LOCALE",{providedIn:"root",factory:function qe(){return(0,m.WQX)(m.xe9)}});class ne{constructor(){this._localeChanges=new I.B,this.localeChanges=this._localeChanges}getValidDateOrNull(o){return this.isDateInstance(o)&&this.isValid(o)?o:null}deserialize(o){return null==o||this.isDateInstance(o)&&this.isValid(o)?o:this.invalid()}setLocale(o){this.locale=o,this._localeChanges.next()}compareDate(o,a){return this.getYear(o)-this.getYear(a)||this.getMonth(o)-this.getMonth(a)||this.getDate(o)-this.getDate(a)}sameDate(o,a){if(o&&a){let i=this.isValid(o),c=this.isValid(a);return i&&c?!this.compareDate(o,a):i==c}return o==a}clampDate(o,a,i){return a&&this.compareDate(o,a)<0?a:i&&this.compareDate(o,i)>0?i:o}}const le=new m.nKC("mat-date-formats"),ke=/^\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|(?:(?:\+|-)\d{2}:\d{2}))?)?$/;function H(d,o){const a=Array(d);for(let i=0;i<d;i++)a[i]=o(i);return a}let et=(()=>{var d;class o extends ne{constructor(i){super(),this.useUtcForDisplay=!1,this._matDateLocale=(0,m.WQX)(ce,{optional:!0}),void 0!==i&&(this._matDateLocale=i),super.setLocale(this._matDateLocale)}getYear(i){return i.getFullYear()}getMonth(i){return i.getMonth()}getDate(i){return i.getDate()}getDayOfWeek(i){return i.getDay()}getMonthNames(i){const c=new Intl.DateTimeFormat(this.locale,{month:i,timeZone:"utc"});return H(12,p=>this._format(c,new Date(2017,p,1)))}getDateNames(){const i=new Intl.DateTimeFormat(this.locale,{day:"numeric",timeZone:"utc"});return H(31,c=>this._format(i,new Date(2017,0,c+1)))}getDayOfWeekNames(i){const c=new Intl.DateTimeFormat(this.locale,{weekday:i,timeZone:"utc"});return H(7,p=>this._format(c,new Date(2017,0,p+1)))}getYearName(i){const c=new Intl.DateTimeFormat(this.locale,{year:"numeric",timeZone:"utc"});return this._format(c,i)}getFirstDayOfWeek(){if(typeof Intl<"u"&&Intl.Locale){const i=new Intl.Locale(this.locale),c=(i.getWeekInfo?.()||i.weekInfo)?.firstDay??0;return 7===c?0:c}return 0}getNumDaysInMonth(i){return this.getDate(this._createDateWithOverflow(this.getYear(i),this.getMonth(i)+1,0))}clone(i){return new Date(i.getTime())}createDate(i,c,p){let E=this._createDateWithOverflow(i,c,p);return E.getMonth(),E}today(){return new Date}parse(i,c){return"number"==typeof i?new Date(i):i?new Date(Date.parse(i)):null}format(i,c){if(!this.isValid(i))throw Error("NativeDateAdapter: Cannot format invalid date.");const p=new Intl.DateTimeFormat(this.locale,{...c,timeZone:"utc"});return this._format(p,i)}addCalendarYears(i,c){return this.addCalendarMonths(i,12*c)}addCalendarMonths(i,c){let p=this._createDateWithOverflow(this.getYear(i),this.getMonth(i)+c,this.getDate(i));return this.getMonth(p)!=((this.getMonth(i)+c)%12+12)%12&&(p=this._createDateWithOverflow(this.getYear(p),this.getMonth(p),0)),p}addCalendarDays(i,c){return this._createDateWithOverflow(this.getYear(i),this.getMonth(i),this.getDate(i)+c)}toIso8601(i){return[i.getUTCFullYear(),this._2digit(i.getUTCMonth()+1),this._2digit(i.getUTCDate())].join("-")}deserialize(i){if("string"==typeof i){if(!i)return null;if(ke.test(i)){let c=new Date(i);if(this.isValid(c))return c}}return super.deserialize(i)}isDateInstance(i){return i instanceof Date}isValid(i){return!isNaN(i.getTime())}invalid(){return new Date(NaN)}_createDateWithOverflow(i,c,p){const E=new Date;return E.setFullYear(i,c,p),E.setHours(0,0,0,0),E}_2digit(i){return("00"+i).slice(-2)}_format(i,c){const p=new Date;return p.setUTCFullYear(c.getFullYear(),c.getMonth(),c.getDate()),p.setUTCHours(c.getHours(),c.getMinutes(),c.getSeconds(),c.getMilliseconds()),i.format(p)}}return(d=o).\u0275fac=function(i){return new(i||d)(m.KVO(ce,8))},d.\u0275prov=m.jDH({token:d,factory:d.\u0275fac}),o})();const pe={parse:{dateInput:null},display:{dateInput:{year:"numeric",month:"numeric",day:"numeric"},monthYearLabel:{year:"numeric",month:"short"},dateA11yLabel:{year:"numeric",month:"long",day:"numeric"},monthYearA11yLabel:{year:"numeric",month:"long"}}};function Fe(d=pe){return[{provide:ne,useClass:et},{provide:le,useValue:d}]}var S=function(d){return d[d.FADING_IN=0]="FADING_IN",d[d.VISIBLE=1]="VISIBLE",d[d.FADING_OUT=2]="FADING_OUT",d[d.HIDDEN=3]="HIDDEN",d}(S||{});class Le{constructor(o,a,i,c=!1){this._renderer=o,this.element=a,this.config=i,this._animationForciblyDisabledThroughCss=c,this.state=S.HIDDEN}fadeOut(){this._renderer.fadeOutRipple(this)}}const ue=(0,T.BQ)({passive:!0,capture:!0});class ve{constructor(){this._events=new Map,this._delegateEventHandler=o=>{const a=(0,T.Fb)(o);a&&this._events.get(o.type)?.forEach((i,c)=>{(c===a||c.contains(a))&&i.forEach(p=>p.handleEvent(o))})}}addHandler(o,a,i,c){const p=this._events.get(a);if(p){const E=p.get(i);E?E.add(c):p.set(i,new Set([c]))}else this._events.set(a,new Map([[i,new Set([c])]])),o.runOutsideAngular(()=>{document.addEventListener(a,this._delegateEventHandler,ue)})}removeHandler(o,a,i){const c=this._events.get(o);if(!c)return;const p=c.get(a);p&&(p.delete(i),0===p.size&&c.delete(a),0===c.size&&(this._events.delete(o),document.removeEventListener(o,this._delegateEventHandler,ue)))}}const Re={enterDuration:225,exitDuration:150},be=(0,T.BQ)({passive:!0,capture:!0}),Pe=["mousedown","touchstart"],Be=["mouseup","mouseleave","touchend","touchcancel"];class W{constructor(o,a,i,c){this._target=o,this._ngZone=a,this._platform=c,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,c.isBrowser&&(this._containerElement=(0,y.i8)(i))}fadeInRipple(o,a,i={}){const c=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),p={...Re,...i.animation};i.centered&&(o=c.left+c.width/2,a=c.top+c.height/2);const E=i.radius||function Ke(d,o,a){const i=Math.max(Math.abs(d-a.left),Math.abs(d-a.right)),c=Math.max(Math.abs(o-a.top),Math.abs(o-a.bottom));return Math.sqrt(i*i+c*c)}(o,a,c),Ee=o-c.left,lt=a-c.top,V=p.enterDuration,N=document.createElement("div");N.classList.add("mat-ripple-element"),N.style.left=Ee-E+"px",N.style.top=lt-E+"px",N.style.height=2*E+"px",N.style.width=2*E+"px",null!=i.color&&(N.style.backgroundColor=i.color),N.style.transitionDuration=`${V}ms`,this._containerElement.appendChild(N);const Y=window.getComputedStyle(N),Ae=Y.transitionDuration,B="none"===Y.transitionProperty||"0s"===Ae||"0s, 0s"===Ae||0===c.width&&0===c.height,U=new Le(this,N,i,B);N.style.transform="scale3d(1, 1, 1)",U.state=S.FADING_IN,i.persistent||(this._mostRecentTransientRipple=U);let X=null;return!B&&(V||p.exitDuration)&&this._ngZone.runOutsideAngular(()=>{const se=()=>{X&&(X.fallbackTimer=null),clearTimeout(Te),this._finishRippleTransition(U)},oe=()=>this._destroyRipple(U),Te=setTimeout(oe,V+100);N.addEventListener("transitionend",se),N.addEventListener("transitioncancel",oe),X={onTransitionEnd:se,onTransitionCancel:oe,fallbackTimer:Te}}),this._activeRipples.set(U,X),(B||!V)&&this._finishRippleTransition(U),U}fadeOutRipple(o){if(o.state===S.FADING_OUT||o.state===S.HIDDEN)return;const a=o.element,i={...Re,...o.config.animation};a.style.transitionDuration=`${i.exitDuration}ms`,a.style.opacity="0",o.state=S.FADING_OUT,(o._animationForciblyDisabledThroughCss||!i.exitDuration)&&this._finishRippleTransition(o)}fadeOutAll(){this._getActiveRipples().forEach(o=>o.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(o=>{o.config.persistent||o.fadeOut()})}setupTriggerEvents(o){const a=(0,y.i8)(o);!this._platform.isBrowser||!a||a===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=a,Pe.forEach(i=>{W._eventManager.addHandler(this._ngZone,i,a,this)}))}handleEvent(o){"mousedown"===o.type?this._onMousedown(o):"touchstart"===o.type?this._onTouchStart(o):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Be.forEach(a=>{this._triggerElement.addEventListener(a,this,be)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(o){o.state===S.FADING_IN?this._startFadeOutTransition(o):o.state===S.FADING_OUT&&this._destroyRipple(o)}_startFadeOutTransition(o){const a=o===this._mostRecentTransientRipple,{persistent:i}=o.config;o.state=S.VISIBLE,!i&&(!a||!this._isPointerDown)&&o.fadeOut()}_destroyRipple(o){const a=this._activeRipples.get(o)??null;this._activeRipples.delete(o),this._activeRipples.size||(this._containerRect=null),o===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),o.state=S.HIDDEN,null!==a&&(o.element.removeEventListener("transitionend",a.onTransitionEnd),o.element.removeEventListener("transitioncancel",a.onTransitionCancel),null!==a.fallbackTimer&&clearTimeout(a.fallbackTimer)),o.element.remove()}_onMousedown(o){const a=(0,u._G)(o),i=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+800;!this._target.rippleDisabled&&!a&&!i&&(this._isPointerDown=!0,this.fadeInRipple(o.clientX,o.clientY,this._target.rippleConfig))}_onTouchStart(o){if(!this._target.rippleDisabled&&!(0,u.w6)(o)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;const a=o.changedTouches;if(a)for(let i=0;i<a.length;i++)this.fadeInRipple(a[i].clientX,a[i].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(o=>{!o.config.persistent&&(o.state===S.VISIBLE||o.config.terminateOnPointerUp&&o.state===S.FADING_IN)&&o.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){const o=this._triggerElement;o&&(Pe.forEach(a=>W._eventManager.removeHandler(a,o,this)),this._pointerUpEventsRegistered&&(Be.forEach(a=>o.removeEventListener(a,this,be)),this._pointerUpEventsRegistered=!1))}}W._eventManager=new ve;const he=new m.nKC("mat-ripple-global-options");let _e=(()=>{var d;class o{get disabled(){return this._disabled}set disabled(i){i&&this.fadeOutAllNonPersistent(),this._disabled=i,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(i){this._trigger=i,this._setupTriggerEventsIfEnabled()}constructor(i,c,p,E,Ee){this._elementRef=i,this._animationMode=Ee,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=E||{},this._rippleRenderer=new W(this,c,i,p)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:{...this._globalOptions.animation,..."NoopAnimations"===this._animationMode?{enterDuration:0,exitDuration:0}:{},...this.animation},terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(i,c=0,p){return"number"==typeof i?this._rippleRenderer.fadeInRipple(i,c,{...this.rippleConfig,...p}):this._rippleRenderer.fadeInRipple(0,0,{...this.rippleConfig,...i})}}return(d=o).\u0275fac=function(i){return new(i||d)(m.rXU(m.aKT),m.rXU(m.SKi),m.rXU(T.OD),m.rXU(he,8),m.rXU(m.bc$,8))},d.\u0275dir=m.FsC({type:d,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(i,c){2&i&&m.AVh("mat-ripple-unbounded",c.unbounded)},inputs:{color:[0,"matRippleColor","color"],unbounded:[0,"matRippleUnbounded","unbounded"],centered:[0,"matRippleCentered","centered"],radius:[0,"matRippleRadius","radius"],animation:[0,"matRippleAnimation","animation"],disabled:[0,"matRippleDisabled","disabled"],trigger:[0,"matRippleTrigger","trigger"]},exportAs:["matRipple"],standalone:!0}),o})(),Ue=(()=>{var d;class o{}return(d=o).\u0275fac=function(i){return new(i||d)},d.\u0275mod=m.$C({type:d}),d.\u0275inj=m.G2t({imports:[P,P]}),o})();const Ge={capture:!0},$e=["focus","mousedown","mouseenter","touchstart"],ye="mat-ripple-loader-uninitialized",Me="mat-ripple-loader-class-name",me="mat-ripple-loader-centered",Q="mat-ripple-loader-disabled";let ze=(()=>{var d;class o{constructor(){this._document=(0,m.WQX)(w.qQ,{optional:!0}),this._animationMode=(0,m.WQX)(m.bc$,{optional:!0}),this._globalRippleOptions=(0,m.WQX)(he,{optional:!0}),this._platform=(0,m.WQX)(T.OD),this._ngZone=(0,m.WQX)(m.SKi),this._hosts=new Map,this._onInteraction=i=>{const c=(0,T.Fb)(i);if(c instanceof HTMLElement){const p=c.closest(`[${ye}="${this._globalRippleOptions?.namespace??""}"]`);p&&this._createRipple(p)}},this._ngZone.runOutsideAngular(()=>{for(const i of $e)this._document?.addEventListener(i,this._onInteraction,Ge)})}ngOnDestroy(){const i=this._hosts.keys();for(const c of i)this.destroyRipple(c);for(const c of $e)this._document?.removeEventListener(c,this._onInteraction,Ge)}configureRipple(i,c){i.setAttribute(ye,this._globalRippleOptions?.namespace??""),(c.className||!i.hasAttribute(Me))&&i.setAttribute(Me,c.className||""),c.centered&&i.setAttribute(me,""),c.disabled&&i.setAttribute(Q,"")}getRipple(i){return this._hosts.get(i)||this._createRipple(i)}setDisabled(i,c){const p=this._hosts.get(i);p?p.disabled=c:c?i.setAttribute(Q,""):i.removeAttribute(Q)}_createRipple(i){if(!this._document)return;const c=this._hosts.get(i);if(c)return c;i.querySelector(".mat-ripple")?.remove();const p=this._document.createElement("span");p.classList.add("mat-ripple",i.getAttribute(Me)),i.append(p);const E=new _e(new m.aKT(p),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return E._isInitialized=!0,E.trigger=i,E.centered=i.hasAttribute(me),E.disabled=i.hasAttribute(Q),this.attachRipple(i,E),E}attachRipple(i,c){i.removeAttribute(ye),this._hosts.set(i,c)}destroyRipple(i){const c=this._hosts.get(i);c&&(c.ngOnDestroy(),this._hosts.delete(i))}}return(d=o).\u0275fac=function(i){return new(i||d)},d.\u0275prov=m.jDH({token:d,factory:d.\u0275fac,providedIn:"root"}),o})(),Ze=(()=>{var d;class o{}return(d=o).\u0275fac=function(i){return new(i||d)},d.\u0275cmp=m.VBU({type:d,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(i,c){2&i&&m.AVh("mdc-form-field--align-end","before"===c.labelPosition)},inputs:{labelPosition:"labelPosition"},standalone:!0,features:[m.aNF],attrs:te,ngContentSelectors:ie,decls:1,vars:0,template:function(i,c){1&i&&(m.NAR(),m.SdG(0))},styles:[".mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-flex;align-items:center;vertical-align:middle}.mat-internal-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mat-internal-form-field>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end .mdc-form-field--align-end label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0}"],encapsulation:2,changeDetection:0}),o})()}}]);