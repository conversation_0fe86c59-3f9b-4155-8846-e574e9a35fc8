"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3767],{3767:(O,c,t)=>{t.r(c),t.d(c,{SendInvoiceComponent:()=>A});var o=t(9842),v=t(1342),h=t(5644),e=t(4438),_=t(4006),f=t(344),E=t(4978),S=t(6473),I=t(6146),C=t(1110),b=t(3492),p=t(9079),g=t(274),r=t(9417),u=t(3719),y=t(177);function D(s,a){if(1&s){const m=e.RV6();e.j41(0,"div",16),e.EFF(1),e.j41(2,"div",17),e.bIt("click",function(n){const l=e.eBV(m).$index,d=e.XpG(2);return e.Njj(d.handleRemoveEmailClient(n,l))}),e.nrm(3,"img",18),e.k0s()()}if(2&s){const m=a.$implicit;e.R7$(),e.SpI(" ",m," ")}}function x(s,a){if(1&s&&(e.j41(0,"div",4)(1,"div",15),e.Z7z(2,D,4,1,"div",16,e.fX1),e.k0s()()),2&s){const m=e.XpG();e.R7$(2),e.Dyx(m.listEmailSelected)}}function M(s,a){1&s&&(e.j41(0,"mat-error",8),e.EFF(1,"Email is required"),e.k0s())}function P(s,a){1&s&&(e.j41(0,"mat-error",8),e.EFF(1,"Invalid email"),e.k0s())}let A=(()=>{var s;class a{static getComponent(){return a}constructor(i,n){(0,o.A)(this,"dialogRef",void 0),(0,o.A)(this,"data",void 0),(0,o.A)(this,"sendInvoiceForm",void 0),(0,o.A)(this,"listEmailSelected",[]),(0,o.A)(this,"invoice",null),(0,o.A)(this,"_storeService",(0,e.WQX)(C.n)),(0,o.A)(this,"_toastService",(0,e.WQX)(b.f)),(0,o.A)(this,"_invoiceService",(0,e.WQX)(h.p)),(0,o.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,o.A)(this,"spinnerService",(0,e.WQX)(v.D)),(0,o.A)(this,"formBuilder",(0,e.WQX)(r.ze)),this.dialogRef=i,this.data=n,this.sendInvoiceForm=this.formBuilder.group({email:["",r.k0.compose([r.k0.required,r.k0.email])]})}get f(){return this.sendInvoiceForm.controls}handleClose(){this.dialogRef.close()}ngOnInit(){this.invoice=this.data??{}}handleCancel(){this.dialogRef.close()}get listSelectedIdClient(){return this.listEmailSelected.map(i=>i.id)}get businessInfo(){const i=this._storeService.get_UserBusiness();return{businessName:i?.company?.businessName??"",businessPhoneNumber:i?.company?.phone??"",businessAddress:(0,S.Aw)({addressLine1:i?.company?.adress??"",addressLine2:i?.company?.adress2??"",stateProvince:i?.company?.province??"",postalCode:i?.company?.postalCode??"",country:i?.company?.country??""})}}handleSelectClient(i){const n=i?.metadata?.client;!n||this.listEmailSelected.some(d=>d.id===n.id)||this.listEmailSelected.push(n)}handleRemoveEmailClient(i,n){i.stopPropagation(),this.listEmailSelected.splice(n,1)}handleShare(){this._toastService.showInfo("The feature is in development."," ")}formatDate(i){return new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}onSubmit(){this.listEmailSelected.push(this.f.email.value),this.f.email.setValue("")}handleSave(){this.listEmailSelected.length>0?(this.spinnerService.show(),this._invoiceService.CreatedInvoice(this.invoice).pipe((0,p.pQ)(this.destroyRef),(0,g.H)(i=>i?this._invoiceService.CreatedInvoiceSend({invoiceId:i.id,clientId:this.data.clientId,isEstimate:!1,ListEmail:this.listEmailSelected}).pipe((0,p.pQ)(this.destroyRef)):[])).subscribe(i=>{if(i){const n=this.listEmailSelected;let l={id:i.id,businessName:this._storeService.get_UserBusiness().businessName,invoiceNumber:i.invoiceNumber,payAmount:i.paidAmount,date:this.formatDate(i.invoiceDate),listEmail:n};this._invoiceService.SendMailInvoice(l).subscribe(),this.spinnerService.hide(),this.dialogRef.close(i),this._toastService.showSuccess("Send","Success")}},i=>{console.error("Error:",i)})):this._toastService.showInfo("Infor","Enter to confirm the email you want to send")}}return s=a,(0,o.A)(a,"\u0275fac",function(i){return new(i||s)(e.rXU(_.CP),e.rXU(_.Vh))}),(0,o.A)(a,"\u0275cmp",e.VBU({type:s,selectors:[["app-send-invoice"]],standalone:!0,features:[e.aNF],decls:23,vars:13,consts:[["title","Send Invoice",3,"onClose"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","min-h-[46px]","cursor-pointer","rounded-md","border-2","border-border-primary","py-[6px]","px-[4px]","flex","justify-between","gap-[4px]"],[1,"mt-3",3,"ngSubmit","formGroup"],[1,"mb-6"],["type","email","id","_email","formControlName","email","placeholder","Email","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"matError"],[1,"bg-bg-brand-primary","rounded-md","p-[16px]","mt-[16px]"],[1,"text-text-primary","text-text-md-semibold","mb-[4px]"],[1,"text-text-secondary","text-text-sm-regular"],[1,"button-link-primary","mt-[16px]",3,"click"],["src","../../../../../../assets/img/icon/ic_link.svg","alt","Icon"],["textCancel","Back to Invoice","textSubmit","Send Email",3,"onCancel","onSubmit"],[1,"grow","flex","flex-wrap","gap-[4px]"],[1,"rounded-[100px]","text-text-secondary","text-text-sm-medium","border","border-border-primary","bg-bg-secondary","flex","gap-[4px]","items-center","py-[4px]","px-[8px]"],[1,"w-[16px]","shrink-0","h-[16px]","hover:bg-bg-danger-secondary","flex","justify-center","items-center","rounded-xs",3,"click"],["src","../../../../../../assets/img/icon/ic_remove.svg","alt","Icon",1,"w-[16px]"]],template:function(i,n){1&i&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return n.handleClose()}),e.j41(1,"div",1)(2,"div",2)(3,"label",3),e.EFF(4,"To Email"),e.k0s(),e.DNE(5,x,4,0,"div",4),e.j41(6,"form",5),e.bIt("ngSubmit",function(){return n.onSubmit()}),e.j41(7,"div",6),e.nrm(8,"input",7),e.DNE(9,M,2,0,"mat-error",8)(10,P,2,0,"mat-error",8),e.k0s()()(),e.j41(11,"div",9)(12,"p",10),e.EFF(13),e.k0s(),e.j41(14,"p",11),e.EFF(15),e.nI1(16,"date"),e.nrm(17,"br"),e.EFF(18," Thank you! "),e.k0s()(),e.j41(19,"button",12),e.bIt("click",function(){return n.handleShare()}),e.nrm(20,"img",13),e.EFF(21," Share by the link "),e.k0s()(),e.j41(22,"app-inno-modal-footer",14),e.bIt("onCancel",function(){return n.handleCancel()})("onSubmit",function(){return n.handleSave()}),e.k0s()()),2&i&&(e.R7$(5),e.vxM(n.listEmailSelected.length>0?5:-1),e.R7$(),e.Y8G("formGroup",n.sendInvoiceForm),e.R7$(3),e.vxM((n.f.email.dirty||n.f.email.touched)&&n.f.email.hasError("required")?9:-1),e.R7$(),e.vxM((n.f.email.dirty||n.f.email.touched)&&n.f.email.hasError("email")?10:-1),e.R7$(3),e.Lme(" ",n.businessInfo.businessName," sent you an invoice (",null==n.invoice?null:n.invoice.invoiceNumber,") "),e.R7$(2),e.LHq(" ",n.businessInfo.businessName," has sent you an invoice (",null==n.invoice?null:n.invoice.invoiceNumber,") with an amount of $",null==n.invoice?null:n.invoice.totalAmount,", due on ",e.i5U(16,10,null==n.invoice?null:n.invoice.dueDate,"MMM d, yyyy"),". If you have any questions or require further details, please feel free to reach out. "))},dependencies:[I.G,r.qT,r.me,r.BC,r.cb,r.YS,r.j4,r.JD,y.vh,u.RG,u.TL,E.I,f.k],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),a})()}}]);