"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9789],{9789:(W,d,o)=>{o.r(d),o.d(d,{AddEntryComponent:()=>y});var i=o(9842),e=o(4438),m=o(4006),v=o(5072),g=o(7656),h=o(7086),u=o(4262),f=o(8232),E=o(2480),C=o(658),I=o(5277),T=o(6146),b=o(6473),A=o(8194),j=o(177);const c=r=>({"text-text-primary":r});function D(r,a){if(1&r&&(e.j41(0,"button",17)(1,"span",18),e.EFF(2),e.k0s()()),2&r){let l;const n=e.XpG();e.R7$(),e.Y8G("ngClass",e.eq3(3,c,n.previewWorkingInfo)),e.R7$(),e.Lme(" ",null!=n.previewWorkingInfo&&null!=n.previewWorkingInfo.metadata&&null!=n.previewWorkingInfo.metadata.objectClient&&n.previewWorkingInfo.metadata.objectClient.clientName?(null==n.previewWorkingInfo||null==n.previewWorkingInfo.metadata||null==n.previewWorkingInfo.metadata.objectClient?null:n.previewWorkingInfo.metadata.objectClient.clientName)+"-":""," ",null!==(l=null==n.previewWorkingInfo?null:n.previewWorkingInfo.label)&&void 0!==l?l:"What are you working on?"," ")}}function k(r,a){if(1&r&&(e.j41(0,"button",20)(1,"span",18),e.EFF(2),e.k0s()()),2&r){let l;const n=e.XpG(2);e.Y8G("ngClass",e.eq3(3,c,n.previewServiceInfo)),e.R7$(),e.Y8G("ngClass",e.eq3(5,c,n.previewWorkingInfo)),e.R7$(),e.SpI(" ",null!==(l=null==n.previewServiceInfo?null:n.previewServiceInfo.label)&&void 0!==l?l:"Choose Service"," ")}}function w(r,a){if(1&r){const l=e.RV6();e.j41(0,"app-inno-select-search-service",19),e.bIt("onSelect",function(t){e.eBV(l);const p=e.XpG();return e.Njj(p.handleSelectServices(t))}),e.k0s(),e.DNE(1,k,3,7,"ng-template",null,2,e.C5r)}if(2&r){const l=e.sdS(2),n=e.XpG();e.Y8G("templateTrigger",l)("lable",n.previewWorkingInfo.label)("value",n.projectId)}}function P(r,a){1&r&&e.nrm(0,"app-inno-tags",21)}let y=(()=>{var r;class a{static getComponent(){return a}constructor(n,t){(0,i.A)(this,"dialogRef",void 0),(0,i.A)(this,"data",void 0),(0,i.A)(this,"previewServiceInfo",void 0),(0,i.A)(this,"previewWorkingInfo",void 0),(0,i.A)(this,"previewDescription",""),(0,i.A)(this,"previewDate",void 0),(0,i.A)(this,"labelDate",""),(0,i.A)(this,"previewBillable",!1),(0,i.A)(this,"previewTimeEnd",""),(0,i.A)(this,"projectId",""),(0,i.A)(this,"dataService",(0,e.WQX)(I.u)),(0,i.A)(this,"timeTrackingProvider",(0,e.WQX)(C.o)),this.dialogRef=n,this.data=t}ngOnInit(){this.previewDate=this.data?.date,this.previewDate&&(this.labelDate=(0,b.lH)(this.previewDate))}handleSelectProject(n){this.previewWorkingInfo=n,this.projectId=n.value;const t=n.metadata?.project?.billable;this.previewBillable=t}handleChangeNote(n){this.previewDescription=n}handleChangeBillable(n){this.previewBillable=n}handleChangeEndTime(n){this.previewTimeEnd=n}handleAddTimeTrackingRecord(){this.timeTrackingProvider.handleCreateTimeTracking({payload:{endTime:this.previewTimeEnd,billable:this.previewBillable??!1,date:this.previewDate,description:this.previewDescription??"",clientId:this.previewWorkingInfo?.metadata?.objectClient?.id,projectId:"project"==this.previewWorkingInfo?.metadata?.type?this.previewWorkingInfo?.value:null,serviceId:this.previewServiceInfo?.value},optional:{callbackSuccess:()=>{this.dataService.triggerRefreshListTimeTracking(),this.dialogRef.close()}}})}handleSelectServices(n){this.previewServiceInfo=n}handleCancel(){this.dialogRef.close()}ngOnDestroy(){}}return r=a,(0,i.A)(a,"\u0275fac",function(n){return new(n||r)(e.rXU(m.CP),e.rXU(m.Vh))}),(0,i.A)(a,"\u0275cmp",e.VBU({type:r,selectors:[["app-add-entry"]],standalone:!0,features:[e.aNF],decls:23,vars:10,consts:[["templateTriggerSelectProject",""],["templateTriggerSelectTags",""],["templateTriggerSelectService",""],[1,"w-full","bg-bg-primary"],[1,"px-[8px]","py-[16px]","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],[1,"w-full","p-[8px]","flex","flex-col","gap-[8px]"],[1,"w-full"],[3,"onSelect","templateTrigger","isOnlySelectProject","value"],["placeholder","Note",3,"onChange","value"],[3,"onChange","value"],[1,"flex","items-center","gap-[12px]"],[3,"templateTrigger"],[3,"onChange","checked"],[1,"w-full","p-[16px]","flex","items-center","justify-end","border-t","border-border-primary-slight","gap-[12px]"],[1,"button-outline","button-size-md",3,"click"],[1,"button-primary","button-size-md",3,"click","disabled"],[1,"h-[40px]","cursor-pointer","border-2","rounded-md","border-border-primary","w-full"],[1,"px-[8px]","text-left","line-clamp-1","text-text-md-regular","text-text-placeholder",3,"ngClass"],[3,"onSelect","templateTrigger","lable","value"],[1,"h-[40px]","cursor-pointer","border-2","rounded-md","border-border-primary","w-ful","mt-2",3,"ngClass"],["target","","value","Only InnoBook"]],template:function(n,t){if(1&n){const p=e.RV6();e.j41(0,"div",3)(1,"div",4)(2,"p",5),e.EFF(3),e.k0s()(),e.j41(4,"div",6)(5,"div",7)(6,"app-inno-select-search-project",8),e.bIt("onSelect",function(s){return e.eBV(p),e.Njj(t.handleSelectProject(s))}),e.k0s(),e.DNE(7,D,3,5,"ng-template",null,0,e.C5r)(9,w,3,3),e.k0s(),e.j41(10,"app-inno-form-textarea",9),e.bIt("onChange",function(s){return e.eBV(p),e.Njj(t.handleChangeNote(s))}),e.k0s(),e.j41(11,"app-inno-enter-hours",10),e.bIt("onChange",function(s){return e.eBV(p),e.Njj(t.handleChangeEndTime(s))}),e.k0s(),e.j41(12,"div",11),e.nrm(13,"app-inno-select-search-tags",12),e.DNE(14,P,1,0,"ng-template",null,1,e.C5r),e.j41(16,"app-inno-form-checkbox",13),e.bIt("onChange",function(s){return e.eBV(p),e.Njj(t.handleChangeBillable(s))}),e.EFF(17," Billable "),e.k0s()()(),e.j41(18,"div",14)(19,"button",15),e.bIt("click",function(){return e.eBV(p),e.Njj(t.handleCancel())}),e.EFF(20," Cancel "),e.k0s(),e.j41(21,"button",16),e.bIt("click",function(){return e.eBV(p),e.Njj(t.handleAddTimeTrackingRecord())}),e.EFF(22," Add entry "),e.k0s()()()}if(2&n){const p=e.sdS(8),_=e.sdS(15);e.R7$(3),e.SpI(" Add entry on ",t.labelDate," "),e.R7$(3),e.Y8G("templateTrigger",p)("isOnlySelectProject",!0)("value",null==t.previewWorkingInfo?null:t.previewWorkingInfo.value),e.R7$(3),e.vxM(t.previewWorkingInfo?9:-1),e.R7$(),e.Y8G("value",t.previewDescription),e.R7$(),e.Y8G("value",t.previewTimeEnd),e.R7$(2),e.Y8G("templateTrigger",_),e.R7$(3),e.Y8G("checked",t.previewBillable),e.R7$(5),e.Y8G("disabled",!t.previewWorkingInfo||!t.previewDate||!t.previewTimeEnd||"00:00"==t.previewTimeEnd)}},dependencies:[T.G,j.YU,u.B,h.C,f.R,E.n,g.V,A.H,v.N],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),a})()}}]);