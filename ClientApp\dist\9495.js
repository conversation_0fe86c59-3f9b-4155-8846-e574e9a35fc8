"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9495],{9495:(A,c,a)=>{a.r(c),a.d(c,{NewCategoryComponent:()=>T});var s=a(9842),g=a(9097),d=a(3492),p=a(1342),u=a(1470),e=a(4438),C=a(6146),m=a(9079),y=a(4006),v=a(177),_=a(9417),b=a(5236);const f=(o,i)=>({"bg-green-700 hover:bg-green-800 text-white":o,"bg-gray-400":i});function h(o,i){if(1&o){const n=e.RV6();e.j41(0,"span",13),e.bIt("click",function(){e.eBV(n);const t=e.XpG();return e.Njj(t.RemoveAll())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}2&o&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"CATEGORY.RemoveAll")," "))}function E(o,i){if(1&o){const n=e.RV6();e.j41(0,"div",4)(1,"div",14),e.EFF(2),e.j41(3,"div",15),e.bIt("click",function(){const t=e.eBV(n).$index,l=e.XpG();return e.Njj(l.RemoveService(t))}),e.j41(4,"span",9),e.EFF(5," delete_forever "),e.k0s()()()()}if(2&o){const n=i.$implicit;e.R7$(2),e.SpI(" ",n.categoryName," ")}}function R(o,i){if(1&o){const n=e.RV6();e.j41(0,"input",16),e.nI1(1,"translate"),e.mxI("ngModelChange",function(t){e.eBV(n);const l=e.XpG();return e.DH7(l.categoryName,t)||(l.categoryName=t),e.Njj(t)}),e.k0s()}if(2&o){const n=e.XpG();e.R50("ngModel",n.categoryName),e.Y8G("placeholder",e.bMT(1,2,"CATEGORY.GIRD.CategoryName"))}}function M(o,i){if(1&o){const n=e.RV6();e.j41(0,"div",17),e.bIt("click",function(){e.eBV(n);const t=e.XpG();return e.Njj(t.CreatedCategory())}),e.j41(1,"span",9),e.EFF(2," add "),e.k0s(),e.EFF(3),e.nI1(4,"translate"),e.j41(5,"span"),e.EFF(6),e.k0s()()}if(2&o){const n=e.XpG();e.R7$(3),e.SpI(" ",e.bMT(4,2,"CATEGORY.CreateCategory")," "),e.R7$(3),e.SpI('" ',n.categoryName,' "')}}let T=(()=>{var o;class i{static getComponent(){return i}constructor(r){(0,s.A)(this,"dialogRef",void 0),(0,s.A)(this,"_spinnerService",(0,e.WQX)(p.D)),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"_toastService",(0,e.WQX)(d.f)),(0,s.A)(this,"_categoryService",(0,e.WQX)(g.M)),(0,s.A)(this,"isAddSvcMode",!0),(0,s.A)(this,"listCategory",[]),(0,s.A)(this,"categoryName",void 0),this.dialogRef=r}ngOnInit(){}RemoveAll(){this.listCategory=[]}RemoveService(r){this.listCategory.splice(r,1)}CreatedCategory(){this.isAddSvcMode=!1,this.listCategory.push({categoryName:this.categoryName}),this.categoryName=""}addCategory(){this.isAddSvcMode=!0}closeDialog(){this.dialogRef.close()}Save(){this._spinnerService.show(),this._categoryService.CreateCategory(this.listCategory).pipe((0,m.pQ)(this.destroyRef)).subscribe(r=>{r&&(this._toastService.showSuccess("Save","Success"),this._spinnerService.hide(),this.dialogRef.close(r))})}}return o=i,(0,s.A)(i,"\u0275fac",function(r){return new(r||o)(e.rXU(y.CP))}),(0,s.A)(i,"\u0275cmp",e.VBU({type:o,selectors:[["app-new-category"]],standalone:!0,features:[e.aNF],decls:26,vars:20,consts:[[3,"onClose"],[1,"w-full","text-center"],[1,"p-3"],[1,"text-blue-500","ml-2","cursor-pointer"],[1,"relative","group","w-full","border","rounded-md","p-2","mb-2"],[1,"mb-2"],["type","text",1,"bg-white","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModel","placeholder"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500"],["type","button",1,"btn","btn-outline-secondary","btn-dashed",3,"click"],[1,"material-icons"],[1,"w-full","flex","justify-around","mt-3"],["type","button",1,"text-gray-900","bg-white","border","border-gray-300","focus:outline-none","hover:bg-gray-100","focus:ring-4","focus:ring-gray-100","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["type","button",1,"text-gray-900","focus:outline-none","hover:bg-gray-300","focus:ring-4","focus:ring-gray-100","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click","disabled","ngClass"],[1,"text-blue-500","ml-2","cursor-pointer",3,"click"],[1,"flex","items-center","w-full","justify-between"],[1,"hidden","group-hover:block","text-end","cursor-pointer",3,"click"],["type","text",1,"bg-white","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","ngModel","placeholder"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500",3,"click"]],template:function(r,t){1&r&&(e.j41(0,"app-innobook-modal-wrapper",0),e.bIt("onClose",function(){return t.closeDialog()}),e.j41(1,"h4",1),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"div",2)(5,"div"),e.DNE(6,h,3,3,"span",3),e.k0s(),e.nrm(7,"hr"),e.Z7z(8,E,6,1,"div",4,e.fX1),e.j41(10,"div")(11,"div",5),e.DNE(12,R,2,4,"input",6)(13,M,7,4,"div",7),e.k0s(),e.j41(14,"button",8),e.bIt("click",function(){return t.addCategory()}),e.j41(15,"span",9),e.EFF(16,"add"),e.k0s(),e.EFF(17),e.nI1(18,"translate"),e.k0s()(),e.j41(19,"div",10)(20,"button",11),e.bIt("click",function(){return t.closeDialog()}),e.EFF(21),e.nI1(22,"translate"),e.k0s(),e.j41(23,"button",12),e.bIt("click",function(){return t.Save()}),e.EFF(24),e.nI1(25,"translate"),e.k0s()()()()),2&r&&(e.R7$(2),e.SpI(" ",e.bMT(3,9,"CATEGORY.TitleAdd"),""),e.R7$(4),e.vxM(t.listCategory.length>0?6:-1),e.R7$(2),e.Dyx(t.listCategory),e.R7$(4),e.vxM(t.isAddSvcMode?12:-1),e.R7$(),e.vxM(t.categoryName?13:-1),e.R7$(4),e.SpI(" ",e.bMT(18,11,"CATEGORY.AddCateGory")," "),e.R7$(4),e.SpI(" ",e.bMT(22,13,"BUTTON.Cancel"),""),e.R7$(2),e.Y8G("disabled",0==t.listCategory.length)("ngClass",e.l_i(17,f,t.listCategory.length>0,0==t.listCategory.length)),e.R7$(),e.JRh(e.bMT(25,15,"BUTTON.Save")))},dependencies:[C.G,v.YU,_.me,_.BC,_.vS,b.D9,u.j]})),i})()}}]);