import { CdnService } from './../../../../service/cdn.service';
import { CompanyService } from './../../../../service/company.service';
import { SelectExpensesDialog } from './../../../../service/dialog/select-expenses.dialog';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { InvoiceService } from './../../../../service/invoice.service';
import { TaxService } from './../../../../service/tax.service';
import { ExpensesService } from './../../../../service/expenses.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, Inject, inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { ToastService } from 'app/service/toast.service';
import moment from 'moment-timezone';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { InnoUploadComponent } from 'app/component/inno-upload/inno-upload.component';
import { getBase64AndFileName, getFullAddress } from 'app/helpers/common.helper';
import { Router, RouterModule } from '@angular/router';
import { DataService } from 'app/service/data.service';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { InvoiceItem } from '../../../../dto/interface/invoiceItem.interface';
import { ModifyInvoiceItemDialog } from '../../../../service/dialog/modify-invoice-item.dialog';
import { ModifyTaxesDialog } from '../../../../service/dialog/modify-taxes.dialog';
import { SelectTimeTrackingDialog } from '../../../../service/dialog/select-time-tracking.dialog';
import { SendInvoiceDialog } from '../../../../service/dialog/send-invoice.dialog';
import { AddNewItemDialog } from 'app/service/dialog/add-new-item-invoice.dialog';
import { SpinnerService } from 'app/service/spinner.service';
import { User } from 'app/dto/interface/user.interface';
import {
  CdkDragDrop,
  CdkDrag,
  CdkDropList,
  CdkDropListGroup,
  moveItemInArray,
  transferArrayItem,
  DragDropModule,
} from '@angular/cdk/drag-drop';
import { Role } from 'app/enum/role.enum';
import { DatePipe } from '@angular/common';
import { calculateGroupedTaxes, calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
import { TranslateService } from '@ngx-translate/core';
interface IModifyInvoice {
  id?: string;
  clientId?: string;
  invoiceDate?: Date,
  invoiceNumber?: string,
  contractorId?: string,
  projectId: string,
  dueDate?: Date,
  createdAt?: any,
  project?: any,
  img?: string,
  position?: number,
  notes?: string,
  totalAmount: number,
  company: any,
  isGenrate: boolean,
  itemInvoices: InvoiceItem[]
}
@Component({
  selector: 'app-new-invoice',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    AvatarModule,
    RouterModule,
    DragDropModule,
    InnoModalWrapperComponent,
    InnoFormSelectSearchComponent,
    InnoFormInputComponent,
    InnoFormDatepickerComponent,
    InnoFormTextareaComponent,
    InnoTableActionComponent,
    InnoModalFooterComponent,
    InnoUploadComponent,
    FormatNumberPipe,
    DecimalPipe,
    CdkDropListGroup,
    CdkDropList,
    CdkDrag
  ],
  templateUrl: './new-invoice.component.html',
  styleUrl: './new-invoice.component.scss'
})
export class NewInvoiceComponent implements OnInit {
  taxArray: any[] = [];
  today = new Date();
  projectName!: string;
  projectId!: string;
  private base64!: string;
  private type!: string;
  private filename!: string;
  public isEdit: boolean;
  public invoiceForm!: UntypedFormGroup;
  public itemInvoice2 = [];
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes
  public calculateGroupedTaxes = calculateGroupedTaxes
  public projectAndClientOptions: IFilterDropdownOption[] = []
  public subtotal: number = 0
  public totalAmount: number = 0
  public selectedDateStart = this.formatDate(new Date())
  public selectedDateEnd = this.formatDate(new Date());
  imageUrl!: string;
  public companyAvatarUrl!: string;
  private companyBase64!: string;
  private companyType!: string;
  private companyFilename!: string;
  public combinedAddress: string;
  private formBuilder = inject(UntypedFormBuilder)
  inforUser: User
  public InforCompany: any
  private payment: any[] = []
  private nameTax: string = ''
  public sumtax: number = 0;
  private invoiceNumber: string = "0000001"
  private listTax: any[] = []
  private _taxService = inject(TaxService)
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _spinnerService = inject(SpinnerService)
  private _invoiceService = inject(InvoiceService)
  private dataService = inject(DataService)
  private dropdownOptionService = inject(DropdownOptionsService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private _companyServices = inject(CompanyService)
  private _expensesService = inject(ExpensesService)
  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;

  static getComponent(): typeof NewInvoiceComponent {
    return NewInvoiceComponent;
  }

  constructor(public dialogRef: MatDialogRef<NewInvoiceComponent>,
    private cdnService: CdnService,
    private modifyInvoiceItemDialog: ModifyInvoiceItemDialog,
    private modifyTaxesDialog: ModifyTaxesDialog,
    private selectTimeTrackingDialog: SelectTimeTrackingDialog,
    private selectExpensesDialog: SelectExpensesDialog,
    private sendInvoiceDialog: SendInvoiceDialog,
    private addNewItemDialog: AddNewItemDialog,
    private translate: TranslateService,
    @Inject(MAT_DIALOG_DATA) public data?: IModifyInvoice) {
    this.inforUser = this._storeService.get_InforUser();
    this.invoiceForm = this.formBuilder.group({
      clientId: [data?.clientId ?? "", Validators.compose([Validators.required])],
      invoiceDate: [data?.invoiceDate ?? null, Validators.compose([Validators.required])],
      dueDate: [data?.dueDate ?? null, Validators.compose([Validators.required])],
      invoiceNumber: [data?.invoiceNumber],
      projectId: [""],
      notes: [data?.notes ?? ""],
      itemInvoice: [[]],
    });
    this.invoiceForm.get('itemInvoice')?.valueChanges.subscribe(listInvoice => {
      this.subtotal = 0
      listInvoice?.forEach((invoiceItem: any) => {
        // Exclude taxes
        this.subtotal += calculateTotalInvoiceItem(invoiceItem?.rate, invoiceItem?.qty);
      })

      // Calculate with discount if exist
      this.calculateAllTax();
    });
    this.invoiceForm.get('invoiceNumber')?.disable();
    if (this.data) {
      this.projectId = this.data?.projectId
      this.projectName = this.data?.project?.projectName;
      this.data.itemInvoices.forEach(c => {
        c.taxes.forEach(t => {
          t.selected = true
        })
      })
      this.f['itemInvoice'].setValue((this.data?.itemInvoices ?? []))
      if (this.data?.img) {
        this.GetImg(this.data?.img);
      }
      this.isEdit = this.data.id ? true : false;
    }

  }
  GetImg(fileName: string) {

    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          this.imageUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    }
    )

  }
  handleClose() {
    this.dialogRef.close();
  }
  calculateAllTax() {
    this.taxArray = []
    this.sumtax = 0;
    const resultTax = calculateGroupedTaxes(this.f['itemInvoice'].value)

    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax
    this.totalAmount = this.subtotal + (isNaN(this.sumtax) ? 0 : this.sumtax);
  }
  async handleSelectProject(item: IFilterDropdownOption) {

    let newClientId = ""
    let newProjectID = ""

    if (item.metadata?.type == 'client') {
      newClientId = item.value
      newProjectID = ""
    } else {
      this.projectName = item.label;
      this.projectId = item.value
      newClientId = item.metadata?.objectClient?.id
      newProjectID = item.value
    }

    const currentClientId = this.f['clientId'].value
    const isSameClient = currentClientId === newClientId
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!isSameClient && currentPaymentInvoice.length) {
      const isConfirm = await this.layoutUtilsService.alertConfirm({
        title: 'Warning',
        description: 'You are changing the client, and the invoices will be reset. Are you sure you want to continue?',
      })
      if (!isConfirm) return;
      this.f['itemInvoice'].setValue([])
    }

    this.f['clientId'].setValue(newClientId)
    this.f['projectId'].setValue(newProjectID)
    this.selectSearchClientElement.handleCloseSearchResult()
  }

  ngOnInit(): void {
    this.dropdownOptionService
      .getDropdownOptionsProjectAndClientTimeTracking()
      .then((projectAndClientOptions) => this.projectAndClientOptions = projectAndClientOptions)
    if (this._storeService.getRoleBusiness() == Role.Contractor) {
      this.CountInvoiceByContractor();
      return;
    }
    if (!this.data) {
      this.CountInvoiceByCompany();
    }
    this.GetInforCompany();
  }

  GetInforCompany() {
    this._companyServices.GetInforCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.InforCompany = res;
        this.combinedAddress = this.getAddress();
        // Load company avatar into the thumbnail area if it exists
        if (res.companyImage) {
          this.GetCompanyAvatar(res.companyImage);
        }
      }
    })
  }

  GetCompanyAvatar(fileName: string) {
    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          // Set the company avatar to the thumbnail area
          this.imageUrl = reader.result as string;
          this.companyAvatarUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    })
  }

  async saveCompanyAvatarIfChanged(): Promise<void> {
    return new Promise((resolve) => {
      // Check if company avatar was changed
      if (this.companyBase64 && this.companyFilename && this.companyType) {
        const companyPayload = {
          ...this.InforCompany,
          businessName: this.InforCompany?.businessName,
          base64: this.companyBase64,
          filename: this.companyFilename,
          type: this.companyType
        };

        this._companyServices.UpdateCompany(companyPayload)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe({
            next: (res) => {
              if (res) {
                this._toastService.showSuccess("Company avatar updated", "Success");
              }
              resolve();
            },
            error: () => {
              this._toastService.showError("Failed to update company avatar", "Error");
              resolve();
            }
          });
      } else {
        resolve();
      }
    });
  }
  public getAddress() {
    return getFullAddress({
      addressLine1: this.InforCompany?.adress ?? '',
      addressLine2: this.InforCompany?.adress2 ?? '',
      stateProvince: this.InforCompany?.province ?? '',
      postalCode: this.InforCompany?.postalCode ?? '',
      country: this.InforCompany?.country ?? '',
      });
  }

  get f() {
    return this.invoiceForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  convertToHours(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours + minutes / 60;
  }
  async handleChangePicture(files: any) {
    if (!files || files.length === 0) {
      // Handle removal of company avatar
      this.imageUrl = '';
      this.companyAvatarUrl = '';
      this.companyBase64 = '';
      this.companyType = '';
      this.companyFilename = '';
      return;
    }

    const pictureFile = files?.[0]
    const { base64, fileName, type } = await getBase64AndFileName(pictureFile)

    // Store company avatar data for potential update
    this.companyBase64 = base64
    this.companyType = type
    this.companyFilename = fileName;

    // Also store for invoice if needed (backward compatibility)
    this.base64 = base64
    this.type = type
    this.filename = fileName;
  }
  CountInvoiceByContractor() {
    this._invoiceService.CountInvoiceByContractor().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        if (res == 0) {
          const number = 1;
          this.invoiceNumber = number.toString().padStart(7, '0');
          this.f['invoiceNumber'].setValue(this.invoiceNumber)
          return;
        }
        const number = res + 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
      }
      else {
        const number = 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
        return;

      }
    }
    )
  }
  CountInvoiceByCompany() {
    this._invoiceService.CountInvoiceByCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        if (res == 0) {
          const number = 1;
          this.invoiceNumber = number.toString().padStart(7, '0');
          this.f['invoiceNumber'].setValue(this.invoiceNumber)
          return;
        }
        const number = res + 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
      }
      else {
        const number = 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
        return;

      }
    }
    )
  }

  GetAllTax() {
    let payload: Parameter = {
      Page: 0,
      PageSize: 100,
      Search: "",
    }

    this._taxService.GetAllTax(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.listTax = res.data
          this.listTax.forEach((element: any) => {
            if (element.selected) {
              this.nameTax = this.nameTax + "," + element.name
              this.sumtax += element.amount
            }
          });
          this.nameTax = this.nameTax.trimStart().replace(/^,/, '+');
        }
      }
    });
  }

  handleDeleteInvoiceItem(index: number) {
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!currentPaymentInvoice?.length) return

    currentPaymentInvoice.splice(index, 1)
    this.f['itemInvoice'].setValue(currentPaymentInvoice)
  }

  handleCancel() {
    this.dialogRef.close();
  }

  get getInvoicePayload() {
    if (this.invoiceForm.invalid) return null;

    let invoice: Invoice = {
      clientId: this.f['clientId'].value,
      invoiceNumber: this.f['invoiceNumber'].value,
      invoiceDate: moment.utc(this.f['invoiceDate'].value).toDate(),
      dueDate: moment.utc(this.f['dueDate'].value).toDate(),
      reference: '',
      notes: this.f['notes'].value,
      projectId: this.f['projectId'].value,
      payments: this.payment,
      taxes: this.listTax,
      base64: this.base64,
      type: this.type,
      filename: this.filename,
      itemInvoices: this.f['itemInvoice'].value.map(item => ({
        ...item,
        taxes: item.taxes.some(tax => tax.companyTax)
          ? item.taxes.map(({ companyTax, ...rest }) => rest)
          : item.taxes.filter(tax => tax.selected),
        description: this.createDescription(item)
      })),
      paidAmount: this.subtotal,
      taxAmount: this.sumtax,
      totalAmount: this.totalAmount,
      rate: 0,
      status: 0,
      timeAmount: 0,
    };
    if (this.data) {
      if (this.data) {
        invoice["id"] = this.data?.id
        invoice["createdAt"] = this.data?.createdAt
      }
    }
    return invoice;
  }
  handleAddUnBillTimeExpenses() {
    let project;
    const clientId = this.f['clientId'].value
    const projectId = this.f['projectId'].value
    if (!clientId) {
      this._toastService.showWarning("No selected client", "Please select a client to add the time.")
      return
    }

    const client = this.projectAndClientOptions.find(item => item.value === clientId)?.metadata?.client
    if (!client) return this._toastService.showWarning("Not fount client")

    if (projectId) {
      project = this.projectAndClientOptions.find(item => item.value === projectId && item.metadata.type == "project")?.metadata?.project
    }

    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    const listIdExpensesSelected = currentPaymentInvoice.map(item => item.ExpensesId).filter(x => x)
    const payload = {
      client,
      project,
      listIdTimeTrackingSelected: listIdExpensesSelected
    }



    const dialogRef = this.selectExpensesDialog.open(payload);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((_listExpensesSelected) => {
        if (!_listExpensesSelected?.length) return

        // Extract expense IDs from selected expenses
        const expenseIds = _listExpensesSelected.map((expense: any) => expense.expensesId)

        // Call API to get expense items for the selected expenses
        this._expensesService.GetExpenseItemsByExpenseIds(expenseIds)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe((expenseItems: any[]) => {
            if (!expenseItems?.length) return

            // Transform expense items to invoice item format
            const transformedExpenseItems = expenseItems.map((expenseItem: any) => {
              // Find the corresponding expense to get additional info
              const parentExpense = _listExpensesSelected.find((expense: any) =>
                expense.expensesId === expenseItem.expensesId
              )

              return {
                ExpensesId: expenseItem.expensesId,
                description: `${parentExpense.expensesName} - ${expenseItem.description}`  || '',
                rate: expenseItem.rate || 0,
                qty: expenseItem.qty || 1,
                total: expenseItem.total || 0,
                taxes: expenseItem.taxes?.map(tax => ({...tax, selected: true })) || [],
                date: parentExpense?.date,
                dateSelectItem: parentExpense?.dateSelectItem,
                projectName: parentExpense?.projectName,
                expensesName: parentExpense?.expensesName,
                inforUser: parentExpense?.inforUser,
                position: 0,
                isnew: true
              }
            });
            currentPaymentInvoice.push(...transformedExpenseItems)
            this.f['itemInvoice'].setValue(currentPaymentInvoice)
          })
      })
    });
  }
  handleAddUnBillTime() {
    let project;
    const clientId = this.f['clientId'].value
    const projectId = this.f['projectId'].value
    if (!clientId) {
      this._toastService.showWarning("No selected client", "Please select a client to add the time.")
      return
    }

    const client = this.projectAndClientOptions.find(item => item.value === clientId)?.metadata?.client
    if (!client) return this._toastService.showWarning("Not fount client")

    if (projectId) {
      project = this.projectAndClientOptions.find(item => item.value === projectId && item.metadata.type == "project")?.metadata?.project
    }

    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    const listIdTimeTrackingSelected = currentPaymentInvoice.map(item => item.trackingId).filter(x => x)
    const payload = {
      client,
      project,
      listIdTimeTrackingSelected
    }



    const dialogRef = this.selectTimeTrackingDialog.open(payload);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((_listTimeTrackingSelected) => {
        if (!_listTimeTrackingSelected?.length) return
        currentPaymentInvoice.push(..._listTimeTrackingSelected)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }
  createDescription(data: any): string {
    const datePipe = new DatePipe('en-US');
    if (data?.position == 0 || this.data && !data?.isnew) {
      const description = data?.description;
      return description;
    }
    else {
      const projectName = data?.projectName;
      const itemName = data?.itemName;
      const description = data?.description;
      const serviceName = data?.serviceName;
      const expensesName = data?.expensesName;
      const formattedDate = datePipe.transform(data.dateSelectItem, 'MMM, d yyyy');
      const user = this.getFullName(data?.inforUser)
      const descriptionParts = [projectName, itemName, serviceName, expensesName, `${user}- ${formattedDate}`, description];
      const descriptionnew = descriptionParts.filter(part => part != null && part !== "").join('\n');
      return descriptionnew;

    }

  }
  handleSave() {

    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      this._toastService.showWarning(this.translate.instant("INVOICES.Warning"), " ");
      return
    }
    this._spinnerService.show();

    // Save company avatar if it was changed
    this.saveCompanyAvatarIfChanged().then(() => {
      if (this.data && !this.data?.isGenrate) {
        this._invoiceService.UpdateInvoice(this.getInvoicePayload)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(res => {
            if (res) {
              this._spinnerService.hide();
              this.dataService.triggerRefreshInvoice()
              this.dialogRef.close(res)
              this._toastService.showSuccess("Save", "Success");
            }
          })
      }
      else {
        this._invoiceService.CreatedInvoice(this.getInvoicePayload)
          .pipe(takeUntilDestroyed(this.destroyRef))
          .subscribe(res => {
            if (res) {
              this._spinnerService.hide();
              this.dataService.triggerRefreshInvoice()
              this.dialogRef.close(res)
              this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
            }
          })
      }
    });

  }

  handleAddNewItem() {
    const dialogRef = this.addNewItemDialog.open(this.getInvoicePayload);
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    dialogRef.then((c) => {
      c.afterClosed().subscribe((itemnew) => {
        if (!itemnew?.length) return
        const updatedData = itemnew.map(({ id, isServices, taxes, itemName, projectId, serviceId, serviceName, projectName, description, createdAt, ...rest }) => ({
          ...rest,
          description: description,
          itemName: itemName,
          itemId: isServices == false ? id : null,
          projectId: isServices == true ? projectId : null,
          projectName: projectName ?? null,
          serviceId: isServices == true ? id : null,
          serviceName: serviceName,
          date: createdAt ?? this.today,
          isnew: true,
          dateSelectItem: createdAt ?? new Date(),
          taxes: taxes?.map(({ id, itemId, serviceId, ...taxRest }) => taxRest)
        }));
        currentPaymentInvoice.push(...updatedData)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }


  handleSendInvoice() {
    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      this._toastService.showWarning("Please fill in all the invoice information completely.", " ")
      return
    }

    // Save company avatar if it was changed before sending invoice
    this.saveCompanyAvatarIfChanged().then(() => {
      const dialogRef = this.sendInvoiceDialog.open(this.getInvoicePayload);
      dialogRef.then((c) => {
        c.afterClosed().subscribe(res => {
          if (!res) return
          this.dataService.triggerRefreshInvoice()
          this.dialogRef.close()
        })
      });
    });
  }

  handleModifyInvoiceItem(index?: number, item?: any) {
    const data = item && { ...item };
    data?.position == 0 ? data.description = this.createDescription(data) : data?.description
    const dialogRef = this.modifyInvoiceItemDialog.open(data);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        if (index === undefined) {
          res.projectName = this.projectName ?? res.projectName ?? currentPaymentInvoice[index]?.projectName ?? ""
          res.projectId = this.projectId ?? res.projectId ?? currentPaymentInvoice[index]?.projectId ?? null
          currentPaymentInvoice.push(res)
          this.f['itemInvoice'].setValue(currentPaymentInvoice)
        }
        else {
          res.projectName = this.projectName ?? res.projectName ?? currentPaymentInvoice[index]?.projectName ?? ""
          res.projectId = this.projectId ?? res.projectId ?? currentPaymentInvoice[index]?.projectId ?? null
          if (currentPaymentInvoice[index].dateSelectItem) {
            currentPaymentInvoice[index] = {
              ...res,
              ["itemName"]: currentPaymentInvoice[index]?.itemName,
              ["date"]: res?.dateSelectItem ?? this.today,
              ["dateSelectItem"]: currentPaymentInvoice[index]["dateSelectItem"],
              ["serviceId"]: currentPaymentInvoice[index]?.serviceId,
              ["serviceName"]: currentPaymentInvoice[index]?.service?.serviceName ?? currentPaymentInvoice[index]?.serviceName ?? ""

            };
          }
          else {
            currentPaymentInvoice[index] = res

          }
          this.f['itemInvoice'].setValue(currentPaymentInvoice)
        }
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemInvoice'].value
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });
          this.calculateAllTax()
        }
      })
    });
  }

  handleSelectClient(item: IFilterDropdownOption) {
    this.f["clientId"].setValue(item.value)
    this.selectSearchClientElement.handleCloseSearchResult()
  }
  RouterSetting() {
    this.dialogRef.close();
    this.router.navigate(["/settings/business"])
  }

  handleModifyTaxes(item: any, index: number) {
    const itemInvoiceOld = structuredClone(this.f['itemInvoice'].value);
    const taxes = item.map(tax => {
      if (tax.companyTax) {
        tax.companyTax.selected = true;
        return tax.companyTax;
      }
      return tax;
    });
    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          this.f['itemInvoice'].setValue(itemInvoiceOld)
          return;
        }
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        itemInvoiceOld.forEach((item: any, indexOld: number) => {
          if (index != indexOld) {
            currentPaymentInvoice[indexOld].taxes = item.taxes
          }
        });


        currentPaymentInvoice[index].taxes = res.taxes
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemInvoice'].value
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });
          this.calculateAllTax()
        }
      })
    });
  }
  getFullName(user?: any) {
    if (user) {
      if (user?.firstName && user?.lastName) {
        return user.firstName + " " + user.lastName
      }
      else {
        return user?.email ?? ""
      }
    }
    else {
      if (this.inforUser?.firstName && this.inforUser?.lastName) {
        return this.inforUser.firstName + " " + this.inforUser.lastName
      }
      else {
        return this.inforUser?.email ?? ""
      }
    }


  }
  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );
    }
  }
}
