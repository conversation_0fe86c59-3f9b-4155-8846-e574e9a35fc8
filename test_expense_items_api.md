# Test Plan for GetExpenseItemsByExpenseIds API

## API Endpoint
- **URL**: `POST /api/Expenses/GetExpenseItemsByExpenseIds`
- **Method**: POST
- **Authorization**: Required (Bearer token)
- **Roles**: <PERSON><PERSON>, Manager, Accountant, Employee

## Request Body
- Array of expense IDs (string[], required): List of expense GUIDs to get items for
```json
[
  "expense-guid-1",
  "expense-guid-2",
  "expense-guid-3"
]
```

## Expected Response
```json
[
  {
    "id": "guid",
    "expensesId": "guid", 
    "rate": 0.00,
    "qty": 0.00,
    "total": 0.00,
    "description": "string",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z", 
    "createdBy": "string",
    "updatedBy": "string",
    "taxes": [
      {
        "id": "guid",
        "name": "string",
        "total": 0.00,
        "taxeNumber": "string", 
        "amount": 0.00,
        "companyTaxId": "guid"
      }
    ]
  }
]
```

## Test Cases

### 1. Valid Expense IDs with Items
- **Input**: List of valid expense IDs that have associated expense items
- **Expected**: 200 OK with array of expense items from all expenses

### 2. Valid Expense IDs with No Items
- **Input**: List of valid expense IDs that have no associated expense items
- **Expected**: 200 OK with empty array

### 3. Mixed Valid and Invalid Expense IDs
- **Input**: List containing both valid and non-existent expense IDs
- **Expected**: 200 OK with expense items only from valid expenses

### 4. Empty List
- **Input**: Empty array of expense IDs
- **Expected**: 200 OK with empty array

### 5. Invalid GUID Format
- **Input**: List containing malformed GUID strings
- **Expected**: 400 Bad Request or 500 Internal Server Error

### 6. Unauthorized Access
- **Input**: Request without valid authorization token
- **Expected**: 401 Unauthorized

### 7. Insufficient Permissions
- **Input**: Request from user without required role
- **Expected**: 403 Forbidden

## Manual Testing Steps

1. Start the application
2. Authenticate and get a valid bearer token
3. Create or find existing expenses with items
4. Call the API with a list of expense IDs
5. Verify the response structure and data
6. Test edge cases (empty list, invalid IDs, mixed valid/invalid IDs, etc.)

## Implementation Details

- **Controller**: `ExpensesController.GetExpenseItemsByExpenseIds`
- **Service**: `ExpensesService.GetExpenseItemsByExpenseIds`
- **DTO**: `DTOItemExpense`
- **Entity**: `ItemExpense` with `Tax` navigation properties
