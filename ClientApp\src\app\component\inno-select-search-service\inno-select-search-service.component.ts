import { SortGird } from './../../dto/interface/SortGird.interface';
import { ModifyItemsAndServiceDialog } from './../../service/dialog/modify-items-and-service.dialog';
import { ItemAndServiceViewEnum } from './../../enum/item-service.enum';
import { InnoInputSearchResultComponent } from './../inno-input-search-result/inno-input-search-result.component';
import { SharedModule } from 'app/module/shared.module';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { Component, EventEmitter, inject, Input, OnInit, Output, TemplateRef, ViewChild, OnDestroy } from '@angular/core';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { Subscription } from 'rxjs';
import { AvatarModule } from 'ngx-avatars';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { InnoTimerProvider } from '../inno-timer/inno-timer.provider';

@Component({
  selector: 'app-inno-select-search-service',
  standalone: true,
  imports: [SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    AvatarModule],
  templateUrl: './inno-select-search-service.component.html',
  styleUrl: './inno-select-search-service.component.scss'
})
export class InnoSelectSearchServiceComponent implements OnInit, OnDestroy {

  @Input() public templateTrigger: TemplateRef<any> | null = null
  @Input() public defaultTextSearch: string = ''
  @Input() public isShowCreateButton: boolean = true;
  @Input() public value: any
  @Input() public lable?: string
  @Input() public placeholderSearch?: string
  @Output() public onSelect = new EventEmitter<IFilterDropdownOption>();
  @Output() public onGetInfoSelectedValue = new EventEmitter<IFilterDropdownOption>();

  public searchService: string = ''
  public listOptionPreview: IFilterDropdownOption[] = []
  public listOptionOriginal: IFilterDropdownOption[] = []
  public isLoading: boolean = false
  public timeout: any;
  public sort: SortGird

  private dropdownOptionService = inject(DropdownOptionsService)
  private modifyItemAndServiceDialog = inject(ModifyItemsAndServiceDialog)
  private innoTimerProvider = inject(InnoTimerProvider)
  // private dialog = inject(MatDialog)
  protected unsubscribe: Subscription[] = [];
  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor() { }

  ngOnInit(): void {
    if (this.value) this.loadData(true)

    // Subscribe to project changes from InnoTimerProvider
    this.unsubscribe.push(
      this.innoTimerProvider.projectChange$.subscribe((newProjectId: string) => {
        // Update the value (projectId) and reload data when project changes
        if (this.value !== newProjectId) {
          this.value = newProjectId;
          this.loadData(false); // Reload services for the new project
          // Clear current service selection since project changed
          this.listOptionPreview = [];
          this.listOptionOriginal = [];
        }
      })
    );
  }

  async loadData(isGetInfoSelectedValue?: boolean) {
    this.sort = {
      columnName: "serviceName",
      direction: "Ascending"
    }
    this.isLoading = true
    const dropdownOption = await this.dropdownOptionService.getDropdownOptionsService(this.value, this.sort)
    this.listOptionOriginal = dropdownOption
    this.handleSearch(this.defaultTextSearch)
    if (isGetInfoSelectedValue && this.value) {
      const selectedValue = this.listOptionOriginal.find(x => x.value == this.value)
      this.onGetInfoSelectedValue.emit(selectedValue)
    }
    this.isLoading = false
  }

  handleSearch(textSearch: string) {
    textSearch = textSearch?.trim()?.toLowerCase()
    this.searchService = textSearch
    if (!textSearch?.length) {
      this.listOptionPreview = this.listOptionOriginal
      return
    }

    this.listOptionPreview = this.listOptionOriginal
      .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)
    const result: any[] = [];
    this.listOptionPreview.forEach(element => {
      result.push(element);
      this.listOptionOriginal.filter(x => x.metadata.type == 'project').forEach((item: any) => {
        if (element.value == item.metadata.objectClient.id) {
          result.push(item)
        }

      });
    });
    this.listOptionPreview = result
  }

  handleCreateNewService() {
    this.searchResultComponent.handleHideContent()
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Service,
      serviceInfo: { projectId: this.value, serviceName: this.searchService, projectName: this.lable }
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.searchResultComponent.handleToggle()
          this.loadData(true)
          this.timeout = setTimeout(() => {
            this.handleSearch(res.serviceName)
          }, 100);
        }
      })
    });

  }

  private handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  isProjectOption(item: IFilterDropdownOption) {
    return item.metadata?.type == 'project'
  }

  handleChooseOption(item: IFilterDropdownOption) {
    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((s) => s.unsubscribe());
    if (this.timeout) {
      clearTimeout(this.timeout);
    }
  }
}
