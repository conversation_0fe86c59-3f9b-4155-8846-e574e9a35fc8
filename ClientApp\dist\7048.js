"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7048],{7048:(D,C,l)=>{l.r(C),l.d(C,{AuthenticatedLayoutComponent:()=>Ne});var s=l(9842),b=l(5277),h=l(2928),e=l(4438),p=l(33),f=l(6146),m=l(5508),g=l(9115),d=l(9079),c=l(6402),j=l(1635),R=l(1863),w=l(177);const A=["ejs-dropdownbutton",""],O=["*"];let F=["disabled","iconCss","id","separator","text","url"],E=[],G=(()=>{class i extends c.qL{constructor(n){super(),this.viewContainerRef=n,(0,c.KY)("currentInstance",this,this.viewContainerRef),this.registerEvents(E),this.directivePropList=F}}return i.\u0275fac=function(n){return new(n||i)(e.rXU(e.c1b))},i.\u0275dir=e.FsC({type:i,selectors:[["e-dropdownbuttonitem"]],inputs:{disabled:"disabled",iconCss:"iconCss",id:"id",separator:"separator",text:"text",url:"url"},features:[e.Vt3]}),i})(),N=(()=>{class i extends c.Zf{constructor(){super("items")}}return i.\u0275fac=function(n){return new(n||i)},i.\u0275dir=e.FsC({type:i,selectors:[["e-dropdownbuttonitems"]],contentQueries:function(n,t,o){if(1&n&&e.wni(o,G,4),2&n){let a;e.mGM(a=e.lsd())&&(t.children=a)}},features:[e.Vt3]}),i})();const $=["beforeClose","beforeItemRender","beforeOpen","close","created","open","select"],L=[];let W=(()=>{let i=class extends R.QF{constructor(n,t,o,a){super(),this.ngEle=n,this.srenderer=t,this.viewContainerRef=o,this.injector=a,this.tags=["items"],this.element=this.ngEle.nativeElement,this.injectedModules=this.injectedModules||[],this.registerEvents($),this.addTwoWay.call(this,L),(0,c.KY)("currentInstance",this,this.viewContainerRef),this.containerContext=new c.xu}ngOnInit(){this.containerContext.ngOnInit(this)}ngAfterViewInit(){this.containerContext.ngAfterViewInit(this)}ngOnDestroy(){this.containerContext.ngOnDestroy(this)}ngAfterContentChecked(){this.tagObjects[0].instance=this.childItems,this.containerContext.ngAfterContentChecked(this)}};return i.\u0275fac=function(n){return new(n||i)(e.rXU(e.aKT),e.rXU(e.sFG),e.rXU(e.c1b),e.rXU(e.zZn))},i.\u0275cmp=e.VBU({type:i,selectors:[["","ejs-dropdownbutton",""]],contentQueries:function(n,t,o){if(1&n&&e.wni(o,N,5),2&n){let a;e.mGM(a=e.lsd())&&(t.childItems=a.first)}},inputs:{closeActionEvents:"closeActionEvents",content:"content",createPopupOnClick:"createPopupOnClick",cssClass:"cssClass",disabled:"disabled",enableHtmlSanitizer:"enableHtmlSanitizer",enablePersistence:"enablePersistence",enableRtl:"enableRtl",iconCss:"iconCss",iconPosition:"iconPosition",items:"items",locale:"locale",target:"target"},outputs:{beforeClose:"beforeClose",beforeItemRender:"beforeItemRender",beforeOpen:"beforeOpen",close:"close",created:"created",open:"open",select:"select"},features:[e.Vt3],attrs:A,ngContentSelectors:O,decls:1,vars:0,template:function(n,t){1&n&&(e.NAR(),e.SdG(0))},encapsulation:2,changeDetection:0}),i=(0,j.Cg)([(0,c.yZ)([c.xu])],i),i})(),X=(()=>{class i{}return i.\u0275fac=function(n){return new(n||i)},i.\u0275mod=e.$C({type:i}),i.\u0275inj=e.G2t({imports:[[w.MD]]}),i})();var V=l(2953),U=l(4823),x=l(5236),z=l(9088),H=l(359),S=l(1110),Q=l(9589);const M=(i,r)=>r.group,k=(i,r)=>r.id,Y=i=>({"bg-bg-brand-strong":i});function K(i,r){1&i&&e.nrm(0,"hr",30)}function Z(i,r){if(1&i&&(e.j41(0,"button",31),e.nI1(1,"translate"),e.nrm(2,"img",32),e.k0s()),2&i){const n=r.$implicit;e.Y8G("matTooltip",e.bMT(1,5,n.text))("routerLink",n.url)("ngClass",e.eq3(7,Y,n.active)),e.R7$(2),e.Mz_("src","../../../assets/img/icon/",n.icon,"",e.B4B)}}function J(i,r){if(1&i&&e.Z7z(0,Z,3,9,"button",31,k),2&i){const n=e.XpG().$implicit;e.Dyx(n.menus)}}function q(i,r){if(1&i&&(e.DNE(0,K,1,0,"hr",30),e.j41(1,"div",8),e.DNE(2,J,2,0),e.k0s()),2&i){const n=r.$implicit;e.vxM(0!=r.$index?0:-1),e.R7$(2),e.vxM(null!=n&&null!=n.menus&&n.menus.length?2:-1)}}function ee(i,r){if(1&i&&(e.EFF(0),e.nI1(1,"translate")),2&i){const n=e.XpG();e.SpI(" ",n.objectBusiness.role+(n.objectBusiness.isOwner?" ("+e.bMT(1,1,"COMMON.Owner")+")":"")," ")}}function te(i,r){1&i&&e.EFF(0," Create business now ")}function ne(i,r){if(1&i&&(e.j41(0,"p",33),e.EFF(1),e.k0s()),2&i){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",n.group," ")}}function ie(i,r){if(1&i){const n=e.RV6();e.j41(0,"img",39),e.bIt("click",function(o){e.eBV(n);const a=e.XpG().$implicit,u=e.XpG(3);return e.Njj(u.expand(a,o))}),e.k0s()}}function oe(i,r){if(1&i&&(e.j41(0,"button",41),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&i){const n=r.$implicit;e.Y8G("routerLink",n.url),e.R7$(),e.SpI(" ",e.bMT(2,2,n.text)," ")}}function re(i,r){if(1&i&&(e.j41(0,"div",40),e.Z7z(1,oe,3,4,"button",41,e.fX1),e.k0s()),2&i){const n=e.XpG().$implicit;e.AVh("active",n.expand),e.R7$(),e.Dyx(n.children)}}function se(i,r){if(1&i){const n=e.RV6();e.j41(0,"div",24)(1,"button",35),e.bIt("click",function(){const o=e.eBV(n).$implicit,a=e.XpG(3);return e.Njj(a.ClearCache(o.id))}),e.nrm(2,"img",36),e.EFF(3),e.nI1(4,"translate"),e.DNE(5,ie,1,0,"img",37),e.k0s(),e.DNE(6,re,3,2,"div",38),e.k0s()}if(2&i){const n=r.$implicit;e.R7$(),e.AVh("hasSubMenu",null==n.children?null:n.children.length)("active",n.active),e.Y8G("routerLink",n.url),e.R7$(),e.Mz_("src","../../../assets/img/icon/",n.icon,"",e.B4B),e.R7$(),e.SpI(" ",e.bMT(4,10,n.text)," "),e.R7$(2),e.vxM(null!=n.children&&n.children.length?5:-1),e.R7$(),e.vxM(null!=n.children&&n.children.length?6:-1)}}function ae(i,r){if(1&i&&(e.j41(0,"div",34),e.Z7z(1,se,7,12,"div",24,k),e.k0s()),2&i){const n=e.XpG().$implicit;e.R7$(),e.Dyx(n.menus)}}function le(i,r){if(1&i&&(e.j41(0,"div",24),e.DNE(1,ne,2,1,"p",33)(2,ae,3,0,"div",34),e.k0s()),2&i){const n=r.$implicit;e.R7$(),e.vxM(null!=n&&n.group?1:-1),e.R7$(),e.vxM(null!=n&&null!=n.menus&&n.menus.length?2:-1)}}function ce(i,r){1&i&&(e.j41(0,"span"),e.EFF(1,"EN"),e.k0s())}function ue(i,r){1&i&&(e.j41(0,"span"),e.EFF(1,"FR"),e.k0s())}let pe=(()=>{var i;class r{constructor(t,o,a,u,$e,Le,We,Xe){(0,s.A)(this,"router",void 0),(0,s.A)(this,"translate",void 0),(0,s.A)(this,"companyService",void 0),(0,s.A)(this,"businessService",void 0),(0,s.A)(this,"storeService",void 0),(0,s.A)(this,"destroyRef",void 0),(0,s.A)(this,"authenticationService",void 0),(0,s.A)(this,"switchWorkspaceDialog",void 0),(0,s.A)(this,"listGroupMenu",[]),(0,s.A)(this,"languages",[{text:"English",id:"en",iconCss:"img-icon-en"},{text:"Fran\xe7ais",id:"fr",iconCss:"img-icon-fr"}]),(0,s.A)(this,"userOptions",[{text:"Profile"},{text:"Log Out"}]),(0,s.A)(this,"isOpenSidebar",!0),(0,s.A)(this,"objectBusiness",null),(0,s.A)(this,"RoleBusiness",void 0),(0,s.A)(this,"_userBusiness",void 0),(0,s.A)(this,"currentLanguage","English"),this.router=t,this.translate=o,this.companyService=a,this.businessService=u,this.storeService=$e,this.destroyRef=Le,this.authenticationService=We,this.switchWorkspaceDialog=Xe,this.router.events.pipe((0,d.pQ)(this.destroyRef)).subscribe(I=>{I instanceof p.wF&&m.r.forEach(v=>{let _=!1;v.children?.forEach(y=>{y.active=I.urlAfterRedirects===y.url,_=_||y.active}),v.active=_||I.urlAfterRedirects.includes(v.url),v.expand=_||I.urlAfterRedirects.includes(v.url)})}),"fr"==localStorage.getItem("lang")&&(this.currentLanguage="Fran\xe7ais")}hasPermission(t){const o=this.RoleBusiness;return t.some(a=>null!=o&&(a===V.X.All||a?.toLowerCase()===o.toLocaleLowerCase()))}expand(t,o){t.expand=!t.expand,o.stopPropagation()}openOtherWorkSpace(){this.switchWorkspaceDialog.open({}).then(o=>{o.afterClosed().subscribe(a=>{a&&this.router.navigate(["/"]).then(()=>{window.location.reload()})})})}ChooseBusiness(t){this.storeService.setChooseBusiness({businessId:t.businessId}),this.GetBusinessById(t.businessId)}GetUserBusiness(){this.businessService.GetUserBusiness().pipe((0,d.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._userBusiness=t,this._userBusiness.length>0?this.ChooseBusiness({businessId:this._userBusiness[0].businessId,companyId:this._userBusiness[0].companyId}):(this.RoleBusiness=null,this.LoadMenu()))})}LoadMenu(){const t=m.r.filter(o=>this.hasPermission(o.permissions));this.listGroupMenu=[{group:"Tracking",menus:t.filter(o=>"Tracking"==o.group)},{group:"Analyze",menus:t.filter(o=>"Analyze"==o.group)},{group:"Manager",menus:t.filter(o=>"Manager"==o.group)}]}ClearCache(t){"time_tracking"!=t&&(localStorage.getItem("cacheService")||localStorage.getItem("cacheClientProject"))&&(localStorage.removeItem("cacheClientProject"),localStorage.removeItem("cacheService")),window.innerWidth<860&&(this.isOpenSidebar=!1)}GetCurrencyCompany(){this.companyService.GetCurrencyCompany().pipe((0,d.pQ)(this.destroyRef)).subscribe(t=>{t&&""!=t.currency&&this.storeService.setCurencyCompany(t.currency)})}GetBusinessById(t){this.businessService.GetBusinessById(t).pipe((0,d.pQ)(this.destroyRef)).subscribe(o=>{o&&(this.objectBusiness=o,this.storeService.set_UserBusiness(o),o.dateFormat&&""!=o.dateFormat&&this.storeService.setdateFormat(o.dateFormat),this.RoleBusiness=this.objectBusiness?.role??"",this.storeService.setRoleBusiness(this.RoleBusiness),this.LoadMenu(),this.authenticationService.UpdateAccessToken({BusinessId:this.objectBusiness.businessId}).pipe((0,d.pQ)(this.destroyRef)).subscribe(a=>{a&&(this.authenticationService.saveToken_cookie(a.accessToken,a.refreshToken),this.GetCurrencyCompany())}))})}select(t){switch(t.item.text){case"Log Out":this.authenticationService.logout();break;case"Profile":this.router.navigate(["/profile"])}}ngOnInit(){const t=this.storeService.getChooseBusiness();t?this.GetBusinessById(JSON.parse(t)?.businessId):this.GetUserBusiness(),window.innerWidth<860&&(this.isOpenSidebar=!1)}handleClickOnUser(t){switch(t.item.text){case"Log Out":this.authenticationService.logout();break;case"Profile":this.router.navigate(["/profile"])}}selectLanguage(t){this.translate.use(t.item.id),this.currentLanguage=t.item.text,localStorage.setItem("lang",t.item.id),window.location.reload()}handleToggleSidebar(){this.isOpenSidebar=!this.isOpenSidebar}}return i=r,(0,s.A)(r,"\u0275fac",function(t){return new(t||i)(e.rXU(p.Ix),e.rXU(x.c$),e.rXU(z.B),e.rXU(H.l),e.rXU(S.n),e.rXU(e.abz),e.rXU(h.k),e.rXU(Q.y))}),(0,s.A)(r,"\u0275cmp",e.VBU({type:i,selectors:[["app-navigation"]],standalone:!0,features:[e.aNF],decls:41,vars:18,consts:[[1,"relative","h-dvh","flex"],[1,"w-[48px]","h-full","bg-bg-brand-strong-hover","shrink-0","horizontalSidebar"],[1,"w-full","h-full","pt-[10px]","flex","flex-col","items-center"],[1,"buttonCollapse",3,"click"],["src","../../../assets/img/icon/ic_collapse_sidebar.svg","alt","Icon",1,"w-[20px]"],[1,"w-full","flex","flex-col","grow","overflow-auto","mt-[20px]"],[1,"overflow-auto"],[1,"w-full","hidden","flex-col","gap-[12px]"],[1,"w-full","flex","flex-col","items-center","gap-[8px]"],[1,"grow","flex","justify-center","items-center","pt-[24px]"],["src","../../../assets/img/logo_verticle.png","alt","Logo",1,"w-[16px]"],[1,"py-[24px]","flex","flex-col","items-center","gap-[12px]"],[1,"w-[32px]","h-[32px]","rounded-md","flex","justify-center","items-center","hover:bg-bg-brand-strong"],["src","../../../assets/img/icon/ic_question.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_notification.svg","alt","Icon",1,"w-[20px]"],["ejs-dropdownbutton","","iconCss","e-icons e-user user-icon","cssClass","e-caret-hide",1,"w-[32px]","h-[32px]","rounded-full","flex","justify-center","items-center",3,"select","items"],[1,"sidebar","h-full","flex","flex-col","bg-bg-brand-strong"],[1,"px-[24px]","py-[15px]","w-full","border-b","border-border-in-tertiary","cursor-pointer",3,"click"],[1,"flex","w-full","items-center","gap-[12px]"],[1,"line-clamp-1","text-text-md-bold","text-text-white"],["src","../../../assets/img/icon/ic_arrow_down_white.svg","alt","Select company",1,"shrink-0","w-[20px]"],[1,"line-clamp-1","text-text-xs-regular","text-text-brand-tertiary"],[1,"mt-[20px]","px-[12px]","grow","overflow-auto"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"w-full","p-[12px]"],["ejs-dropdownbutton","","cssClass","e-caret-hide",1,"btnSwitchLanguage",3,"select","items"],[1,"wrapIcon"],[1,"txtLang"],[1,"overlayMobile",3,"click"],[1,"w-[80%]","mx-auto"],["matTooltipPosition","right",1,"w-[32px]","h-[32px]","rounded-md","flex","justify-center","items-center","hover:bg-bg-brand-strong","flex-shrink-0",3,"matTooltip","routerLink","ngClass"],["alt","Icon",1,"w-[20px]",3,"src"],[1,"px-[12px]","!mb-[4px]","text-[12px]","font-bold","uppercase","text-text-brand-tertiary"],[1,"listMenu"],[1,"menuItem",3,"click","routerLink"],["alt","icon menu",1,"w-[20px]","shrink-0",3,"src"],["src","../../../assets/img/icon/ic_arrow_down_white.svg","alt","icon menu",1,"ml-auto","w-[20px]","shrink-0"],[1,"listMenu","submenu",3,"active"],["src","../../../assets/img/icon/ic_arrow_down_white.svg","alt","icon menu",1,"ml-auto","w-[20px]","shrink-0",3,"click"],[1,"listMenu","submenu"],[1,"menuItem",3,"routerLink"]],template:function(t,o){if(1&t&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),e.bIt("click",function(){return o.handleToggleSidebar()}),e.nrm(4,"img",4),e.k0s(),e.j41(5,"div",5)(6,"div",6)(7,"div",7),e.Z7z(8,q,3,2,"div",8,M),e.k0s()(),e.j41(10,"div",9),e.nrm(11,"img",10),e.k0s()(),e.j41(12,"div",11)(13,"button",12),e.nrm(14,"img",13),e.k0s(),e.j41(15,"button",12),e.nrm(16,"img",14),e.k0s(),e.j41(17,"button",15),e.bIt("select",function(u){return o.handleClickOnUser(u)}),e.k0s()()()(),e.j41(18,"div",16)(19,"div",17),e.bIt("click",function(){return o.openOtherWorkSpace()}),e.j41(20,"div",18)(21,"p",19),e.EFF(22),e.k0s(),e.nrm(23,"img",20),e.k0s(),e.j41(24,"p",21),e.DNE(25,ee,2,3)(26,te,1,0),e.k0s()(),e.j41(27,"div",22)(28,"div",23),e.Z7z(29,le,3,2,"div",24,M),e.k0s()(),e.j41(31,"div",25)(32,"button",26),e.nI1(33,"translate"),e.nI1(34,"translate"),e.bIt("select",function(u){return o.selectLanguage(u)}),e.j41(35,"div",27),e.DNE(36,ce,2,0,"span")(37,ue,2,0,"span"),e.k0s(),e.j41(38,"span",28),e.EFF(39),e.k0s()()()(),e.j41(40,"div",29),e.bIt("click",function(){return o.handleToggleSidebar()}),e.k0s()()),2&t){let a;e.R7$(3),e.AVh("active",o.isOpenSidebar),e.R7$(4),e.AVh("activeSubSidebar",!o.isOpenSidebar),e.R7$(),e.Dyx(o.listGroupMenu),e.R7$(9),e.Y8G("items",o.userOptions),e.R7$(),e.AVh("active",o.isOpenSidebar),e.R7$(4),e.SpI(" ",null!==(a=null==o.objectBusiness?null:o.objectBusiness.businessName)&&void 0!==a?a:"WelCome InnoBook"," "),e.R7$(3),e.vxM(o.objectBusiness?25:26),e.R7$(4),e.Dyx(o.listGroupMenu),e.R7$(3),e.Y8G("items",o.languages),e.BMQ("aria-label",e.bMT(33,14,"Zipplex")+" "+e.bMT(34,16,"Language")),e.R7$(4),e.vxM("English"===o.currentLanguage?36:-1),e.R7$(),e.vxM("Fran\xe7ais"===o.currentLanguage?37:-1),e.R7$(2),e.SpI(" ",o.currentLanguage," ")}},dependencies:[X,W,p.iI,p.Wk,f.G,w.YU,x.D9,U.oV,g.Cn],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.buttonCollapse[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;justify-content:center;align-items:center;transform:rotate(180deg)}.buttonCollapse.active[_ngcontent-%COMP%]{transform:rotate(0)}.overlayMobile[_ngcontent-%COMP%]{width:100dvw;height:100dvh;position:fixed;top:0;left:0;display:none}.sidebar[_ngcontent-%COMP%]{width:0;opacity:0;transition:all .3s;pointer-events:none}.sidebar.active[_ngcontent-%COMP%]{width:192px;opacity:1;transition:all .3s;pointer-events:unset}@media screen and (max-width: 860px){.horizontalSidebar[_ngcontent-%COMP%]{position:relative;z-index:2}.sidebar[_ngcontent-%COMP%]{position:absolute;left:48px;z-index:2}.sidebar.active[_ngcontent-%COMP%] ~ .overlayMobile[_ngcontent-%COMP%]{display:block;background-color:transparent;z-index:1}}.listMenu[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:4px}.menuItem[_ngcontent-%COMP%]{width:100%;display:flex;gap:12px;padding:10px 12px;cursor:pointer;border-radius:8px;font-size:14px;line-height:20px;font-weight:500;color:var(--text-white);transition:all .3s;text-align:left}.menuItem.active[_ngcontent-%COMP%], .menuItem[_ngcontent-%COMP%]:hover{background-color:var(--bg-brand-strong-hover);transition:all .3s}.menuItem.active[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:700}.menuItem.active.hasSubMenu[_ngcontent-%COMP%]{border-bottom-right-radius:0;border-bottom-left-radius:0}.submenu[_ngcontent-%COMP%]{display:none;padding:10px 12px}.submenu[_ngcontent-%COMP%]   .menuItem[_ngcontent-%COMP%]:hover, .submenu[_ngcontent-%COMP%]   .menuItem.active[_ngcontent-%COMP%]{background-color:var(--bg-brand-strong);border-bottom-right-radius:8px;border-bottom-left-radius:8px}.menuItem.active[_ngcontent-%COMP%] ~ .submenu[_ngcontent-%COMP%]{display:flex;background-color:var(--bg-brand-strong-hover);padding-left:30px;border-bottom-right-radius:8px;border-bottom-left-radius:8px}.btnSwitchLanguage[_ngcontent-%COMP%]{border:none;outline:none;cursor:pointer;background:transparent;display:flex;justify-content:center;align-items:center;gap:8px;width:100%;justify-content:flex-start}.btnSwitchLanguage[_ngcontent-%COMP%]   .wrapIcon[_ngcontent-%COMP%]{width:32px;height:32px;display:flex;justify-content:center;align-items:center;background-color:#fff;border-radius:50%}.btnSwitchLanguage[_ngcontent-%COMP%]   .wrapIcon[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{text-transform:uppercase;color:#fff;font-size:12px;font-weight:500}.btnSwitchLanguage[_ngcontent-%COMP%]   .txtLang[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:18px;color:#fff;display:none;display:inline}.btnSwitchLanguage[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:16px;height:12px;object-fit:cover}.activeSubSidebar[_ngcontent-%COMP%]{display:flex}.img-icon-fr[_ngcontent-%COMP%]{background-image:url(fr.svg);background-repeat:no-repeat;background-size:16px 16px;padding-left:20px;height:16px}.img-icon-en[_ngcontent-%COMP%]{background-image:url(en.svg);background-repeat:no-repeat;background-size:16px 16px;padding-left:20px;height:16px}.e-dropdown-popup[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   .e-item[_ngcontent-%COMP%]{align-items:center!important}"]})),r})();var de=l(5316),me=l(7086),ge=l(2480),he=l(8232),fe=l(6408),ve=l(7656),_e=l(4262),B=l(1556),P=l(6473),Ce=l(5331),be=l(8194);const xe=i=>({"transform rotate-180":i}),T=i=>({"!text-text-primary !font-bold":i});function Te(i,r){if(1&i&&e.EFF(0),2&i){const n=e.XpG(2);e.SpI(" ",n.title," ")}}function Ie(i,r){1&i&&e.EFF(0," No client ")}function we(i,r){if(1&i){const n=e.RV6();e.j41(0,"app-inno-enter-edit-hours",25),e.bIt("onChange",function(o){e.eBV(n);const a=e.XpG(2);return e.Njj(a.handleEditEndTime(o))}),e.k0s()}if(2&i){const n=e.XpG(2);e.Y8G("value",n.timerHoursEdit)("dynamicWidth",210)}}function ye(i,r){if(1&i){const n=e.RV6();e.j41(0,"p",26),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.EditTimer())}),e.EFF(1),e.k0s()}if(2&i){const n=e.XpG(2);e.R7$(),e.SpI(" ",n.timerHours||"00:00:00"," ")}}function Se(i,r){1&i&&e.nrm(0,"img",14)}function Me(i,r){1&i&&e.nrm(0,"img",15)}function ke(i,r){if(1&i&&(e.j41(0,"button",17)(1,"span",28),e.EFF(2),e.nI1(3,"translate"),e.k0s()()),2&i){let n;const t=e.XpG(3);e.R7$(),e.Y8G("ngClass",e.eq3(5,T,t.previewWorkingInfo)),e.R7$(),e.Lme(" ",null==t.previewWorkingInfo||null==t.previewWorkingInfo.metadata||null==t.previewWorkingInfo.metadata.objectClient?null:t.previewWorkingInfo.metadata.objectClient.clientName," - ",e.bMT(3,3,null!==(n=null==t.previewWorkingInfo?null:t.previewWorkingInfo.label)&&void 0!==n?n:"TIMETRACKING.EntryPlaceholder")," ")}}function Be(i,r){if(1&i){const n=e.RV6();e.j41(0,"app-inno-select-search-project",27),e.bIt("onSelect",function(o){e.eBV(n);const a=e.XpG(2);return e.Njj(a.handleSelectProject(o))}),e.k0s(),e.DNE(1,ke,4,7,"ng-template",null,1,e.C5r)}if(2&i){const n=e.sdS(2),t=e.XpG(2);e.Y8G("templateTrigger",n)("isOnlySelectProject",!0)("isTimetracking",!0)("value",null==t.previewWorkingInfo?null:t.previewWorkingInfo.value)}}function Pe(i,r){if(1&i&&(e.j41(0,"button",17)(1,"span",28),e.EFF(2),e.k0s()()),2&i){const n=e.XpG(2);e.R7$(),e.Y8G("ngClass",e.eq3(3,T,n.previewWorkingInfo)),e.R7$(),e.Lme(" ",null==n.previewWorkingInfo||null==n.previewWorkingInfo.metadata||null==n.previewWorkingInfo.metadata.objectClient?null:n.previewWorkingInfo.metadata.objectClient.clientName," - ",null==n.previewWorkingInfo?null:n.previewWorkingInfo.label," ")}}function De(i,r){if(1&i&&(e.j41(0,"button",17)(1,"span",28),e.EFF(2),e.nI1(3,"translate"),e.k0s()()),2&i){let n;const t=e.XpG(4);e.R7$(),e.Y8G("ngClass",e.eq3(4,T,t.previewServiceInfo)),e.R7$(),e.SpI(" ",e.bMT(3,2,null!==(n=null==t.previewServiceInfo?null:t.previewServiceInfo.label)&&void 0!==n?n:"TIMETRACKING.ChooseService")," ")}}function je(i,r){if(1&i){const n=e.RV6();e.j41(0,"app-inno-select-search-service",29),e.bIt("onSelect",function(o){e.eBV(n);const a=e.XpG(3);return e.Njj(a.handleSelectServices(o))}),e.k0s(),e.DNE(1,De,4,6,"ng-template",null,2,e.C5r)}if(2&i){const n=e.sdS(2),t=e.XpG(3);e.Y8G("templateTrigger",n)("lable",t.previewWorkingInfo.label)("value",t.projectId)}}function Re(i,r){if(1&i&&(e.j41(0,"button",17)(1,"span",28),e.EFF(2),e.k0s()()),2&i){let n;const t=e.XpG(3);e.R7$(),e.Y8G("ngClass",e.eq3(2,T,t.previewServiceInfo)),e.R7$(),e.SpI(" ",null!==(n=null==t.previewServiceInfo?null:t.previewServiceInfo.label)&&void 0!==n?n:"No Service"," ")}}function Ae(i,r){if(1&i&&e.DNE(0,je,3,3)(1,Re,3,4,"button",17),2&i){const n=e.XpG(2);e.vxM(n.isResume?1:0)}}function Oe(i,r){1&i&&e.nrm(0,"app-inno-tags",30)}function Fe(i,r){if(1&i){const n=e.RV6();e.j41(0,"app-inno-form-checkbox",31),e.bIt("onChange",function(o){e.eBV(n);const a=e.XpG(2);return e.Njj(a.handleChangeBillable(o))}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}if(2&i){const n=e.XpG(2);e.Y8G("checked",n.previewBillable),e.R7$(),e.SpI(" ",e.bMT(2,2,"TIMETRACKING.Billable")," ")}}function Ee(i,r){if(1&i){const n=e.RV6();e.j41(0,"div",4)(1,"div",5)(2,"button",6),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handleToggleExpand())}),e.nrm(3,"img",7),e.k0s(),e.j41(4,"p",8),e.DNE(5,Te,1,1)(6,Ie,1,0),e.k0s()(),e.j41(7,"div",9)(8,"div",10),e.DNE(9,we,1,2,"app-inno-enter-edit-hours",11)(10,ye,2,1,"p",12),e.j41(11,"button",13),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handlePauseOrResumeTime())}),e.DNE(12,Se,1,0,"img",14)(13,Me,1,0,"img",15),e.k0s()(),e.j41(14,"div",16),e.DNE(15,Be,3,4)(16,Pe,3,5,"button",17)(17,Ae,2,1),e.j41(18,"app-inno-form-textarea",18),e.bIt("onChange",function(o){e.eBV(n);const a=e.XpG();return e.Njj(a.handleChangeNote(o))}),e.k0s(),e.nrm(19,"app-inno-select-search-tags",19),e.DNE(20,Oe,1,0,"ng-template",null,0,e.C5r),e.j41(22,"app-inno-datepicker",20),e.bIt("onChange",function(o){e.eBV(n);const a=e.XpG();return e.Njj(a.handleChangeDate(o))}),e.k0s(),e.DNE(23,Fe,3,4,"app-inno-form-checkbox",21),e.j41(24,"div",22)(25,"button",23),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handleAddTimeTrackingRecord())}),e.EFF(26),e.nI1(27,"translate"),e.k0s(),e.j41(28,"button",24),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handleDiscard())}),e.EFF(29),e.nI1(30,"translate"),e.k0s()()()()()}if(2&i){const n=e.sdS(21),t=e.XpG();e.AVh("active",t.isExpand),e.R7$(2),e.Y8G("ngClass",e.eq3(19,xe,!t.isExpand)),e.R7$(3),e.vxM(t.title?5:6),e.R7$(4),e.vxM(t.isEditTimerHours?9:10),e.R7$(3),e.vxM(t.isPause?12:13),e.R7$(3),e.vxM(t.isResume?16:15),e.R7$(2),e.vxM(t.previewWorkingInfo?17:-1),e.R7$(),e.Y8G("value",t.previewDescription),e.R7$(),e.Y8G("templateTrigger",n),e.R7$(3),e.Y8G("value",t.previewDate),e.R7$(),e.vxM(t.isInternal?-1:23),e.R7$(2),e.Y8G("disabled",!t.previewWorkingInfo||!t.previewDate),e.R7$(),e.SpI(" ",e.bMT(27,15,"TIMETRACKING.LogTime")," "),e.R7$(3),e.SpI(" ",e.bMT(30,17,"TIMETRACKING.Discard")," ")}}let Ge=(()=>{var i;class r{constructor(){(0,s.A)(this,"isEditTimerHours",!1),(0,s.A)(this,"isExpand",!1),(0,s.A)(this,"isShowingTimer",!1),(0,s.A)(this,"isResume",!1),(0,s.A)(this,"title",void 0),(0,s.A)(this,"isPause",!0),(0,s.A)(this,"projectId",""),(0,s.A)(this,"previewWorkingInfo",void 0),(0,s.A)(this,"previewServiceInfo",void 0),(0,s.A)(this,"previewDescription",""),(0,s.A)(this,"previewDate",new Date),(0,s.A)(this,"previewBillable",!1),(0,s.A)(this,"timerHours","00:00:00"),(0,s.A)(this,"timerHoursEdit","00:00:00"),(0,s.A)(this,"timerHoursChange","00:00:00"),(0,s.A)(this,"isInternal",!1),(0,s.A)(this,"dataService",(0,e.WQX)(b.u)),(0,s.A)(this,"layoutUtilsService",(0,e.WQX)(B.Z)),(0,s.A)(this,"innoTimerProvider",(0,e.WQX)(Ce.n)),(0,s.A)(this,"translate",(0,e.WQX)(x.c$)),(0,s.A)(this,"interval",null),(0,s.A)(this,"unsubscribe",[]),(0,s.A)(this,"unsubscribeTrackingTimer",void 0),(0,s.A)(this,"stopClock",()=>{this.isPause=!0,clearInterval(this.interval),this.interval=null}),(0,s.A)(this,"startClock",()=>{this.isPause=!1,!this.interval&&(this.interval=setInterval(()=>{let t=this.dataService.GetTimeTrackingCreateTimerInfoValue()?.totalSeconds??0;t+=1,this.timerHours=(0,P.Lc)(t),this.innoTimerProvider.updateTimeTrackingTimerInfo({totalSeconds:t})},1e3))}),(0,s.A)(this,"handlePauseOrResumeTime",this.innoTimerProvider.handlePauseOrResumeTime),(0,s.A)(this,"handleAddTimeTrackingRecord",this.innoTimerProvider.createTimeTrackingFromTimer)}visibilitychange(){this.checkActiveBrowser()}checkActiveBrowser(){document.hidden||this.innoTimerProvider.getTimerInfoFromServer().then(({totalSecondsTimerOnServer:t})=>{const[o,a]=this.timerHoursChange.split(":").map(Number);this.innoTimerProvider.updateTimeTrackingTimerInfo({totalSeconds:3600*o+60*a+t})})}ngOnInit(){if(this.unsubscribe.push(this.dataService.GetTimeTrackingShowingTimer().subscribe(t=>{if(this.dataService.getResume()?(this.isResume=!0,this.title=this.dataService.getResume().title,this.previewWorkingInfo=this.dataService.getResume()?.workingInfo,this.previewDescription=this.dataService.getResume()?.description,this.previewServiceInfo=this.dataService.getResume()?.serviceInfo,this.projectId=this.previewWorkingInfo?.value,this.previewDate=this.dataService.getResume()?.date,this.innoTimerProvider.updateTimeTrackingTimerInfo({workingInfo:this.previewWorkingInfo,description:this.previewDescription,date:this.dataService.getResume()?.date,serviceInfo:this.previewServiceInfo})):this.isResume=!1,this.isShowingTimer=t,this.isShowingTimer){if(this.unsubscribeTrackingTimer)return;this.unsubscribeTrackingTimer=this.dataService.GetTimeTrackingCreateTimerInfo().subscribe(o=>{"running"===(this.timerHours=(0,P.Lc)(o?.totalSeconds),this.previewDate=o?.date??new Date,this.previewDescription=o?.description??"",this.previewBillable=o?.billable??!1,this.previewWorkingInfo=o?.workingInfo,this.title=this.previewWorkingInfo?.label,this.previewServiceInfo=o?.serviceInfo,o?.timerStatus)?this.startClock():this.stopClock()})}})),this.innoTimerProvider.getTimerInfoFromServer().then(({totalSecondsTimerOnServer:t})=>{this.innoTimerProvider.updateTimeTrackingTimerInfo({totalSeconds:t}),t>0&&"true"===localStorage.getItem("isRunning")&&this.dataService.SetNewTimeTrackingShowingTimer(!0)}),this.innoTimerProvider.getCacheClientProject()){this.isInternal=!!this.innoTimerProvider.getCacheClientProject().metadata?.objectClient?.isInternal,this.dataService.SetisInternalClient(this.isInternal),this.title=this.innoTimerProvider.getCacheClientProject().label,this.projectId=this.innoTimerProvider.getCacheClientProject().value;const t=this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date??new Date;if(this.innoTimerProvider.updateTimeTrackingTimerInfo({workingInfo:this.innoTimerProvider.getCacheClientProject(),date:t}),this.isInternal)this.previewBillable=!1,this.innoTimerProvider.updateTimeTrackingTimerInfo({billable:!1});else{const o=this.innoTimerProvider.getCacheClientProject().metadata?.project?.billable;this.previewBillable=o,this.innoTimerProvider.updateTimeTrackingTimerInfo({billable:o})}}this.innoTimerProvider.getCacheService()&&this.innoTimerProvider.updateTimeTrackingTimerInfo({serviceInfo:this.innoTimerProvider.getCacheService()})}EditTimer(){this.timerHoursEdit=this.timerHours,this.isEditTimerHours=!this.isEditTimerHours}handleEditEndTime(t){this.isEditTimerHours=!1,t.isAlreadyEdit&&this.innoTimerProvider.handleUpdateActualTimer(t.newHours)}handleToggleExpand(){this.isExpand=!this.isExpand}handleSelectServices(t){this.previewServiceInfo=t,this.innoTimerProvider.setCacheService(t),this.innoTimerProvider.updateTimeTrackingTimerInfo({serviceInfo:t})}handleSelectProject(t){this.isInternal=!!t.metadata?.objectClient?.isInternal,this.dataService.SetisInternalClient(this.isInternal),this.innoTimerProvider.setCacheClientProject(t),this.title=t.label,this.projectId=t.value;const o=this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date??new Date;if(this.innoTimerProvider.updateTimeTrackingTimerInfo({workingInfo:t,date:o}),this.previewServiceInfo=void 0,this.innoTimerProvider.updateTimeTrackingTimerInfo({serviceInfo:void 0}),this.isInternal)this.previewBillable=!1,this.innoTimerProvider.updateTimeTrackingTimerInfo({billable:!1});else{const u=t.metadata?.project?.billable;this.previewBillable=u,this.innoTimerProvider.updateTimeTrackingTimerInfo({billable:u})}}handleChangeDate(t){this.innoTimerProvider.updateTimeTrackingTimerInfo({date:t})}handleChangeNote(t){this.innoTimerProvider.updateTimeTrackingTimerInfo({description:t})}handleChangeBillable(t){this.innoTimerProvider.updateTimeTrackingTimerInfo({billable:t})}handleCloseTimer(){this.dataService.SetNewTimeTrackingShowingTimer(!1)}handleDiscard(){const t=this.translate.instant("TIMETRACKING.DIALOG.TitleConfirm"),o=this.translate.instant("TIMETRACKING.DIALOG.Discard");this.layoutUtilsService.alertConfirm({title:t,description:o}).then(a=>{a&&(this.stopClock(),this.innoTimerProvider.clearCache(),this.innoTimerProvider.handleResetTimer())})}ngOnDestroy(){this.unsubscribe.forEach(t=>t.unsubscribe()),this.stopClock(),this.title="",this.dataService.SetNewTimeTrackingShowingTimer(!1),this.unsubscribeTrackingTimer?.unsubscribe(),this.unsubscribeTrackingTimer=void 0}}return i=r,(0,s.A)(r,"\u0275fac",function(t){return new(t||i)}),(0,s.A)(r,"\u0275cmp",e.VBU({type:i,selectors:[["app-inno-timer"]],hostBindings:function(t,o){1&t&&e.bIt("visibilitychange",function(u){return o.visibilitychange(u)},!1,e.EBC)},standalone:!0,features:[e.Jv_([B.Z]),e.aNF],decls:1,vars:1,consts:[["templateTriggerSelectTags",""],["templateTriggerSelectProject",""],["templateTriggerSelectService",""],[1,"innoTimer","hide",3,"active"],[1,"innoTimer","hide"],[1,"h-[40px]","rounded-tl-[8px]","rounded-tr-[8px]","w-full","flex","gap-[6px]","items-center","bg-bg-secondary","py-[8px]","px-[16px]"],[1,"button-icon","shrink-0",3,"click","ngClass"],["src","../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon"],[1,"grow","text-center","line-clamp-1","text-text-sm-bold","text-text-primary"],[1,"w-full","px-[16px]","overflow-auto"],[1,"h-[70px]","flex","justify-center","items-center","gap-[16px]"],[3,"value","dynamicWidth"],[1,"px-[10px]","transition-all","cursor-pointer","hover:bg-bg-tertiary","rounded-sm","text-headline-xl-bold","text-text-primary"],[1,"bg-bg-brand-primary","cursor-pointer","rounded-full","w-[36px]","h-[36px]","flex","justify-center","items-center","border-1","border-bg-brand-strong-hover",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[16px]"],["src","../../../assets/img/icon/ic_pause.svg","alt","Icon",1,"w-[16px]"],[1,"w-full","flex","flex-col","gap-[8px]"],[1,"h-[40px]","cursor-pointer","border-2","rounded-md","border-border-primary","w-full"],["placeholder","Note",3,"onChange","value"],[3,"templateTrigger"],["placeholder","Select date",3,"onChange","value"],[3,"checked"],[1,"w-full","flex","flex-col","mt-[16px]","gap-[8px]"],[1,"button-primary","button-size-md","w-full","justify-center",3,"click","disabled"],[1,"button-outline","button-size-md","w-full","justify-center",3,"click"],[3,"onChange","value","dynamicWidth"],[1,"px-[10px]","transition-all","cursor-pointer","hover:bg-bg-tertiary","rounded-sm","text-headline-xl-bold","text-text-primary",3,"click"],[3,"onSelect","templateTrigger","isOnlySelectProject","isTimetracking","value"],[1,"px-[8px]","text-left","line-clamp-1","text-text-md-regular","text-text-placeholder",3,"ngClass"],[3,"onSelect","templateTrigger","lable","value"],["target","","value","Only InnoBook"],[3,"onChange","checked"]],template:function(t,o){1&t&&e.DNE(0,Ee,31,21,"div",3),2&t&&e.vxM(o.isShowingTimer?0:-1)},dependencies:[f.G,w.YU,x.D9,me.C,ge.n,he.R,fe.G,ve.V,_e.B,be.H,de.X],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.innoTimer[_ngcontent-%COMP%]{position:fixed;bottom:0;right:24px;width:300px;transform:translateY(calc(100% - 110px));padding-bottom:20px;transition:all .3s;border-radius:16px;--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);background-color:var(--bg-primary)}.innoTimer.active[_ngcontent-%COMP%]{transform:translateY(0);transition:all .3s}@media screen and (max-width: 600px){.innoTimer[_ngcontent-%COMP%]{right:0}}"]})),r})(),Ne=(()=>{var i;class r{constructor(){(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"dataService",(0,e.WQX)(b.u)),(0,s.A)(this,"storeService",(0,e.WQX)(S.n)),(0,s.A)(this,"authenticationService",(0,e.WQX)(h.k))}ngOnInit(){this.authenticationService.CheckTimer().pipe((0,d.pQ)(this.destroyRef)).subscribe(t=>{t&&1==t.isRunning&&(localStorage.setItem("isRunning",t.isRunning),this.dataService.SetNewTimeTrackingShowingTimer(!0),this.updateTimeTrackingTimerInfo({timerStatus:"running",billable:!0}))}),this.authenticationService.GetUser().subscribe(t=>{this.storeService.set_InforUser(t)})}updateTimeTrackingTimerInfo(t){const a={...this.dataService.GetTimeTrackingCreateTimerInfoValue(),...t};this.dataService.SetNewTimeTrackingCreateTimerInfo(a)}}return i=r,(0,s.A)(r,"\u0275fac",function(t){return new(t||i)}),(0,s.A)(r,"\u0275cmp",e.VBU({type:i,selectors:[["app-authenticated-layout"]],standalone:!0,features:[e.aNF],decls:7,vars:0,consts:[[1,"authenticatedLayout"],[1,"navigation"],[1,"pageContent"],[1,"route"]],template:function(t,o){1&t&&(e.j41(0,"div",0)(1,"div",1),e.nrm(2,"app-navigation"),e.k0s(),e.j41(3,"div",2)(4,"div",3),e.nrm(5,"router-outlet"),e.k0s(),e.nrm(6,"app-inno-timer"),e.k0s()())},dependencies:[p.iI,p.n3,f.G,pe,Ge],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.authenticatedLayout[_ngcontent-%COMP%]{width:100vw;height:100dvh;display:flex;overflow:hidden}.authenticatedLayout[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]{flex-shrink:0;height:100%;z-index:2}.authenticatedLayout[_ngcontent-%COMP%]   .pageContent[_ngcontent-%COMP%]{width:100%;height:100%;overflow:hidden;display:flex;flex-direction:column;position:relative;background-color:var(--bg-secondary)}.authenticatedLayout[_ngcontent-%COMP%]   .pageContent[_ngcontent-%COMP%]   .route[_ngcontent-%COMP%]{width:100%;height:100%;overflow:auto;display:flex;flex-direction:column}"]})),r})()},9088:(D,C,l)=>{l.d(C,{B:()=>f});var s=l(9842),b=l(1626),h=l(4438);const p=l(5312).c.HOST_API+"/api";let f=(()=>{var m;class g{constructor(){(0,s.A)(this,"http",(0,h.WQX)(b.Qq))}CreateCompany(c){return this.http.post(p+"/Company",c)}UpdateCompany(c){return this.http.put(p+"/Company",c)}RemoveImgCompany(){return this.http.put(p+"/Company/RemoveImgCompany",null)}UpdateFinancial(c){return this.http.post(p+"/Company/UpdateFinancial",c)}GetCurrencyCompany(){return this.http.get(p+"/Company/GetCurrencyCompany?")}GetInforCompany(){return this.http.get(p+"/Company/GetInforCompany")}}return m=g,(0,s.A)(g,"\u0275fac",function(c){return new(c||m)}),(0,s.A)(g,"\u0275prov",h.jDH({token:m,factory:m.\u0275fac,providedIn:"root"})),g})()}}]);