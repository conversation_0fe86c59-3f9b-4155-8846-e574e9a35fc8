"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8697],{9248:(R,x,o)=>{o.d(x,{M:()=>C});var i=o(9842),e=o(4438),I=o(6146),A=o(8192),M=o(3652),y=o(5599),b=o(9417),S=o(177),t=o(5236);const l=p=>({"text-text-primary":p});function O(p,g){if(1&p&&(e.j41(0,"label",10),e.EFF(1),e.k0s()),2&p){const h=e.XpG();e.AVh("required",h.isRequired),e.R7$(),e.JRh(h.label)}}function u(p,g){if(1&p&&(e.j41(0,"p",16),e.EFF(1),e.k0s()),2&p){const h=e.XpG().$implicit;e.R7$(),e.SpI(" ",h.description," ")}}function v(p,g){if(1&p){const h=e.RV6();e.j41(0,"div",13),e.bIt("click",function(){const n=e.eBV(h).$implicit,a=e.XpG(2);return e.Njj(a.handleChooseOption(n))}),e.j41(1,"div",14)(2,"p",15),e.EFF(3),e.k0s(),e.DNE(4,u,2,1,"p",16),e.k0s()()}if(2&p){const h=g.$implicit,r=e.XpG(2);e.AVh("selected",h.value===r.value),e.R7$(3),e.SpI(" ",h.label," "),e.R7$(),e.vxM(h.description?4:-1)}}function d(p,g){if(1&p){const h=e.RV6();e.j41(0,"button",17),e.bIt("click",function(){e.eBV(h);const n=e.XpG(2);return e.Njj(n.handleCreateNew())}),e.nrm(1,"img",18),e.EFF(2),e.nI1(3,"translate"),e.k0s()}if(2&p){const h=e.XpG(2);e.R7$(2),e.Lme(" ",e.bMT(3,2,"COMMON.CreateNew"),' "',h.textSearch,'" ')}}function _(p,g){if(1&p){const h=e.RV6();e.j41(0,"div",11)(1,"app-inno-input-search-result",12),e.nI1(2,"translate"),e.bIt("onChange",function(n){e.eBV(h);const a=e.XpG();return e.Njj(a.handleSearch(n))}),e.DNE(3,v,5,4,"ng-template",null,1,e.C5r)(5,d,4,4,"ng-template",null,2,e.C5r),e.k0s()()}if(2&p){let h;const r=e.sdS(4),n=e.sdS(6),a=e.XpG();e.R7$(),e.Y8G("placeholder",e.bMT(2,7,"COMMON.Search"))("data",a.listOptionPreview)("isNotFound",!a.listOptionPreview.length)("isEmptyData",!a.listOptionOriginal.length)("isDisableSearch",a.isDisableSearch)("optionTemplate",null!==(h=a.customOptionTemplate)&&void 0!==h?h:r)("footerTemplate",a.isShowCreateButton?n:null)}}let C=(()=>{var p;class g{constructor(){(0,i.A)(this,"isRequired",void 0),(0,i.A)(this,"label",""),(0,i.A)(this,"options",[]),(0,i.A)(this,"placeholder",""),(0,i.A)(this,"value",""),(0,i.A)(this,"projectId",""),(0,i.A)(this,"isProjectClient",!1),(0,i.A)(this,"errorMessages",void 0),(0,i.A)(this,"formControl",void 0),(0,i.A)(this,"customOptionTemplate",null),(0,i.A)(this,"isDisableSearch",!1),(0,i.A)(this,"isForYear",!1),(0,i.A)(this,"onChange",new e.bkB),(0,i.A)(this,"onSelect",new e.bkB),(0,i.A)(this,"onCreateNew",new e.bkB),(0,i.A)(this,"textSearch",""),(0,i.A)(this,"clientName",""),(0,i.A)(this,"labelOfValueSelected",""),(0,i.A)(this,"listOptionPreview",[]),(0,i.A)(this,"listOptionOriginal",[]),(0,i.A)(this,"searchResultComponent",void 0)}registerOnChange(r){}registerOnTouched(r){}setDisabledState(r){}writeValue(r){}ngOnChanges(r){const n=r?.value?.currentValue??null,a=r?.projectId?.currentValue??null;n&&(this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===n)?.label,this.clientName=this.listOptionOriginal.find(f=>f.value===n)?.label);const c=r?.options?.currentValue;c?.length&&(this.options=c,this.listOptionOriginal=this.options,this.listOptionPreview=this.listOptionOriginal,this.formControl?.value&&(this.value=this.formControl.value,this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===this.value)?.label)),a?(this.clientName=this.listOptionOriginal.find(f=>f.value===a)?.metadata?.objectClient?.clientName,this.labelOfValueSelected=this.clientName+"-"+this.listOptionOriginal.find(f=>f.value===a)?.label):this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===n)?.label,this.value&&!this.labelOfValueSelected&&(this.labelOfValueSelected=this.listOptionOriginal.find(f=>f.value===this.value)?.label)}get isShowCreateButton(){return this.onCreateNew?.observed&&this.textSearch.length&&(!this.listOptionOriginal?.length||!this.listOptionPreview?.length)}handleChange(r){this.onChange?.emit&&this.onChange.emit(r)}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const r in this.formControl.errors)if(this.errorMessages[r])return this.errorMessages[r];return""}handleSearch(r){if(this.isProjectClient){if(r=r?.trim()?.toLowerCase(),!r?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(a=>a.label.toLowerCase().indexOf(r)>-1);const n=[];this.listOptionPreview.forEach(a=>{let c=!1,f=!1;this.listOptionOriginal.filter(T=>"project"==T.metadata.type).forEach(T=>{if(a.value==T.metadata.objectClient.id)c||(n.push(a),c=!0),n.push(T);else{let D=this.listOptionPreview.find(P=>"client"==P.metadata?.type);if(!c&&!D){let P=this.listOptionOriginal.find(F=>F.metadata?.client?.id==a.metadata?.objectClient?.id),G=n.find(F=>F.value==a.metadata?.objectClient?.id);G||(f=!0,n.push(P)),(f||G)&&(n.push(a),c=!0)}}})}),this.listOptionPreview=n}else{if(r=r?.trim()?.toLowerCase(),this.textSearch=r,!r?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(a=>a.label.toLowerCase().indexOf(r)>-1);const n=[];this.listOptionPreview.forEach(a=>{n.push(a),this.listOptionOriginal.filter(c=>"project"==c.metadata?.type).forEach(c=>{a.value==c.metadata.objectClient.id&&n.push(c)})})}}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}touchControl(){this.formControl&&(this.formControl.markAsDirty(),this.formControl.markAsTouched())}handleChooseOption(r){r.value!=this.value&&(this.formControl&&this.formControl.setValue(r.value),this.labelOfValueSelected=r.label,this.value=r.value,this.onSelect.emit(r),this.handleCloseSearchResult())}callbackAfterHideSearchResult(){this.listOptionPreview=this.listOptionOriginal}handleCreateNew(){this.onCreateNew.emit(this.textSearch),this.handleCloseSearchResult()}}return p=g,(0,i.A)(g,"\u0275fac",function(r){return new(r||p)}),(0,i.A)(g,"\u0275cmp",e.VBU({type:p,selectors:[["app-inno-form-select-search"]],viewQuery:function(r,n){if(1&r&&e.GBs(y.x,5),2&r){let a;e.mGM(a=e.lsd())&&(n.searchResultComponent=a.first)}},inputs:{isRequired:"isRequired",label:"label",options:"options",placeholder:"placeholder",value:"value",projectId:"projectId",isProjectClient:"isProjectClient",errorMessages:"errorMessages",formControl:"formControl",customOptionTemplate:"customOptionTemplate",isDisableSearch:"isDisableSearch",isForYear:"isForYear"},outputs:{onChange:"onChange",onSelect:"onSelect",onCreateNew:"onCreateNew"},standalone:!0,features:[e.Jv_([{provide:b.kq,useExisting:(0,e.Rfq)(()=>p),multi:!0}]),e.OA$,e.aNF],decls:10,vars:9,consts:[["templateSearchProject",""],["optionTemplate",""],["buttonCreateNew",""],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],["position","bottom-start",3,"onOpen","onClose","content","isClickOnContentToClose","isClearPadding"],["type","button","target","",1,"dropdown-md","w-full"],[1,"w-full","text-left","line-clamp-1","text-text-placeholder-slight",3,"ngClass"],["src","../../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"shrink-0"],[3,"message"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","max-w-[90dvw]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isDisableSearch","optionTemplate","footerTemplate"],[1,"w-full","flex","p-[8px]","my-1","items-center","gap-[10px]","hover:bg-bg-secondary","rounded-md","cursor-pointer",3,"click"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-teapp-inno-input-search-resultxt-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(r,n){if(1&r){const a=e.RV6();e.j41(0,"div",3),e.DNE(1,O,2,3,"label",4),e.j41(2,"app-inno-popover",5),e.bIt("onOpen",function(){return e.eBV(a),e.Njj(n.touchControl())})("onClose",function(){return e.eBV(a),e.Njj(n.callbackAfterHideSearchResult())}),e.j41(3,"button",6)(4,"div",7),e.EFF(5),e.k0s(),e.nrm(6,"img",8),e.k0s()(),e.DNE(7,_,7,9,"ng-template",null,0,e.C5r),e.nrm(9,"app-inno-error-message",9),e.k0s()}if(2&r){const a=e.sdS(8);e.R7$(),e.vxM(n.label?1:-1),e.R7$(),e.Y8G("content",a)("isClickOnContentToClose",!1)("isClearPadding",!0),e.R7$(2),e.Y8G("ngClass",e.eq3(7,l,n.labelOfValueSelected)),e.R7$(),e.SpI(" ",n.labelOfValueSelected||n.placeholder," "),e.R7$(4),e.Y8G("message",n.getErrorMessage())}},dependencies:[I.G,S.YU,t.D9,y.x,M.t,A.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}']})),g})()},7086:(R,x,o)=>{o.d(x,{C:()=>O});var i=o(9842),e=o(4438),I=o(9417),A=o(6146),M=o(8192),y=o(177);const b=u=>({"resize-none":u});function S(u,v){if(1&u&&(e.j41(0,"label",3),e.EFF(1),e.k0s()),2&u){const d=e.XpG();e.AVh("required",d.isRequired),e.R7$(),e.JRh(d.label)}}function t(u,v){if(1&u){const d=e.RV6();e.j41(0,"textarea",4),e.bIt("keyup",function(C){e.eBV(d);const p=e.XpG();return e.Njj(p.handleChange(C))}),e.k0s(),e.nrm(1,"app-inno-error-message",5)}if(2&u){const d=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",d.class,""),e.Y8G("placeholder",d.placeholder)("formControl",d.formControl)("ngClass",e.eq3(7,b,!d.isAbleResize)),e.R7$(),e.Y8G("message",d.getErrorMessage())}}function l(u,v){if(1&u){const d=e.RV6();e.j41(0,"textarea",6),e.bIt("keyup",function(C){e.eBV(d);const p=e.XpG();return e.Njj(p.handleChange(C))}),e.k0s()}if(2&u){const d=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",d.class,""),e.Y8G("value",d.value)("placeholder",d.placeholder)("ngClass",e.eq3(6,b,!d.isAbleResize))}}let O=(()=>{var u;class v{constructor(){(0,i.A)(this,"class",""),(0,i.A)(this,"isAbleResize",!1),(0,i.A)(this,"isRequired",void 0),(0,i.A)(this,"label",""),(0,i.A)(this,"placeholder",""),(0,i.A)(this,"value",""),(0,i.A)(this,"onChange",new e.bkB),(0,i.A)(this,"formControl",void 0),(0,i.A)(this,"errorMessages",void 0)}registerOnChange(_){}registerOnTouched(_){}setDisabledState(_){}writeValue(_){}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const _ in this.formControl.errors)if(this.errorMessages[_])return this.errorMessages[_];return""}handleChange(_){this.onChange.emit(_?.target?.value??"")}}return u=v,(0,i.A)(v,"\u0275fac",function(_){return new(_||u)}),(0,i.A)(v,"\u0275cmp",e.VBU({type:u,selectors:[["app-inno-form-textarea"]],inputs:{class:"class",isAbleResize:"isAbleResize",isRequired:"isRequired",label:"label",placeholder:"placeholder",value:"value",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:I.kq,useExisting:(0,e.Rfq)(()=>u),multi:!0}]),e.aNF],decls:4,vars:4,consts:[[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],[3,"value","placeholder","class","ngClass"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[3,"keyup","placeholder","formControl","ngClass"],[3,"message"],[3,"keyup","value","placeholder","ngClass"]],template:function(_,C){1&_&&(e.j41(0,"div",0),e.DNE(1,S,2,3,"label",1)(2,t,2,9)(3,l,1,8,"textarea",2),e.k0s()),2&_&&(e.AVh("error",C.hasError()),e.R7$(),e.vxM(C.label?1:-1),e.R7$(),e.vxM(C.formControl?2:3))},dependencies:[A.G,y.YU,I.me,I.BC,I.l_,M.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}']})),v})()},3652:(R,x,o)=>{o.d(x,{t:()=>r});var i=o(9842),e=o(4438),I=o(6146),A=o(9424),M=o(3200);let y=(()=>{var n;class a{constructor(f){(0,i.A)(this,"el",void 0),(0,i.A)(this,"timeoutId",void 0),this.el=f}ngAfterViewInit(){this.timeoutId=setTimeout(()=>{this.el.nativeElement&&this.el.nativeElement.focus()},100)}ngOnDestroy(){this.timeoutId&&clearTimeout(this.timeoutId)}}return n=a,(0,i.A)(a,"\u0275fac",function(f){return new(f||n)(e.rXU(e.aKT))}),(0,i.A)(a,"\u0275dir",e.FsC({type:n,selectors:[["","appAutofocus",""]],standalone:!0})),a})();var b=o(177);const S=n=>({$implicit:n});function t(n,a){if(1&n){const c=e.RV6();e.j41(0,"div",1)(1,"div",3),e.nrm(2,"img",4),e.j41(3,"input",5),e.bIt("keyup",function(T){e.eBV(c);const D=e.XpG();return e.Njj(D.handleOnChange(T))}),e.k0s()()()}if(2&n){const c=e.XpG();e.R7$(3),e.FS9("placeholder",c.placeholder),e.Y8G("value",c.inputSearchValue)}}function l(n,a){1&n&&(e.j41(0,"div",2),e.nrm(1,"app-inno-spin"),e.k0s())}function O(n,a){1&n&&e.nrm(0,"app-inno-empty-data",8),2&n&&e.Y8G("title","COMMON.EmptyData")}function u(n,a){1&n&&e.nrm(0,"app-inno-empty-data",9),2&n&&e.Y8G("title","COMMON.NoResult")("description","COMMON.DifferentKeywords")}function v(n,a){if(1&n&&e.DNE(0,O,1,1,"app-inno-empty-data",8)(1,u,1,2,"app-inno-empty-data",9),2&n){const c=e.XpG(2);e.vxM(c.isEmptyData?0:1)}}function d(n,a){1&n&&e.eu8(0)}function _(n,a){if(1&n&&e.DNE(0,d,1,0,"ng-container",10),2&n){const c=a.$implicit,f=e.XpG(3);e.Y8G("ngTemplateOutlet",f.optionTemplate)("ngTemplateOutletContext",e.eq3(2,S,c))}}function C(n,a){if(1&n&&e.Z7z(0,_,1,4,"ng-container",null,e.fX1),2&n){const c=e.XpG(2);e.Dyx(c.data)}}function p(n,a){1&n&&e.eu8(0)}function g(n,a){if(1&n&&(e.j41(0,"div",7),e.DNE(1,p,1,0,"ng-container",11),e.k0s()),2&n){const c=e.XpG(2);e.R7$(),e.Y8G("ngTemplateOutlet",c.footerTemplate)}}function h(n,a){if(1&n&&(e.j41(0,"div",6),e.DNE(1,v,2,1)(2,C,2,0),e.k0s(),e.DNE(3,g,2,1,"div",7)),2&n){const c=e.XpG();e.R7$(),e.vxM(c.isNotFound?1:2),e.R7$(2),e.vxM(c.footerTemplate?3:-1)}}let r=(()=>{var n;class a{constructor(){(0,i.A)(this,"defaultValue",""),(0,i.A)(this,"placeholder",""),(0,i.A)(this,"data",[]),(0,i.A)(this,"optionTemplate",null),(0,i.A)(this,"footerTemplate",null),(0,i.A)(this,"isNotFound",!1),(0,i.A)(this,"isEmptyData",!1),(0,i.A)(this,"isLoading",!1),(0,i.A)(this,"isDisableSearch",!1),(0,i.A)(this,"inputSearchValue",""),(0,i.A)(this,"onChange",new e.bkB)}ngOnInit(){this.inputSearchValue=this.defaultValue}handleOnChange(f){const T=f?.target?.value??"";this.onChange.emit(T),this.inputSearchValue=T}}return n=a,(0,i.A)(a,"\u0275fac",function(f){return new(f||n)}),(0,i.A)(a,"\u0275cmp",e.VBU({type:n,selectors:[["app-inno-input-search-result"]],inputs:{defaultValue:"defaultValue",placeholder:"placeholder",data:"data",optionTemplate:"optionTemplate",footerTemplate:"footerTemplate",isNotFound:"isNotFound",isEmptyData:"isEmptyData",isLoading:"isLoading",isDisableSearch:"isDisableSearch"},outputs:{onChange:"onChange"},standalone:!0,features:[e.aNF],decls:4,vars:2,consts:[[1,"min-w-[300px]","w-full","shadow-md","rounded-md","border","border-border-primary-slight","bg-bg-primary"],[1,"w-full","p-[16px]","border-b","border-border-primary-slight"],[1,"flex","justify-center","py-3"],[1,"w-full","h-[40px]","flex","items-center","rounded-[8px]","border-[2px]","px-[12px]"],["src","../../../assets/img/icon/ic_search_gray.svg","alt","Icon search",1,"w-[16px]","shrink-0"],["appAutofocus","","type","text",1,"h-full","w-full","pl-[8px]","text-text-md-regular",3,"keyup","placeholder","value"],[1,"w-full","p-[8px]","max-h-[300px]","max-w-[500px]","overflow-auto"],[1,"border-t","border-border-primary","p-[8px]","w-full"],[3,"title"],[3,"title","description"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[4,"ngTemplateOutlet"]],template:function(f,T){1&f&&(e.j41(0,"div",0),e.DNE(1,t,4,2,"div",1)(2,l,2,0,"div",2)(3,h,4,2),e.k0s()),2&f&&(e.R7$(),e.vxM(T.isDisableSearch?-1:1),e.R7$(),e.vxM(T.isLoading?2:3))},dependencies:[I.G,b.T3,A.f,M.J,y],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),a})()},1556:(R,x,o)=>{o.d(x,{Z:()=>S});var i=o(9842),e=o(4438),I=o(467),A=o(2716),M=o(7987);let y=(()=>{var t;class l extends A.H{open(u){var v=this;return(0,I.A)(function*(){const d=yield o.e(3190).then(o.bind(o,3190));return v.matDialog.open(d.AlertConfirmComponent.getComponent(),{data:u,width:"440px",panelClass:"custom_dialog",scrollStrategy:new M.t0,disableClose:!0})})()}}return t=l,(0,i.A)(l,"\u0275fac",(()=>{let O;return function(v){return(O||(O=e.xGo(t)))(v||t)}})()),(0,i.A)(l,"\u0275prov",e.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})),l})(),S=(()=>{var t;class l{constructor(u){(0,i.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=u}alertDelete(u){const{title:v,description:d,textSubmit:_="COMMON.Delete",textCancel:C}=u;return new Promise(p=>{this.alertConfirmDialog.open({title:v,description:d,textSubmit:_,textCancel:C,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(h=>{h.afterClosed().subscribe(r=>{p(r??!1)})})})}alertConfirm(u){const{title:v,description:d,textSubmit:_,textCancel:C}=u;return new Promise(p=>{this.alertConfirmDialog.open({title:v,description:d,textSubmit:_,textCancel:C}).then(h=>{h.afterClosed().subscribe(r=>{p(r??!1)})})})}}return t=l,(0,i.A)(l,"\u0275fac",function(u){return new(u||t)(e.KVO(y))}),(0,i.A)(l,"\u0275prov",e.jDH({token:t,factory:t.\u0275fac})),l})()},5936:(R,x,o)=>{o.d(x,{H:()=>y});var i=o(9842),e=o(1626),I=o(4438);const M=o(5312).c.HOST_API+"/api";let y=(()=>{var b;class S{constructor(){(0,i.A)(this,"http",(0,I.WQX)(e.Qq))}GetFile(l){return this.http.get(M+`/Images/GetFile?nameFile=${l}`,{responseType:"blob"})}GetFileURL(l){return this.http.get(M+`/Images/GetFileURL?nameFile=${l}`,{responseType:"blob"})}}return b=S,(0,i.A)(S,"\u0275fac",function(l){return new(l||b)}),(0,i.A)(S,"\u0275prov",I.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})),S})()},6042:(R,x,o)=>{o.r(x),o.d(x,{BusinessComponent:()=>G});var i=o(467),e=o(9842),I=o(5936),A=o(7572),M=o(1110);const y=[{value:"dd/MM/YYYY",moment:"DD/MM/YYYY"},{value:"dd-MM-YYYY",moment:"DD-MM-YYYY"},{value:"YYYY-MM-dd",moment:"YYYY-MM-DD"},{value:"MMMM Do YYYY",moment:"MMMM Do YYYY"}];var b=o(9088),S=o(6146),t=o(4438),l=o(9417),O=o(33),u=o(1342),v=o(6327),d=o(6508),_=o.n(d),C=o(9830),p=o(9079),g=o(3492),h=o(1328),r=o(7086),n=o(9248),a=o(5236),c=o(6473),f=o(177);const T=F=>({required:F}),D=(F,E)=>({required:F,email:E}),P=(F,E,N,m)=>({required:F,minlength:E,maxlength:N,pattern:m});let G=(()=>{var F;class E{constructor(m){(0,e.A)(this,"location",void 0),(0,e.A)(this,"base64",void 0),(0,e.A)(this,"type",void 0),(0,e.A)(this,"filename",void 0),(0,e.A)(this,"imageUrl",void 0),(0,e.A)(this,"createdBy",void 0),(0,e.A)(this,"businessForm",void 0),(0,e.A)(this,"dateFormatOption",[]),(0,e.A)(this,"countriesOption",v.F.map(s=>({label:s.name,value:s.code}))),(0,e.A)(this,"startWeekOption",[{label:"Monday",value:"Monday"},{label:"Sunday",value:"Sunday"}]),(0,e.A)(this,"timezoneOption",_().tz.names().map(s=>{const B=_().tz(s).utcOffset()/60;return{label:`(UTC${B>=0?"+":""}${B}:00) ${s}`,value:s}})),(0,e.A)(this,"companyId",void 0),(0,e.A)(this,"spiner_services",(0,t.WQX)(u.D)),(0,e.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,e.A)(this,"router",(0,t.WQX)(O.Ix)),(0,e.A)(this,"_toastService",(0,t.WQX)(g.f)),(0,e.A)(this,"_companyServices",(0,t.WQX)(b.B)),(0,e.A)(this,"formBuilder",(0,t.WQX)(l.ze)),(0,e.A)(this,"_storeService",(0,t.WQX)(M.n)),(0,e.A)(this,"translate",(0,t.WQX)(a.c$)),(0,e.A)(this,"cdnService",(0,t.WQX)(I.H)),this.location=m,this.businessForm=this.formBuilder.group({business:["",l.k0.compose([l.k0.required])],email:["",l.k0.compose([l.k0.required,l.k0.email])],phone:["",l.k0.compose([l.k0.required,l.k0.pattern("[0-9]*"),l.k0.minLength(10),l.k0.maxLength(10)])],country:["",l.k0.compose([l.k0.required])],address1:[""],address2:[""],tow_city:[""],state_povince:[""],postal_code:[""],timezone:[""],start_week_on:[""],dateFormat:[""],note:[""]}),this.businessForm.controls.timezone.setValue(_().tz.guess()),this.businessForm.controls.start_week_on.setValue("Monday"),this.dateFormatOption=y.map(s=>({label:`${s.value} ${_()().format(s.moment)}`,value:s.value})),this.businessForm.controls.dateFormat.setValue(this.dateFormatOption[0].value)}ngOnInit(){this.GetBusiness()}GetImg(m){this.cdnService.GetFile(m).pipe((0,p.pQ)(this.destroyRef)).subscribe(s=>{if(s){const B=new FileReader;B.onload=()=>{this.imageUrl=B.result},B.readAsDataURL(s)}})}get f(){return this.businessForm.controls}_handleData(m){this.businessForm.controls.business.setValue(m.businessName),this.businessForm.controls.phone.setValue(m.phone),this.businessForm.controls.country.setValue(m.country),this.businessForm.controls.address1.setValue(m?.adress),this.businessForm.controls.address2.setValue(m?.adress2),this.businessForm.controls.tow_city.setValue(m.city),this.businessForm.controls.email.setValue(m.email),this.businessForm.controls.state_povince.setValue(m.province),this.businessForm.controls.postal_code.setValue(m.postalCode),this.businessForm.controls.timezone.setValue(m.timeZone?m.timeZone:_().tz.guess()),this.businessForm.controls.start_week_on.setValue(m.startWeekOn),this.businessForm.controls.dateFormat.setValue(m.dateFormat),this.businessForm.controls.note.setValue(m.note),m.companyImage&&this.GetImg(m.companyImage)}handleChangePicture(m){var s=this;return(0,i.A)(function*(){const B=m?.[0],{base64:j,fileName:V,type:Y}=yield(0,c.EX)(B);s.base64=j,s.type=Y,s.filename=V})()}GetBusiness(){this._companyServices.GetInforCompany().pipe((0,p.pQ)(this.destroyRef)).subscribe(m=>{m&&(this.createdBy=m.createdBy,this._handleData(m),this.companyId=m.id)})}markAllControlsAsTouched(){Object.values(this.f).forEach(m=>{m.markAsTouched()})}onSubmit(){if(this.businessForm.invalid)return void this.markAllControlsAsTouched();this.spiner_services.show();let m={businessName:this.businessForm.controls.business.value,phone:this.businessForm.controls.phone.value,email:this.businessForm.controls.email.value,country:this.businessForm.controls.country.value,adress:this.businessForm.controls.address1.value,adress2:this.businessForm.controls.address2.value,city:this.businessForm.controls.tow_city.value,province:this.businessForm.controls.state_povince.value,postalCode:this.businessForm.controls.postal_code.value,timeZone:this.businessForm.controls.timezone.value,startWeekOn:this.businessForm.controls.start_week_on.value,dateFormat:this.businessForm.controls.dateFormat.value,note:this.businessForm.controls.note.value,createdBy:this.createdBy,base64:this.base64,type:this.type,filename:this.filename};this._storeService.getChooseBusiness()?(m.id=this.companyId,this._companyServices.UpdateCompany(m).pipe((0,p.pQ)(this.destroyRef)).subscribe(s=>{s&&(this.spiner_services.hide(),this._toastService.showSuccess(this.translate.instant("TOAST.Success"),this.translate.instant("TOAST.UpdateBusiness")),this.GetBusiness(),s.dateFormat&&""!=s.dateFormat&&this._storeService.setdateFormat(s.dateFormat))})):this._companyServices.CreateCompany(m).pipe((0,p.pQ)(this.destroyRef)).subscribe({next:s=>{s&&(this.spiner_services.hide(),this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this.router.navigate(["/"]))}})}handleBack(){this.location.back()}}return F=E,(0,e.A)(E,"\u0275fac",function(m){return new(m||F)(t.rXU(f.aZ))}),(0,e.A)(E,"\u0275cmp",t.VBU({type:F,selectors:[["app-business"]],standalone:!0,features:[t.Jv_([(0,C.Dw)()]),t.aNF],decls:63,vars:153,consts:[[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full"],[1,"flex","items-center","gap-[8px]"],[1,"button-icon","button-size-md",3,"click"],["src","../../../../assets/img/icon/ic_arrow_left.svg","alt","Icon"],[1,"text-text-primary","text-headline-lg-bold"],[1,"container-full","py-[20px]","bg-white"],[1,"w-[160px]","shrink-0","mb-2","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'",3,"onChange","canEdit","isBussiness","imageUrl"],[1,"flex","flex-col","w-full","mx-auto","gap-[16px]",3,"ngSubmit","formGroup"],[3,"label","placeholder","formControl","value","errorMessages"],["type","email",3,"label","placeholder","formControl","value","errorMessages"],["mask","(*************","autocomplete","phone","pattern","[0-9]*",3,"label","placeholder","formControl","value","errorMessages"],[3,"label","placeholder","options","formControl","value","errorMessages"],[3,"label","placeholder","formControl","value"],[3,"label","placeholder","options","isDisableSearch","formControl","value"],[3,"label","placeholder","options","formControl","value"],[1,"w-full","flex","justify-center","mt-[20px]"],["type","submit",1,"button-primary","button-size-md","px-[20px]"]],template:function(m,s){1&m&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),t.bIt("click",function(){return s.handleBack()}),t.nrm(4,"img",4),t.k0s(),t.j41(5,"p",5),t.EFF(6),t.nI1(7,"translate"),t.k0s()()()(),t.j41(8,"div",6)(9,"div",7)(10,"app-inno-upload",8),t.bIt("onChange",function(j){return s.handleChangePicture(j)}),t.k0s()(),t.j41(11,"form",9),t.bIt("ngSubmit",function(){return s.onSubmit()}),t.nrm(12,"app-inno-form-input",10),t.nI1(13,"translate"),t.nI1(14,"translate"),t.nI1(15,"translate"),t.nrm(16,"app-inno-form-input",11),t.nI1(17,"translate"),t.nI1(18,"translate"),t.nI1(19,"translate"),t.nI1(20,"translate"),t.nrm(21,"app-inno-form-input",12),t.nI1(22,"translate"),t.nI1(23,"translate"),t.nI1(24,"translate"),t.nI1(25,"translate"),t.nI1(26,"translate"),t.nI1(27,"translate"),t.nrm(28,"app-inno-form-select-search",13),t.nI1(29,"translate"),t.nI1(30,"translate"),t.nI1(31,"translate"),t.nrm(32,"app-inno-form-input",14),t.nI1(33,"translate"),t.nI1(34,"translate"),t.nrm(35,"app-inno-form-input",14),t.nI1(36,"translate"),t.nI1(37,"translate"),t.nrm(38,"app-inno-form-input",14),t.nI1(39,"translate"),t.nI1(40,"translate"),t.nrm(41,"app-inno-form-input",14),t.nI1(42,"translate"),t.nI1(43,"translate"),t.nrm(44,"app-inno-form-input",14),t.nI1(45,"translate"),t.nI1(46,"translate"),t.nrm(47,"app-inno-form-select-search",15),t.nI1(48,"translate"),t.nI1(49,"translate"),t.nrm(50,"app-inno-form-select-search",16),t.nI1(51,"translate"),t.nI1(52,"translate"),t.nrm(53,"app-inno-form-select-search",15),t.nI1(54,"translate"),t.nI1(55,"translate"),t.nrm(56,"app-inno-form-textarea",14),t.nI1(57,"translate"),t.nI1(58,"translate"),t.j41(59,"div",17)(60,"button",18),t.EFF(61),t.nI1(62,"translate"),t.k0s()()()()),2&m&&(t.R7$(6),t.SpI(" ",t.bMT(7,69,"SETTINGS.BasicInformation.Title")," "),t.R7$(4),t.Y8G("canEdit",!0)("isBussiness",!0)("imageUrl",s.imageUrl),t.R7$(),t.Y8G("formGroup",s.businessForm),t.R7$(),t.Y8G("label",t.bMT(13,71,"SETTINGS.BasicInformation.BasicInformationForm.BusinessName"))("placeholder",t.bMT(14,73,"SETTINGS.BasicInformation.BasicInformationForm.BusinessNamePlaceholder"))("formControl",s.f.business)("value",s.f.business.value)("errorMessages",t.eq3(141,T,t.bMT(15,75,"SETTINGS.BasicInformation.BasicInformationForm.BusinessNameRequired"))),t.R7$(4),t.Y8G("label",t.bMT(17,77,"SETTINGS.BasicInformation.BasicInformationForm.EmailBusiness"))("placeholder",t.bMT(18,79,"SETTINGS.BasicInformation.BasicInformationForm.EmailPlaceholder"))("formControl",s.f.email)("value",s.f.email.value)("errorMessages",t.l_i(143,D,t.bMT(19,81,"SETTINGS.BasicInformation.BasicInformationForm.EmailRequired"),t.bMT(20,83,"SETTINGS.BasicInformation.BasicInformationForm.InvalidEmail"))),t.R7$(5),t.Y8G("label",t.bMT(22,85,"SETTINGS.BasicInformation.BasicInformationForm.PhoneNumber"))("placeholder",t.bMT(23,87,"SETTINGS.BasicInformation.BasicInformationForm.PhonePlaceholder"))("formControl",s.f.phone)("value",s.f.phone.value)("errorMessages",t.ziG(146,P,t.bMT(24,89,"SETTINGS.BasicInformation.BasicInformationForm.PhoneRequired"),t.bMT(25,91,"SETTINGS.BasicInformation.BasicInformationForm.PhoneLength"),t.bMT(26,93,"SETTINGS.BasicInformation.BasicInformationForm.PhoneLength"),t.bMT(27,95,"SETTINGS.BasicInformation.BasicInformationForm.PhonePattern"))),t.R7$(7),t.Y8G("label",t.bMT(29,97,"SETTINGS.BasicInformation.BasicInformationForm.Country"))("placeholder",t.bMT(30,99,"SETTINGS.BasicInformation.BasicInformationForm.Country"))("options",s.countriesOption)("formControl",s.f.country)("value",s.f.country.value)("errorMessages",t.eq3(151,T,t.bMT(31,101,"SETTINGS.BasicInformation.BasicInformationForm.CountryRequired"))),t.R7$(4),t.Y8G("label",t.bMT(33,103,"SETTINGS.BasicInformation.BasicInformationForm.AddressLine1"))("placeholder",t.bMT(34,105,"SETTINGS.BasicInformation.BasicInformationForm.AddressLine1Placeholder"))("formControl",s.f.address1)("value",s.f.address1.value),t.R7$(3),t.Y8G("label",t.bMT(36,107,"SETTINGS.BasicInformation.BasicInformationForm.AddressLine2"))("placeholder",t.bMT(37,109,"SETTINGS.BasicInformation.BasicInformationForm.AddressLine2Placeholder"))("formControl",s.f.address2)("value",s.f.address2.value),t.R7$(3),t.Y8G("label",t.bMT(39,111,"SETTINGS.BasicInformation.BasicInformationForm.TownCity"))("placeholder",t.bMT(40,113,"SETTINGS.BasicInformation.BasicInformationForm.TownCityPlaceholder"))("formControl",s.f.tow_city)("value",s.f.tow_city.value),t.R7$(3),t.Y8G("label",t.bMT(42,115,"SETTINGS.BasicInformation.BasicInformationForm.StateProvince"))("placeholder",t.bMT(43,117,"SETTINGS.BasicInformation.BasicInformationForm.StateProvincePlaceholder"))("formControl",s.f.state_povince)("formControl",s.f.state_povince)("value",s.f.state_povince.value),t.R7$(3),t.Y8G("label",t.bMT(45,119,"SETTINGS.BasicInformation.BasicInformationForm.PostalCode"))("placeholder",t.bMT(46,121,"SETTINGS.BasicInformation.BasicInformationForm.PostalCodePlaceholder"))("formControl",s.f.postal_code)("value",s.f.postal_code.value),t.R7$(3),t.Y8G("label",t.bMT(48,123,"SETTINGS.BasicInformation.BasicInformationForm.StartWeekOn"))("placeholder",t.bMT(49,125,"SETTINGS.BasicInformation.BasicInformationForm.StartWeekOnPlaceholder"))("options",s.startWeekOption)("isDisableSearch",!0)("formControl",s.f.start_week_on)("value",s.f.start_week_on.value),t.R7$(3),t.Y8G("label",t.bMT(51,127,"SETTINGS.BasicInformation.BasicInformationForm.Timezone"))("placeholder",t.bMT(52,129,"SETTINGS.BasicInformation.BasicInformationForm.PlaceholderTimezone"))("options",s.timezoneOption)("formControl",s.f.timezone)("value",s.f.timezone.value),t.R7$(3),t.Y8G("label",t.bMT(54,131,"SETTINGS.BasicInformation.BasicInformationForm.DateFormat"))("placeholder",t.bMT(55,133,"SETTINGS.BasicInformation.BasicInformationForm.PlaceholderDateFormat"))("options",s.dateFormatOption)("isDisableSearch",!0)("formControl",s.f.dateFormat)("value",s.f.dateFormat.value),t.R7$(3),t.Y8G("label",t.bMT(57,135,"SETTINGS.BasicInformation.BasicInformationForm.Note"))("placeholder",t.bMT(58,137,"SETTINGS.BasicInformation.BasicInformationForm.NotePlaceholder"))("formControl",s.f.note)("value",s.f.note.value),t.R7$(5),t.SpI(" ",t.bMT(62,139,"BUTTON.Save")," "))},dependencies:[S.G,l.qT,l.BC,l.cb,l.R_,l.l_,l.j4,a.D9,h.a,r.C,n.M,A.P],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.navigate-link[_ngcontent-%COMP%]{text-decoration:none;font-size:15px!important;display:flex;flex-direction:row;width:100px;gap:5px;-webkit-user-select:none;user-select:none}[_nghost-%COMP%]   .e-ddl.e-input-group[_ngcontent-%COMP%]{font-size:15px!important}"]})),E})()}}]);