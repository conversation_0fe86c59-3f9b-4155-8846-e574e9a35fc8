"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6539],{6539:(b,m,t)=>{t.r(m),t.d(m,{LoginComponent:()=>M});var s=t(9842),_=t(1342),d=t(6146),r=t(9417),e=t(4438),l=t(33),u=t(9079),c=t(2928),h=t(828),v=t(3492),f=t(1328),E=t(1149),g=t(5236);const I=(i,a)=>({required:i,email:a}),x=i=>({required:i});let M=(()=>{var i;class a{constructor(){(0,s.A)(this,"isShowPassword",!1),(0,s.A)(this,"loginForm",void 0),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"formBuilder",(0,e.WQX)(r.ze)),(0,s.A)(this,"auth_services",(0,e.WQX)(c.k)),(0,s.A)(this,"spinnerService",(0,e.WQX)(_.D)),(0,s.A)(this,"router",(0,e.WQX)(l.Ix)),(0,s.A)(this,"_toastService",(0,e.WQX)(v.f)),this.loginForm=this.formBuilder.group({email:["",[r.k0.required,r.k0.email]],password:["",r.k0.required],rememberMe:[!1,[]]})}ngOnInit(){this.auth_services.getAccessToken()&&this.router.navigate(["/"])}get f(){return this.loginForm.controls}handleToggleShowPassword(){this.isShowPassword=!this.isShowPassword}markAllControlsAsTouched(){Object.values(this.f).forEach(n=>{n.markAsTouched()})}onSubmit(){this.loginForm.invalid?this.markAllControlsAsTouched():(this.spinnerService.show(),this.auth_services.login({Email:this.loginForm.controls.email.value,Password:this.loginForm.controls.password.value}).pipe((0,u.pQ)(this.destroyRef)).subscribe(o=>{o.isSuccess?(this.auth_services.saveToken_cookie(o.token.accessToken,o.token.refreshToken),this.spinnerService.hide(),this.router.navigate(["/"])):(this.spinnerService.hide(),this._toastService.showError("Error",o.message))}))}}return i=a,(0,s.A)(a,"\u0275fac",function(n){return new(n||i)}),(0,s.A)(a,"\u0275cmp",e.VBU({type:i,selectors:[["app-login"]],standalone:!0,features:[e.aNF],decls:54,vars:50,consts:[[1,"max-w-md","w-full","space-y-8"],[1,"text-center"],[1,"text-headline-lg-bold"],[1,"mt-2","text-text-sm-regular","text-text-secondary"],["routerLink","/sign-up",1,"font-semibold","text-text-brand-primary"],[1,"mt-10",3,"ngSubmit","formGroup"],[1,"flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-secondary","text-text-sm-semibold","!mb-[5px]"],[1,"relative"],[1,"material-icons","absolute","top-[12px]","left-[10px]","z-10","text-text-tertiary","!text-[22px]"],["type","email","inputClassName","!h-[46px] pl-[40px]",3,"placeholder","formControl","value","errorMessages"],[1,"flex","justify-between","items-center","gap-[5px]","flex-wrap","!mb-[5px]"],[1,"text-text-secondary","text-text-sm-semibold"],["routerLink","/forgot-password",1,"text-text-brand-primary","text-text-sm-semibold"],["inputClassName","!h-[46px] px-[40px]",3,"type","placeholder","formControl","value","errorMessages"],[1,"material-icons","absolute","top-[12px]","right-[10px]","z-10","text-text-tertiary","!text-[22px]","cursor-pointer",3,"click"],["type","submit",1,"button-size-md","button-primary","w-full","justify-center","mt-[34px]"],[1,"mt-6"],[1,"absolute","inset-0","flex","items-center"],[1,"w-full","border-t","border-border-primary"],[1,"relative","flex","justify-center","text-sm"],[1,"px-2","bg-white","text-text-tertiary"],[1,"w-full","mt-6"]],template:function(n,o){1&n&&(e.j41(0,"app-auth-layout")(1,"div",0)(2,"div",1)(3,"p",2),e.EFF(4),e.nI1(5,"translate"),e.k0s(),e.j41(6,"p",3),e.EFF(7),e.nI1(8,"translate"),e.j41(9,"a",4),e.EFF(10),e.nI1(11,"translate"),e.k0s()()(),e.j41(12,"form",5),e.bIt("ngSubmit",function(){return o.onSubmit()}),e.j41(13,"div",6)(14,"div",7)(15,"p",8),e.EFF(16),e.nI1(17,"translate"),e.k0s(),e.j41(18,"div",9)(19,"i",10),e.EFF(20,"email"),e.k0s(),e.nrm(21,"app-inno-form-input",11),e.nI1(22,"translate"),e.nI1(23,"translate"),e.nI1(24,"translate"),e.k0s()(),e.j41(25,"div",7)(26,"div",12)(27,"p",13),e.EFF(28),e.nI1(29,"translate"),e.k0s(),e.j41(30,"a",14),e.EFF(31),e.nI1(32,"translate"),e.k0s()(),e.j41(33,"div",9)(34,"i",10),e.EFF(35,"lock"),e.k0s(),e.nrm(36,"app-inno-form-input",15),e.nI1(37,"translate"),e.nI1(38,"translate"),e.j41(39,"i",16),e.bIt("click",function(){return o.handleToggleShowPassword()}),e.EFF(40),e.k0s()()()(),e.j41(41,"button",17),e.EFF(42),e.nI1(43,"translate"),e.k0s()(),e.j41(44,"div",18)(45,"div",9)(46,"div",19),e.nrm(47,"div",20),e.k0s(),e.j41(48,"div",21)(49,"span",22),e.EFF(50),e.nI1(51,"translate"),e.k0s()()()(),e.j41(52,"div",23),e.nrm(53,"app-login-social"),e.k0s()()()),2&n&&(e.R7$(4),e.JRh(e.bMT(5,19,"LOGIN.TitleSign")),e.R7$(3),e.SpI(" ",e.bMT(8,21,"LOGIN.Or")," "),e.R7$(3),e.SpI(" ",e.bMT(11,23,"LOGIN.TitleNewAccount")," "),e.R7$(2),e.Y8G("formGroup",o.loginForm),e.R7$(4),e.SpI(" ",e.bMT(17,25,"LOGIN.EmailAddress")," "),e.R7$(5),e.Y8G("placeholder",e.bMT(22,27,"LOGIN.PlaceholderEmail"))("formControl",o.f.email)("value",o.f.email.value)("errorMessages",e.l_i(45,I,e.bMT(23,29,"LOGIN.EmailRequired"),e.bMT(24,31,"LOGIN.InvalidRequired"))),e.R7$(7),e.SpI(" ",e.bMT(29,33,"LOGIN.Password")," "),e.R7$(3),e.SpI(" ",e.bMT(32,35,"LOGIN.ForgotPassword")," "),e.R7$(5),e.Y8G("type",o.isShowPassword?"text":"password")("placeholder",e.bMT(37,37,"LOGIN.PlaceholderPassword"))("formControl",o.f.password)("value",o.f.password.value)("errorMessages",e.eq3(48,x,e.bMT(38,39,"LOGIN.PasswordRequired"))),e.R7$(4),e.SpI(" ",o.isShowPassword?"visibility_off":"visibility"," "),e.R7$(2),e.SpI(" ",e.bMT(43,41,"LOGIN.Signin")," "),e.R7$(8),e.SpI(" ",e.bMT(51,43,"LOGIN.OrContinue"),""))},dependencies:[d.G,r.qT,r.BC,r.cb,r.l_,r.j4,g.D9,l.iI,l.Wk,f.a,E.K,h.G],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),a})()}}]);