"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3099],{7656:(B,x,r)=>{r.d(x,{V:()=>j});var a=r(9842),i=r(4438),v=r(6146),e=r(9417);const T=["*"];function I(u,h){if(1&u){const p=i.RV6();i.j41(0,"input",5),i.bIt("change",function(C){i.eBV(p);const f=i.XpG();return i.Njj(f.handleChange(C))}),i.k0s()}if(2&u){const p=i.XpG();i.Y8G("checked",p.checked)("formControl",p.formControl)}}function E(u,h){if(1&u){const p=i.RV6();i.j41(0,"input",6),i.bIt("change",function(C){i.eBV(p);const f=i.XpG();return i.Njj(f.handleChange(C))}),i.k0s()}if(2&u){const p=i.XpG();i.Y8G("checked",p.checked)}}let j=(()=>{var u;class h{constructor(){(0,a.A)(this,"checked",void 0),(0,a.A)(this,"onChange",new i.bkB),(0,a.A)(this,"formControl",void 0),(0,a.A)(this,"errorMessages",void 0)}registerOnChange(d){}registerOnTouched(d){}setDisabledState(d){}writeValue(d){}handleChange(d){this.onChange.emit(d?.target?.checked??!1)}}return u=h,(0,a.A)(h,"\u0275fac",function(d){return new(d||u)}),(0,a.A)(h,"\u0275cmp",i.VBU({type:u,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[i.Jv_([{provide:e.kq,useExisting:(0,i.Rfq)(()=>u),multi:!0}]),i.aNF],ngContentSelectors:T,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(d,C){1&d&&(i.NAR(),i.j41(0,"div",0)(1,"label",1),i.DNE(2,I,1,2,"input",2)(3,E,1,1,"input",3),i.j41(4,"div",4),i.SdG(5),i.k0s()()()),2&d&&(i.R7$(2),i.vxM(C.formControl?2:3))},dependencies:[v.G,e.Zm,e.BC,e.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),h})()},3099:(B,x,r)=>{r.r(x),r.d(x,{SelectTimeTrackingComponent:()=>V});var a=r(9842),i=r(4433),v=r(1110),e=r(4438),T=r(4006),I=r(3200),E=r(7656),j=r(344),u=r(4978),h=r(9424),p=r(6473),d=r(6146),C=r(6617),f=r(4823),A=r(1970),U=r(9079),R=r(4805),b=r(2840),M=r(1448),S=r(5909),O=r(5236),P=r(177);const F=l=>({"font-bold text-black":l});function y(l,m){1&l&&(e.j41(0,"div",4),e.nrm(1,"app-inno-spin"),e.k0s())}function N(l,m){1&l&&(e.j41(0,"div",3),e.nrm(1,"app-inno-empty-data",7),e.k0s())}function G(l,m){if(1&l&&(e.j41(0,"span",16),e.EFF(1),e.k0s()),2&l){const t=e.XpG(3);e.R7$(),e.SpI(" ","Ascending"===t.sortDirection?"arrow_upward":"arrow_downward"," ")}}function L(l,m){if(1&l&&(e.j41(0,"span",16),e.EFF(1),e.k0s()),2&l){const t=e.XpG(3);e.R7$(),e.SpI(" ","Ascending"===t.sortDirection?"arrow_upward":"arrow_downward"," ")}}function $(l,m){if(1&l&&(e.j41(0,"div",24),e.nrm(1,"ngx-avatars",27),e.j41(2,"span",28),e.EFF(3),e.k0s()()),2&l){const t=e.XpG().$implicit,n=e.XpG(3);e.R7$(),e.FCK("matTooltip","",null==t.inforUser?null:t.inforUser.firstName," ",null==t.inforUser?null:t.inforUser.lastName," "),e.FS9("bgColor",n._storeService.getBgColor(null==t.inforUser?null:t.inforUser.firstName.slice(0,1))),e.Y8G("size",30)("name",(null==t.inforUser?null:t.inforUser.firstName.charAt(0))+" "+(null!=t.inforUser&&t.inforUser.lastName?null==t.inforUser?null:t.inforUser.lastName.charAt(0):"")),e.R7$(2),e.Lme(" ",null==t.inforUser?null:t.inforUser.firstName," ",null==t.inforUser?null:t.inforUser.lastName,"")}}function W(l,m){if(1&l&&(e.j41(0,"div",24),e.nrm(1,"ngx-avatars",27),e.j41(2,"span",28),e.EFF(3),e.k0s()()),2&l){const t=e.XpG().$implicit,n=e.XpG(3);e.R7$(),e.FS9("matTooltip",null==t.inforUser?null:t.inforUser.email),e.FS9("bgColor",n._storeService.getBgColor(null==t.inforUser?null:t.inforUser.email.slice(0,1))),e.Y8G("size",30)("name",null==t.inforUser?null:t.inforUser.email.slice(0,1)),e.R7$(2),e.SpI(" ",null==t.inforUser?null:t.inforUser.email,"")}}function K(l,m){if(1&l){const t=e.RV6();e.j41(0,"div",9)(1,"div",10)(2,"div",11)(3,"app-inno-form-checkbox",12),e.bIt("onChange",function(){const o=e.eBV(t).$index,_=e.XpG(3);return e.Njj(_.handleToggleCheckedIndex(o))}),e.k0s()(),e.j41(4,"p",21),e.EFF(5),e.k0s()(),e.j41(6,"p",23),e.EFF(7),e.k0s(),e.j41(8,"p",23),e.EFF(9),e.k0s(),e.j41(10,"p",23),e.EFF(11),e.k0s(),e.j41(12,"p",23),e.DNE(13,$,4,8,"div",24)(14,W,4,5,"div",24),e.k0s(),e.j41(15,"p",25),e.EFF(16),e.k0s(),e.j41(17,"p",25),e.EFF(18),e.nI1(19,"decimal"),e.nI1(20,"formatNumber"),e.k0s(),e.j41(21,"p",26),e.EFF(22),e.nI1(23,"date"),e.k0s()()}if(2&l){let t,n,o,_,g;const c=m.$implicit,k=m.$index,s=e.XpG(3);e.R7$(3),e.Y8G("checked",s.isCheckedIndex(k)),e.R7$(2),e.SpI(" ",null!==(t=c.description)&&void 0!==t?t:""," "),e.R7$(2),e.SpI(" ",null!==(n=null==c.metadata||null==c.metadata.timeTracking||null==c.metadata.timeTracking.project?null:c.metadata.timeTracking.project.projectName)&&void 0!==n?n:"-"," "),e.R7$(2),e.SpI(" ",null!==(o=null==c.metadata||null==c.metadata.timeTracking||null==c.metadata.timeTracking.project?null:c.metadata.timeTracking.project.hourlyRate)&&void 0!==o?o:"-"," "),e.R7$(2),e.SpI(" ",null!==(_=null==c.service?null:c.service.serviceName)&&void 0!==_?_:"-"," "),e.R7$(2),e.vxM(null!=c.inforUser&&c.inforUser.firstName?13:14),e.R7$(3),e.SpI(" ",null!==(g=null==c.metadata?null:c.metadata.hours)&&void 0!==g?g:""," "),e.R7$(2),e.SpI(" $",e.bMT(20,12,e.i5U(19,9,s.calculateTotalInvoiceItem(null==c.metadata?null:c.metadata.hours,null==c.metadata||null==c.metadata.timeTracking||null==c.metadata.timeTracking.project?null:c.metadata.timeTracking.project.hourlyRate),2))," "),e.R7$(4),e.SpI(" ",e.i5U(23,14,c.date,s._storeService.getdateFormat())," ")}}function X(l,m){if(1&l){const t=e.RV6();e.j41(0,"div",8)(1,"div",9)(2,"div",10)(3,"div",11)(4,"app-inno-form-checkbox",12),e.bIt("onChange",function(o){e.eBV(t);const _=e.XpG(2);return e.Njj(_.handleCheckedAll(o))}),e.k0s()(),e.j41(5,"p",13),e.EFF(6," Description "),e.k0s()(),e.j41(7,"p",14),e.EFF(8," Project "),e.k0s(),e.j41(9,"p",14),e.EFF(10," Hourly "),e.k0s(),e.j41(11,"p",14),e.EFF(12," Service "),e.k0s(),e.j41(13,"p",14),e.EFF(14," User "),e.k0s(),e.j41(15,"p",15),e.bIt("click",function(){e.eBV(t);const o=e.XpG(2);return e.Njj(o.sortDates("endTime"))}),e.EFF(16," Hours "),e.DNE(17,G,2,1,"span",16),e.k0s(),e.j41(18,"p",17),e.EFF(19," Line Total "),e.k0s(),e.j41(20,"p",18),e.bIt("click",function(){e.eBV(t);const o=e.XpG(2);return e.Njj(o.sortDates("date"))}),e.EFF(21," Date "),e.DNE(22,L,2,1,"span",16),e.k0s()(),e.Z7z(23,K,24,17,"div",9,e.fX1),e.k0s(),e.j41(25,"ejs-pager",19),e.bIt("click",function(o){e.eBV(t);const _=e.XpG(2);return e.Njj(_.onPageChange(o))}),e.k0s(),e.j41(26,"div",20)(27,"p",21),e.EFF(28),e.nI1(29,"async"),e.k0s(),e.j41(30,"p",22),e.EFF(31),e.nI1(32,"decimal"),e.nI1(33,"formatNumber"),e.k0s()()}if(2&l){const t=e.XpG(2);e.R7$(4),e.Y8G("checked",t.listIndexInvoiceSelected.length===t.listInvoiceItem.length),e.R7$(11),e.Y8G("ngClass",e.eq3(18,F,"endTime"===t.sortColumn)),e.R7$(2),e.vxM("endTime"==t.sortColumn?17:-1),e.R7$(3),e.Y8G("ngClass",e.eq3(20,F,"date"===t.sortColumn)),e.R7$(2),e.vxM("date"==t.sortColumn?22:-1),e.R7$(),e.Dyx(t.listInvoiceItem),e.R7$(2),e.Y8G("pageSize",t.pageSizesDefault)("totalRecordsCount",t.totalPages)("currentPage",t.currentPage)("pageSizes",t.pageSizes),e.R7$(3),e.SpI(" Amount Due (",e.bMT(29,11,t._storeService.curencyCompany),") "),e.R7$(3),e.SpI(" $",e.bMT(33,16,e.i5U(32,13,t.totalAmount(),2))," ")}}function z(l,m){if(1&l&&e.DNE(0,N,2,0,"div",3)(1,X,34,22),2&l){const t=e.XpG();e.vxM(0==t.listInvoiceItem.length?0:1)}}b.is5.Inject(b.Rav);let V=(()=>{var l;class m{static getComponent(){return m}constructor(n,o){(0,a.A)(this,"dialogRef",void 0),(0,a.A)(this,"data",void 0),(0,a.A)(this,"sort",void 0),(0,a.A)(this,"today",new Date),(0,a.A)(this,"totalPages",1),(0,a.A)(this,"currentPage",1),(0,a.A)(this,"pageSizes",[10,20,50,100]),(0,a.A)(this,"pageSizesDefault",20),(0,a.A)(this,"title",""),(0,a.A)(this,"clientName",""),(0,a.A)(this,"projectName",""),(0,a.A)(this,"listInvoiceItem",[]),(0,a.A)(this,"listIndexInvoiceSelected",[]),(0,a.A)(this,"isFetchingProject",!1),(0,a.A)(this,"calculateTotalInvoiceItem",S.jQ),(0,a.A)(this,"sortDirection","Ascending"),(0,a.A)(this,"sortColumn",""),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"timetrackingService",(0,e.WQX)(R.y)),(0,a.A)(this,"_storeService",(0,e.WQX)(v.n)),(0,a.A)(this,"translate",(0,e.WQX)(O.c$)),this.dialogRef=n,this.data=o,this.clientName=o?.client?.clientName??"",this.projectName=o?.project?.projectName??"",this.title=`Select time tracking by ${this.clientName}${""!=this.projectName?" / "+this.projectName:""}`}handleClose(){this.dialogRef.close()}handleSort(){this.currentPage=1,this.GetAllTimTrackingUnBillTime({page:this.currentPage,textSearch:"",isGetAll:!1})}sortDates(n){this.sortColumn===n?(this.sortDirection="Ascending"===this.sortDirection?"Descending":"Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection}):(this.sortColumn=n,this.sortDirection="Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection}),this.handleSort()}GetAllTimTrackingUnBillTime(n){const o=this.data?.client?.id,_=this.data?.project;if(!o)return;const g={Page:n?.isGetAll?0:n?.page??1,Search:n?.textSearch??"",PageSize:this.pageSizesDefault,clientId:o,loggedBy:"all",columnName:this?.sort?.columnName,direction:this?.sort?.direction,projectId:_?.id,isUnBill:!0};this.isFetchingProject=!0,this.timetrackingService.GetAllTimeTracking(g).pipe((0,U.pQ)(this.destroyRef)).subscribe(c=>{if(c){this.totalPages=c.totalRecords;const k=this.data?.listIdTimeTrackingSelected??[];c=c?.data?c?.data?.filter(s=>!k.includes(s.id))??[]:c?.filter(s=>!k.includes(s.id))??[],this.listInvoiceItem=c.map(s=>{const D=(0,p.FA)(s?.endTime??"00:00:00");return{trackingId:s?.id??"",date:s?.date??this.today,description:s?.description??"",rate:s?.project?.hourlyRate,qty:D,taxes:[],total:(0,S.jQ)(s?.endTime,s?.project?.hourlyRate),metadata:{hours:s?.endTime??"00:00:00",timeTracking:s},dateSelectItem:s.date,projectName:s?.project.projectName,serviceName:s?.service?.serviceName,projectId:s?.project?.id,serviceId:s?.service?.id,inforUser:s?.user,service:s?.service}}),this.isFetchingProject=!1,n.isGetAll&&(this.pageSizesDefault=this.totalPages,this.listIndexInvoiceSelected=this.listInvoiceItem.map((s,D)=>D))}})}onPageChange(n){n?.newProp?.pageSize&&(this.pageSizesDefault=n.newProp.pageSize,this.GetAllTimTrackingUnBillTime({page:this.currentPage,textSearch:"",isGetAll:!1})),n?.currentPage&&(this.currentPage=n.currentPage,this.GetAllTimTrackingUnBillTime({page:this.currentPage,textSearch:"",isGetAll:!1}))}ngOnInit(){this.sortDirection="Ascending",this.sortColumn="date",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort()}handleCheckedAll(n){n?this.GetAllTimTrackingUnBillTime({page:this.currentPage,textSearch:"",isGetAll:!0}):this.listIndexInvoiceSelected=[]}isCheckedIndex(n){return this.listIndexInvoiceSelected.includes(n)}handleToggleCheckedIndex(n){const o=this.isCheckedIndex(n);let _=[...this.listIndexInvoiceSelected];o?_=_.filter(g=>g!==n):_.push(n),this.listIndexInvoiceSelected=_}totalAmount(){const n=(0,S.yo)(this.listInvoiceItem);return n.subtotal+n.grandTotalTax}handleCancel(){this.dialogRef.close()}handleSubmit(){const n=this.listIndexInvoiceSelected.map(o=>this.listInvoiceItem[o]);this.dialogRef.close(n.map(({service:o,..._})=>_))}}return l=m,(0,a.A)(m,"\u0275fac",function(n){return new(n||l)(e.rXU(T.CP),e.rXU(T.Vh))}),(0,a.A)(m,"\u0275cmp",e.VBU({type:l,selectors:[["app-select-time-tracking"]],standalone:!0,features:[e.aNF],decls:8,vars:3,consts:[[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","relative"],[1,"w-full"],[1,"w-full","py-2","flex","justify-center","items-center"],["footer",""],[3,"onCancel","onSubmit","isDisableSubmit"],["title","No result"],[1,"overflow-auto","w-full"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center","cursor-pointer",3,"click","ngClass"],[1,"material-icons","pl-1","!text-[15px]"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right","cursor-pointer",3,"click","ngClass"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","pt-[15px]","pb-[3px]","flex-wrap","justify-end","items-center","gap-[16px]"],[1,"text-text-primary","text-text-sm-regular"],[1,"text-headline-sm-bold","text-text-primary"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],[1,"flex","items-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-right"],[3,"matTooltip","size","bgColor","name"],[1,"pl-1"]],template:function(n,o){1&n&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return o.handleClose()}),e.j41(1,"div",1)(2,"div",2)(3,"div",3),e.DNE(4,y,2,0,"div",4)(5,z,2,1),e.k0s()()(),e.j41(6,"div",5)(7,"app-inno-modal-footer",6),e.bIt("onCancel",function(){return o.handleCancel()})("onSubmit",function(){return o.handleSubmit()}),e.k0s()()()),2&n&&(e.FS9("title",o.title),e.R7$(4),e.vxM(o.isFetchingProject?4:5),e.R7$(3),e.Y8G("isDisableSubmit",!o.listIndexInvoiceSelected.length))},dependencies:[f.oV,A.mC,A.fw,d.G,P.YU,P.Jj,P.vh,M.iov,M.BzB,C.p,i.Q,u.I,j.k,E.V,h.f,I.J],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(200px,1fr) 130px 100px 120px 120px 100px 100px 120px 100px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),m})()}}]);