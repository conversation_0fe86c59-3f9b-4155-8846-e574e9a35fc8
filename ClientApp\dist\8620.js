"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8620],{6196:(D,h,e)=>{e.d(h,{A:()=>o});var l=e(9842),m=e(6146),c=e(4438),p=e(177),u=e(5236);const d=(s,n,a,r,t,i)=>({"text-text-warning bg-bg-warning-primary":s,"text-text-primary bg-bg-tertiary":n,"text-text-primary bg-bg-disabled":a,"text-text-success bg-bg-success-secondary":r,"text-text-success bg-bg-secondary-subtle":t,"text-text-danger bg-bg-secondary":i});let o=(()=>{var s;class n{constructor(){(0,l.A)(this,"STATUS",{DRAFT:0,UN_BILLED:1,NON_BILLABLE:2,PAID:3,BILLED:4,SENT:5}),(0,l.A)(this,"status",void 0)}getStatusText(){switch(this.status){case this.STATUS.DRAFT:return"STATUS.Draft";case this.STATUS.UN_BILLED:return"STATUS.Unbilled";case this.STATUS.NON_BILLABLE:return"STATUS.NonBillable";case this.STATUS.PAID:return"STATUS.Paid";case this.STATUS.BILLED:return"STATUS.Billed";case this.STATUS.SENT:return"STATUS.Sent";default:return"Unknown"}}}return s=n,(0,l.A)(n,"\u0275fac",function(r){return new(r||s)}),(0,l.A)(n,"\u0275cmp",c.VBU({type:s,selectors:[["app-inno-status"]],inputs:{status:"status"},standalone:!0,features:[c.aNF],decls:3,vars:11,consts:[[1,"text-text-sm-semibold","px-[8px]","py-[2px]","rounded-md","text-center",3,"ngClass"]],template:function(r,t){1&r&&(c.j41(0,"span",0),c.EFF(1),c.nI1(2,"translate"),c.k0s()),2&r&&(c.Y8G("ngClass",c.l4e(4,d,t.status===t.STATUS.UN_BILLED,t.status===t.STATUS.NON_BILLABLE,t.status===t.STATUS.DRAFT,t.status===t.STATUS.PAID,t.status===t.STATUS.BILLED,t.status===t.STATUS.SENT)),c.R7$(),c.SpI(" ",c.bMT(2,2,t.getStatusText()),"\n"))},dependencies:[m.G,p.YU,u.D9]})),n})()},1556:(D,h,e)=>{e.d(h,{Z:()=>s});var l=e(9842),m=e(4438),c=e(467),p=e(2716),u=e(7987);let d=(()=>{var n;class a extends p.H{open(t){var i=this;return(0,c.A)(function*(){const v=yield e.e(3190).then(e.bind(e,3190));return i.matDialog.open(v.AlertConfirmComponent.getComponent(),{data:t,width:"440px",panelClass:"custom_dialog",scrollStrategy:new u.t0,disableClose:!0})})()}}return n=a,(0,l.A)(a,"\u0275fac",(()=>{let r;return function(i){return(r||(r=m.xGo(n)))(i||n)}})()),(0,l.A)(a,"\u0275prov",m.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})),a})(),s=(()=>{var n;class a{constructor(t){(0,l.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=t}alertDelete(t){const{title:i,description:v,textSubmit:C="COMMON.Delete",textCancel:I}=t;return new Promise(T=>{this.alertConfirmDialog.open({title:i,description:v,textSubmit:C,textCancel:I,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(A=>{A.afterClosed().subscribe(E=>{T(E??!1)})})})}alertConfirm(t){const{title:i,description:v,textSubmit:C,textCancel:I}=t;return new Promise(T=>{this.alertConfirmDialog.open({title:i,description:v,textSubmit:C,textCancel:I}).then(A=>{A.afterClosed().subscribe(E=>{T(E??!1)})})})}}return n=a,(0,l.A)(a,"\u0275fac",function(t){return new(t||n)(m.KVO(d))}),(0,l.A)(a,"\u0275prov",m.jDH({token:n,factory:n.\u0275fac})),a})()},4433:(D,h,e)=>{e.d(h,{Q:()=>c});var l=e(9842),m=e(4438);let c=(()=>{var p;class u{transform(o,s=2){const n=Math.pow(10,s);return(Math.trunc(Number((o*n).toFixed(s+5)))/n).toFixed(s)}}return p=u,(0,l.A)(u,"\u0275fac",function(o){return new(o||p)}),(0,l.A)(u,"\u0275pipe",m.EJ8({name:"decimal",type:p,pure:!0,standalone:!0})),u})()},6617:(D,h,e)=>{e.d(h,{p:()=>p});var l=e(9842),m=e(6473),c=e(4438);let p=(()=>{var u;class d{transform(s){return(0,m.ZV)(s)}}return u=d,(0,l.A)(d,"\u0275fac",function(s){return new(s||u)}),(0,l.A)(d,"\u0275pipe",c.EJ8({name:"formatNumber",type:u,pure:!0,standalone:!0})),d})()},335:(D,h,e)=>{e.d(h,{L:()=>c});var l=e(9842),m=e(4438);let c=(()=>{var p;class u{transform(o){if(!o)return"";let s=o.toString().replace(/\D/g,"");return 10===s.length?`(${s.slice(0,3)}) ${s.slice(3,6)}-${s.slice(6)}`:o.toString()}}return p=u,(0,l.A)(u,"\u0275fac",function(o){return new(o||p)}),(0,l.A)(u,"\u0275pipe",m.EJ8({name:"phoneMask",type:p,pure:!0,standalone:!0})),u})()},402:(D,h,e)=>{e.d(h,{x:()=>d});var l=e(467),m=e(9842),c=e(2716),p=e(7987),u=e(4438);let d=(()=>{var o;class s extends c.H{open(a){var r=this;return(0,l.A)(function*(){const t=yield Promise.all([e.e(6437),e.e(9775),e.e(1328),e.e(4823),e.e(3924),e.e(6217),e.e(9717),e.e(1875),e.e(5354),e.e(9114),e.e(5834)]).then(e.bind(e,5834));return r.matDialog.open(t.DuplicateInvoiceComponent.getComponent(),{data:a,width:"80vw",maxWidth:"100%",maxHeight:"100%",panelClass:"custom_dialog",scrollStrategy:new p.t0})})()}}return o=s,(0,m.A)(s,"\u0275fac",(()=>{let n;return function(r){return(n||(n=u.xGo(o)))(r||o)}})()),(0,m.A)(s,"\u0275prov",u.jDH({token:o,factory:o.\u0275fac,providedIn:"root"})),s})()},5644:(D,h,e)=>{e.d(h,{p:()=>s});var l=e(9842),m=e(1342),c=e(5312),p=e(1626),u=e(4438),d=e(6473);const o=c.c.HOST_API+"/api";let s=(()=>{var n;class a{constructor(){(0,l.A)(this,"http",(0,u.WQX)(p.Qq)),(0,l.A)(this,"spinnerService",(0,u.WQX)(m.D))}CreatedInvoice(t){return this.http.post(o+"/Invoices/CreatedInvoice",t)}CreatedInvoiceSend(t){return this.http.post(o+"/Invoices/CreatedInvoiceSend",t)}CountInvoiceByCompany(){return this.http.get(o+"/Invoices/CountInvoiceByCompany")}CountInvoiceByContractor(){return this.http.get(o+"/Invoices/CountInvoiceByContractor")}CountEstimate(){return this.http.get(o+"/Invoices/CountEstimate")}SendMailInvoice(t){return this.http.post(o+"/Invoices/SendMailInvoice",t)}GetAllEstimate(t){const i=(0,d.yU)(t);return this.http.get(o+"/Invoices/GetAllEstimate",{params:i})}GetAllInvoice(t){const i=(0,d.yU)(t);return this.http.get(o+"/Invoices/GetAllInvoice",{params:i})}GetAllInvoiceSendToMe(t){const i=(0,d.yU)(t);return this.http.get(o+"/Invoices/GetAllInvoiceSendToMe",{params:i})}GetAllEstimateSendToMe(t){return this.http.post(o+"/Invoices/GetAllEstimateSendToMe",t)}DeleteInvoice(t,i){return this.http.post(o+`/Invoices/DeleteInvoice?isActive=${i}`,t)}ChangePosition(t){return this.http.post(o+"/Invoices/ChangePosition?",t)}GetRevenueChart(t){return this.http.get(o+`/Invoices/GetRevenueChart?year=${t}`)}GraphicsChart(t){return this.http.get(o+`/Invoices/GraphicsChart?year=${t}`)}MarkAsPaid(t){return this.http.post(o+`/Invoices/MarkAsPaid?Id=${t}`,null)}MarkAsSent(t){return this.http.post(o+`/Invoices/MarkAsSent?Id=${t}`,null)}UpdateInvoice(t){return t?.itemInvoices.forEach(i=>{i.user=null}),this.http.post(o+"/Invoices/UpdateInvoice",t)}UpdateArchive(t,i){return this.http.post(o+`/Invoices/UpdateArchive?invoiceId=${t}&isArchive=${i}`,{})}ConvertToInvoice(t){return this.http.post(o+`/Invoices/ConvertToInvoice?invoiceId=${t}`,{})}GetInvoiceById(t){return this.http.get(o+`/Invoices/GetInvoiceById?InvoiceId=${t}`)}PrintInvoiceById(t,i){this.http.get(`${o}/Invoices/PrintInvoice?InvoiceId=${t}`,{responseType:"blob"}).subscribe({next:v=>{const C=new Blob([v],{type:"application/pdf"}),I=window.URL.createObjectURL(C),T=document.createElement("a");T.href=I,T.download=`Invoice_${i}.pdf`,document.body.appendChild(T),T.click(),this.spinnerService.hide(),window.URL.revokeObjectURL(I)},error:v=>{console.error("Error downloading the invoice:",v)}})}GetInvoiceByIdLink(t){return this.http.get(o+`/Invoices/GetInvoiceByIdLink?InvoiceId=${t}`)}CalculationInvoice(t){return this.http.get(o+`/Invoices/CalculationInvoice?status=${t}`)}CalculationInvoiceSendToMe(t){return this.http.get(o+`/Invoices/CalculationInvoiceSendToMe?status=${t}`)}CalculationEstimateSendToMe(t){return this.http.get(o+`/Invoices/CalculationEstimateSendToMe?status=${t}`)}CalculationEstimate(t){return this.http.get(o+`/Invoices/CalculationEstimate?status=${t}`)}uploadFile(t){const i=new FormData;return i.append("formFile",t),this.http.post(o+"/Invoices/UploadFile",i)}}return n=a,(0,l.A)(a,"\u0275fac",function(t){return new(t||n)}),(0,l.A)(a,"\u0275prov",u.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})),a})()},5909:(D,h,e)=>{e.d(h,{R2:()=>c,Xj:()=>m,az:()=>o,jQ:()=>l,yo:()=>d});const l=(s,n)=>{if(!s||!n)return 0;const a=s.split(":").map(Number);let r=0,t=0,i=0;return 3===a.length?[r,t,i]=a:2===a.length?[r,t]=a:1===a.length&&([r]=a),Number(((r+t/60+i/3600)*n).toFixed(2))},m=(s=[],n=!1)=>(s.some(r=>r.companyTax||r.selected||void 0===r.selected)?s:s.filter(r=>r.selected)).map(r=>{let t,i;return r.companyTax?(t=r.companyTax.name,i=r.companyTax.amount):(t=r.name,i=r.amount),n?`${t} (${i}%)`:t}).filter(Boolean).sort((r,t)=>r.localeCompare(t)).join(", "),c=(s,n)=>s&&n?Number((s*n).toFixed(2)):0,d=s=>{let n=0,a={};s.forEach(({rate:t,qty:i,taxes:v})=>{if(!t||!i)return;const C=Number((t*i).toFixed(2));n+=C,v&&0!==v.length&&(v.some(_=>_.companyTaxId)?v.filter(_=>_.companyTaxId):v.filter(_=>_.selected)).forEach(_=>{const A=_.name||"Unknown Tax",E=_.taxeNumber||"",f=Number(_?.companyTax?.amount??_.amount??0);a[A]||(a[A]={name:A,numberTax:E,amount:f,taxableAmount:0,total:0}),a[A].taxableAmount+=C})});let r=0;return Object.values(a).forEach(t=>{t.total=Number((t.taxableAmount*(t.amount/100)).toFixed(2)),r+=t.total}),{subtotal:Number(n.toFixed(2)),totalTaxes:a,grandTotalTax:Number(r.toFixed(2))}},o=s=>{let n=0,a={};s.forEach(({total:t,taxes:i})=>{t&&(n+=t,i&&0!==i.length)&&(i.some(I=>I.companyTaxId)?i.filter(I=>I.companyTaxId):i.filter(I=>I.selected)).forEach(I=>{const T=I.name||"Unknown Tax",_=I.taxeNumber||"",A=Number(I?.companyTax?.amount??I.amount??0);a[T]||(a[T]={name:T,numberTax:_,amount:A,taxableAmount:0,total:0}),a[T].taxableAmount+=t})});let r=0;return Object.values(a).forEach(t=>{t.total=Number((t.taxableAmount*(t.amount/100)).toFixed(2)),r+=t.total}),{subtotal:Number(n.toFixed(2)),totalTaxes:a,grandTotalTax:Number(r.toFixed(2))}}}}]);