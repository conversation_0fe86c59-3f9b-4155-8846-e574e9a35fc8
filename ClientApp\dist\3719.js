"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3719],{3719:(R,s,r)=>{r.d(s,{RG:()=>w,TL:()=>M,xb:()=>C});var a=r(4438),p=r(177),u=(r(9969),r(2318)),f=r(6600);let F=0;const y=new a.nKC("MatError");let M=(()=>{var e;class l{constructor(i,o){this.id="mat-mdc-error-"+F++,i||o.nativeElement.setAttribute("aria-live","polite")}}return(e=l).\u0275fac=function(i){return new(i||e)(a.kS0("aria-live"),a.rXU(a.aKT))},e.\u0275dir=a.FsC({type:e,selectors:[["mat-error"],["","matError",""]],hostAttrs:["aria-atomic","true",1,"mat-mdc-form-field-error","mat-mdc-form-field-bottom-align"],hostVars:1,hostBindings:function(i,o){2&i&&a.Mr5("id",o.id)},inputs:{id:"id"},standalone:!0,features:[a.Jv_([{provide:y,useExisting:e}])]}),l})();const C=new a.nKC("MatFormField");let w=(()=>{var e;class l{}return(e=l).\u0275fac=function(i){return new(i||e)},e.\u0275mod=a.$C({type:e}),e.\u0275inj=a.G2t({imports:[f.yE,p.MD,u.w5,f.yE]}),l})()}}]);