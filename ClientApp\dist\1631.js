"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1631],{1631:(d,s,e)=>{e.r(s),e.d(s,{DoneInviteComponent:()=>c});var a=e(9842),r=e(1470),n=e(4438),p=e(4006);let c=(()=>{var i;class o{static getComponent(){return o}constructor(t){(0,a.A)(this,"dialogRef",void 0),this.dialogRef=t}closeDialog(){this.dialogRef.close()}Done(){this.dialogRef.close("done")}}return i=o,(0,a.A)(o,"\u0275fac",function(t){return new(t||i)(n.rXU(p.CP))}),(0,a.A)(o,"\u0275cmp",n.VBU({type:i,selectors:[["app-done-invite"]],standalone:!0,features:[n.aNF],decls:10,vars:0,consts:[[3,"onClose"],[1,"p-4"],[1,"flex","flex-col","justify-center","items-center"],["src","../../../../assets//img/invite.png",2,"width","220px"],[1,"text-lg","pb-6","font-bold","text-center"],[1,"pb-6","text-center"],[1,"bg-green-500","hover:bg-blue-700","text-white","font-bold","py-2","px-4","rounded",3,"click"]],template:function(t,l){1&t&&(n.j41(0,"app-innobook-modal-wrapper",0),n.bIt("onClose",function(){return l.closeDialog()}),n.j41(1,"div",1)(2,"div",2),n.nrm(3,"img",3),n.j41(4,"span",4),n.EFF(5,"Accountant Invite Sent, Working With Your Accountant Just Go Easier"),n.k0s(),n.j41(6,"span",5),n.EFF(7," Contact your accountant in a few day to make sure they've signed in, If they need a hand with their account, have them Contact InnoBooks Support : ********* "),n.k0s(),n.j41(8,"button",6),n.bIt("click",function(){return l.Done()}),n.EFF(9," Done "),n.k0s()()()())},dependencies:[r.j]})),o})()}}]);