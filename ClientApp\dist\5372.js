"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5372],{6586:(M,h,s)=>{s.d(h,{A:()=>n,a:()=>c});var n=function(l){return l.Created_Tab="Created_Tab",l.<PERSON>_To_Me_Tab="Sent_To_Me_Tab",l}(n||{}),c=function(l){return l[l.all=1]="all",l[l.this_month=2]="this_month",l[l.last_month=3]="last_month",l[l.custom=4]="custom",l}(c||{})},1588:(M,h,s)=>{s.d(h,{j:()=>n});var n=function(c){return c.Day="Day",c.Week="Week",c.Month="Month",c.All="All",c}(n||{})},5936:(M,h,s)=>{s.d(h,{H:()=>b});var n=s(9842),c=s(1626),l=s(4438);const p=s(5312).c.HOST_API+"/api";let b=(()=>{var T;class u{constructor(){(0,n.A)(this,"http",(0,l.WQX)(c.Qq))}GetFile(I){return this.http.get(p+`/Images/GetFile?nameFile=${I}`,{responseType:"blob"})}GetFileURL(I){return this.http.get(p+`/Images/GetFileURL?nameFile=${I}`,{responseType:"blob"})}}return T=u,(0,n.A)(u,"\u0275fac",function(I){return new(I||T)}),(0,n.A)(u,"\u0275prov",l.jDH({token:T,factory:T.\u0275fac,providedIn:"root"})),u})()},5277:(M,h,s)=>{s.d(h,{u:()=>T});var n=s(9842),c=s(4438),l=s(6586),f=s(1588),p=s(4412),b=s(1413);let T=(()=>{var u;class v{constructor(){(0,n.A)(this,"behaviorTimeTrackingTypeView",new p.t(f.j.Day)),(0,n.A)(this,"behaviorTimeTrackingDate",new p.t(void 0)),(0,n.A)(this,"behaviorTimeTrackingFilter",new p.t({typeView:f.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,n.A)(this,"behaviorTimeTrackingCreateTimer",new p.t(void 0)),(0,n.A)(this,"behaviorisInternalClient",new p.t(!1)),(0,n.A)(this,"behaviorTimeTrackingShowingTimer",new p.t(!1)),(0,n.A)(this,"reloadItem",new b.B),(0,n.A)(this,"reloadService",new b.B),(0,n.A)(this,"isEstimate",(0,c.vPA)(!1)),(0,n.A)(this,"behaviorInvoiceFilter",new p.t({typeView:l.A.Created_Tab,textSearch:""}))}SetisInternalClient(m){this.behaviorisInternalClient.next(m)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(m){this.behaviorTimeTrackingTypeView.next(m)}SetNewTimeTrackingDate(m){this.behaviorTimeTrackingDate.next(m)}SetNewTimeTrackingShowingTimer(m){this.behaviorTimeTrackingShowingTimer.next(m)}SetNewTimeTrackingCreateTimerInfo(m){this.behaviorTimeTrackingCreateTimer.next(m)}SetNewTimeTrackingFilter(m){this.behaviorTimeTrackingFilter.next(m)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(m){localStorage.setItem("ResumeData",JSON.stringify(m))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(m){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...m})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return u=v,(0,n.A)(v,"\u0275fac",function(m){return new(m||u)}),(0,n.A)(v,"\u0275prov",c.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})),v})()},5372:(M,h,s)=>{s.r(h),s.d(h,{EstimateDetailsComponent:()=>Z});var n=s(9842),c=s(5936),l=s(5277),f=s(3570),p=s(2928),b=s(335),T=s(402),u=s(4433),v=s(1556),I=s(6146),m=s(5644),S=s(1110),e=s(4438),E=s(9079),A=s(33),O=s(3719),P=s(9115),j=s(3492),R=s(2840),y=s(1448),k=s(5236),B=s(1970),L=s(6196),U=s(5599),$=s(6473),W=s(6617),D=s(1875),x=s(5909),C=s(177),N=s(1342),K=s(7977),w=s(584);function G(r,_){if(1&r){const o=e.RV6();e.j41(0,"button",46),e.bIt("click",function(){const i=e.eBV(o).$implicit,a=e.XpG(2);return e.Njj(a.handleAction(i.action))}),e.nrm(1,"img",47),e.EFF(2),e.nI1(3,"translate"),e.k0s()}if(2&r){const o=_.$implicit;e.R7$(),e.FS9("src",o.icon,e.B4B),e.R7$(),e.SpI(" ",e.bMT(3,2,o.label)," ")}}function X(r,_){if(1&r&&e.Z7z(0,G,4,4,"button",45,e.fX1),2&r){const o=e.XpG();e.Dyx(o.filteredMenu)}}function Q(r,_){1&r&&(e.j41(0,"p",24),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&r&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"COMMON.Loading")," "))}function V(r,_){if(1&r&&(e.j41(0,"div",3)(1,"p",48),e.EFF(2),e.nI1(3,"phoneMask"),e.k0s()()),2&r){const o=e.XpG();e.R7$(2),e.SpI(" ",e.bMT(3,1,o.businessInfo.businessPhoneNumber)," ")}}function z(r,_){if(1&r&&(e.j41(0,"p",26),e.EFF(1),e.k0s()),2&r){const o=e.XpG();e.R7$(),e.SpI(" ",o.businessInfo.businessAddress," ")}}function H(r,_){if(1&r&&(e.j41(0,"div",37)(1,"div",49)(2,"div",50)(3,"span",51),e.EFF(4," drag_indicator "),e.k0s(),e.j41(5,"p",52),e.EFF(6),e.k0s()()(),e.j41(7,"p",53),e.EFF(8),e.nI1(9,"formatNumber"),e.k0s(),e.j41(10,"div",54)(11,"p"),e.EFF(12),e.nI1(13,"decimal"),e.nI1(14,"formatNumber"),e.k0s(),e.j41(15,"p"),e.EFF(16),e.k0s()(),e.j41(17,"p",55),e.EFF(18),e.nI1(19,"decimal"),e.nI1(20,"formatNumber"),e.k0s()()),2&r){let o;const t=_.$implicit,i=e.XpG();e.R7$(6),e.SpI(" ",null!==(o=null==t?null:t.description)&&void 0!==o?o:""," "),e.R7$(2),e.SpI(" $",e.bMT(9,5,t.rate)," "),e.R7$(4),e.SpI(" ",e.bMT(14,10,e.i5U(13,7,t.qty,2))," "),e.R7$(4),e.SpI(" ",i.getNameTaxes(null==t?null:t.taxes,!0),""),e.R7$(2),e.SpI(" $",e.bMT(20,15,e.i5U(19,12,i.calculateTotal(t),2))," ")}}function Y(r,_){if(1&r&&(e.j41(0,"div",42)(1,"div",56)(2,"p",57),e.EFF(3),e.k0s(),e.j41(4,"p",57),e.EFF(5),e.k0s()(),e.j41(6,"p",41),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&r){const o=_.$implicit;e.R7$(3),e.Lme(" ",o.name," (",o.amount,"%) "),e.R7$(2),e.SpI(" #",o.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,o.total,2))," ")}}R.is5.Inject(R.Rav);let Z=(()=>{var r;class _{constructor(t,i,a,d){(0,n.A)(this,"location",void 0),(0,n.A)(this,"spinnerService",void 0),(0,n.A)(this,"addEstimateDialog",void 0),(0,n.A)(this,"shareLinkDialog",void 0),(0,n.A)(this,"imageUrl",void 0),(0,n.A)(this,"estimateInfo",void 0),(0,n.A)(this,"InforBussiness",void 0),(0,n.A)(this,"clientId",void 0),(0,n.A)(this,"reference",void 0),(0,n.A)(this,"note",void 0),(0,n.A)(this,"nameTax",""),(0,n.A)(this,"sumtax",0),(0,n.A)(this,"subtotal",0),(0,n.A)(this,"qty",void 0),(0,n.A)(this,"total",void 0),(0,n.A)(this,"rate",void 0),(0,n.A)(this,"selectedDate",void 0),(0,n.A)(this,"selectedDueDate",void 0),(0,n.A)(this,"clientName",void 0),(0,n.A)(this,"_id",void 0),(0,n.A)(this,"taxArray",[]),(0,n.A)(this,"calculateGroupedTaxes",x.yo),(0,n.A)(this,"getNameTaxes",x.Xj),(0,n.A)(this,"activatedRoute",(0,e.WQX)(A.nX)),(0,n.A)(this,"dataService",(0,e.WQX)(l.u)),(0,n.A)(this,"router",(0,e.WQX)(A.Ix)),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"layoutUtilsService",(0,e.WQX)(v.Z)),(0,n.A)(this,"translate",(0,e.WQX)(k.c$)),(0,n.A)(this,"_toastService",(0,e.WQX)(j.f)),(0,n.A)(this,"_storeService",(0,e.WQX)(S.n)),(0,n.A)(this,"duplicateInvoiceDialog",(0,e.WQX)(T.x)),(0,n.A)(this,"authenticationService",(0,e.WQX)(p.k)),(0,n.A)(this,"_invoiceService",(0,e.WQX)(m.p)),(0,n.A)(this,"cdnService",(0,e.WQX)(c.H)),this.location=t,this.spinnerService=i,this.addEstimateDialog=a,this.shareLinkDialog=d,this.InforBussiness=this._storeService.get_UserBusiness()}get filteredMenu(){return f.l.filter(t=>t.permissions.includes(this.authenticationService.getBusinessRole()))}formatDate(t){return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}handleDuplicate(){this.duplicateInvoiceDialog.open({id:this._id,isInvoice:!1}).then(i=>{i.afterClosed().subscribe(a=>{})})}handleMarkAsPaid(){this._invoiceService.MarkAsPaid(this._id).pipe((0,E.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._toastService.showSuccess("Mark As Paid","Success"),this.GetInvoiceById(this._id))})}handleBack(){this.location.back()}get businessInfo(){return{businessName:this.estimateInfo?.company?.businessName??"",businessPhoneNumber:this.estimateInfo?.company?.phone??"",businessAddress:(0,$.Aw)({addressLine1:this.estimateInfo?.company?.adress??"",addressLine2:this.estimateInfo?.company?.adress2??"",stateProvince:this.estimateInfo?.company?.province??"",postalCode:this.estimateInfo?.company?.postalCode??"",country:this.estimateInfo?.company?.country??""})}}ngOnInit(){this.activatedRoute.params.pipe((0,E.pQ)(this.destroyRef)).subscribe(t=>{t?.id&&(this._id=t?.id,this.GetInvoiceById(t?.id))})}_handleData(t){this.selectedDueDate=this.formatDate(new Date(this.estimateInfo?.dueDate??0)),this.selectedDate=this.formatDate(new Date(this.estimateInfo?.invoiceDate??0)),this.note=this.estimateInfo?.notes,this.rate=this.estimateInfo?.rate??0,this.qty=this.estimateInfo?.timeAmount??0,this.total=this.estimateInfo?.paidAmount??0,this.reference=this.estimateInfo?.reference,this.imageUrl=this.estimateInfo?.company?.companyImage}GetImg(t){this.cdnService.GetFile(t).pipe((0,E.pQ)(this.destroyRef)).subscribe(i=>{if(i){const a=new FileReader;a.onload=()=>{this.imageUrl=a.result},a.readAsDataURL(i)}})}GetInvoiceById(t){this._invoiceService.GetInvoiceById(t).pipe((0,E.pQ)(this.destroyRef)).subscribe(i=>{i&&(this.estimateInfo=i,this.GetImg(this.estimateInfo.img?this.estimateInfo.img:this.estimateInfo?.company?.companyImage),this._handleData(i),this.calculateAllTax())})}calculateTotal(t){let i=0;return i=t.rate*t.qty,this._formatTotal(this.CheckIsNaN(i))}handleDownloadPDF(){this.spinnerService.show(),this._invoiceService.PrintInvoiceById(this._id,this.estimateInfo.invoiceNumber)}_formatTotal(t){return Math.floor(1e3*t)/1e3}calculateAllTax(){this.taxArray=[],this.sumtax=0;const t=(0,x.yo)(this.estimateInfo?.itemInvoices);this.taxArray=Object.values(t.totalTaxes),this.sumtax=t.grandTotalTax,this.subtotal=this.estimateInfo.itemInvoices.reduce((i,a)=>i+(0,x.R2)(a.rate,a.qty),0)}EditEstimate(){this.dataService.isEstimate.set(!0),this.addEstimateDialog.open(this.estimateInfo).then(i=>{i.afterClosed().subscribe(a=>{a&&this.GetInvoiceById(this._id)})})}handleArchive(){this._invoiceService.UpdateArchive(this._id,!0).pipe((0,E.pQ)(this.destroyRef)).subscribe(t=>{t?(this.GetInvoiceById(this._id),this._toastService.showSuccess("Save","Success")):this._toastService.showError("Fail","Fail")})}handleDelete(){this.layoutUtilsService.alertDelete({title:this.translate.instant("Delete Estimate !"),description:this.translate.instant("Do you want to delete?")}).then(t=>{t&&this._invoiceService.DeleteInvoice([this._id],!1).pipe((0,E.pQ)(this.destroyRef)).subscribe(i=>{i?(this.GetInvoiceById(this._id),this._toastService.showSuccess("Delete","Success")):this._toastService.showError("Fail","Fail")})})}ShareLink(){this.shareLinkDialog.open(this._id)}handleConvertToInvoice(){const t=this.translate.instant("Convert To Invoice !"),i=this.translate.instant("Do you want to convert?");this.layoutUtilsService.alertConfirm({title:t,description:i}).then(a=>{a&&this._invoiceService.ConvertToInvoice(this._id).pipe((0,E.pQ)(this.destroyRef)).subscribe(d=>{d?(this.handleBack(),this._toastService.showSuccess("Update","Success")):this._toastService.showError("Fail","Fail")})})}handleFunctionInDevelopment(){this._toastService.showInfo("The feature is in development.")}CheckIsNaN(t){return isNaN(t)?0:t}getFullName(t){return t?.firstName&&t?.lastName?t?.firstName+" "+t?.lastName:t?.email??""}handleMarkAsSent(){this._invoiceService.MarkAsSent(this._id).pipe((0,E.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._toastService.showSuccess("Mark As Sent","Success"),this.GetInvoiceById(this._id))})}handleAction(t){switch(t){case"paid":this.handleMarkAsPaid();break;case"sent":this.handleMarkAsSent();break;case"duplicate":this.handleDuplicate();break;case"download":this.handleDownloadPDF();break;case"print":default:this.handleFunctionInDevelopment();break;case"archive":this.handleArchive();break;case"delete":this.handleDelete()}}drop(t){t.previousContainer===t.container?(0,D.HD)(t.container.data,t.previousIndex,t.currentIndex):(0,D.eg)(t.previousContainer.data,t.container.data,t.previousIndex,t.currentIndex)}}return r=_,(0,n.A)(_,"\u0275fac",function(t){return new(t||r)(e.rXU(C.aZ),e.rXU(N.D),e.rXU(K.w),e.rXU(w.y))}),(0,n.A)(_,"\u0275cmp",e.VBU({type:r,selectors:[["app-estimate-details"]],standalone:!0,features:[e.Jv_([v.Z]),e.aNF],decls:118,vars:85,consts:[["templateSearchProject",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"flex","items-center","gap-[8px]"],[1,"button-icon","button-size-md",3,"click"],["src","../../../../assets/img/icon/ic_arrow_left.svg","alt","Icon"],[1,"text-text-primary","text-headline-lg-bold"],[3,"status"],[1,"flex","items-center","gap-[12px]","flex-wrap"],["position","bottom-end",3,"content"],["target","",1,"button-size-md","button-outline"],["src","../../../../assets/img/icon/ic_three_dot_horizontal.svg","alt","icon"],[1,"button-size-md","button-outline",3,"click"],["src","../../../../assets/img/icon/ic_edit.svg","alt","icon"],["src","../../../../assets/img/icon/ic_link_black.svg","alt","icon"],[1,"button-size-md","button-primary",3,"click"],["src","../../../../assets/img/icon/ic_email_sending.svg","alt","icon"],[1,"container-full","mt-[24px]"],[1,"w-full","w-fit","mx-auto","bg-bg-primary","p-[32px]","relative"],[1,"flex","w-full","gap-[18px]","md:flex-row","flex-col"],[1,"w-[160px]","h-[100px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'","alt","image",1,"rounded-md","w-full","h-full","object-cover",3,"src"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]","font-bold"],[1,"w-full","text-text-xs-regular","text-text-secondary"],[1,"w-full","grid","sm:grid-cols-2","lg:grid-cols-4","gap-[16px]"],[1,"text-text-brand-primary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-secondary","text-text-md-regular"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-secondary","text-text-md-regular","whitespace-pre-line"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],["cdkDropList","",1,"overflow-auto","w-full",3,"cdkDropListDropped","cdkDropListData"],[1,"invoiceTableLayout"],[1,"text-text-brand-primary","text-text-sm-semibold","font-bold"],[1,"text-text-brand-primary","text-text-sm-semibold","text-right","font-bold"],["cdkDrag","",1,"invoiceTableLayout"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["src","../../../../assets/img/bg_footer_invoice_details.png","alt","Footer",1,"invoiceDetailsFooter"],[1,"button-size-md","button-transparent"],[1,"button-size-md","button-transparent",3,"click"],["alt","Icon",3,"src"],[1,"text-text-xs-regular","text-text-secondary"],[1,"flex","flex-col"],[1,"flex","items-center"],["cdkDragHandle","",1,"material-icons","cursor-pointer","mr-3","!text-[20px]"],[1,"text-text-primary","text-text-md-regular","whitespace-pre-line"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","flex","flex-col"],[1,"text-text-primary","text-text-md-bold","text-right"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"]],template:function(t,i){if(1&t){const a=e.RV6();e.j41(0,"div",1)(1,"div",2)(2,"div",3)(3,"button",4),e.bIt("click",function(){return e.eBV(a),e.Njj(i.handleBack())}),e.nrm(4,"img",5),e.k0s(),e.j41(5,"p",6),e.EFF(6),e.nI1(7,"translate"),e.k0s(),e.nrm(8,"app-inno-status",7),e.k0s(),e.j41(9,"div",8)(10,"app-inno-popover",9)(11,"button",10),e.nrm(12,"img",11),e.EFF(13),e.nI1(14,"translate"),e.k0s()(),e.DNE(15,X,2,0,"ng-template",null,0,e.C5r),e.j41(17,"button",12),e.bIt("click",function(){return e.eBV(a),e.Njj(i.EditEstimate())}),e.nrm(18,"img",13),e.EFF(19),e.nI1(20,"translate"),e.k0s(),e.j41(21,"button",12),e.bIt("click",function(){return e.eBV(a),e.Njj(i.ShareLink())}),e.nrm(22,"img",14),e.EFF(23),e.nI1(24,"translate"),e.k0s(),e.j41(25,"button",15),e.bIt("click",function(){return e.eBV(a),e.Njj(i.handleFunctionInDevelopment())}),e.nrm(26,"img",16),e.EFF(27),e.nI1(28,"translate"),e.k0s()()()(),e.j41(29,"div",17)(30,"div",18)(31,"div",19)(32,"div",20),e.nrm(33,"img",21),e.k0s(),e.j41(34,"div",22)(35,"div",23),e.DNE(36,Q,3,3,"p",24),e.j41(37,"p",25),e.EFF(38),e.k0s(),e.DNE(39,V,4,3,"div",3)(40,z,2,1,"p",26),e.k0s(),e.j41(41,"div",27)(42,"div",23)(43,"p",28),e.EFF(44),e.nI1(45,"translate"),e.k0s(),e.j41(46,"p",29),e.EFF(47),e.k0s()(),e.j41(48,"div",23)(49,"p",28),e.EFF(50),e.nI1(51,"translate"),e.k0s(),e.j41(52,"p",29),e.EFF(53),e.k0s()(),e.j41(54,"div",23)(55,"p",28),e.EFF(56),e.nI1(57,"translate"),e.k0s(),e.j41(58,"p",29),e.EFF(59),e.k0s()()(),e.j41(60,"div",23)(61,"p",30),e.EFF(62),e.nI1(63,"translate"),e.k0s(),e.j41(64,"p",31),e.EFF(65),e.k0s()()()(),e.j41(66,"div",32)(67,"div",33),e.bIt("cdkDropListDropped",function(g){return e.eBV(a),e.Njj(i.drop(g))}),e.j41(68,"div",34)(69,"p",35),e.EFF(70),e.nI1(71,"translate"),e.k0s(),e.j41(72,"p",35),e.EFF(73),e.nI1(74,"translate"),e.k0s(),e.j41(75,"p",35),e.EFF(76),e.nI1(77,"translate"),e.k0s(),e.j41(78,"p",36),e.EFF(79),e.nI1(80,"translate"),e.k0s()(),e.Z7z(81,H,21,17,"div",37,e.fX1),e.k0s()(),e.j41(83,"div",38)(84,"div",39)(85,"p",40),e.EFF(86),e.nI1(87,"translate"),e.k0s(),e.j41(88,"p",41),e.EFF(89),e.nI1(90,"decimal"),e.nI1(91,"formatNumber"),e.k0s()(),e.Z7z(92,Y,10,9,"div",42,e.fX1),e.j41(94,"div",39)(95,"p",40),e.EFF(96),e.nI1(97,"translate"),e.k0s(),e.j41(98,"p",41),e.EFF(99),e.nI1(100,"decimal"),e.nI1(101,"formatNumber"),e.k0s()(),e.j41(102,"div",39)(103,"p",40),e.EFF(104),e.nI1(105,"translate"),e.k0s(),e.j41(106,"p",41),e.EFF(107," $0 "),e.k0s()(),e.j41(108,"div",39)(109,"p",40),e.EFF(110),e.nI1(111,"translate"),e.nI1(112,"async"),e.k0s(),e.j41(113,"p",43),e.EFF(114),e.nI1(115,"decimal"),e.nI1(116,"formatNumber"),e.k0s()()(),e.nrm(117,"img",44),e.k0s()()}if(2&t){let a,d,g,F;const J=e.sdS(16);e.R7$(6),e.Lme(" ",e.bMT(7,34,"ESTIMATE.Title")," ",null!==(a=null==i.estimateInfo?null:i.estimateInfo.invoiceNumber)&&void 0!==a?a:""," "),e.R7$(2),e.Y8G("status",null==i.estimateInfo?null:i.estimateInfo.status),e.R7$(2),e.Y8G("content",J),e.R7$(3),e.SpI(" ",e.bMT(14,36,"ESTIMATE.Summary.More")," "),e.R7$(6),e.SpI(" ",e.bMT(20,38,"ESTIMATE.Summary.Edit")," "),e.R7$(4),e.SpI(" ",e.bMT(24,40,"ESTIMATE.Summary.ByLink")," "),e.R7$(4),e.SpI(" ",e.bMT(28,42,"ESTIMATE.Summary.SendMail")," "),e.R7$(6),e.Y8G("src",i.imageUrl,e.B4B),e.R7$(3),e.vxM(i.businessInfo.businessName?-1:36),e.R7$(2),e.SpI("",null==i.businessInfo?null:i.businessInfo.businessName," "),e.R7$(),e.vxM(i.businessInfo.businessPhoneNumber?39:-1),e.R7$(),e.vxM(i.businessInfo.businessAddress?40:-1),e.R7$(4),e.SpI(" ",e.bMT(45,44,"ESTIMATE.Summary.BilledTo")," "),e.R7$(3),e.SpI(" ",null!==(d=null==i.estimateInfo||null==i.estimateInfo.client?null:i.estimateInfo.client.clientName)&&void 0!==d?d:""," "),e.R7$(3),e.SpI("",e.bMT(51,46,"ESTIMATE.GIRD.EstimateNumber")," "),e.R7$(3),e.SpI(" ",null!==(g=null==i.estimateInfo?null:i.estimateInfo.invoiceNumber)&&void 0!==g?g:""," "),e.R7$(3),e.SpI(" ",e.bMT(57,48,"ESTIMATE.Summary.DatefIssue")," "),e.R7$(3),e.SpI(" ",i.selectedDate," "),e.R7$(3),e.JRh(e.bMT(63,50,"ESTIMATE.Summary.Description")),e.R7$(3),e.SpI(" ",null!==(F=null==i.estimateInfo?null:i.estimateInfo.notes)&&void 0!==F?F:""," "),e.R7$(2),e.Y8G("cdkDropListData",null==i.estimateInfo?null:i.estimateInfo.itemInvoices),e.R7$(3),e.SpI(" ",e.bMT(71,52,"ESTIMATE.ESTIMATE_FORM.TableHeaders.EstimateItem")," "),e.R7$(3),e.SpI(" ",e.bMT(74,54,"ESTIMATE.ESTIMATE_FORM.TableHeaders.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(77,56,"ESTIMATE.ESTIMATE_FORM.TableHeaders.Quantity")," "),e.R7$(3),e.SpI(" ",e.bMT(80,58,"ESTIMATE.ESTIMATE_FORM.TableHeaders.LineTotal")," "),e.R7$(2),e.Dyx(null==i.estimateInfo?null:i.estimateInfo.itemInvoices),e.R7$(5),e.SpI(" ",e.bMT(87,60,"ESTIMATE.ESTIMATE_FORM.Subtotal")," "),e.R7$(3),e.SpI(" $",e.bMT(91,65,e.i5U(90,62,i.subtotal,2))," "),e.R7$(3),e.Dyx(i.taxArray),e.R7$(4),e.SpI(" ",e.bMT(97,67,"ESTIMATE.ESTIMATE_FORM.Tax")," "),e.R7$(3),e.SpI(" $",e.bMT(101,72,e.i5U(100,69,i.CheckIsNaN(i.sumtax),2))," "),e.R7$(5),e.SpI(" ",e.bMT(105,74,"ESTIMATE.ESTIMATE_FORM.Discount")," "),e.R7$(6),e.Lme(" ",e.bMT(111,76,"ESTIMATE.ESTIMATE_FORM.EstimateTotal")," (",e.bMT(112,78,i._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(116,83,e.i5U(115,80,null==i.estimateInfo?null:i.estimateInfo.totalAmount,2))," ")}},dependencies:[I.G,C.Jj,k.D9,O.RG,B.mC,A.iI,P.Cn,y.gFV,y.iov,D.ad,D.O7,D.T1,D.Fb,L.A,U.x,W.p,b.L,u.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceDetailsFooter[_ngcontent-%COMP%]{position:absolute;-webkit-user-select:none;user-select:none;pointer-events:none;bottom:5px;left:0;transform:translateY(100%);width:100%;height:auto;object-fit:contain}.invoiceTableLayout[_ngcontent-%COMP%]{width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(100px,1fr) 200px 200px 200px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),_})()}}]);