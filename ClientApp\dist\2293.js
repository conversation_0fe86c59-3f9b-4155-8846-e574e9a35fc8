"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2293],{2293:(x,d,o)=>{o.r(d),o.d(d,{RolePermissionProjectComponent:()=>y});var r=o(9842),u=o(1626),e=o(4438);const h=o(5312).c.HOST_API+"/api";let P=(()=>{var t;class i{constructor(){(0,r.A)(this,"http",(0,e.WQX)(u.Qq))}GetPermission(){return this.http.get(h+"/Permission")}}return t=i,(0,r.A)(i,"\u0275fac",function(s){return new(s||t)}),(0,r.A)(i,"\u0275prov",e.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})),i})();var l=o(1448),f=o(6146),g=o(9079),R=o(8600),m=o(5236),v=o(467),b=o(2716),T=o(7987);let A=(()=>{var t;class i extends b.H{open(s){var n=this;return(0,v.A)(function*(){const c=yield Promise.all([o.e(2076),o.e(3653)]).then(o.bind(o,3653));return n.matDialog.open(c.AddPermissionComponent.getComponent(),{panelClass:"custom_dialog",scrollStrategy:new T.t0,disableClose:!0})})()}}return t=i,(0,r.A)(i,"\u0275fac",(()=>{let a;return function(n){return(a||(a=e.xGo(t)))(n||t)}})()),(0,r.A)(i,"\u0275prov",e.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})),i})();var j=o(177);function C(t,i){if(1&t){const a=e.RV6();e.j41(0,"div",9)(1,"div",11)(2,"h6"),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.j41(5,"button",12),e.bIt("click",function(){e.eBV(a);const n=e.XpG();return e.Njj(n.OpenDialog())}),e.EFF(6,"New"),e.k0s()(),e.nrm(7,"hr"),e.k0s()}2&t&&(e.R7$(3),e.SpI(" ",e.bMT(4,1,"ROLE.UserGroupAuthorization")," "))}function E(t,i){}function S(t,i){if(1&t&&(e.j41(0,"ejs-grid",10)(1,"e-columns"),e.nrm(2,"e-column",13),e.nI1(3,"translate"),e.nrm(4,"e-column",14),e.nI1(5,"translate"),e.nrm(6,"e-column",15),e.nI1(7,"translate"),e.k0s()()),2&t){const a=e.XpG();e.Y8G("dataSource",a.dataSource)("allowPaging",!1)("allowSorting",!0)("allowFiltering",!1),e.R7$(2),e.Y8G("headerText",e.bMT(3,7,"ROLE.GIRD.RoleName")),e.R7$(2),e.Y8G("headerText",e.bMT(5,9,"ROLE.GIRD.Description")),e.R7$(2),e.Y8G("headerText",e.bMT(7,11,"ROLE.GIRD.Code"))}}let y=(()=>{var t;class i{constructor(s,n){(0,r.A)(this,"addPermissionDialog",void 0),(0,r.A)(this,"location",void 0),(0,r.A)(this,"TYPE_TAB",{USER_GROUP:1,USER:2,ROLE:3}),(0,r.A)(this,"tabs",[]),(0,r.A)(this,"currentTab",this.TYPE_TAB.USER_GROUP),(0,r.A)(this,"translate",(0,e.WQX)(m.c$)),(0,r.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,r.A)(this,"_permissionService",(0,e.WQX)(P)),(0,r.A)(this,"dataSource",void 0),this.addPermissionDialog=s,this.location=n,this.tabs=[{label:this.translate.instant("ROLE.RolePermission"),value:this.TYPE_TAB.USER_GROUP},{label:this.translate.instant("ROLE.User"),value:this.TYPE_TAB.USER},{label:this.translate.instant("ROLE.Role"),value:this.TYPE_TAB.ROLE}]}ngOnInit(){}handleChangeTab(s){this.currentTab=s}GetAllPermission(){this._permissionService.GetPermission().pipe((0,g.pQ)(this.destroyRef)).subscribe(s=>{s&&(this.dataSource=s.data)})}OpenDialog(){this.addPermissionDialog.open({}).then(n=>{n.afterClosed().subscribe(c=>{})})}handleBack(){this.location.back()}}return t=i,(0,r.A)(i,"\u0275fac",function(s){return new(s||t)(e.rXU(A),e.rXU(j.aZ))}),(0,r.A)(i,"\u0275cmp",e.VBU({type:t,selectors:[["app-role-permission-project"]],standalone:!0,features:[e.aNF],decls:14,vars:6,consts:[[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"flex","items-center","gap-[8px]"],[1,"button-icon","button-size-md",3,"click"],["src","../../../../assets/img/icon/ic_arrow_left.svg","alt","Icon"],[1,"text-text-primary","text-headline-lg-bold"],[1,"container-full","mt-[24px]"],[3,"onChange","tabs","value"],[1,"w-full","mt-[24px]"],[1,"container-full"],[3,"dataSource","allowPaging","allowSorting","allowFiltering"],[1,"flex","justify-between"],["type","button",1,"text-white","bg-blue-700","hover:bg-blue-800","focus:ring-4","focus:ring-blue-300","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["field","roleName","width","70",3,"headerText"],["field","description","width","70",3,"headerText"],["field","code","width","100",3,"headerText"]],template:function(s,n){1&s&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),e.bIt("click",function(){return n.handleBack()}),e.nrm(4,"img",4),e.k0s(),e.j41(5,"p",5),e.EFF(6),e.nI1(7,"translate"),e.k0s()()()(),e.j41(8,"div",6)(9,"app-inno-tabs",7),e.bIt("onChange",function(G){return n.handleChangeTab(G)}),e.k0s()(),e.j41(10,"div",8),e.DNE(11,C,8,3,"div",9)(12,E,0,0)(13,S,8,13,"ejs-grid",10),e.k0s()),2&s&&(e.R7$(6),e.SpI(" ",e.bMT(7,4,"ROLE.RolePermission")," "),e.R7$(3),e.Y8G("tabs",n.tabs)("value",n.currentTab),e.R7$(2),e.vxM(n.currentTab===n.TYPE_TAB.USER_GROUP?11:n.currentTab===n.TYPE_TAB.USER?12:n.currentTab===n.TYPE_TAB.ROLE?13:-1))},dependencies:[R.k,l.gFV,l._ab,l.eeu,l.rFS,l.LGG,l.cvh,f.G,m.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),i})()}}]);