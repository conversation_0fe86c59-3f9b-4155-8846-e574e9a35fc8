using Microsoft.EntityFrameworkCore.Diagnostics;
using System.Data.Common;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Configuration;
using SkiaSharp;

namespace InnoBook.Middlewares
{
    /// <summary>
    /// Interceptor that modifies SQL queries to automatically encrypt/decrypt sensitive fields
    /// </summary>
    public class SqlEncryptionInterceptor : DbCommandInterceptor
    {
        private readonly string _encryptionKey;
        
        // Define which tables and columns should be encrypted
        private readonly Dictionary<string, List<string>> _encryptedFields = new()
        {
            ["users"] = new() { "first_name", "last_name" },
            ["clients"] = new() { "first_name", "last_name", "phone_number", "business_phone_number", "mobile_phone_number", "address_line1", "address_line2" },
            ["companies"] = new() { "phone", "adress2" }
        };

        public SqlEncryptionInterceptor(IConfiguration configuration)
        {
            _encryptionKey = configuration["ENCRYPTION_KEY"] ?? throw new InvalidOperationException("Encryption key not configured");
        }

        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result)
        {
            ModifyCommandForEncryption(command);
            return base.ReaderExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            ModifyCommandForEncryption(command);
            return base.ReaderExecutingAsync(command, eventData, result, cancellationToken);
        }

        public override InterceptionResult<int> NonQueryExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result)
        {
            ModifyCommandForEncryption(command);
            return base.NonQueryExecuting(command, eventData, result);
        }

        public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            ModifyCommandForEncryption(command);
            return base.NonQueryExecutingAsync(command, eventData, result, cancellationToken);
        }

        private void ModifyCommandForEncryption(DbCommand command)
        {
            if (string.IsNullOrEmpty(command.CommandText))
                return;

            try
            {
                var originalSql = command.CommandText;
                var modifiedSql = originalSql;

                // Handle different types of SQL operations
                if (IsSelectQuery(originalSql))
                {
                    modifiedSql = ModifySelectQuery(originalSql);
                }
                else if (IsInsertQuery(originalSql))
                {
                    modifiedSql = ModifyInsertQuery(originalSql);
                }
                else if (IsUpdateQuery(originalSql))
                {
                    modifiedSql = ModifyUpdateQuery(originalSql);
                }

                if (modifiedSql != originalSql)
                {
                    command.CommandText = modifiedSql;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't break the query
                Console.WriteLine($"Error modifying SQL for encryption: {ex.Message}");
            }
        }

        private bool IsSelectQuery(string sql)
        {
            return sql.TrimStart().StartsWith("SELECT", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsInsertQuery(string sql)
        {
            return sql.TrimStart().StartsWith("INSERT", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsUpdateQuery(string sql)
        {
            return sql.TrimStart().StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase);
        }

        public string ModifySelectQuery(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return sql;
            }

            string modifiedSql = sql;
            var replacedColumns = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var table in _encryptedFields.Keys)
            {
                if (!ContainsTable(sql, table))
                {
                    continue;
                }

                foreach (var column in _encryptedFields[table])
                {
                    // Pattern 1: table_alias.column_name in SELECT clause, capturing AS clause
                    string aliasPattern = $@"\b(\w+)\.\b{Regex.Escape(column)}\b(?!\d)(?:\s+(AS\s+[""]?\w+[""]?))?(\s*,|\s*FROM\b|$)?";
                    var aliasMatches = Regex.Matches(modifiedSql, aliasPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in aliasMatches)
                    {
                        var alias = match.Groups[1].Value;
                        var asClause = match.Groups[2].Success ? match.Groups[2].Value : string.Empty;
                        var originalMatch = match.Value;
                        string columnKey = $"{alias}.{column}";

                        if (replacedColumns.Contains(columnKey))
                        {
                            continue;
                        }

                        if (IsTableAlias(sql, alias, table))
                        {
                            var replacement = $"CASE WHEN {alias}.{column} IS NOT NULL AND LENGTH({alias}.{column}) > 50 " +
                                             $"THEN pgp_sym_decrypt(decode({alias}.{column}, 'base64')::bytea, '{_encryptionKey}') " +
                                             $"ELSE {alias}.{column} END";
                            modifiedSql = Regex.Replace(modifiedSql, Regex.Escape(originalMatch), string.IsNullOrEmpty(asClause) ? $"{replacement}, " : $"({replacement}) {asClause.Trim(' ', ',')}," , RegexOptions.IgnoreCase);
                            replacedColumns.Add(columnKey);
                        }
                    }

                    // Pattern 2: Direct column reference in SELECT clause, capturing AS clause
                    string directColumnPattern = $@"\bSELECT\s+[^,]*?\b{Regex.Escape(column)}\b(?!\d)(?:\s+(AS\s+[""]?\w+[""]?))?(\s*,|\s*FROM\b|$)?";
                    if (Regex.IsMatch(modifiedSql, directColumnPattern, RegexOptions.IgnoreCase))
                    {
                        string tableAlias = GetTableAliasFromQuery(sql, table);
                        if (!string.IsNullOrEmpty(tableAlias))
                        {
                            string columnKey = $"{tableAlias}.{column}";
                            if (replacedColumns.Contains(columnKey))
                            {
                                continue;
                            }

                            string directPattern = $@"\b{Regex.Escape(column)}\b(?!\d)(?:\s+(AS\s+[""]?\w+[""]?))?(\s*,|\s*FROM\b|$)?";
                            var directMatch = Regex.Match(modifiedSql, directPattern, RegexOptions.IgnoreCase);
                            var asClause = directMatch.Success && directMatch.Groups[1].Success ? directMatch.Groups[1].Value : string.Empty;
                            var replacement = $"CASE WHEN {tableAlias}.{column} IS NOT NULL AND LENGTH({tableAlias}.{column}) > 50 " +
                                             $"THEN pgp_sym_decrypt(decode({tableAlias}.{column}, 'base64')::bytea, '{_encryptionKey}') " +
                                             $"ELSE {tableAlias}.{column} END";
                            modifiedSql = Regex.Replace(modifiedSql, directPattern, string.IsNullOrEmpty(asClause) ? $"{replacement}, " : $"({replacement}) {asClause.Trim(' ', ',')},", RegexOptions.IgnoreCase);
                            replacedColumns.Add(columnKey);
                        }
                    }
                }
            }

            return modifiedSql;
        }

        private bool ContainsTable(string sql, string tableName)
        {
            string escapedTableName = Regex.Escape(tableName);

            // Pattern for FROM clause: matches tableName after FROM, with optional schema and quotes
            string fromPattern = $@"\bFROM\s+(?:(?:[\w]+\.)?[""]?{escapedTableName}[""]?)(?:\s+AS\s+[\w]+)?\s*";

            // Pattern for JOIN clause: matches tableName after JOIN (any type), with optional schema and quotes
            string joinPattern = $@"\b(INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN\s+(?:(?:[\w]+\.)?[""]?{escapedTableName}[""]?)(?:\s+AS\s+[\w]+)?\s*";

            // Check for table name in FROM or JOIN clauses, case-insensitive
            bool isFrom = Regex.IsMatch(sql, fromPattern, RegexOptions.IgnoreCase);
            bool isJoin = Regex.IsMatch(sql, joinPattern, RegexOptions.IgnoreCase);

            return isFrom || isJoin;
        }

        public bool IsTableAlias(string sql, string alias, string tableName)
        {
            // Ensure tableName and alias are escaped for regex to handle special characters
            string escapedTableName = Regex.Escape(tableName);
            string escapedAlias = Regex.Escape(alias);

            // Pattern for FROM or JOIN clause: matches tableName followed by alias, with optional schema and quotes
            string aliasPattern = $@"\b(FROM|INNER\s+JOIN|LEFT\s+JOIN|RIGHT\s+JOIN|FULL\s+JOIN|CROSS\s+JOIN)\s+(?:(?:[\w]+\.)?[""]?{escapedTableName}[""]?)\s+(?:AS\s+)?{escapedAlias}(?:\s|$|[,;()])";

            // Check if alias is defined for the table in FROM or JOIN clauses, case-insensitive
            return Regex.IsMatch(sql, aliasPattern, RegexOptions.IgnoreCase);
        }

        public string GetTableAliasFromQuery(string sql, string tableName)
        {
            // Ensure tableName is escaped for regex to handle special characters
            string escapedTableName = Regex.Escape(tableName);

            // Pattern for FROM or JOIN clause: captures alias after tableName, with optional schema and quotes
            string aliasPattern = $@"\b(FROM|INNER\s+JOIN|LEFT\s+JOIN|RIGHT\s+JOIN|FULL\s+JOIN|CROSS\s+JOIN)\s+(?:(?:[\w]+\.)?[""]?{escapedTableName}[""]?)\s+(?:AS\s+)?(\w+)(?:\s|$|[,;()])";

            // Try to find a match
            var match = Regex.Match(sql, aliasPattern, RegexOptions.IgnoreCase);

            if (match.Success)
            {
                return match.Groups[2].Value;
            }

            // If no alias is found, return null to indicate no alias
            return null;
        }

        private string ModifyInsertQuery(string sql)
        {
            foreach (var table in _encryptedFields.Keys)
            {
                if (!sql.Contains(table, StringComparison.OrdinalIgnoreCase))
                    continue;

                // Handle different INSERT patterns that Entity Framework generates

                // Pattern 1: INSERT INTO table (columns) VALUES (values)
                var insertPattern = $@"INSERT\s+INTO\s+{table}\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)";
                var match = Regex.Match(sql, insertPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

                if (match.Success)
                {
                    sql = ProcessInsertMatch(sql, match, table);
                }
                else
                {
                    // Pattern 2: INSERT INTO table (columns) OUTPUT ... VALUES (values)
                    var insertOutputPattern = $@"INSERT\s+INTO\s+{table}\s*\(([^)]+)\)\s*OUTPUT[^V]*VALUES\s*\(([^)]+)\)";
                    var outputMatch = Regex.Match(sql, insertOutputPattern, RegexOptions.IgnoreCase | RegexOptions.Singleline);

                    if (outputMatch.Success)
                    {
                        sql = ProcessInsertWithOutput(sql, outputMatch, table);
                    }
                }
            }

            return sql;
        }

        private string ProcessInsertMatch(string sql, Match match, string table)
        {
            var columnsText = match.Groups[1].Value;
            var valuesText = match.Groups[2].Value;

            var columns = columnsText.Split(',').Select(c => c.Trim().Trim('"', '`', '[')).ToArray();
            var values = SplitValues(valuesText);

            var modifiedValues = new List<string>();

            for (int i = 0; i < columns.Length && i < values.Count; i++)
            {
                var column = columns[i].ToLower().Replace("]", ""); // Handle [column_name] format
                var value = values[i].Trim();

                if (_encryptedFields[table].Contains(column))
                {
                    modifiedValues.Add(WrapWithEncryption(value));
                }
                else
                {
                    modifiedValues.Add(value);
                }
            }

            var newValuesClause = string.Join(", ", modifiedValues);
            return sql.Replace(match.Groups[2].Value, newValuesClause);
        }

        private string ProcessInsertWithOutput(string sql, Match match, string table)
        {
            // Similar to ProcessInsertMatch but handles OUTPUT clause
            var columnsText = match.Groups[1].Value;
            var valuesText = match.Groups[2].Value;

            var columns = columnsText.Split(',').Select(c => c.Trim().Trim('"', '`', '[', ']')).ToArray();
            var values = SplitValues(valuesText);

            var modifiedValues = new List<string>();

            for (int i = 0; i < columns.Length && i < values.Count; i++)
            {
                var column = columns[i].ToLower();
                var value = values[i].Trim();

                if (_encryptedFields[table].Contains(column))
                {
                    modifiedValues.Add(WrapWithEncryption(value));
                }
                else
                {
                    modifiedValues.Add(value);
                }
            }

            var newValuesClause = string.Join(", ", modifiedValues);
            return sql.Replace(match.Groups[2].Value, newValuesClause);
        }

        private string WrapWithEncryption(string value)
        {
            // Handle different value types
            if (value.StartsWith("@") || value.StartsWith("$"))
            {
                // Parameter - wrap with encryption, handle NULL case
                return $"CASE WHEN {value} IS NULL THEN NULL ELSE encode(pgp_sym_encrypt({value}::text, '{_encryptionKey}'),'base64') END";
            }
            else if (value.Equals("NULL", StringComparison.OrdinalIgnoreCase))
            {
                // NULL value - keep as is
                return value;
            }
            else if (value.StartsWith("'") && value.EndsWith("'"))
            {
                // String literal - encrypt directly
                return $"encode(pgp_sym_encrypt({value}, '{_encryptionKey}'). 'base64')";
            }
            else
            {
                // Other cases - try to encrypt with NULL check
                return $"CASE WHEN {value} IS NULL THEN NULL ELSE encode(pgp_sym_encrypt({value}::text, '{_encryptionKey}'),'base64') END";
            }
        }

        public string ModifyUpdateQuery(string sql)
        {
            if (string.IsNullOrWhiteSpace(sql))
            {
                return sql;
            }

            string modifiedSql = sql;
            var replacedClauses = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var table in _encryptedFields.Keys)
            {
                if (!ContainsTable(sql, table))
                {
                    continue;
                }

                string tableAlias = GetTableAliasFromQuery(sql, table) ?? table;

                foreach (var column in _encryptedFields[table])
                {
                    // Pattern: SET [alias.]column = value or SET [column] = value
                    // Matches column names precisely, ensuring no digit follows (e.g., adress vs adress2)
                    string setPattern = $@"\bSET\s+([^,]*?(?:(?:\b{Regex.Escape(tableAlias)}\.)|\[{Regex.Escape(column)}\]|\b{Regex.Escape(column)}\b(?!\d))\s*=\s*)([^,\r\n]+)(?=\s*,|\s*WHERE|\s*$)";
                    var matches = Regex.Matches(modifiedSql, setPattern, RegexOptions.IgnoreCase);

                    foreach (Match match in matches)
                    {
                        string setClause = match.Groups[1].Value;
                        string value = match.Groups[2].Value.Trim();
                        string delimiter = match.Groups[3].Value; // Capture comma or end of clause
                        string fullMatch = match.Value;

                        // Skip if already replaced
                        if (replacedClauses.Contains(fullMatch))
                        {
                            continue;
                        }

                        // Verify table alias if present
                        string? columnPrefix = match.Groups[1].Value.Contains($"{tableAlias}.") || !match.Groups[1].Value.Contains(".") || match.Groups[1].Value.Contains($"[{column}]")
                            ? tableAlias
                            : null;

                        if (columnPrefix == null || IsTableAlias(sql, columnPrefix, table))
                        {
                            string encryptedValue = WrapWithEncryption(value);
                            string newSetClause = $"{setClause}{encryptedValue}{delimiter}";
                            modifiedSql = Regex.Replace(modifiedSql, Regex.Escape(fullMatch), newSetClause, RegexOptions.IgnoreCase);
                            replacedClauses.Add(fullMatch);
                        }
                    }
                }
            }

            return modifiedSql;
        }

        private List<string> SplitValues(string valuesString)
        {
            var values = new List<string>();
            var current = "";
            var inQuotes = false;
            var quoteChar = '\0';
            var parenLevel = 0;

            for (int i = 0; i < valuesString.Length; i++)
            {
                var c = valuesString[i];

                if (!inQuotes && (c == '\'' || c == '"'))
                {
                    inQuotes = true;
                    quoteChar = c;
                    current += c;
                }
                else if (inQuotes && c == quoteChar)
                {
                    inQuotes = false;
                    current += c;
                }
                else if (!inQuotes && c == '(')
                {
                    parenLevel++;
                    current += c;
                }
                else if (!inQuotes && c == ')')
                {
                    parenLevel--;
                    current += c;
                }
                else if (!inQuotes && c == ',' && parenLevel == 0)
                {
                    values.Add(current.Trim());
                    current = "";
                }
                else
                {
                    current += c;
                }
            }

            if (!string.IsNullOrEmpty(current))
            {
                values.Add(current.Trim());
            }

            return values;
        }
    }
}
