"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9972],{9972:(k,l,n)=>{n.r(l),n.d(l,{NewBusinessComponent:()=>B});var i=n(9842),d=n(359),_=n(6146),b=n(1470),e=n(4438),o=n(9417),f=n(6327),m=n(3719),p=n(9079),c=n(4006),g=n(177);const E=(r,t)=>({"bg-green-700 hover:bg-green-800":r,"bg-gray-400":t});function y(r,t){1&r&&(e.j41(0,"mat-error",8),e.EFF(1,"Business is required"),e.k0s())}function C(r,t){1&r&&(e.j41(0,"mat-error",8),e.EFF(1,"email is required"),e.k0s())}function h(r,t){1&r&&(e.j41(0,"mat-error",8),e.EFF(1,"Invalid email"),e.k0s())}function v(r,t){if(1&r&&(e.j41(0,"option",12),e.EFF(1),e.k0s()),2&r){const a=t.$implicit;e.Y8G("value",a.name),e.R7$(),e.SpI(" ",a.name," ")}}function F(r,t){1&r&&(e.j41(0,"mat-error",8),e.EFF(1,"Country is required"),e.k0s())}let B=(()=>{var r;class t{static getComponent(){return t}constructor(u){(0,i.A)(this,"dialogRef",void 0),(0,i.A)(this,"businessForm",void 0),(0,i.A)(this,"countries",f.F),(0,i.A)(this,"submitted",!1),(0,i.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,i.A)(this,"formBuilder",(0,e.WQX)(o.ze)),(0,i.A)(this,"user_business",(0,e.WQX)(d.l)),this.dialogRef=u,this.businessForm=this.formBuilder.group({business:["",o.k0.compose([o.k0.required])],note:[""],email:["",o.k0.compose([o.k0.required,o.k0.email])],country:["",o.k0.compose([o.k0.required])]})}onSubmit(){this.submitted||(this.submitted=!0,this.user_business.CreateUserBusiness({name:this.businessForm.controls.business.value,note:this.businessForm.controls.note.value,email:this.businessForm.controls.email.value,country:this.businessForm.controls.country.value}).pipe((0,p.pQ)(this.destroyRef)).subscribe({next:s=>{s&&(this.submitted=!1,this.dialogRef.close(s))}}))}ngOnInit(){}get f(){return this.businessForm.controls}closeDialog(){this.dialogRef.close()}}return r=t,(0,i.A)(t,"\u0275fac",function(u){return new(u||r)(e.rXU(c.CP))}),(0,i.A)(t,"\u0275cmp",e.VBU({type:r,selectors:[["app-new-business"]],standalone:!0,features:[e.aNF],decls:31,vars:10,consts:[[3,"onClose"],[1,"input-content"],[3,"ngSubmit","formGroup"],[1,"mb-4"],[1,"fw-bold","m-0","text-center"],[1,"mb-6"],["for","business",1,"block","mb-2","text-sm","font-medium","text-gray-900","text-start"],["formControlName","business","type","text","id","business",1,"bg-gray-100","border","border-gray-300","text-gray-900","text-sm","rounded-lg","block","w-full","p-2.5"],[1,"matError"],["type","email","id","_email","formControlName","email","placeholder","Email","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],["for","countries",1,"block","mb-2","text-sm","font-medium","text-gray-900","text-start"],["formControlName","country","id","country",1,"bg-gray-100","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[3,"value"],["for","note",1,"block","mb-2","text-sm","font-medium","text-gray-900","text-start"],["id","message","rows","3","formControlName","note","placeholder","What do you do for your clients?",1,"block","p-2.5","w-full","text-sm","text-gray-900","bg-gray-100","rounded-lg","border","border-gray-300","focus:ring-blue-500","focus:border-blue-500"],[1,"mt-3","flex","justify-center"],["type","submit",1,"text-white","font-medium","rounded-lg","text-sm","w-full","sm:w-auto","px-5","py-2.5","text-center",3,"disabled","ngClass"]],template:function(u,s){1&u&&(e.j41(0,"app-innobook-modal-wrapper",0),e.bIt("onClose",function(){return s.closeDialog()}),e.j41(1,"div",1)(2,"form",2),e.bIt("ngSubmit",function(){return s.onSubmit()}),e.j41(3,"div",3)(4,"h6",4),e.EFF(5,"Create your new business"),e.k0s()(),e.j41(6,"div",5)(7,"label",6),e.EFF(8,"Business Name "),e.k0s(),e.nrm(9,"input",7),e.DNE(10,y,2,0,"mat-error",8),e.k0s(),e.j41(11,"div",5)(12,"label",6),e.EFF(13,"Email Business "),e.k0s(),e.nrm(14,"input",9),e.DNE(15,C,2,0,"mat-error",8)(16,h,2,0,"mat-error",8),e.k0s(),e.j41(17,"div",5)(18,"label",10),e.EFF(19,"Country "),e.k0s(),e.j41(20,"select",11),e.Z7z(21,v,2,2,"option",12,e.fX1),e.k0s(),e.DNE(23,F,2,0,"mat-error",8),e.k0s(),e.j41(24,"div",5)(25,"label",13),e.EFF(26,"What do you do for your clients? "),e.k0s(),e.nrm(27,"textarea",14),e.k0s(),e.j41(28,"div",15)(29,"button",16),e.EFF(30,"Save Changes"),e.k0s()()()()()),2&u&&(e.R7$(2),e.Y8G("formGroup",s.businessForm),e.R7$(8),e.vxM((s.f.business.dirty||s.f.business.touched)&&s.f.business.hasError("required")?10:-1),e.R7$(5),e.vxM((s.f.email.dirty||s.f.email.touched)&&s.f.email.hasError("required")?15:-1),e.R7$(),e.vxM((s.f.email.dirty||s.f.email.touched)&&s.f.email.hasError("email")?16:-1),e.R7$(5),e.Dyx(s.countries),e.R7$(2),e.vxM((s.f.country.dirty||s.f.country.touched)&&s.f.country.hasError("required")?23:-1),e.R7$(6),e.Y8G("disabled",!s.businessForm.valid)("ngClass",e.l_i(7,E,s.businessForm.valid,!s.businessForm.valid)))},dependencies:[b.j,m.RG,m.TL,_.G,g.YU,o.qT,o.xH,o.y7,o.me,o.wz,o.BC,o.cb,o.YS,o.j4,o.JD]})),t})()}}]);