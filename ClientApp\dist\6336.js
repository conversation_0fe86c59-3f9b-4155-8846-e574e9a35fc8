"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6336],{6408:(M,A,o)=>{o.d(A,{G:()=>x});var i=o(9842),t=o(4438),r=o(3924);let x=(()=>{var f;class d{constructor(){(0,i.A)(this,"mode","day"),(0,i.A)(this,"id",void 0),(0,i.A)(this,"enableMask",void 0),(0,i.A)(this,"name",void 0),(0,i.A)(this,"format","dd-MM-yyyy"),(0,i.A)(this,"placeholder",void 0),(0,i.A)(this,"value",void 0),(0,i.A)(this,"onChange",new t.bkB),(0,i.A)(this,"datePickerValue",void 0),(0,i.A)(this,"fields",{text:"label",value:"value"}),(0,i.A)(this,"start","Month"),(0,i.A)(this,"depth","Month"),(0,i.A)(this,"showTodayButton",!0),(0,i.A)(this,"weekNumber",!1)}ngOnChanges(c){const s=c.mode?.currentValue??c.value?.currentValue;s&&this.updateModeSettings(s)}updateModeSettings(c){switch(c){case"day":this.start="Month",this.depth="Month",this.showTodayButton=!0,this.weekNumber=!1;break;case"week":this.start="Month",this.depth="Month",this.showTodayButton=!1,this.weekNumber=!0;break;case"month":this.start="Year",this.depth="Year",this.showTodayButton=!1,this.weekNumber=!1}}handleChangeValue(c){this.onChange.emit(c?.value??void 0)}}return f=d,(0,i.A)(d,"\u0275fac",function(c){return new(c||f)}),(0,i.A)(d,"\u0275cmp",t.VBU({type:f,selectors:[["app-inno-datepicker"]],inputs:{mode:"mode",id:"id",enableMask:"enableMask",name:"name",format:"format",placeholder:"placeholder",value:"value"},outputs:{onChange:"onChange"},standalone:!0,features:[t.OA$,t.aNF],decls:1,vars:10,consts:[[1,"customDatePickerV2",3,"change","name","id","format","enableMask","placeholder","value","start","depth","showTodayButton","weekNumber"]],template:function(c,s){1&c&&(t.j41(0,"ejs-datepicker",0),t.bIt("change",function(u){return s.handleChangeValue(u)}),t.k0s()),2&c&&(t.FS9("name",s.name||""),t.FS9("id",s.id||""),t.Y8G("format",s.format)("enableMask",s.enableMask)("placeholder",s.placeholder)("value",s.value)("start",s.start)("depth",s.depth)("showTodayButton",s.showTodayButton)("weekNumber",s.weekNumber))},dependencies:[r.tZ,r.I],styles:[".customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{margin:0!important;padding:8px 12px;display:flex;min-width:125px!important;height:40px!important;border-radius:8px}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{background-color:var(--bg-primary)}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%] > input[_ngcontent-%COMP%]{padding:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:after{display:none}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group-icon.e-date-icon[_ngcontent-%COMP%]{margin:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons[_ngcontent-%COMP%]{min-height:unset!important}.customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%]::selection, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:after, .e-datepicker[_ngcontent-%COMP%]   .e-focused-date.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-cell.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%]{background-color:#0f182e!important}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons.e-active[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:not(.e-focused-date)   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]{color:#0f182e!important}.e-datepicker[_ngcontent-%COMP%]   .e-model-header[_ngcontent-%COMP%]{background-color:#fff!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{border:2px solid #E9EAEB!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:hover{background-color:#24242433!important}"]})),d})()},9424:(M,A,o)=>{o.d(A,{f:()=>f});var i=o(9842),t=o(177),r=o(4438);const x=(d,_,c)=>({"w-4 h-4":d,"w-6 h-6":_,"w-10 h-10":c});let f=(()=>{var d;class _{constructor(){(0,i.A)(this,"size","md")}}return d=_,(0,i.A)(_,"\u0275fac",function(s){return new(s||d)}),(0,i.A)(_,"\u0275cmp",r.VBU({type:d,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[r.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(s,C){1&s&&(r.j41(0,"div",0),r.qSk(),r.j41(1,"svg",1),r.nrm(2,"path",2)(3,"path",3),r.k0s(),r.joV(),r.j41(4,"span",4),r.EFF(5,"Loading..."),r.k0s()()),2&s&&(r.R7$(),r.Y8G("ngClass",r.sMw(1,x,"sm"===C.size,"md"===C.size,"lg"===C.size)))},dependencies:[t.MD,t.YU]})),_})()},6196:(M,A,o)=>{o.d(A,{A:()=>_});var i=o(9842),t=o(6146),r=o(4438),x=o(177),f=o(5236);const d=(c,s,C,u,m,S)=>({"text-text-warning bg-bg-warning-primary":c,"text-text-primary bg-bg-tertiary":s,"text-text-primary bg-bg-disabled":C,"text-text-success bg-bg-success-secondary":u,"text-text-success bg-bg-secondary-subtle":m,"text-text-danger bg-bg-secondary":S});let _=(()=>{var c;class s{constructor(){(0,i.A)(this,"STATUS",{DRAFT:0,UN_BILLED:1,NON_BILLABLE:2,PAID:3,BILLED:4,SENT:5}),(0,i.A)(this,"status",void 0)}getStatusText(){switch(this.status){case this.STATUS.DRAFT:return"STATUS.Draft";case this.STATUS.UN_BILLED:return"STATUS.Unbilled";case this.STATUS.NON_BILLABLE:return"STATUS.NonBillable";case this.STATUS.PAID:return"STATUS.Paid";case this.STATUS.BILLED:return"STATUS.Billed";case this.STATUS.SENT:return"STATUS.Sent";default:return"Unknown"}}}return c=s,(0,i.A)(s,"\u0275fac",function(u){return new(u||c)}),(0,i.A)(s,"\u0275cmp",r.VBU({type:c,selectors:[["app-inno-status"]],inputs:{status:"status"},standalone:!0,features:[r.aNF],decls:3,vars:11,consts:[[1,"text-text-sm-semibold","px-[8px]","py-[2px]","rounded-md","text-center",3,"ngClass"]],template:function(u,m){1&u&&(r.j41(0,"span",0),r.EFF(1),r.nI1(2,"translate"),r.k0s()),2&u&&(r.Y8G("ngClass",r.l4e(4,d,m.status===m.STATUS.UN_BILLED,m.status===m.STATUS.NON_BILLABLE,m.status===m.STATUS.DRAFT,m.status===m.STATUS.PAID,m.status===m.STATUS.BILLED,m.status===m.STATUS.SENT)),r.R7$(),r.SpI(" ",r.bMT(2,2,m.getStatusText()),"\n"))},dependencies:[t.G,x.YU,f.D9]})),s})()},8556:(M,A,o)=>{o.d(A,{K:()=>I});var i=o(9842),t=o(4438),r=o(5599),x=o(6146),f=o(4823),d=o(5236);function _(g,T){if(1&g){const v=t.RV6();t.j41(0,"button",4),t.bIt("click",function(){t.eBV(v);const e=t.XpG();return t.Njj(e.handleResume())}),t.nrm(1,"img",5),t.k0s()}}function c(g,T){if(1&g){const v=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(v);const e=t.XpG();return t.Njj(e.handleEdit())}),t.nrm(1,"img",7),t.k0s()}}function s(g,T){if(1&g){const v=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(v);const e=t.XpG();return t.Njj(e.handleDowload())}),t.nrm(1,"img",8),t.k0s()}}function C(g,T){if(1&g){const v=t.RV6();t.j41(0,"div",12)(1,"button",13),t.bIt("click",function(){t.eBV(v);const e=t.XpG(2);return t.Njj(e.handleArchive())}),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"button",14),t.bIt("click",function(){t.eBV(v);const e=t.XpG(2);return t.Njj(e.handleDelete())}),t.EFF(5),t.nI1(6,"translate"),t.k0s()()}2&g&&(t.R7$(2),t.SpI(" ",t.bMT(3,2,"COMMON.Archive")," "),t.R7$(3),t.SpI(" ",t.bMT(6,4,"COMMON.Delete")," "))}function u(g,T){if(1&g&&(t.j41(0,"app-inno-popover",9)(1,"button",10),t.nrm(2,"img",11),t.k0s()(),t.DNE(3,C,7,6,"ng-template",null,0,t.C5r)),2&g){const v=t.sdS(4);t.Y8G("content",v)}}function m(g,T){if(1&g){const v=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(v);const e=t.XpG(2);return t.Njj(e.handleArchive())}),t.nrm(1,"img",15),t.k0s()}}function S(g,T){if(1&g){const v=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(v);const e=t.XpG(2);return t.Njj(e.handleDelete())}),t.nrm(1,"img",16),t.k0s()}}function P(g,T){if(1&g&&t.DNE(0,m,2,0,"button",3)(1,S,2,0,"button",3),2&g){const v=t.XpG();t.vxM(v.onArchive.observed?0:-1),t.R7$(),t.vxM(v.onDelete.observed?1:-1)}}let I=(()=>{var g;class T{constructor(){(0,i.A)(this,"onEdit",new t.bkB),(0,i.A)(this,"onResume",new t.bkB),(0,i.A)(this,"onDelete",new t.bkB),(0,i.A)(this,"onArchive",new t.bkB),(0,i.A)(this,"onDowload",new t.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return g=T,(0,i.A)(T,"\u0275fac",function(D){return new(D||g)}),(0,i.A)(T,"\u0275cmp",t.VBU({type:g,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[t.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(D,e){1&D&&(t.j41(0,"div",1),t.DNE(1,_,2,0,"button",2)(2,c,2,0,"button",3)(3,s,2,0,"button",3)(4,u,5,1)(5,P,2,2),t.k0s()),2&D&&(t.R7$(),t.vxM(e.onResume.observed?1:-1),t.R7$(),t.vxM(e.onEdit.observed?2:-1),t.R7$(),t.vxM(e.onDowload.observed?3:-1),t.R7$(),t.vxM(e.onArchive.observed&&e.onDelete.observed?4:5))},dependencies:[x.G,d.D9,f.oV,r.x]})),T})()},1556:(M,A,o)=>{o.d(A,{Z:()=>c});var i=o(9842),t=o(4438),r=o(467),x=o(2716),f=o(7987);let d=(()=>{var s;class C extends x.H{open(m){var S=this;return(0,r.A)(function*(){const P=yield o.e(3190).then(o.bind(o,3190));return S.matDialog.open(P.AlertConfirmComponent.getComponent(),{data:m,width:"440px",panelClass:"custom_dialog",scrollStrategy:new f.t0,disableClose:!0})})()}}return s=C,(0,i.A)(C,"\u0275fac",(()=>{let u;return function(S){return(u||(u=t.xGo(s)))(S||s)}})()),(0,i.A)(C,"\u0275prov",t.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})),C})(),c=(()=>{var s;class C{constructor(m){(0,i.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=m}alertDelete(m){const{title:S,description:P,textSubmit:I="COMMON.Delete",textCancel:g}=m;return new Promise(T=>{this.alertConfirmDialog.open({title:S,description:P,textSubmit:I,textCancel:g,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(D=>{D.afterClosed().subscribe(e=>{T(e??!1)})})})}alertConfirm(m){const{title:S,description:P,textSubmit:I,textCancel:g}=m;return new Promise(T=>{this.alertConfirmDialog.open({title:S,description:P,textSubmit:I,textCancel:g}).then(D=>{D.afterClosed().subscribe(e=>{T(e??!1)})})})}}return s=C,(0,i.A)(C,"\u0275fac",function(m){return new(m||s)(t.KVO(d))}),(0,i.A)(C,"\u0275prov",t.jDH({token:s,factory:s.\u0275fac})),C})()},4433:(M,A,o)=>{o.d(A,{Q:()=>r});var i=o(9842),t=o(4438);let r=(()=>{var x;class f{transform(_,c=2){const s=Math.pow(10,c);return(Math.trunc(Number((_*s).toFixed(c+5)))/s).toFixed(c)}}return x=f,(0,i.A)(f,"\u0275fac",function(_){return new(_||x)}),(0,i.A)(f,"\u0275pipe",t.EJ8({name:"decimal",type:x,pure:!0,standalone:!0})),f})()},6617:(M,A,o)=>{o.d(A,{p:()=>x});var i=o(9842),t=o(6473),r=o(4438);let x=(()=>{var f;class d{transform(c){return(0,t.ZV)(c)}}return f=d,(0,i.A)(d,"\u0275fac",function(c){return new(c||f)}),(0,i.A)(d,"\u0275pipe",r.EJ8({name:"formatNumber",type:f,pure:!0,standalone:!0})),d})()},1456:(M,A,o)=>{o.d(A,{D:()=>_});var i=o(9842),t=o(5312),r=o(1626),x=o(4438),f=o(6473);const d=t.c.HOST_API+"/api";let _=(()=>{var c;class s{constructor(){(0,i.A)(this,"http",(0,x.WQX)(r.Qq))}CreateExpenses(u){return this.http.post(d+"/Expenses/CreateExpenses",u)}UpdateExpenses(u){return this.http.post(d+"/Expenses/UpdateExpenses",u)}GetExpensesById(u){return this.http.get(d+`/Expenses/GetExpensesById?Id=${u}`)}GetAllExpenses(u){const m=(0,f.yU)(u);return this.http.get(d+"/Expenses/GetAllExpenses",{params:m})}GetAllUploadExpenses(u){const m=(0,f.yU)(u);return this.http.get(d+"/Expenses/GetAllUploadExpenses",{params:m})}DeleteFileExpenses(u){return this.http.post(d+"/Expenses/DeleteExpenses",u)}DeleteExpenses(u){return this.http.post(d+"/Expenses/DeleteExpenses",u)}MarkAsPaid(u){return this.http.put(d+`/Expenses/MarkAsPaid?Id=${u}`,null)}GetExpenseItemsByExpenseIds(u){return this.http.post(d+"/Expenses/GetExpenseItemsByExpenseIds",u)}}return c=s,(0,i.A)(s,"\u0275fac",function(u){return new(u||c)}),(0,i.A)(s,"\u0275prov",x.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),s})()},6336:(M,A,o)=>{o.r(A),o.d(A,{ExpensesComponent:()=>le});var i=o(9842),t=o(5599),r=o(6617),x=o(9424),f=o(1556),d=o(6146),_=o(1448),c=o(1413),s=o(152),C=o(9079),u=o(5900),m=o(6408),S=o(8556),P=o(6196),I=o(1970),g=o(4823),T=o(4433),v=o(6473),D=o(2953),e=o(4438),N=o(1456),B=o(3492),y=o(5236),R=o(33),j=o(2928),k=o(1110),F=o(467),G=o(2716),$=o(7987);let L=(()=>{var a;class h extends G.H{open(l){var p=this;return(0,F.A)(function*(){const E=yield Promise.all([o.e(1328),o.e(6217),o.e(9717),o.e(2076),o.e(8586)]).then(o.bind(o,8599));return p.matDialog.open(E.NewExpenseFormComponent.getComponent(),{data:l,width:"calc(80% - 10px)",maxWidth:"100%",height:"auto",disableClose:!0,panelClass:"custom_dialog",scrollStrategy:new $.t0})})()}}return a=h,(0,i.A)(h,"\u0275fac",(()=>{let n;return function(p){return(n||(n=e.xGo(a)))(p||a)}})()),(0,i.A)(h,"\u0275prov",e.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),h})();var U=o(177);const w=["grid"],V=a=>({"mb-28":a});function X(a,h){if(1&a){const n=e.RV6();e.j41(0,"button",17),e.bIt("click",function(){e.eBV(n);const p=e.XpG();return e.Njj(p.modifyExpenses())}),e.nrm(1,"img",18),e.EFF(2),e.nI1(3,"translate"),e.k0s()}2&a&&(e.R7$(2),e.SpI(" ",e.bMT(3,1,"EXPENSES.NewExpenseButton")," "))}function K(a,h){1&a&&(e.j41(0,"div",15),e.nrm(1,"app-inno-spin",19),e.k0s())}function W(a,h){if(1&a&&(e.j41(0,"p",28),e.EFF(1),e.k0s()),2&a){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",null==n?null:n.expensesName," ")}}function z(a,h){if(1&a&&(e.j41(0,"p",29),e.EFF(1),e.k0s()),2&a){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",n.categoryName," ")}}function Y(a,h){if(1&a&&(e.j41(0,"p",29),e.EFF(1),e.k0s()),2&a){const n=e.XpG().$implicit;e.R7$(),e.SpI(" ",n.itemName," ")}}function Q(a,h){if(1&a&&(e.j41(0,"div",27),e.DNE(1,W,2,1,"p",28)(2,z,2,1,"p",29)(3,Y,2,1,"p",29),e.k0s()),2&a){const n=h.$implicit;e.R7$(),e.vxM(n.expensesName?1:-1),e.R7$(),e.vxM(null!=n&&n.categoryName?2:-1),e.R7$(),e.vxM(null!=n&&n.itemName?3:-1)}}function H(a,h){if(1&a&&e.EFF(0),2&a){const n=e.XpG().$implicit;e.SpI(" [",null==n?null:n.projectName,"] ")}}function Z(a,h){if(1&a&&(e.j41(0,"p",30),e.DNE(1,H,1,1),e.EFF(2),e.k0s()),2&a){let n;const l=h.$implicit;e.R7$(),e.vxM(null!=l&&l.projectName?1:-1),e.R7$(),e.SpI(" ",null!==(n=null==l?null:l.clientName)&&void 0!==n?n:""," ")}}function J(a,h){if(1&a&&(e.j41(0,"p",31),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&a){const n=h.$implicit,l=e.XpG(2);e.R7$(),e.SpI(" ",e.i5U(2,1,n.date,l._storeService.getdateFormat())," ")}}function q(a,h){if(1&a&&(e.j41(0,"div",33),e.nrm(1,"ngx-avatars",34),e.j41(2,"span",35),e.EFF(3),e.k0s()()),2&a){const n=e.XpG().$implicit,l=e.XpG(2);e.R7$(),e.FCK("matTooltip","",null==n||null==n.inforUser?null:n.inforUser.firstName," ",null==n||null==n.inforUser?null:n.inforUser.lastName," "),e.FS9("bgColor",l._storeService.getBgColor(null==n||null==n.inforUser?null:n.inforUser.firstName.slice(0,1))),e.Y8G("size",35)("name",(null==n?null:n.inforUser.firstName.charAt(0))+" "+(null!=n&&null!=n.inforUser&&n.inforUser.lastName?null==n||null==n.inforUser?null:n.inforUser.lastName.charAt(0):"")),e.R7$(2),e.Lme(" ",null==n||null==n.inforUser?null:n.inforUser.firstName," ",null==n||null==n.inforUser?null:n.inforUser.lastName,"")}}function ee(a,h){if(1&a&&(e.j41(0,"div",33),e.nrm(1,"ngx-avatars",34),e.j41(2,"span",35),e.EFF(3),e.k0s()()),2&a){const n=e.XpG().$implicit,l=e.XpG(2);e.R7$(),e.FS9("matTooltip",null==n||null==n.inforUser?null:n.inforUser.email),e.FS9("bgColor",l._storeService.getBgColor(null==n||null==n.inforUser?null:n.inforUser.email.slice(0,1))),e.Y8G("size",35)("name",null==n||null==n.inforUser?null:n.inforUser.email.slice(0,1)),e.R7$(2),e.SpI(" ",null==n||null==n.inforUser?null:n.inforUser.email,"")}}function te(a,h){if(1&a&&(e.j41(0,"p",32),e.DNE(1,q,4,8,"div",33)(2,ee,4,5,"div",33),e.k0s()),2&a){const n=h.$implicit;e.R7$(),e.vxM(null!=n&&null!=n.inforUser&&n.inforUser.firstName?1:2)}}function ne(a,h){if(1&a&&(e.j41(0,"div",36),e.nrm(1,"app-inno-status",37),e.j41(2,"p",38),e.EFF(3),e.nI1(4,"decimal"),e.nI1(5,"formatNumber"),e.k0s()()),2&a){const n=h.$implicit;e.R7$(),e.Y8G("status",n.status),e.R7$(2),e.SpI(" $",e.bMT(5,5,e.i5U(4,2,n.paidAmount,2))," ")}}function oe(a,h){if(1&a){const n=e.RV6();e.j41(0,"div",43)(1,"button",44),e.bIt("click",function(){e.eBV(n);const p=e.XpG().$implicit,E=e.XpG(3);return e.Njj(E.MarkAsPaid(p.id))}),e.EFF(2),e.nI1(3,"translate"),e.k0s()()}2&a&&(e.R7$(2),e.SpI(" ",e.bMT(3,1,"COMMON.MarkBill")," "))}function ie(a,h){if(1&a){const n=e.RV6();e.j41(0,"div",33)(1,"app-inno-table-action",39),e.bIt("onEdit",function(){const p=e.eBV(n).$implicit,E=e.XpG(3);return e.Njj(E.modifyExpenses(p.id))})("onDelete",function(){const p=e.eBV(n).$implicit,E=e.XpG(3);return e.Njj(E.handleDeleteExpense(p))}),e.k0s(),e.j41(2,"app-inno-popover",40)(3,"button",41),e.nrm(4,"img",42),e.k0s()(),e.DNE(5,oe,4,3,"ng-template",null,2,e.C5r),e.k0s()}if(2&a){const n=e.sdS(6);e.R7$(2),e.Y8G("content",n)}}function se(a,h){1&a&&(e.j41(0,"e-column",25),e.DNE(1,ie,7,1,"ng-template",null,1,e.C5r),e.k0s())}function ae(a,h){if(1&a){const n=e.RV6();e.j41(0,"div",16)(1,"ejs-grid",20,0),e.bIt("actionBegin",function(p){e.eBV(n);const E=e.XpG();return e.Njj(E.onActionBegin(p))}),e.j41(3,"e-columns")(4,"e-column",21),e.nI1(5,"translate"),e.DNE(6,Q,4,3,"ng-template",null,1,e.C5r),e.k0s(),e.j41(8,"e-column",22),e.nI1(9,"translate"),e.DNE(10,Z,3,2,"ng-template",null,1,e.C5r),e.k0s(),e.j41(12,"e-column",23),e.nI1(13,"translate"),e.DNE(14,J,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(16,"e-column",22),e.nI1(17,"translate"),e.DNE(18,te,3,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(20,"e-column",24),e.nI1(21,"translate"),e.DNE(22,ne,6,7,"ng-template",null,1,e.C5r),e.k0s(),e.DNE(24,se,3,0,"e-column",25),e.k0s()(),e.j41(25,"ejs-pager",26),e.bIt("click",function(p){e.eBV(n);const E=e.XpG();return e.Njj(E.onPageChange(p))}),e.k0s()()}if(2&a){const n=e.XpG();e.Y8G("ngClass",e.eq3(25,V,n._storeService.getIsRunning())),e.R7$(),e.Y8G("dataSource",n.dataSource)("sortSettings",n.sortOptions)("allowSorting",!0)("allowSelection",!0),e.R7$(3),e.Y8G("headerText",e.bMT(5,15,"EXPENSES.GIRD.ExpenseName")),e.R7$(4),e.Y8G("headerText",e.bMT(9,17,"EXPENSES.GIRD.ProjectClient")),e.R7$(4),e.Y8G("headerText",e.bMT(13,19,"EXPENSES.GIRD.Date")),e.R7$(4),e.Y8G("headerText",e.bMT(17,21,"EXPENSES.GIRD.User")),e.R7$(4),e.Y8G("headerText",e.bMT(21,23,"EXPENSES.GIRD.Amount")),e.R7$(4),e.vxM(n.isAdmin?24:-1),e.R7$(),e.Y8G("pageSize",n.pageSizesDefault)("totalRecordsCount",n.totalPages)("currentPage",n.currentPage)("pageSizes",n.pageSizes)}}let le=(()=>{var a;class h{constructor(l,p,E,b,O,re,ce,pe,ue,de){(0,i.A)(this,"expenseService",void 0),(0,i.A)(this,"toastService",void 0),(0,i.A)(this,"translate",void 0),(0,i.A)(this,"activatedRoute",void 0),(0,i.A)(this,"layoutUtilsService",void 0),(0,i.A)(this,"authenticationService",void 0),(0,i.A)(this,"router",void 0),(0,i.A)(this,"_storeService",void 0),(0,i.A)(this,"destroyRef",void 0),(0,i.A)(this,"newExpenseFormDialog",void 0),(0,i.A)(this,"sort",void 0),(0,i.A)(this,"sortOptions",{columns:[]}),(0,i.A)(this,"search",""),(0,i.A)(this,"dataSource",void 0),(0,i.A)(this,"totalPages",1),(0,i.A)(this,"currentPage",1),(0,i.A)(this,"pageSizes",[10,20,50,100]),(0,i.A)(this,"pageSizesDefault",10),(0,i.A)(this,"searchSubject",new c.B),(0,i.A)(this,"_subscriptions",[]),(0,i.A)(this,"grid",void 0),(0,i.A)(this,"idTime",void 0),(0,i.A)(this,"columnName",void 0),(0,i.A)(this,"direction",void 0),(0,i.A)(this,"date",void 0),(0,i.A)(this,"isLoading",!1),(0,i.A)(this,"isAdmin",!1),this.expenseService=l,this.toastService=p,this.translate=E,this.activatedRoute=b,this.layoutUtilsService=O,this.authenticationService=re,this.router=ce,this._storeService=pe,this.destroyRef=ue,this.newExpenseFormDialog=de}handleSearch(l){this.searchSubject.next(l)}ngOnInit(){this._subscriptions.push(this._storeService.getRoleBusinessAsObservable().subscribe(p=>{this.isAdmin=p===D.X.Admin})),this.activatedRoute.queryParams.pipe((0,C.pQ)(this.destroyRef)).subscribe(p=>{this.currentPage=p?.page??1,this.GetAllExpenses()});const l=this.searchSubject.pipe((0,s.B)(550)).subscribe(p=>{this.search=p??"",this.GetAllExpenses()});this._subscriptions.push(l)}onPageChange(l){l?.newProp?.pageSize&&(this.pageSizesDefault=l.newProp.pageSize,this.GetAllExpenses()),l?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:l.currentPage},queryParamsHandling:"merge"})}GetAllExpenses(){this.isLoading=!0;let l={Page:this.currentPage,PageSize:this.pageSizesDefault,Search:this.search,filterDate:this.date,...this.sort};this.expenseService.GetAllExpenses(l).pipe((0,C.pQ)(this.destroyRef)).subscribe(p=>{p&&(this.totalPages=p.totalRecords,this.dataSource=p.data,this.isLoading=!1,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))})}modifyExpenses(l){this.newExpenseFormDialog.open(l).then(E=>{E.afterClosed().subscribe(b=>{b&&(this.toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this.GetAllExpenses())})})}MarkAsPaid(l){this.expenseService.MarkAsPaid(l).pipe((0,C.pQ)(this.destroyRef)).subscribe(p=>{p&&(this.toastService.showSuccess(this.translate.instant("TOAST.Paid"),this.translate.instant("TOAST.Success")),this.GetAllExpenses())})}handleChangeDateFilter(l){this.date=(0,v.cn)(l),this.GetAllExpenses()}handleDeleteExpense(l){const p=this.translate.instant("EXPENSES.DeleteExpeneses"),E=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:p,description:E}).then(b=>{b&&this.expenseService.DeleteExpenses([l.id]).pipe((0,C.pQ)(this.destroyRef)).subscribe(O=>{O?(this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success")),this.GetAllExpenses()):this.toastService.showError(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Fail"))})})}onActionBegin(l){if("sorting"===l.requestType){if(this.columnName=l.columnName,this.direction=l.direction,this.sort={columnName:l.columnName,direction:l.direction},this.columnName)return void this.GetAllExpenses();this.sortOptions={columns:[]},this.sort=null,this.GetAllExpenses()}}ngOnDestroy(){this.idTime&&clearTimeout(this.idTime),this._subscriptions.forEach(l=>l.unsubscribe())}}return a=h,(0,i.A)(h,"\u0275fac",function(l){return new(l||a)(e.rXU(N.D),e.rXU(B.f),e.rXU(y.c$),e.rXU(R.nX),e.rXU(f.Z),e.rXU(j.k),e.rXU(R.Ix),e.rXU(k.n),e.rXU(e.abz),e.rXU(L))}),(0,i.A)(h,"\u0275cmp",e.VBU({type:a,selectors:[["app-expenses"]],viewQuery:function(l,p){if(1&l&&e.GBs(w,5),2&l){let E;e.mGM(E=e.lsd())&&(p.grid=E.first)}},standalone:!0,features:[e.Jv_([f.Z]),e.aNF],decls:19,vars:12,consts:[["grid",""],["template",""],["contentPopover",""],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"flex","items-center","gap-2"],[1,"button-outline","button-size-md"],["src","../../../assets/img/icon/ic_download.svg","alt","icon"],[1,"button-size-md","button-primary"],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"w-[200px]"],[3,"onChange","placeholder"],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],[1,"w-full","mt-[12px]",3,"ngClass"],[1,"button-size-md","button-primary",3,"click"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"],["size","lg"],[1,"customTable",3,"actionBegin","dataSource","sortSettings","allowSorting","allowSelection"],["width","200","field","expensesName",3,"headerText"],["width","200",3,"headerText"],["width","120","field","date",3,"headerText"],["width","230","field","paidAmount",3,"headerText"],["width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","flex-col"],[1,"text-text-primary","text-text-md-semibold","line-clamp-1"],[1,"text-text-tertiary","text-text-sm-regular","line-clamp-1"],[1,"text-text-primary","text-text-md-regular","line-clamp-1"],[1,"text-text-md-regular","line-clamp-1","text-text-primary"],[1,"text-text-primary","text-text-md-semibold"],[1,"flex","items-center"],[3,"matTooltip","size","bgColor","name"],[1,"pl-1","line-clamp-1"],[1,"w-full","flex","gap-[8px]","flex-wrap"],["text","Unbilled",3,"status"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"]],template:function(l,p){1&l&&(e.j41(0,"div",3)(1,"div",4)(2,"p",5),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.j41(5,"div",6)(6,"button",7),e.nrm(7,"img",8),e.EFF(8),e.nI1(9,"translate"),e.k0s(),e.DNE(10,X,4,3,"button",9),e.k0s()()(),e.j41(11,"div",10)(12,"div",11)(13,"app-inno-input-search",12),e.bIt("onChange",function(b){return p.handleSearch(b)}),e.k0s()(),e.j41(14,"div",13)(15,"app-inno-datepicker",14),e.nI1(16,"translate"),e.bIt("onChange",function(b){return p.handleChangeDateFilter(b)}),e.k0s()()(),e.DNE(17,K,2,0,"div",15)(18,ae,26,27,"div",16)),2&l&&(e.R7$(3),e.SpI(" ",e.bMT(4,6,"EXPENSES.Title")," "),e.R7$(5),e.SpI(" ",e.bMT(9,8,"EXPENSES.ExportButton")," "),e.R7$(2),e.vxM(p.isAdmin?10:-1),e.R7$(3),e.Y8G("value",p.search),e.R7$(2),e.Y8G("placeholder",e.bMT(16,10,"EXPENSES.DatePlaceholder")),e.R7$(2),e.vxM(p.isLoading?17:18))},dependencies:[_.gFV,_._ab,_.eeu,_.rFS,_.LGG,_.cvh,_.iov,_.BzB,d.G,U.YU,U.vh,y.D9,_.pc9,g.uc,g.oV,I.mC,I.fw,T.Q,r.p,u.M,m.G,S.K,P.A,x.f,t.x],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),h})()}}]);