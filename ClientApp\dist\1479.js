"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1479],{1479:($,d,l)=>{l.r(d),l.d(d,{PlanSelectionComponent:()=>C});var a=l(9842),e=l(4438),p=l(6146),_=l(33),c=l(9079),h=l(1342),x=l(3492),u=l(7152),v=l(3114),m=l(177),g=l(5236);function b(r,s){1&r&&(e.qex(0),e.nrm(1,"img",5),e.j41(2,"span",13),e.EFF(3,"Review & Confirm"),e.k0s(),e.bVm())}function f(r,s){1&r&&(e.j41(0,"div",34)(1,"span",35),e.qSk(),e.j41(2,"svg",36),e.nrm(3,"path",37),e.k0s(),e.<PERSON>FF(4," Recommended "),e.k0s()())}function P(r,s){if(1&r&&(e.j41(0,"li",38)(1,"span",39),e.qSk(),e.j41(2,"svg",40),e.nrm(3,"polyline",41),e.k0s()(),e.joV(),e.j41(4,"span",42),e.EFF(5),e.k0s()()),2&r){const t=s.$implicit;e.R7$(5),e.JRh(t)}}function y(r,s){if(1&r){const t=e.RV6();e.j41(0,"div",23),e.DNE(1,f,5,0,"div",24),e.j41(2,"div",25)(3,"h2",26),e.EFF(4),e.j41(5,"span",27),e.EFF(6," / Month"),e.k0s()(),e.j41(7,"h3",28),e.EFF(8),e.k0s(),e.j41(9,"p",29),e.EFF(10),e.k0s(),e.j41(11,"button",30),e.bIt("click",function(){const i=e.eBV(t).$implicit,o=e.XpG(2);return e.Njj(o.selectPlan(i))}),e.EFF(12),e.k0s()(),e.j41(13,"div",31)(14,"ul",32),e.DNE(15,P,6,1,"li",33),e.k0s()()()}if(2&r){const t=s.$implicit,n=e.XpG(2);e.AVh("border-green-600",t.isRecommended)("relative",t.isRecommended),e.R7$(),e.Y8G("ngIf",t.isRecommended),e.R7$(3),e.SpI(" $",n.getDisplayPrice(t),""),e.R7$(4),e.JRh(t.name),e.R7$(2),e.JRh(t.description),e.R7$(),e.AVh("button-primary",0!==t.planType)("button-secondary",0===t.planType),e.R7$(),e.SpI(" ",0===t.planType?"Free Access":"Get Started"," "),e.R7$(3),e.Y8G("ngForOf",t.features)}}function F(r,s){if(1&r){const t=e.RV6();e.j41(0,"div")(1,"div",14)(2,"span",15),e.EFF(3,"Monthly"),e.k0s(),e.j41(4,"div",16),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.toggleBillingInterval())}),e.nrm(5,"div",17)(6,"div",18),e.k0s(),e.j41(7,"span",19),e.EFF(8,"Yearly "),e.j41(9,"span",20),e.EFF(10,"Save 10%"),e.k0s()()(),e.j41(11,"div",21),e.DNE(12,y,16,14,"div",22),e.k0s()()}if(2&r){const t=e.XpG();e.R7$(2),e.AVh("text-text-primary","month"===t.billingInterval)("text-text-secondary","month"!==t.billingInterval),e.R7$(4),e.AVh("translate-x-6","year"===t.billingInterval),e.R7$(),e.AVh("text-text-primary","year"===t.billingInterval)("text-text-secondary","year"!==t.billingInterval),e.R7$(5),e.Y8G("ngForOf",t.plans)}}function E(r,s){if(1&r&&(e.j41(0,"span"),e.EFF(1),e.k0s()),2&r){const t=e.XpG(2);e.R7$(),e.SpI("(~$",(t.selectedPlan.yearlyPrice/12).toFixed(2)," per month)")}}function k(r,s){if(1&r&&(e.j41(0,"li",38)(1,"span",39),e.qSk(),e.j41(2,"svg",40),e.nrm(3,"polyline",41),e.k0s()(),e.joV(),e.j41(4,"span",42),e.EFF(5),e.k0s()()),2&r){const t=s.$implicit;e.R7$(5),e.JRh(t)}}function I(r,s){if(1&r){const t=e.RV6();e.j41(0,"div",56)(1,"p",71),e.EFF(2,"Additional Team Members"),e.k0s(),e.j41(3,"p",72),e.EFF(4),e.k0s(),e.j41(5,"div",35)(6,"button",73),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.decreaseAdditionalUsers())}),e.EFF(7," - "),e.k0s(),e.j41(8,"span",74),e.EFF(9),e.k0s(),e.j41(10,"button",75),e.bIt("click",function(){e.eBV(t);const i=e.XpG(2);return e.Njj(i.increaseAdditionalUsers())}),e.EFF(11," + "),e.k0s()()()}if(2&r){const t=e.XpG(2);e.R7$(4),e.SpI(" $",t.selectedPlan.additionalUserPrice," per user per month "),e.R7$(2),e.Y8G("disabled",0===t.additionalUsers),e.R7$(3),e.JRh(t.additionalUsers)}}function j(r,s){if(1&r&&(e.j41(0,"div",66)(1,"p",42),e.EFF(2),e.k0s(),e.j41(3,"p",42),e.EFF(4),e.k0s()()),2&r){const t=e.XpG(2);e.R7$(2),e.SpI("Additional Users (",t.additionalUsers,")"),e.R7$(2),e.SpI(" $",(t.selectedPlan.additionalUserPrice*t.additionalUsers).toFixed(2),"/mo ")}}function R(r,s){if(1&r&&(e.j41(0,"p",76),e.EFF(1),e.k0s()),2&r){const t=e.XpG(2);e.R7$(),e.SpI(" Billed as $",(12*t.getTotalPrice()).toFixed(2)," per year ")}}function S(r,s){if(1&r){const t=e.RV6();e.j41(0,"div",43)(1,"div",44)(2,"div",45)(3,"h2",46),e.EFF(4,"Bill Summary"),e.k0s()(),e.j41(5,"div",47)(6,"div",48)(7,"div",49)(8,"div",50)(9,"div",51),e.qSk(),e.j41(10,"svg",52),e.nrm(11,"path",53),e.k0s()(),e.joV(),e.j41(12,"div")(13,"h3",54),e.EFF(14),e.k0s(),e.j41(15,"p",55),e.EFF(16),e.k0s()()(),e.j41(17,"div",56)(18,"p",57),e.EFF(19,"Billing Interval"),e.k0s(),e.j41(20,"div",58)(21,"span",59),e.EFF(22,"Monthly"),e.k0s(),e.j41(23,"div",16),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.toggleBillingInterval())}),e.nrm(24,"div",17)(25,"div",18),e.k0s(),e.j41(26,"span",60),e.EFF(27,"Yearly "),e.j41(28,"span",20),e.EFF(29,"Save 10%"),e.k0s()()()(),e.j41(30,"div",56)(31,"p",57),e.EFF(32,"Base Price"),e.k0s(),e.j41(33,"p",27),e.EFF(34),e.DNE(35,E,2,1,"span",7),e.k0s()()(),e.j41(36,"div",49)(37,"h3",61),e.EFF(38,"Included Features"),e.k0s(),e.j41(39,"ul",62),e.DNE(40,k,6,1,"li",33),e.k0s()(),e.j41(41,"div",63)(42,"h3",61),e.EFF(43,"Payment Summary"),e.k0s(),e.DNE(44,I,12,3,"div",64),e.j41(45,"div",65)(46,"div",66)(47,"p",42),e.EFF(48,"Base Plan"),e.k0s(),e.j41(49,"p",42),e.EFF(50),e.k0s()(),e.DNE(51,j,5,2,"div",67),e.j41(52,"div",68)(53,"p",54),e.EFF(54,"Total"),e.k0s(),e.j41(55,"div")(56,"p",54),e.EFF(57),e.k0s(),e.DNE(58,R,2,1,"p",69),e.k0s()()(),e.j41(59,"button",70),e.bIt("click",function(){e.eBV(t);const i=e.XpG();return e.Njj(i.subscribeToPlan())}),e.EFF(60),e.k0s()()()()()()}if(2&r){const t=e.XpG();e.R7$(14),e.JRh(t.selectedPlan.name),e.R7$(2),e.JRh(t.selectedPlan.description),e.R7$(5),e.AVh("text-text-primary","month"===t.billingInterval)("text-text-secondary","month"!==t.billingInterval),e.R7$(4),e.AVh("translate-x-6","year"===t.billingInterval),e.R7$(),e.AVh("text-text-primary","year"===t.billingInterval)("text-text-secondary","year"!==t.billingInterval),e.R7$(8),e.Lme(" $","month"===t.billingInterval?t.selectedPlan.monthlyPrice:t.selectedPlan.yearlyPrice," per ",t.billingInterval," "),e.R7$(),e.Y8G("ngIf","year"===t.billingInterval),e.R7$(5),e.Y8G("ngForOf",t.selectedPlan.features),e.R7$(4),e.Y8G("ngIf",t.selectedPlan.additionalUserPrice>0),e.R7$(6),e.SpI(" $","month"===t.billingInterval?t.selectedPlan.monthlyPrice:(t.selectedPlan.yearlyPrice/12).toFixed(2),"/mo "),e.R7$(),e.Y8G("ngIf",t.additionalUsers>0),e.R7$(6),e.SpI(" $",t.getTotalPrice().toFixed(2),"/mo "),e.R7$(),e.Y8G("ngIf","year"===t.billingInterval),e.R7$(2),e.SpI(" ",0===t.selectedPlan.planType?"Activate Free Plan":"Subscribe Now"," ")}}let C=(()=>{var r;class s{constructor(){(0,a.A)(this,"plans",[]),(0,a.A)(this,"selectedPlan",null),(0,a.A)(this,"billingInterval","month"),(0,a.A)(this,"additionalUsers",0),(0,a.A)(this,"isLoading",!1),(0,a.A)(this,"planService",(0,e.WQX)(v.J)),(0,a.A)(this,"stripeService",(0,e.WQX)(u.d)),(0,a.A)(this,"router",(0,e.WQX)(_.Ix)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"spinnerService",(0,e.WQX)(h.D)),(0,a.A)(this,"toastService",(0,e.WQX)(x.f))}ngOnInit(){this.loadPlans()}loadPlans(){this.isLoading=!0,this.spinnerService.show(),this.planService.getAllPlans().pipe((0,c.pQ)(this.destroyRef)).subscribe({next:n=>{this.plans=n,this.plans.forEach(i=>{(!i.features||0===i.features.length)&&(i.features=this.generateFeaturesFromPlan(i))}),this.isLoading=!1,this.spinnerService.hide()},error:n=>{console.error("Error loading plans:",n),this.isLoading=!1,this.spinnerService.hide(),this.toastService.showError("Error","Failed to load plans")}})}generateFeaturesFromPlan(n){const i=[];return n.hasUnlimitedClients?i.push("Unlimited customers"):n.maxClients>0&&i.push(`Up to ${n.maxClients} customers`),n.hasUnlimitedUsers?i.push("Unlimited users"):n.maxTeamMembers>0&&i.push(`Up to ${n.maxTeamMembers} team members`),n.hasUnlimitedInvoices&&i.push("Unlimited number of invoices"),n.hasTimeTracking&&i.push("Time tracking"),n.hasCalendar&&i.push("Calendar"),n.hasDashboard&&i.push("Dashboard"),n.hasExpenses&&i.push("Expenses"),n.hasEstimates&&i.push("Estimates"),n.hasIntegrations&&i.push("Integrations"),n.hasReports&&i.push("Reports"),n.hasAccountingUser&&i.push("Accounting User"),n.hasContractingUser&&i.push("Contracting User"),n.additionalUserPrice>0&&i.push(`Additional $${n.additionalUserPrice} per user per month`),i}selectPlan(n){0!==n.planType&&(this.selectedPlan=n)}toggleBillingInterval(){this.billingInterval="month"===this.billingInterval?"year":"month"}getDisplayPrice(n){return"month"===this.billingInterval?n.monthlyPrice:n.yearlyPrice/12}getTotalPrice(){return this.selectedPlan?this.getDisplayPrice(this.selectedPlan)+this.selectedPlan.additionalUserPrice*this.additionalUsers:0}increaseAdditionalUsers(){this.additionalUsers++}decreaseAdditionalUsers(){this.additionalUsers>0&&this.additionalUsers--}subscribeToPlan(){this.selectedPlan?(this.spinnerService.show(),this.stripeService.createPaymentSession("month"===this.billingInterval?this.selectedPlan.monthlyPrice+this.selectedPlan.additionalUserPrice*this.additionalUsers:this.selectedPlan.yearlyPrice+12*this.selectedPlan.additionalUserPrice*this.additionalUsers,"USD",`${this.selectedPlan.name} Subscription`,"subscription",this.billingInterval,this.selectedPlan.id,this.additionalUsers).pipe((0,c.pQ)(this.destroyRef)).subscribe({next:i=>{this.spinnerService.hide(),localStorage.setItem("selectedPlanId",this.selectedPlan.id),localStorage.setItem("billingInterval",this.billingInterval),localStorage.setItem("additionalUsers",this.additionalUsers.toString()),i&&i.url?window.location.href=i.url:this.toastService.showError("Error","Failed to initialize payment")},error:i=>{this.spinnerService.hide(),this.toastService.showError("Error","Failed to initialize payment: "+i.message)}})):this.toastService.showError("Error","Please select a plan first")}goBack(){this.router.navigate(["/billing"])}backToPlans(){this.selectedPlan=null}}return r=s,(0,a.A)(s,"\u0275fac",function(n){return new(n||r)}),(0,a.A)(s,"\u0275cmp",e.VBU({type:r,selectors:[["app-plan-selection"]],standalone:!0,features:[e.aNF],decls:18,vars:12,consts:[[1,"w-full","pb-3"],[1,"w-full","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","mt-3"],[1,"flex","items-center","text-sm"],["href","javascript:void(0)",1,"text-text-secondary","hover:text-text-primary",3,"click"],["src","../../../../assets/img/icon/ic_arrow_right.svg","alt","Icon"],["href","javascript:void(0)",1,"hover:text-text-primary",3,"click"],[4,"ngIf"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2","mt-2","mb-2"],[1,"flex","items-center","gap-[8px]"],[1,"text-text-primary","text-headline-lg-bold"],[1,"container-full","mt-[24px]","max-w-[1200px]","mx-auto"],["class","mt-6",4,"ngIf"],[1,"text-text-primary","font-medium"],[1,"flex","justify-center","items-center","mb-8"],[1,"text-text-md-bold","mr-3"],[1,"relative","inline-block","w-12","h-6","cursor-pointer",3,"click"],[1,"absolute","inset-0","rounded-full","bg-gray-300","transition-all","duration-300"],[1,"absolute","left-0","top-0.5","w-5","h-5","bg-white","rounded-full","shadow","transform","transition-transform","duration-300","ml-0.5"],[1,"text-text-md-bold","ml-3"],[1,"text-green-500","text-text-sm-regular"],[1,"grid","grid-cols-1","md:grid-cols-3","gap-6","mb-8"],["class","border border-border-primary rounded-lg overflow-hidden shadow-sm plan-card",3,"border-green-600","relative",4,"ngFor","ngForOf"],[1,"border","border-border-primary","rounded-lg","overflow-hidden","shadow-sm","plan-card"],["class","absolute top-0 right-0 bg-green-600 text-white py-1 px-3 rounded-bl-lg text-text-sm-bold",4,"ngIf"],[1,"p-6","text-center","border-b","border-border-primary"],[1,"text-headline-lg-bold","text-text-primary","mb-2"],[1,"text-text-md-regular","text-text-secondary"],[1,"text-headline-sm-bold","text-text-primary","mb-2"],[1,"text-text-sm-regular","text-text-secondary","mb-4"],[1,"text-center","button-size-md","button-primary","w-full",3,"click"],[1,"px-6","py-4"],[1,"space-y-3"],["class","flex items-start",4,"ngFor","ngForOf"],[1,"absolute","top-0","right-0","bg-green-600","text-white","py-1","px-3","rounded-bl-lg","text-text-sm-bold"],[1,"flex","items-center"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 20 20","fill","currentColor",1,"h-4","w-4","mr-1"],["fill-rule","evenodd","d","M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule","evenodd"],[1,"flex","items-start"],[1,"text-green-500","mr-2","flex-shrink-0","mt-1"],["xmlns","http://www.w3.org/2000/svg","width","16","height","16","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["points","20 6 9 17 4 12"],[1,"text-text-md-regular","text-text-primary"],[1,"mt-6"],[1,"border","border-border-primary","rounded-lg","overflow-hidden","shadow-md","mb-8"],[1,"bg-gray-50","p-4","border-b","border-border-primary"],[1,"text-headline-md-bold","text-text-primary"],[1,"p-6"],[1,"flex","flex-col","md:flex-row","gap-6"],[1,"md:w-1/3","md:border-r","border-border-primary","md:pr-6"],[1,"flex","items-center","mb-4"],[1,"w-12","h-12","rounded-full","bg-green-100","flex","items-center","justify-center","mr-4"],["xmlns","http://www.w3.org/2000/svg","fill","none","viewBox","0 0 24 24","stroke","currentColor",1,"h-6","w-6","text-green-600"],["stroke-linecap","round","stroke-linejoin","round","stroke-width","2","d","M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"],[1,"text-headline-sm-bold","text-text-primary"],[1,"text-text-sm-regular","text-text-secondary"],[1,"mb-4"],[1,"text-text-md-bold","text-text-primary"],[1,"flex","items-center","mt-2"],[1,"text-text-md-regular","mr-3"],[1,"text-text-md-regular","ml-3"],[1,"text-text-md-bold","text-text-primary","mb-3"],[1,"space-y-2"],[1,"md:w-1/3"],["class","mb-4",4,"ngIf"],[1,"border-t","border-border-primary","pt-4","mt-4"],[1,"flex","justify-between","items-center","mb-2"],["class","flex justify-between items-center mb-2",4,"ngIf"],[1,"flex","justify-between","items-center","mt-4","pt-4","border-t","border-border-primary"],["class","text-text-sm-regular text-text-secondary text-right",4,"ngIf"],[1,"button-primary","button-size-md","w-full","mt-6",3,"click"],[1,"text-text-md-bold","text-text-primary","mb-2"],[1,"text-text-sm-regular","text-text-secondary","mb-2"],[1,"border","border-border-primary","rounded-full","w-8","h-8","flex","items-center","justify-center",3,"click","disabled"],[1,"mx-4","text-text-md-bold"],[1,"border","border-border-primary","rounded-full","w-8","h-8","flex","items-center","justify-center",3,"click"],[1,"text-text-sm-regular","text-text-secondary","text-right"]],template:function(n,i){1&n&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"a",4),e.bIt("click",function(){return i.goBack()}),e.EFF(5,"Billing & Plans"),e.k0s(),e.nrm(6,"img",5),e.j41(7,"a",6),e.bIt("click",function(){return i.backToPlans()}),e.EFF(8,"Choose Plan"),e.k0s(),e.DNE(9,b,4,0,"ng-container",7),e.k0s()(),e.j41(10,"div",8)(11,"div",9)(12,"p",10),e.EFF(13),e.nI1(14,"translate"),e.k0s()()()(),e.j41(15,"div",11),e.DNE(16,F,13,11,"div",7)(17,S,61,22,"div",12),e.k0s()()),2&n&&(e.R7$(7),e.AVh("text-text-primary",!i.selectedPlan)("text-text-secondary",i.selectedPlan)("font-medium",!i.selectedPlan),e.R7$(2),e.Y8G("ngIf",i.selectedPlan),e.R7$(4),e.SpI(" ",e.bMT(14,10,"Choose Plan")," "),e.R7$(3),e.Y8G("ngIf",!i.selectedPlan),e.R7$(),e.Y8G("ngIf",i.selectedPlan))},dependencies:[p.G,m.Sq,m.bT,g.D9],styles:[".border-green-600[_ngcontent-%COMP%]{border-color:#059669;border-width:2px}.plan-card[_ngcontent-%COMP%]{transition:transform .3s ease,box-shadow .3s ease}.plan-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d}@keyframes _ngcontent-%COMP%_checkmark-appear{0%{opacity:0;transform:scale(.5)}to{opacity:1;transform:scale(1)}}.text-green-500[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_checkmark-appear .3s ease-out forwards}"]})),s})()}}]);