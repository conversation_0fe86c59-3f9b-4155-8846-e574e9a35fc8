"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[655],{6408:(y,E,o)=>{o.d(E,{G:()=>c});var i=o(9842),e=o(4438),t=o(3924);let c=(()=>{var f;class g{constructor(){(0,i.A)(this,"mode","day"),(0,i.A)(this,"id",void 0),(0,i.A)(this,"enableMask",void 0),(0,i.A)(this,"name",void 0),(0,i.A)(this,"format","dd-MM-yyyy"),(0,i.A)(this,"placeholder",void 0),(0,i.A)(this,"value",void 0),(0,i.A)(this,"onChange",new e.bkB),(0,i.A)(this,"datePickerValue",void 0),(0,i.A)(this,"fields",{text:"label",value:"value"}),(0,i.A)(this,"start","Month"),(0,i.A)(this,"depth","Month"),(0,i.A)(this,"showTodayButton",!0),(0,i.A)(this,"weekNumber",!1)}ngOnChanges(n){const a=n.mode?.currentValue??n.value?.currentValue;a&&this.updateModeSettings(a)}updateModeSettings(n){switch(n){case"day":this.start="Month",this.depth="Month",this.showTodayButton=!0,this.weekNumber=!1;break;case"week":this.start="Month",this.depth="Month",this.showTodayButton=!1,this.weekNumber=!0;break;case"month":this.start="Year",this.depth="Year",this.showTodayButton=!1,this.weekNumber=!1}}handleChangeValue(n){this.onChange.emit(n?.value??void 0)}}return f=g,(0,i.A)(g,"\u0275fac",function(n){return new(n||f)}),(0,i.A)(g,"\u0275cmp",e.VBU({type:f,selectors:[["app-inno-datepicker"]],inputs:{mode:"mode",id:"id",enableMask:"enableMask",name:"name",format:"format",placeholder:"placeholder",value:"value"},outputs:{onChange:"onChange"},standalone:!0,features:[e.OA$,e.aNF],decls:1,vars:10,consts:[[1,"customDatePickerV2",3,"change","name","id","format","enableMask","placeholder","value","start","depth","showTodayButton","weekNumber"]],template:function(n,a){1&n&&(e.j41(0,"ejs-datepicker",0),e.bIt("change",function(l){return a.handleChangeValue(l)}),e.k0s()),2&n&&(e.FS9("name",a.name||""),e.FS9("id",a.id||""),e.Y8G("format",a.format)("enableMask",a.enableMask)("placeholder",a.placeholder)("value",a.value)("start",a.start)("depth",a.depth)("showTodayButton",a.showTodayButton)("weekNumber",a.weekNumber))},dependencies:[t.tZ,t.I],styles:[".customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{margin:0!important;padding:8px 12px;display:flex;min-width:125px!important;height:40px!important;border-radius:8px}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]{background-color:var(--bg-primary)}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%] > input[_ngcontent-%COMP%]{padding:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-input-group[_ngcontent-%COMP%]:after{display:none}.customDatePickerV2[_ngcontent-%COMP%]   .e-input-group-icon.e-date-icon[_ngcontent-%COMP%]{margin:0}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons[_ngcontent-%COMP%]{min-height:unset!important}.customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%]::selection, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:before, .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]:after, .e-datepicker[_ngcontent-%COMP%]   .e-focused-date.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-cell.e-selected[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%]{background-color:#0f182e!important}.customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]   .e-icons.e-active[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   input.e-input[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:not(.e-focused-date)   .e-day[_ngcontent-%COMP%], .e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]{color:#0f182e!important}.e-datepicker[_ngcontent-%COMP%]   .e-model-header[_ngcontent-%COMP%]{background-color:#fff!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]   .e-day[_ngcontent-%COMP%], .customDatePickerV2[_ngcontent-%COMP%]   .e-date-wrapper[_ngcontent-%COMP%]{border:2px solid #E9EAEB!important}.e-datepicker[_ngcontent-%COMP%]   .e-today[_ngcontent-%COMP%]:hover{background-color:#24242433!important}"]})),g})()},3200:(y,E,o)=>{o.d(E,{J:()=>g});var i=o(9842),e=o(177),t=o(5236),c=o(4438);function f(d,n){if(1&d&&(c.j41(0,"p",4),c.EFF(1),c.nI1(2,"translate"),c.k0s()),2&d){const a=c.XpG();c.R7$(),c.SpI(" ",c.bMT(2,1,a.description)," ")}}let g=(()=>{var d;class n{constructor(){(0,i.A)(this,"title",""),(0,i.A)(this,"description",""),(0,i.A)(this,"icon",""),(0,i.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return d=n,(0,i.A)(n,"\u0275fac",function(s){return new(s||d)}),(0,i.A)(n,"\u0275cmp",c.VBU({type:d,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[c.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(s,l){1&s&&(c.j41(0,"div",0),c.nrm(1,"img",1),c.j41(2,"div",2)(3,"p",3),c.EFF(4),c.nI1(5,"translate"),c.nI1(6,"translate"),c.k0s(),c.DNE(7,f,3,3,"p",4),c.k0s()()),2&s&&(c.R7$(),c.Y8G("src",l.icon||l.defaultIcon,c.B4B),c.R7$(3),c.SpI(" ",l.title?c.bMT(5,3,l.title):c.bMT(6,5,"COMMON.EmptyData")," "),c.R7$(3),c.vxM(l.description?7:-1))},dependencies:[e.MD,t.h,t.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),n})()},5316:(y,E,o)=>{o.d(E,{X:()=>c});var i=o(9842),e=o(4438);const t=["inputField"];let c=(()=>{var f;class g{constructor(){(0,i.A)(this,"inputField",void 0),(0,i.A)(this,"value",""),(0,i.A)(this,"dynamicWidth",0),(0,i.A)(this,"onChange",new e.bkB),(0,i.A)(this,"previewTimeEnd",""),(0,i.A)(this,"isAlreadyEdit",!1)}handleSetTimeEnd(n){this.isAlreadyEdit=!0,this.previewTimeEnd=n?.target?.value??""}ngAfterViewInit(){this.inputField&&this.inputField.nativeElement.focus()}ngOnChanges(n){let a="00:00";const s=n.mode?.currentValue??n.value?.currentValue;s&&(a=s),this.previewTimeEnd="00:00"===a?"":a.substring(0,5)}updateTimeEnd(n){this.onChange.emit({newHours:n,isAlreadyEdit:this.isAlreadyEdit}),this.isAlreadyEdit=!1,this.previewTimeEnd="00:00"===n?"":n}onBlur(){if(!this.previewTimeEnd)return this.updateTimeEnd("00:00");let n=this.previewTimeEnd;if(n=n.replace(/[^0-9:]/g,""),-1!==n.indexOf(":")){const C=n.split(":");2===C.length&&""===C[1]?n=C[0]+":00":C.length>2&&(n=C[0]+":"+(C[1]||"00"))}let[a,s]=n.split(":");s||(s="00"),a&&Number(a)>23?a="23":a&&Number(a)<10&&1===a.length&&(a="0"+a),s&&Number(s)>59?s="59":s&&Number(s)<10&&1===s.length&&(s="0"+s),this.updateTimeEnd(`${a||"00"}:${s||"00"}`)}}return f=g,(0,i.A)(g,"\u0275fac",function(n){return new(n||f)}),(0,i.A)(g,"\u0275cmp",e.VBU({type:f,selectors:[["app-inno-enter-edit-hours"]],viewQuery:function(n,a){if(1&n&&e.GBs(t,5),2&n){let s;e.mGM(s=e.lsd())&&(a.inputField=s.first)}},inputs:{value:"value",dynamicWidth:"dynamicWidth"},outputs:{onChange:"onChange"},standalone:!0,features:[e.OA$,e.aNF],decls:2,vars:3,consts:[["inputField",""],["type","text","placeholder","HH:MM",1,"text-left","text-text-md-regular","text-text-secondary","p-[8px]","rounded-md","border-2","border-border-primary","h-[40px]",3,"keyup","blur","value"]],template:function(n,a){if(1&n){const s=e.RV6();e.j41(0,"input",1,0),e.bIt("keyup",function(C){return e.eBV(s),e.Njj(a.handleSetTimeEnd(C))})("blur",function(){return e.eBV(s),e.Njj(a.onBlur())}),e.k0s()}2&n&&(e.xc7("width",a.dynamicWidth+"px"),e.Y8G("value",a.previewTimeEnd))}})),g})()},8192:(y,E,o)=>{o.d(E,{Y:()=>f});var i=o(9842),e=o(6146),t=o(4438);function c(g,d){if(1&g&&(t.j41(0,"p",0),t.EFF(1),t.k0s()),2&g){const n=t.XpG();t.R7$(),t.JRh(n.message)}}let f=(()=>{var g;class d{constructor(){(0,i.A)(this,"message","")}}return g=d,(0,i.A)(d,"\u0275fac",function(a){return new(a||g)}),(0,i.A)(d,"\u0275cmp",t.VBU({type:g,selectors:[["app-inno-error-message"]],inputs:{message:"message"},standalone:!0,features:[t.aNF],decls:1,vars:1,consts:[[1,"errorMessage"]],template:function(a,s){1&a&&t.DNE(0,c,2,1,"p",0),2&a&&t.vxM(s.message?0:-1)},dependencies:[e.G],styles:['.errorMessage[_ngcontent-%COMP%]{font-size:12px;line-height:18px;margin-top:4px;margin-bottom:0;color:var(--text-danger)}.errorMessage[_ngcontent-%COMP%]:empty{display:none}.errorMessage[_ngcontent-%COMP%]:before{content:"* "}']})),d})()},7656:(y,E,o)=>{o.d(E,{V:()=>n});var i=o(9842),e=o(4438),t=o(6146),c=o(9417);const f=["*"];function g(a,s){if(1&a){const l=e.RV6();e.j41(0,"input",5),e.bIt("change",function(b){e.eBV(l);const A=e.XpG();return e.Njj(A.handleChange(b))}),e.k0s()}if(2&a){const l=e.XpG();e.Y8G("checked",l.checked)("formControl",l.formControl)}}function d(a,s){if(1&a){const l=e.RV6();e.j41(0,"input",6),e.bIt("change",function(b){e.eBV(l);const A=e.XpG();return e.Njj(A.handleChange(b))}),e.k0s()}if(2&a){const l=e.XpG();e.Y8G("checked",l.checked)}}let n=(()=>{var a;class s{constructor(){(0,i.A)(this,"checked",void 0),(0,i.A)(this,"onChange",new e.bkB),(0,i.A)(this,"formControl",void 0),(0,i.A)(this,"errorMessages",void 0)}registerOnChange(C){}registerOnTouched(C){}setDisabledState(C){}writeValue(C){}handleChange(C){this.onChange.emit(C?.target?.checked??!1)}}return a=s,(0,i.A)(s,"\u0275fac",function(C){return new(C||a)}),(0,i.A)(s,"\u0275cmp",e.VBU({type:a,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:c.kq,useExisting:(0,e.Rfq)(()=>a),multi:!0}]),e.aNF],ngContentSelectors:f,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(C,b){1&C&&(e.NAR(),e.j41(0,"div",0)(1,"label",1),e.DNE(2,g,1,2,"input",2)(3,d,1,1,"input",3),e.j41(4,"div",4),e.SdG(5),e.k0s()()()),2&C&&(e.R7$(2),e.vxM(b.formControl?2:3))},dependencies:[t.G,c.Zm,c.BC,c.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),s})()},4262:(y,E,o)=>{o.d(E,{B:()=>p});var i=o(467),e=o(9842),t=o(4438),c=o(6146),f=o(5599),g=o(3652),d=o(1970),n=o(6463),a=o(6812),s=o(177),l=o(5236);const C=u=>({"pl-8":u});function b(u,D){1&u&&t.eu8(0,4)}function A(u,D){if(1&u&&t.DNE(0,b,1,0,"ng-container",6),2&u){const I=t.XpG();t.Y8G("ngTemplateOutlet",I.templateTrigger)}}function k(u,D){1&u&&(t.j41(0,"button",5),t.EFF(1," Select project or client "),t.nrm(2,"img",7),t.k0s())}function R(u,D){if(1&u&&t.nrm(0,"ngx-avatars",11),2&u){const I=t.XpG().$implicit;t.Y8G("size",32)("name",I.label)}}function v(u,D){1&u&&(t.j41(0,"div",12),t.nrm(1,"img",16),t.k0s())}function M(u,D){if(1&u&&(t.j41(0,"p",15),t.EFF(1),t.k0s()),2&u){const I=t.XpG().$implicit;t.R7$(),t.SpI(" ",I.description," ")}}function r(u,D){if(1&u){const I=t.RV6();t.j41(0,"div",10),t.bIt("click",function(){const h=t.eBV(I).$implicit,S=t.XpG(2);return t.Njj(S.handleChooseOption(h))}),t.DNE(1,R,1,2,"ngx-avatars",11)(2,v,2,0,"div",12),t.j41(3,"div",13)(4,"p",14),t.EFF(5),t.k0s(),t.DNE(6,M,2,1,"p",15),t.k0s()()}if(2&u){const I=D.$implicit,_=t.XpG(2);t.AVh("selected",I.value===_.value),t.Y8G("ngClass",t.eq3(6,C,_.isProjectOption(I))),t.R7$(),t.vxM("client"==(null==I||null==I.metadata?null:I.metadata.type)?1:2),t.R7$(4),t.SpI(" ",I.label," "),t.R7$(),t.vxM(I.description?6:-1)}}function m(u,D){if(1&u){const I=t.RV6();t.j41(0,"button",17),t.bIt("click",function(){t.eBV(I);const h=t.XpG(2);return t.Njj(h.handleCreateNewProject())}),t.nrm(1,"img",18),t.EFF(2),t.nI1(3,"translate"),t.k0s()}2&u&&(t.R7$(2),t.SpI(" ",t.bMT(3,1,"PROJECT.NewAProject")," "))}function P(u,D){if(1&u){const I=t.RV6();t.j41(0,"div",8)(1,"app-inno-input-search-result",9),t.nI1(2,"translate"),t.bIt("onChange",function(h){t.eBV(I);const S=t.XpG();return t.Njj(S.handleSearch(h))}),t.DNE(3,r,7,8,"ng-template",null,1,t.C5r)(5,m,4,3,"ng-template",null,2,t.C5r),t.k0s()()}if(2&u){const I=t.sdS(4),_=t.sdS(6),h=t.XpG();t.R7$(),t.Y8G("placeholder",t.bMT(2,8,"COMMON.SearchProjectsClients"))("data",h.listOptionPreview)("isNotFound",!h.listOptionPreview.length)("isEmptyData",!h.listOptionOriginal.length)("isLoading",h.isLoading)("optionTemplate",I)("footerTemplate",h.isShowCreateButton?_:null)("defaultValue",h.defaultTextSearch)}}let p=(()=>{var u;class D{constructor(_){(0,e.A)(this,"addProjectDialog",void 0),(0,e.A)(this,"templateTrigger",null),(0,e.A)(this,"defaultTextSearch",""),(0,e.A)(this,"isShowCreateButton",!0),(0,e.A)(this,"isOnlySelectProject",!1),(0,e.A)(this,"isTimetracking",!1),(0,e.A)(this,"value",void 0),(0,e.A)(this,"placeholderSearch",void 0),(0,e.A)(this,"onSelect",new t.bkB),(0,e.A)(this,"onGetInfoSelectedValue",new t.bkB),(0,e.A)(this,"listOptionPreview",[]),(0,e.A)(this,"listOptionOriginal",[]),(0,e.A)(this,"isLoading",!1),(0,e.A)(this,"dropdownOptionService",(0,t.WQX)(n.R)),(0,e.A)(this,"unsubscribe",[]),(0,e.A)(this,"searchResultComponent",void 0),this.addProjectDialog=_}ngOnInit(){this.value&&this.loadData(!0)}loadData(_){var h=this;return(0,i.A)(function*(){h.isLoading=!0;const S=1==h.isTimetracking?yield h.dropdownOptionService.getDropdownOptionsProjectAndClientTimeTracking():yield h.dropdownOptionService.getDropdownOptionsProjectAndClient();if(h.listOptionOriginal=S,h.handleSearch(h.defaultTextSearch),_&&h.value){const T=h.listOptionOriginal.find(O=>O.value==h.value);h.onGetInfoSelectedValue.emit(T)}h.isLoading=!1})()}handleSearch(_){if(_=_?.trim()?.toLowerCase(),!_?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(S=>S.label.toLowerCase().indexOf(_)>-1);const h=[];this.listOptionPreview.forEach(S=>{let T=!1,O=!1;this.listOptionOriginal.filter(x=>"project"==x.metadata.type).forEach(x=>{if(S.value==x.metadata.objectClient.id)T||(h.push(S),T=!0),h.push(x);else{let j=this.listOptionPreview.find(B=>"client"==B.metadata?.type);if(!T&&!j){let B=this.listOptionOriginal.find(w=>w.metadata?.client?.id==S.metadata?.objectClient?.id),F=h.find(w=>w.value==S.metadata?.objectClient?.id);F||(O=!0,h.push(B)),(O||F)&&(h.push(S),T=!0)}}})}),this.listOptionPreview=h}handleCreateNewProject(){this.addProjectDialog.open(null),this.handleCloseSearchResult()}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}isProjectOption(_){return"project"==_.metadata?.type}handleChooseOption(_){this.isOnlySelectProject&&"project"!=_.metadata?.type||_.value!=this.value&&(this.onSelect.emit(_),this.handleCloseSearchResult())}ngOnDestroy(){this.unsubscribe.forEach(_=>_.unsubscribe())}}return u=D,(0,e.A)(D,"\u0275fac",function(_){return new(_||u)(t.rXU(a.J))}),(0,e.A)(D,"\u0275cmp",t.VBU({type:u,selectors:[["app-inno-select-search-project"]],viewQuery:function(_,h){if(1&_&&t.GBs(f.x,5),2&_){let S;t.mGM(S=t.lsd())&&(h.searchResultComponent=S.first)}},inputs:{templateTrigger:"templateTrigger",defaultTextSearch:"defaultTextSearch",isShowCreateButton:"isShowCreateButton",isOnlySelectProject:"isOnlySelectProject",isTimetracking:"isTimetracking",value:"value",placeholderSearch:"placeholderSearch"},outputs:{onSelect:"onSelect",onGetInfoSelectedValue:"onGetInfoSelectedValue"},standalone:!0,features:[t.aNF],decls:5,vars:4,consts:[["templateSearchProject",""],["optionTemplate",""],["footerTemplate",""],["position","bottom-start",3,"onOpen","content","isClickOnContentToClose","isClearPadding"],["target",""],["target","",1,"dropdown-invisible","flex","gap-[4px]"],["target","",4,"ngTemplateOutlet"],["src","../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"w-[16px]","translate-y-[2px]"],[1,"min-w-[320px]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isLoading","optionTemplate","footerTemplate","defaultValue"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-s[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(_,h){if(1&_){const S=t.RV6();t.j41(0,"app-inno-popover",3),t.bIt("onOpen",function(){return t.eBV(S),t.Njj(h.loadData())}),t.DNE(1,A,1,1,"ng-container",4)(2,k,3,0,"button",5)(3,P,7,10,"ng-template",null,0,t.C5r),t.k0s()}if(2&_){const S=t.sdS(4);t.Y8G("content",S)("isClickOnContentToClose",!1)("isClearPadding",!0),t.R7$(),t.vxM(h.templateTrigger?1:2)}},dependencies:[c.G,s.YU,s.T3,l.D9,f.x,g.t,d.mC,d.fw],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}"]})),D})()},8194:(y,E,o)=>{o.d(E,{H:()=>I});var i=o(467),e=o(9842),t=o(3814),c=o(5402),f=o(3652),g=o(6146),d=o(6463),n=o(4438),a=o(5599),s=o(1970),l=o(5331),C=o(177),b=o(5236);const A=_=>({"pl-8":_});function k(_,h){1&_&&n.eu8(0,4)}function R(_,h){if(1&_&&n.DNE(0,k,1,0,"ng-container",6),2&_){const S=n.XpG();n.Y8G("ngTemplateOutlet",S.templateTrigger)}}function v(_,h){1&_&&(n.j41(0,"button",5),n.EFF(1," Select project or client "),n.nrm(2,"img",7),n.k0s())}function M(_,h){if(1&_&&n.nrm(0,"ngx-avatars",11),2&_){const S=n.XpG().$implicit;n.Y8G("size",32)("name",S.label)}}function r(_,h){1&_&&(n.j41(0,"div",12),n.nrm(1,"img",16),n.k0s())}function m(_,h){if(1&_&&(n.j41(0,"p",15),n.EFF(1),n.k0s()),2&_){const S=n.XpG().$implicit;n.R7$(),n.SpI(" ",S.description," ")}}function P(_,h){if(1&_){const S=n.RV6();n.j41(0,"div",10),n.bIt("click",function(){const O=n.eBV(S).$implicit,x=n.XpG(2);return n.Njj(x.handleChooseOption(O))}),n.DNE(1,M,1,2,"ngx-avatars",11)(2,r,2,0,"div",12),n.j41(3,"div",13)(4,"p",14),n.EFF(5),n.k0s(),n.DNE(6,m,2,1,"p",15),n.k0s()()}if(2&_){const S=h.$implicit,T=n.XpG(2);n.AVh("selected",S.value===T.value),n.Y8G("ngClass",n.eq3(6,A,T.isProjectOption(S))),n.R7$(),n.vxM("client"==(null==S||null==S.metadata?null:S.metadata.type)?1:2),n.R7$(4),n.SpI(" ",S.label," "),n.R7$(),n.vxM(S.description?6:-1)}}function p(_,h){if(1&_){const S=n.RV6();n.j41(0,"button",18),n.bIt("click",function(){n.eBV(S);const O=n.XpG(3);return n.Njj(O.handleCreateNewService())}),n.nrm(1,"img",19),n.EFF(2," Create a new service "),n.k0s()}}function u(_,h){if(1&_&&n.DNE(0,p,3,0,"button",17),2&_){const S=n.XpG(2);n.vxM(S.searchService?0:-1)}}function D(_,h){if(1&_){const S=n.RV6();n.j41(0,"div",8)(1,"app-inno-input-search-result",9),n.nI1(2,"translate"),n.bIt("onChange",function(O){n.eBV(S);const x=n.XpG();return n.Njj(x.handleSearch(O))}),n.DNE(3,P,7,8,"ng-template",null,1,n.C5r)(5,u,1,1,"ng-template",null,2,n.C5r),n.k0s()()}if(2&_){const S=n.sdS(4),T=n.sdS(6),O=n.XpG();n.R7$(),n.Y8G("placeholder",n.bMT(2,8,"COMMON.Search"))("data",O.listOptionPreview)("isNotFound",!O.listOptionPreview.length)("isEmptyData",!O.listOptionOriginal.length)("isLoading",O.isLoading)("optionTemplate",S)("footerTemplate",O.isShowCreateButton?T:null)("defaultValue",O.defaultTextSearch)}}let I=(()=>{var _;class h{constructor(){(0,e.A)(this,"templateTrigger",null),(0,e.A)(this,"defaultTextSearch",""),(0,e.A)(this,"isShowCreateButton",!0),(0,e.A)(this,"value",void 0),(0,e.A)(this,"lable",void 0),(0,e.A)(this,"placeholderSearch",void 0),(0,e.A)(this,"onSelect",new n.bkB),(0,e.A)(this,"onGetInfoSelectedValue",new n.bkB),(0,e.A)(this,"searchService",""),(0,e.A)(this,"listOptionPreview",[]),(0,e.A)(this,"listOptionOriginal",[]),(0,e.A)(this,"isLoading",!1),(0,e.A)(this,"timeout",void 0),(0,e.A)(this,"sort",void 0),(0,e.A)(this,"dropdownOptionService",(0,n.WQX)(d.R)),(0,e.A)(this,"modifyItemAndServiceDialog",(0,n.WQX)(t.v)),(0,e.A)(this,"innoTimerProvider",(0,n.WQX)(l.n)),(0,e.A)(this,"unsubscribe",[]),(0,e.A)(this,"searchResultComponent",void 0)}ngOnInit(){this.value&&this.loadData(!0),this.unsubscribe.push(this.innoTimerProvider.projectChange$.subscribe(T=>{this.value!==T&&(this.value=T,this.loadData(!1),this.listOptionPreview=[],this.listOptionOriginal=[])}))}loadData(T){var O=this;return(0,i.A)(function*(){O.sort={columnName:"serviceName",direction:"Ascending"},O.isLoading=!0;const x=yield O.dropdownOptionService.getDropdownOptionsService(O.value,O.sort);if(O.listOptionOriginal=x,O.handleSearch(O.defaultTextSearch),T&&O.value){const j=O.listOptionOriginal.find(B=>B.value==O.value);O.onGetInfoSelectedValue.emit(j)}O.isLoading=!1})()}handleSearch(T){if(T=T?.trim()?.toLowerCase(),this.searchService=T,!T?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(x=>x.label.toLowerCase().indexOf(T)>-1);const O=[];this.listOptionPreview.forEach(x=>{O.push(x),this.listOptionOriginal.filter(j=>"project"==j.metadata.type).forEach(j=>{x.value==j.metadata.objectClient.id&&O.push(j)})}),this.listOptionPreview=O}handleCreateNewService(){this.searchResultComponent.handleHideContent(),this.modifyItemAndServiceDialog.open({mode:c.Q.Service,serviceInfo:{projectId:this.value,serviceName:this.searchService,projectName:this.lable}}).then(O=>{O.afterClosed().subscribe(x=>{x&&(this.searchResultComponent.handleToggle(),this.loadData(!0),this.timeout=setTimeout(()=>{this.handleSearch(x.serviceName)},100))})})}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}isProjectOption(T){return"project"==T.metadata?.type}handleChooseOption(T){this.onSelect.emit(T),this.handleCloseSearchResult()}ngOnDestroy(){this.unsubscribe.forEach(T=>T.unsubscribe()),this.timeout&&clearTimeout(this.timeout)}}return _=h,(0,e.A)(h,"\u0275fac",function(T){return new(T||_)}),(0,e.A)(h,"\u0275cmp",n.VBU({type:_,selectors:[["app-inno-select-search-service"]],viewQuery:function(T,O){if(1&T&&n.GBs(a.x,5),2&T){let x;n.mGM(x=n.lsd())&&(O.searchResultComponent=x.first)}},inputs:{templateTrigger:"templateTrigger",defaultTextSearch:"defaultTextSearch",isShowCreateButton:"isShowCreateButton",value:"value",lable:"lable",placeholderSearch:"placeholderSearch"},outputs:{onSelect:"onSelect",onGetInfoSelectedValue:"onGetInfoSelectedValue"},standalone:!0,features:[n.aNF],decls:5,vars:4,consts:[["templateSearchProject",""],["optionTemplate",""],["footerTemplate",""],["position","bottom-start",3,"onOpen","content","isClickOnContentToClose","isClearPadding"],["target",""],["target","",1,"dropdown-invisible","flex","gap-[4px]"],["target","",4,"ngTemplateOutlet"],["src","../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"w-[16px]","translate-y-[2px]"],[1,"min-w-[320px]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isLoading","optionTemplate","footerTemplate","defaultValue"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-s[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(T,O){if(1&T){const x=n.RV6();n.j41(0,"app-inno-popover",3),n.bIt("onOpen",function(){return n.eBV(x),n.Njj(O.loadData())}),n.DNE(1,R,1,1,"ng-container",4)(2,v,3,0,"button",5)(3,D,7,10,"ng-template",null,0,n.C5r),n.k0s()}if(2&T){const x=n.sdS(4);n.Y8G("content",x)("isClickOnContentToClose",!1)("isClearPadding",!0),n.R7$(),n.vxM(O.templateTrigger?1:2)}},dependencies:[g.G,C.YU,C.T3,b.D9,a.x,f.t,s.mC,s.fw],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%]{font-size:14px;line-height:20px;font-weight:600}.selected[_ngcontent-%COMP%]   .txtTitle[_ngcontent-%COMP%], .selected[_ngcontent-%COMP%]   .txtDescription[_ngcontent-%COMP%]{color:var(--text-brand-primary)}"]})),h})()},8232:(y,E,o)=>{o.d(E,{R:()=>M});var i=o(9842),e=o(4438),t=o(6146),c=o(5599),f=o(3652),g=o(1970),d=o(7656),n=o(6812),a=o(177),s=o(5236);const l=()=>[];function C(r,m){1&r&&e.eu8(0,4)}function b(r,m){if(1&r&&e.DNE(0,C,1,0,"ng-container",6),2&r){const P=e.XpG();e.Y8G("ngTemplateOutlet",P.templateTrigger)}}function A(r,m){1&r&&(e.j41(0,"button",5),e.EFF(1," Select project or client "),e.nrm(2,"img",7),e.k0s())}function k(r,m){1&r&&(e.j41(0,"div",10)(1,"app-inno-form-checkbox"),e.EFF(2,"IOS platform"),e.k0s()())}function R(r,m){if(1&r){const P=e.RV6();e.j41(0,"div",11)(1,"button",12),e.nrm(2,"img",13),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.j41(5,"div",14)(6,"button",15),e.bIt("click",function(){e.eBV(P);const u=e.XpG(2);return e.Njj(u.handleCloseSearchResult())}),e.EFF(7),e.nI1(8,"translate"),e.k0s(),e.j41(9,"button",16),e.EFF(10),e.nI1(11,"translate"),e.k0s()()()}2&r&&(e.R7$(3),e.SpI(" ",e.bMT(4,3,"TAG.CreateTag")," "),e.R7$(4),e.SpI(" ",e.bMT(8,5,"BUTTON.Cancel")," "),e.R7$(3),e.SpI(" ",e.bMT(11,7,"BUTTON.Save")," "))}function v(r,m){if(1&r){const P=e.RV6();e.j41(0,"div",8)(1,"app-inno-input-search-result",9),e.nI1(2,"translate"),e.bIt("onChange",function(u){e.eBV(P);const D=e.XpG();return e.Njj(D.handleSearch(u))}),e.DNE(3,k,3,0,"ng-template",null,1,e.C5r)(5,R,12,9,"ng-template",null,2,e.C5r),e.k0s()()}if(2&r){const P=e.sdS(4),p=e.sdS(6),u=e.XpG();e.R7$(),e.Y8G("placeholder",e.bMT(2,8,"TAG.Search"))("data",e.lJ4(10,l))("isNotFound",!u.listOptionPreview.length)("isEmptyData",!u.listOptionOriginal.length)("isLoading",u.isLoading)("defaultValue",u.defaultTextSearch)("optionTemplate",P)("footerTemplate",p)}}let M=(()=>{var r;class m{constructor(p){(0,i.A)(this,"addProjectDialog",void 0),(0,i.A)(this,"templateTrigger",null),(0,i.A)(this,"defaultTextSearch",""),(0,i.A)(this,"onSelect",new e.bkB),(0,i.A)(this,"listOptionPreview",[]),(0,i.A)(this,"listOptionOriginal",[]),(0,i.A)(this,"isLoading",!1),(0,i.A)(this,"unsubscribe",[]),(0,i.A)(this,"searchResultComponent",void 0),this.addProjectDialog=p}loadData(){}handleSearch(p){if(p=p?.trim()?.toLowerCase(),!p?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(D=>D.label.toLowerCase().indexOf(p)>-1);const u=[];this.listOptionPreview.forEach(D=>{u.push(D),this.listOptionOriginal.filter(I=>"project"==I.type).forEach(I=>{D.value==I.objectClient.id&&u.push(I)})}),this.listOptionPreview=u}handleCreateNewProject(){this.addProjectDialog.open({}),this.handleCloseSearchResult()}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}handleChooseOption(p){this.onSelect.emit(p),this.handleCloseSearchResult()}ngOnDestroy(){this.unsubscribe.forEach(p=>p.unsubscribe())}}return r=m,(0,i.A)(m,"\u0275fac",function(p){return new(p||r)(e.rXU(n.J))}),(0,i.A)(m,"\u0275cmp",e.VBU({type:r,selectors:[["app-inno-select-search-tags"]],viewQuery:function(p,u){if(1&p&&e.GBs(c.x,5),2&p){let D;e.mGM(D=e.lsd())&&(u.searchResultComponent=D.first)}},inputs:{templateTrigger:"templateTrigger",defaultTextSearch:"defaultTextSearch"},outputs:{onSelect:"onSelect"},standalone:!0,features:[e.aNF],decls:5,vars:4,consts:[["templateSearchProject",""],["optionTemplate",""],["footerTemplate",""],["position","bottom-start",3,"onOpen","content","isClickOnContentToClose","isClearPadding"],["target",""],["target","",1,"dropdown-invisible","flex","gap-[4px]"],["target","",4,"ngTemplateOutlet"],["src","../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"w-[16px]","translate-y-[2px]"],[1,"min-w-[320px]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isLoading","defaultValue","optionTemplate","footerTemplate"],[1,"w-full","py-[6px]"],[1,"w-full","flex","flex-col","gap-[12px]"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","flex","justify-end","gap-[6px]"],[1,"button-outline","button-size-md",3,"click"],[1,"button-primary","button-size-md"]],template:function(p,u){if(1&p){const D=e.RV6();e.j41(0,"app-inno-popover",3),e.bIt("onOpen",function(){return e.eBV(D),e.Njj(u.loadData())}),e.DNE(1,b,1,1,"ng-container",4)(2,A,3,0,"button",5)(3,v,7,11,"ng-template",null,0,e.C5r),e.k0s()}if(2&p){const D=e.sdS(4);e.Y8G("content",D)("isClickOnContentToClose",!1)("isClearPadding",!0),e.R7$(),e.vxM(u.templateTrigger?1:2)}},dependencies:[t.G,a.T3,s.D9,g.mC,c.x,d.V,f.t],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),m})()},9424:(y,E,o)=>{o.d(E,{f:()=>f});var i=o(9842),e=o(177),t=o(4438);const c=(g,d,n)=>({"w-4 h-4":g,"w-6 h-6":d,"w-10 h-10":n});let f=(()=>{var g;class d{constructor(){(0,i.A)(this,"size","md")}}return g=d,(0,i.A)(d,"\u0275fac",function(a){return new(a||g)}),(0,i.A)(d,"\u0275cmp",t.VBU({type:g,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[t.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(a,s){1&a&&(t.j41(0,"div",0),t.qSk(),t.j41(1,"svg",1),t.nrm(2,"path",2)(3,"path",3),t.k0s(),t.joV(),t.j41(4,"span",4),t.EFF(5,"Loading..."),t.k0s()()),2&a&&(t.R7$(),t.Y8G("ngClass",t.sMw(1,c,"sm"===s.size,"md"===s.size,"lg"===s.size)))},dependencies:[e.MD,e.YU]})),d})()},2480:(y,E,o)=>{o.d(E,{n:()=>a});var i=o(9842),e=o(6146),t=o(4438),c=o(177);const f=s=>({"bg-bg-brand-primary":s}),g=s=>({"text-text-brand-primary":s});function d(s,l){1&s&&t.nrm(0,"img",1)}function n(s,l){1&s&&t.nrm(0,"img",2)}let a=(()=>{var s;class l{constructor(){(0,i.A)(this,"placeholder","Add tags"),(0,i.A)(this,"value","")}}return s=l,(0,i.A)(l,"\u0275fac",function(b){return new(b||s)}),(0,i.A)(l,"\u0275cmp",t.VBU({type:s,selectors:[["app-inno-tags"]],inputs:{placeholder:"placeholder",value:"value"},standalone:!0,features:[t.aNF],decls:5,vars:8,consts:[[1,"w-full","max-w-[160px]","px-[8px]","py-[6px]","flex","items-center","gap-[8px]","cursor-default","rounded-md","hover:bg-bg-brand-primary","transition-all",3,"ngClass"],["src","../../../assets/img/icon/ic_tags_green.svg","alt","Icon",1,"w-[16px]","shrink-0"],["src","../../../assets/img/icon/ic_tags_black.svg","alt","Icon",1,"w-[16px]","shrink-0"],[1,"text-text-sm-medium","line-clamp-1",3,"ngClass"]],template:function(b,A){1&b&&(t.j41(0,"div",0),t.DNE(1,d,1,0,"img",1)(2,n,1,0,"img",2),t.j41(3,"span",3),t.EFF(4),t.k0s()()),2&b&&(t.Y8G("ngClass",t.eq3(4,f,A.value)),t.R7$(),t.vxM(A.value?1:2),t.R7$(2),t.Y8G("ngClass",t.eq3(6,g,A.value)),t.R7$(),t.SpI(" ",A.value?A.value:A.placeholder," "))},dependencies:[e.G,c.YU]})),l})()},5331:(y,E,o)=>{o.d(E,{n:()=>b});var i=o(467),e=o(9842),t=o(4438),c=o(9079),f=o(2928),g=o(6473),d=o(5277),n=o(3492),a=o(658),s=o(1413),l=o(4843),C=o(5236);let b=(()=>{var A;class k{constructor(){var v=this;(0,e.A)(this,"dataService",(0,t.WQX)(d.u)),(0,e.A)(this,"authenticationService",(0,t.WQX)(f.k)),(0,e.A)(this,"toastService",(0,t.WQX)(n.f)),(0,e.A)(this,"timeTrackingProvider",(0,t.WQX)(a.o)),(0,e.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,e.A)(this,"translate",(0,t.WQX)(C.c$)),(0,e.A)(this,"projectChangeSubject",new s.B),(0,e.A)(this,"projectChange$",this.projectChangeSubject.asObservable()),(0,e.A)(this,"getTimerInfoFromServer",(0,i.A)(function*(){const M={isRunningTimerOnServer:!1,totalSecondsTimerOnServer:0},r=yield(0,l._)(v.authenticationService.CheckTimer()),m=r?.isRunning??!1,P=r?.timer??"00:00:00",p=r?.timerStartTime,u=(0,g.R)(P);if(m){if(!p)return M;const{hours:D,minutes:I,seconds:_}=(0,g.Yh)({startTime:p,endTime:new Date}),h=(0,g.R)(`${D}:${I}:${_}`);M.isRunningTimerOnServer=!0,M.totalSecondsTimerOnServer=u+h}else M.isRunningTimerOnServer=!1,M.totalSecondsTimerOnServer=u;return M})),(0,e.A)(this,"updateTimeTrackingTimerInfo",M=>{const m={...this.dataService.GetTimeTrackingCreateTimerInfoValue(),...M};this.dataService.SetNewTimeTrackingCreateTimerInfo(m)}),(0,e.A)(this,"handleUpdateActualTimer",function(){var M=(0,i.A)(function*(r){3!==r.split(":").length&&(r+=":00");const{isRunningTimerOnServer:m}=yield v.getTimerInfoFromServer(),P={timer:r},p={totalSeconds:(0,g.R)(r)};m?(P.isRunning=!0,P.timerStartTime=new Date,p.timerStatus="running"):(P.isRunning=!1,P.timerStartTime=null,p.timerStatus="paused"),v.authenticationService.UpdateTimer(P).pipe((0,c.pQ)(v.destroyRef)).subscribe(()=>{v.updateTimeTrackingTimerInfo(p)})});return function(r){return M.apply(this,arguments)}}()),(0,e.A)(this,"handlePauseOrResumeTime",(0,i.A)(function*(){const{timerStatus:M}=v.dataService.GetTimeTrackingCreateTimerInfoValue()||{},r=v.dataService.GetTimeTrackingCreateTimerInfoValue(),{isRunningTimerOnServer:m,totalSecondsTimerOnServer:P}=yield v.getTimerInfoFromServer();if("running"===M){if(!m)return v.updateTimeTrackingTimerInfo({timerStatus:"paused",totalSeconds:r?.totalSeconds});{const p={isRunning:!1,timerStartTime:null,timer:(0,g.Lc)(P)};v.authenticationService.UpdateTimer(p).pipe((0,c.pQ)(v.destroyRef)).subscribe(),v.updateTimeTrackingTimerInfo({timerStatus:"paused",totalSeconds:r?.totalSeconds})}}else{if(m)localStorage.setItem("isRunning","true"),v.updateTimeTrackingTimerInfo({timerStatus:"running",totalSeconds:r?.totalSeconds});else{v.authenticationService.UpdateTimer({isRunning:!0,timerStartTime:new Date}).pipe((0,c.pQ)(v.destroyRef)).subscribe();const u=v.dataService.getisInternalClient();v.updateTimeTrackingTimerInfo({timerStatus:"running",totalSeconds:r?.totalSeconds,billable:!u}),localStorage.setItem("isRunning","true")}"true"===localStorage.getItem("isRunning")&&v.dataService.SetNewTimeTrackingShowingTimer(!0)}})),(0,e.A)(this,"handleResetTimer",()=>{this.authenticationService.UpdateTimer({isRunning:!1,timer:"00:00:00",timerStartTime:null}).pipe((0,c.pQ)(this.destroyRef)).subscribe(),this.dataService.SetNewTimeTrackingShowingTimer(!1),this.dataService.SetNewTimeTrackingCreateTimerInfo(void 0),this.toastService.showSuccess(this.translate.instant("TIMETRACKING.Timerdiscarded")),localStorage.removeItem("isRunning"),localStorage.removeItem("ResumeData")}),(0,e.A)(this,"handleSuccessTimer",()=>{this.clearCache(),this.authenticationService.UpdateTimer({isRunning:!1,timer:"00:00:00",timerStartTime:null}).pipe((0,c.pQ)(this.destroyRef)).subscribe(),this.dataService.SetNewTimeTrackingShowingTimer(!1),this.dataService.SetNewTimeTrackingCreateTimerInfo(void 0)}),(0,e.A)(this,"createTimeTrackingFromTimer",(0,i.A)(function*(){if(localStorage.getItem("ResumeData")){const{isRunningTimerOnServer:M,totalSecondsTimerOnServer:r}=yield v.getTimerInfoFromServer();if(!M&&!localStorage.getItem("isRunning"))return v.toastService.showInfo("This timer is not running","Please reload this window to start the timer");const m=v.dataService.GetTimeTrackingCreateTimerInfoValue(),P={endTime:(0,g.Lc)(m?.totalSeconds==r?r:m?.totalSeconds||0),billable:!v.dataService.getisInternalClient()&&(m?.billable??!1),date:m?.date,description:m?.description??"",clientId:m?.workingInfo?.metadata?.objectClient?.id,projectId:"project"==m?.workingInfo?.metadata?.type?m?.workingInfo?.value:null,serviceId:m?.serviceInfo?.value};v.timeTrackingProvider.handleUpdateTimeTracking({payload:P,optional:{callbackSuccess:()=>v.handleSuccessTimer()}})}else{const{isRunningTimerOnServer:M,totalSecondsTimerOnServer:r}=yield v.getTimerInfoFromServer();if(!M&&!localStorage.getItem("isRunning"))return v.toastService.showInfo("This timer is not running","Please reload this window to start the timer");const m=v.dataService.GetTimeTrackingCreateTimerInfoValue(),P={endTime:(0,g.Lc)(m?.totalSeconds==r?r:m?.totalSeconds||0),billable:!v.dataService.getisInternalClient()&&(m?.billable??!1),date:m?.date,description:m?.description??"",clientId:m?.workingInfo?.metadata?.objectClient?.id,projectId:"project"==m?.workingInfo?.metadata?.type?m?.workingInfo?.value:null,serviceId:m?.serviceInfo?.value};v.timeTrackingProvider.handleCreateTimeTracking({payload:P,optional:{callbackSuccess:()=>v.handleSuccessTimer()}})}}))}setCacheService(v){localStorage.setItem("cacheService",JSON.stringify(v))}getCacheService(){if(localStorage.getItem("cacheService"))return JSON.parse(localStorage.getItem("cacheService"))}setCacheClientProject(v){localStorage.setItem("cacheClientProject",JSON.stringify(v)),v?.value&&this.projectChangeSubject.next(v.value)}getCacheClientProject(){if(localStorage.getItem("cacheClientProject"))return JSON.parse(localStorage.getItem("cacheClientProject"))}clearCache(){localStorage.removeItem("cacheClientProject"),localStorage.removeItem("cacheService")}}return A=k,(0,e.A)(k,"\u0275fac",function(v){return new(v||A)}),(0,e.A)(k,"\u0275prov",t.jDH({token:A,factory:A.\u0275fac,providedIn:"root"})),k})()},1556:(y,E,o)=>{o.d(E,{Z:()=>n});var i=o(9842),e=o(4438),t=o(467),c=o(2716),f=o(7987);let g=(()=>{var a;class s extends c.H{open(C){var b=this;return(0,t.A)(function*(){const A=yield o.e(3190).then(o.bind(o,3190));return b.matDialog.open(A.AlertConfirmComponent.getComponent(),{data:C,width:"440px",panelClass:"custom_dialog",scrollStrategy:new f.t0,disableClose:!0})})()}}return a=s,(0,i.A)(s,"\u0275fac",(()=>{let l;return function(b){return(l||(l=e.xGo(a)))(b||a)}})()),(0,i.A)(s,"\u0275prov",e.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),s})(),n=(()=>{var a;class s{constructor(C){(0,i.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=C}alertDelete(C){const{title:b,description:A,textSubmit:k="COMMON.Delete",textCancel:R}=C;return new Promise(v=>{this.alertConfirmDialog.open({title:b,description:A,textSubmit:k,textCancel:R,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(r=>{r.afterClosed().subscribe(m=>{v(m??!1)})})})}alertConfirm(C){const{title:b,description:A,textSubmit:k,textCancel:R}=C;return new Promise(v=>{this.alertConfirmDialog.open({title:b,description:A,textSubmit:k,textCancel:R}).then(r=>{r.afterClosed().subscribe(m=>{v(m??!1)})})})}}return a=s,(0,i.A)(s,"\u0275fac",function(C){return new(C||a)(e.KVO(g))}),(0,i.A)(s,"\u0275prov",e.jDH({token:a,factory:a.\u0275fac})),s})()},6586:(y,E,o)=>{o.d(E,{A:()=>i,a:()=>e});var i=function(t){return t.Created_Tab="Created_Tab",t.Sent_To_Me_Tab="Sent_To_Me_Tab",t}(i||{}),e=function(t){return t[t.all=1]="all",t[t.this_month=2]="this_month",t[t.last_month=3]="last_month",t[t.custom=4]="custom",t}(e||{})},5402:(y,E,o)=>{o.d(E,{Q:()=>i});var i=function(e){return e.Item="Item",e.Service="Service",e}(i||{})},1588:(y,E,o)=>{o.d(E,{j:()=>i});var i=function(e){return e.Day="Day",e.Week="Week",e.Month="Month",e.All="All",e}(i||{})},5277:(y,E,o)=>{o.d(E,{u:()=>d});var i=o(9842),e=o(4438),t=o(6586),c=o(1588),f=o(4412),g=o(1413);let d=(()=>{var n;class a{constructor(){(0,i.A)(this,"behaviorTimeTrackingTypeView",new f.t(c.j.Day)),(0,i.A)(this,"behaviorTimeTrackingDate",new f.t(void 0)),(0,i.A)(this,"behaviorTimeTrackingFilter",new f.t({typeView:c.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,i.A)(this,"behaviorTimeTrackingCreateTimer",new f.t(void 0)),(0,i.A)(this,"behaviorisInternalClient",new f.t(!1)),(0,i.A)(this,"behaviorTimeTrackingShowingTimer",new f.t(!1)),(0,i.A)(this,"reloadItem",new g.B),(0,i.A)(this,"reloadService",new g.B),(0,i.A)(this,"isEstimate",(0,e.vPA)(!1)),(0,i.A)(this,"behaviorInvoiceFilter",new f.t({typeView:t.A.Created_Tab,textSearch:""}))}SetisInternalClient(l){this.behaviorisInternalClient.next(l)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(l){this.behaviorTimeTrackingTypeView.next(l)}SetNewTimeTrackingDate(l){this.behaviorTimeTrackingDate.next(l)}SetNewTimeTrackingShowingTimer(l){this.behaviorTimeTrackingShowingTimer.next(l)}SetNewTimeTrackingCreateTimerInfo(l){this.behaviorTimeTrackingCreateTimer.next(l)}SetNewTimeTrackingFilter(l){this.behaviorTimeTrackingFilter.next(l)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(l){localStorage.setItem("ResumeData",JSON.stringify(l))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(l){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...l})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return n=a,(0,i.A)(a,"\u0275fac",function(l){return new(l||n)}),(0,i.A)(a,"\u0275prov",e.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})),a})()},6812:(y,E,o)=>{o.d(E,{J:()=>g});var i=o(467),e=o(9842),t=o(2716),c=o(7987),f=o(4438);let g=(()=>{var d;class n extends t.H{open(s){var l=this;return(0,i.A)(function*(){const C=yield Promise.all([o.e(1328),o.e(4823),o.e(6217),o.e(9717),o.e(5475),o.e(2076),o.e(2456)]).then(o.bind(o,2456));return l.matDialog.open(C.AddProjectFormComponent.getComponent(),{panelClass:"custom_dialog",data:s,maxWidth:"700px",width:"100%",scrollStrategy:new c.t0,disableClose:!0})})()}}return d=n,(0,e.A)(n,"\u0275fac",(()=>{let a;return function(l){return(a||(a=f.xGo(d)))(l||d)}})()),(0,e.A)(n,"\u0275prov",f.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})),n})()},3814:(y,E,o)=>{o.d(E,{v:()=>g});var i=o(467),e=o(9842),t=o(2716),c=o(7987),f=o(4438);let g=(()=>{var d;class n extends t.H{open(s){var l=this;return(0,i.A)(function*(){const C=yield Promise.all([o.e(1328),o.e(2076),o.e(7371)]).then(o.bind(o,2508));return l.matDialog.open(C.DialogModifyItemServiceComponent.getComponent(),{disableClose:!0,panelClass:"custom_dialog",data:s,width:"500px",scrollStrategy:new c.t0})})()}}return d=n,(0,e.A)(n,"\u0275fac",(()=>{let a;return function(l){return(a||(a=f.xGo(d)))(l||d)}})()),(0,e.A)(n,"\u0275prov",f.jDH({token:d,factory:d.\u0275fac,providedIn:"root"})),n})()},658:(y,E,o)=>{o.d(E,{o:()=>k});var i=o(9842),e=o(2928),t=o(6508),c=o.n(t),f=o(4438),g=o(33),d=o(1588),n=o(6473),a=o(5277),s=o(1110),l=o(4805),C=o(3492),b=o(7673),A=o(5236);let k=(()=>{var R;class v{constructor(){(0,i.A)(this,"storeService",(0,f.WQX)(s.n)),(0,i.A)(this,"timeTrackingService",(0,f.WQX)(l.y)),(0,i.A)(this,"toastService",(0,f.WQX)(C.f)),(0,i.A)(this,"authenticationService",(0,f.WQX)(e.k)),(0,i.A)(this,"dataService",(0,f.WQX)(a.u)),(0,i.A)(this,"translate",(0,f.WQX)(A.c$)),(0,i.A)(this,"activatedRoute",(0,f.WQX)(g.nX))}__updateFilter(r){const P={...this.dataService.GetTimeTrackingFilterValue(),...r};this.dataService.SetNewTimeTrackingFilter(P)}reloadTimeTrackingData(){const r=this.dataService.GetTimeTrackingFilterValue(),m=r.typeView??d.j.Day,P=this.activatedRoute.snapshot.queryParams.page||1,p=r.textSearch??"",u=r.dateSelected??new Date,h=r.startDate??void 0,S=r.endDate??void 0;let T={Page:P,PageSize:20,Search:p,loggedBy:r.userSelected??void 0,projectId:r.projectSelected??void 0,clientId:r.clientSelected??void 0};switch(m){case d.j.Day:T={...T,filterDate:(0,n.cn)(u)};break;case d.j.Week:{const O=(0,n._7)(u??new Date);T={...T,Page:0,startDate:O.startOfWeek,endDate:O.endOfWeek};break}case d.j.Month:{const O=(0,n.mQ)(u??new Date);T={...T,Page:0,startDate:O.startOfMonth,endDate:O.endOfMonth};break}case d.j.All:T={...T,startDate:(0,n.cn)(h),endDate:(0,n.cn)(S)};break;default:return(0,b.of)(null)}return this.timeTrackingService.GetAllTimeTracking(T)}handleCreateTimeTracking({payload:r,optional:m}){if(!r.date||!r.endTime)return this.toastService.showError("Fail","Missing required fields");r={memberId:null,service:null,startTime:null,serviceId:null,clientId:null,projectId:null,...r},this.timeTrackingService.CreateTimeTracking(r).subscribe({next:()=>{localStorage.removeItem("isRunning"),this.toastService.showSuccess(this.translate.instant("TOAST.Create"),this.translate.instant("TOAST.Success")),this.dataService.triggerRefreshListTimeTracking(),m?.callbackSuccess&&m.callbackSuccess()},error:()=>{this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))}})}handleUpdateTimeTracking({payload:r,optional:m}){if(!r.date||!r.endTime)return this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.MissingRequiredFields"));r.id=this.dataService.getResume().id,r={memberId:null,service:null,startTime:null,serviceId:null,clientId:null,projectId:null,...r},this.timeTrackingService.UpdateTimeTracking(r).subscribe({next:()=>{localStorage.removeItem("ResumeData"),localStorage.removeItem("isRunning"),this.toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.Success")),this.dataService.triggerRefreshListTimeTracking(),m?.callbackSuccess&&m.callbackSuccess()},error:()=>{this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))}})}handleChangeDateFilter(r){this.__updateFilter({dateSelected:r})}handleNextDate(){const r=this.dataService.GetTimeTrackingFilterValue(),m=r.dateSelected??new Date,P=c().tz.guess();let p=c().tz(m,P);switch(r.typeView){case d.j.Day:p=p.add(1,"day");break;case d.j.Week:p=p.add(1,"week");break;case d.j.Month:p=p.add(1,"month")}this.__updateFilter({dateSelected:p.toDate()})}handlePreDate(){const r=this.dataService.GetTimeTrackingFilterValue(),m=r.dateSelected??new Date,P=c().tz.guess();let p=c().tz(m,P);switch(r.typeView){case d.j.Day:p=p.subtract(1,"day");break;case d.j.Week:p=p.subtract(1,"week");break;case d.j.Month:p=p.subtract(1,"month")}this.__updateFilter({dateSelected:p.toDate()})}handleChangeTypeView(r){this.__updateFilter({typeView:r})}}return R=v,(0,i.A)(v,"\u0275fac",function(r){return new(r||R)}),(0,i.A)(v,"\u0275prov",f.jDH({token:R,factory:R.\u0275fac,providedIn:"root"})),v})()}}]);