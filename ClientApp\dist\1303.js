"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1303],{9424:(R,E,n)=>{n.d(E,{f:()=>M});var o=n(9842),e=n(177),m=n(4438);const I=(d,D,f)=>({"w-4 h-4":d,"w-6 h-6":D,"w-10 h-10":f});let M=(()=>{var d;class D{constructor(){(0,o.A)(this,"size","md")}}return d=D,(0,o.A)(D,"\u0275fac",function(s){return new(s||d)}),(0,o.A)(D,"\u0275cmp",m.VBU({type:d,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[m.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(s,t){1&s&&(m.j41(0,"div",0),m.qSk(),m.j41(1,"svg",1),m.nrm(2,"path",2)(3,"path",3),m.k0s(),m.joV(),m.j41(4,"span",4),m.EFF(5,"Loading..."),m.k0s()()),2&s&&(m.R7$(),m.Y8G("ngClass",m.sMw(1,I,"sm"===t.size,"md"===t.size,"lg"===t.size)))},dependencies:[e.MD,e.YU]})),D})()},8556:(R,E,n)=>{n.d(E,{K:()=>T});var o=n(9842),e=n(4438),m=n(5599),I=n(6146),M=n(4823),d=n(5236);function D(c,C){if(1&c){const l=e.RV6();e.j41(0,"button",4),e.bIt("click",function(){e.eBV(l);const _=e.XpG();return e.Njj(_.handleResume())}),e.nrm(1,"img",5),e.k0s()}}function f(c,C){if(1&c){const l=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(l);const _=e.XpG();return e.Njj(_.handleEdit())}),e.nrm(1,"img",7),e.k0s()}}function s(c,C){if(1&c){const l=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(l);const _=e.XpG();return e.Njj(_.handleDowload())}),e.nrm(1,"img",8),e.k0s()}}function t(c,C){if(1&c){const l=e.RV6();e.j41(0,"div",12)(1,"button",13),e.bIt("click",function(){e.eBV(l);const _=e.XpG(2);return e.Njj(_.handleArchive())}),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"button",14),e.bIt("click",function(){e.eBV(l);const _=e.XpG(2);return e.Njj(_.handleDelete())}),e.EFF(5),e.nI1(6,"translate"),e.k0s()()}2&c&&(e.R7$(2),e.SpI(" ",e.bMT(3,2,"COMMON.Archive")," "),e.R7$(3),e.SpI(" ",e.bMT(6,4,"COMMON.Delete")," "))}function a(c,C){if(1&c&&(e.j41(0,"app-inno-popover",9)(1,"button",10),e.nrm(2,"img",11),e.k0s()(),e.DNE(3,t,7,6,"ng-template",null,0,e.C5r)),2&c){const l=e.sdS(4);e.Y8G("content",l)}}function h(c,C){if(1&c){const l=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(l);const _=e.XpG(2);return e.Njj(_.handleArchive())}),e.nrm(1,"img",15),e.k0s()}}function g(c,C){if(1&c){const l=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(l);const _=e.XpG(2);return e.Njj(_.handleDelete())}),e.nrm(1,"img",16),e.k0s()}}function B(c,C){if(1&c&&e.DNE(0,h,2,0,"button",3)(1,g,2,0,"button",3),2&c){const l=e.XpG();e.vxM(l.onArchive.observed?0:-1),e.R7$(),e.vxM(l.onDelete.observed?1:-1)}}let T=(()=>{var c;class C{constructor(){(0,o.A)(this,"onEdit",new e.bkB),(0,o.A)(this,"onResume",new e.bkB),(0,o.A)(this,"onDelete",new e.bkB),(0,o.A)(this,"onArchive",new e.bkB),(0,o.A)(this,"onDowload",new e.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return c=C,(0,o.A)(C,"\u0275fac",function(v){return new(v||c)}),(0,o.A)(C,"\u0275cmp",e.VBU({type:c,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[e.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(v,_){1&v&&(e.j41(0,"div",1),e.DNE(1,D,2,0,"button",2)(2,f,2,0,"button",3)(3,s,2,0,"button",3)(4,a,5,1)(5,B,2,2),e.k0s()),2&v&&(e.R7$(),e.vxM(_.onResume.observed?1:-1),e.R7$(),e.vxM(_.onEdit.observed?2:-1),e.R7$(),e.vxM(_.onDowload.observed?3:-1),e.R7$(),e.vxM(_.onArchive.observed&&_.onDelete.observed?4:5))},dependencies:[I.G,d.D9,M.oV,m.x]})),C})()},1556:(R,E,n)=>{n.d(E,{Z:()=>f});var o=n(9842),e=n(4438),m=n(467),I=n(2716),M=n(7987);let d=(()=>{var s;class t extends I.H{open(h){var g=this;return(0,m.A)(function*(){const B=yield n.e(3190).then(n.bind(n,3190));return g.matDialog.open(B.AlertConfirmComponent.getComponent(),{data:h,width:"440px",panelClass:"custom_dialog",scrollStrategy:new M.t0,disableClose:!0})})()}}return s=t,(0,o.A)(t,"\u0275fac",(()=>{let a;return function(g){return(a||(a=e.xGo(s)))(g||s)}})()),(0,o.A)(t,"\u0275prov",e.jDH({token:s,factory:s.\u0275fac,providedIn:"root"})),t})(),f=(()=>{var s;class t{constructor(h){(0,o.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=h}alertDelete(h){const{title:g,description:B,textSubmit:T="COMMON.Delete",textCancel:c}=h;return new Promise(C=>{this.alertConfirmDialog.open({title:g,description:B,textSubmit:T,textCancel:c,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(v=>{v.afterClosed().subscribe(_=>{C(_??!1)})})})}alertConfirm(h){const{title:g,description:B,textSubmit:T,textCancel:c}=h;return new Promise(C=>{this.alertConfirmDialog.open({title:g,description:B,textSubmit:T,textCancel:c}).then(v=>{v.afterClosed().subscribe(_=>{C(_??!1)})})})}}return s=t,(0,o.A)(t,"\u0275fac",function(h){return new(h||s)(e.KVO(d))}),(0,o.A)(t,"\u0275prov",e.jDH({token:s,factory:s.\u0275fac})),t})()},359:(R,E,n)=>{n.d(E,{l:()=>D});var o=n(9842),e=n(1626),m=n(4438),I=n(33);const d=n(5312).c.HOST_API+"/api";let D=(()=>{var f;class s{constructor(){(0,o.A)(this,"http",(0,m.WQX)(e.Qq)),(0,o.A)(this,"router",(0,m.WQX)(I.Ix))}CreateUserBusiness(a){return this.http.post(d+"/Business/user-business",a)}GetUserBusiness(){return this.http.get(d+"/Business/user-business").pipe(a=>a)}GetBusinessById(a){return this.http.get(d+`/Business/user-business-byid?businessId=${a}`)}GetInfoCompany(){return this.http.get(d+"/Business/GetInfoCompany")}GetAllUserBusiness(a){const h={...a};return Object.keys(h).forEach(g=>null==h[g]&&delete h[g]),this.http.get(d+"/Business/GetAllUserBusiness",{params:h})}AddMemberBusiness(a){return this.http.post(d+"/Business/AddMemberBusiness",a)}DeleteMemberInBusiness(a){return this.http.post(d+`/Business/DeleteMemberInBusiness?memberId=${a}`,null)}userBusinessById(a){return this.http.get(d+`/Business/userBusinessById?UserId=${a}`)}UpdateRoleMember(a,h){return this.http.get(d+`/Business/UpdateRoleMember?UserId=${a}&role=${h}`)}UpdateStatus(a){return this.http.post(d+"/Business/UpdateStatus",a)}SendMailAddMember(a){return this.http.post(d+"/Business/SendMailAddMember",a)}}return f=s,(0,o.A)(s,"\u0275fac",function(a){return new(a||f)}),(0,o.A)(s,"\u0275prov",m.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})),s})()},1303:(R,E,n)=>{n.r(E),n.d(E,{AccountComponent:()=>F});var o=n(9842),e=n(9424),m=n(5900),I=n(8556),M=n(359),d=n(2928),D=n(1110),f=n(3492),s=n(1556),t=n(4438),a=n(33),h=n(5236),g=n(9079),B=n(6146),T=n(9115),c=n(1970),C=n(2840),l=n(1448),v=n(1413),_=n(152),x=n(177);const S=["grid"];function j(u,b){1&u&&(t.j41(0,"div",11),t.nrm(1,"app-inno-spin"),t.k0s())}function U(u,b){if(1&u&&(t.nrm(0,"ngx-avatars",19),t.j41(1,"span",20),t.EFF(2),t.k0s()),2&u){const r=t.XpG(2).$implicit,i=t.XpG(2);t.FS9("bgColor",i.storeService.getBgColor(r.user.firstName.slice(0,1))),t.Y8G("size",30)("name",i.GetFullName(r.user)),t.R7$(2),t.Lme(" ",r.user.firstName," ",r.user.lastName,"")}}function y(u,b){if(1&u&&(t.nrm(0,"ngx-avatars",19),t.j41(1,"span",20),t.EFF(2),t.k0s()),2&u){const r=t.XpG(2).$implicit,i=t.XpG(2);t.FS9("bgColor",i.storeService.getBgColor(r.user.email.slice(0,1))),t.Y8G("size",30)("name",r.user.email.slice(0,1)),t.R7$(2),t.SpI(" ",r.user.email,"")}}function G(u,b){if(1&u&&(t.j41(0,"div",18),t.DNE(1,U,3,5)(2,y,3,4),t.k0s()),2&u){const r=t.XpG().$implicit;t.R7$(),t.vxM(null!=r.user&&r.user.firstName&&null!=r.user&&r.user.lastName?1:2)}}function N(u,b){1&u&&t.DNE(0,G,3,1,"div",18),2&u&&t.vxM(b.$implicit.user?0:-1)}function L(u,b){if(1&u&&(t.j41(0,"div")(1,"span"),t.EFF(2),t.k0s()()),2&u){const r=b.$implicit;t.R7$(2),t.JRh(r.role)}}function W(u,b){if(1&u){const r=t.RV6();t.j41(0,"app-inno-table-action",21),t.bIt("onDelete",function(){const p=t.eBV(r).$implicit,A=t.XpG(2);return t.Njj(A.creaFormDelete(p))}),t.k0s()}}function K(u,b){if(1&u){const r=t.RV6();t.j41(0,"div",12)(1,"ejs-grid",13,0),t.bIt("actionBegin",function(p){t.eBV(r);const A=t.XpG();return t.Njj(A.onActionBegin(p))}),t.j41(3,"e-columns")(4,"e-column",14),t.nI1(5,"translate"),t.DNE(6,N,1,1,"ng-template",null,1,t.C5r),t.k0s(),t.j41(8,"e-column",15),t.nI1(9,"translate"),t.DNE(10,L,3,1,"ng-template",null,1,t.C5r),t.k0s(),t.j41(12,"e-column",16),t.nI1(13,"translate"),t.DNE(14,W,1,0,"ng-template",null,1,t.C5r),t.k0s()()(),t.j41(16,"ejs-pager",17),t.bIt("click",function(p){t.eBV(r);const A=t.XpG();return t.Njj(A.onPageChange(p))}),t.k0s()()}if(2&u){const r=t.XpG();t.R7$(),t.Y8G("allowSorting",!0)("sortSettings",r.sortOptions)("dataSource",r.dataSource),t.R7$(3),t.Y8G("headerText",t.bMT(5,10,"ACCOUNT.GIRD.Name")),t.R7$(4),t.Y8G("headerText",t.bMT(9,12,"ACCOUNT.GIRD.Role")),t.R7$(4),t.Y8G("headerText",t.bMT(13,14,"ACCOUNT.GIRD.Action")),t.R7$(4),t.Y8G("pageSize",r.pageSizesDefault)("totalRecordsCount",r.totalPages)("currentPage",r.currentPage)("pageSizes",r.pageSizes)}}C.is5.Inject(C.Rav);let F=(()=>{var u;class b{constructor(i){(0,o.A)(this,"location",void 0),(0,o.A)(this,"sort",void 0),(0,o.A)(this,"sortOptions",{columns:[]}),(0,o.A)(this,"isLoading",!1),(0,o.A)(this,"data",void 0),(0,o.A)(this,"_subscriptions",[]),(0,o.A)(this,"searchSubject",new v.B),(0,o.A)(this,"selectionOptions",{type:"Multiple",checkboxOnly:!0}),(0,o.A)(this,"columnSelection",!1),(0,o.A)(this,"dataSource",void 0),(0,o.A)(this,"totalPages",0),(0,o.A)(this,"pageSizes",[10,20,50,100]),(0,o.A)(this,"pageSizesDefault",10),(0,o.A)(this,"currentPage",1),(0,o.A)(this,"search",""),(0,o.A)(this,"grid",void 0),(0,o.A)(this,"columnName",void 0),(0,o.A)(this,"direction",void 0),(0,o.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,o.A)(this,"router",(0,t.WQX)(a.Ix)),(0,o.A)(this,"translate",(0,t.WQX)(h.c$)),(0,o.A)(this,"layoutUtilsService",(0,t.WQX)(s.Z)),(0,o.A)(this,"_authServices",(0,t.WQX)(d.k)),(0,o.A)(this,"_toastService",(0,t.WQX)(f.f)),(0,o.A)(this,"activatedRoute",(0,t.WQX)(a.nX)),(0,o.A)(this,"storeService",(0,t.WQX)(D.n)),(0,o.A)(this,"userbusinesServices",(0,t.WQX)(M.l)),this.location=i}ngOnDestroy(){}onActionBegin(i){if("sorting"===i.requestType){if(this.columnName=i.columnName,this.direction=i.direction,this.sort={columnName:i.columnName,direction:i.direction},this.columnName){const p={Sort:JSON.stringify(this.sort)};return void this.GetAllMemberInBusiness(this.currentPage,"",p)}this.sortOptions={columns:[]},this.sort=null,this.GetAllMemberInBusiness(this.currentPage,"")}}ngOnInit(){this.activatedRoute.queryParams.pipe((0,g.pQ)(this.destroyRef)).subscribe(p=>{if(p?.page){this.currentPage=p.page;const A={Sort:JSON.stringify(this.sort)};this.sort?this.GetAllMemberInBusiness(this.currentPage,"",A):this.GetAllMemberInBusiness(this.currentPage,"")}else this.GetAllMemberInBusiness(this.currentPage,"")});const i=this.searchSubject.pipe((0,_.B)(550)).subscribe(p=>{p?(this.search=p,this.GetAllMemberInBusiness(this.currentPage,this.search)):(this.search="",this.GetAllMemberInBusiness(this.currentPage,""))});this._subscriptions.push(i)}onPageChange(i){i?.newProp?.pageSize&&(this.pageSizesDefault=i.newProp.pageSize,this.GetAllMemberInBusiness(this.currentPage,"")),i?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:i.currentPage},queryParamsHandling:"merge"})}GetAllMemberInBusiness(i,p,A){this.isLoading=!0,this.userbusinesServices.GetAllUserBusiness({Page:i,PageSize:this.pageSizesDefault,Search:p,Filter:A}).pipe((0,g.pQ)(this.destroyRef)).subscribe(O=>{O&&(this.isLoading=!1,this.totalPages=O.totalRecords,this.dataSource=O.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))})}handleSearch(i){this.searchSubject.next(i)}GetFullName(i){return i.firstName+" "+i.lastName}creaFormDelete(i){const p=this.translate.instant("ACCOUNT.DeleteUser"),A=this.translate.instant("COMMON.ConfirmDelete");this.layoutUtilsService.alertDelete({title:p,description:A}).then(P=>{P&&this.userbusinesServices.DeleteMemberInBusiness(i?.user?.id).pipe((0,g.pQ)(this.destroyRef)).subscribe({next:O=>{if(O)return this.GetAllMemberInBusiness(this.currentPage,""),void this._toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success"));this._toastService.showError(this.translate.instant("TOAST.Fail"))}})})}handleBack(){this.location.back()}}return u=b,(0,o.A)(b,"\u0275fac",function(i){return new(i||u)(t.rXU(x.aZ))}),(0,o.A)(b,"\u0275cmp",t.VBU({type:u,selectors:[["app-account"]],viewQuery:function(i,p){if(1&i&&t.GBs(S,5),2&i){let A;t.mGM(A=t.lsd())&&(p.grid=A.first)}},standalone:!0,features:[t.Jv_([s.Z]),t.aNF],decls:13,vars:5,consts:[["grid",""],["template",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full"],[1,"flex","items-center","gap-[8px]"],[1,"button-icon","button-size-md",3,"click"],["src","../../../../assets/img/icon/ic_arrow_left.svg","alt","Icon"],[1,"text-text-primary","text-headline-lg-bold"],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],[1,"flex","justify-center","items-center","grow","py-3"],[1,"w-full","mt-[12px]"],[1,"customTable",3,"actionBegin","allowSorting","sortSettings","dataSource"],["width","200","field","FirstName",3,"headerText"],["width","150","field","role",3,"headerText"],["width","100",3,"headerText"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"flex","items-center","cursor-pointer"],[3,"size","bgColor","name"],[1,"ml-2"],[3,"onDelete"]],template:function(i,p){1&i&&(t.j41(0,"div",2)(1,"div",3)(2,"div",4)(3,"button",5),t.bIt("click",function(){return p.handleBack()}),t.nrm(4,"img",6),t.k0s(),t.j41(5,"p",7),t.EFF(6),t.nI1(7,"translate"),t.k0s()()()(),t.j41(8,"div",8)(9,"div",9)(10,"app-inno-input-search",10),t.bIt("onChange",function(P){return p.handleSearch(P)}),t.k0s()()(),t.DNE(11,j,2,0,"div",11)(12,K,17,16,"div",12)),2&i&&(t.R7$(6),t.SpI(" ",t.bMT(7,3,"ACCOUNT.Title")," "),t.R7$(4),t.Y8G("value",p.search),t.R7$(),t.vxM(p.isLoading?11:12))},dependencies:[I.K,e.f,m.M,B.G,h.D9,l.iov,l.BzB,T.Cn,c.mC,c.fw,l.pc9,l._ab,l.eeu,l.rFS,l.LGG,l.cvh,l.gFV],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),b})()}}]);