"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[4592],{7656:(E,x,a)=>{a.d(x,{V:()=>u});var n=a(9842),e=a(4438),T=a(6146),t=a(9417);const b=["*"];function g(p,_){if(1&p){const c=e.RV6();e.j41(0,"input",5),e.bIt("change",function(d){e.eBV(c);const h=e.XpG();return e.Njj(h.handleChange(d))}),e.k0s()}if(2&p){const c=e.XpG();e.Y8G("checked",c.checked)("formControl",c.formControl)}}function M(p,_){if(1&p){const c=e.RV6();e.j41(0,"input",6),e.bIt("change",function(d){e.eBV(c);const h=e.XpG();return e.Njj(h.handleChange(d))}),e.k0s()}if(2&p){const c=e.XpG();e.Y8G("checked",c.checked)}}let u=(()=>{var p;class _{constructor(){(0,n.A)(this,"checked",void 0),(0,n.A)(this,"onChange",new e.bkB),(0,n.A)(this,"formControl",void 0),(0,n.A)(this,"errorMessages",void 0)}registerOnChange(i){}registerOnTouched(i){}setDisabledState(i){}writeValue(i){}handleChange(i){this.onChange.emit(i?.target?.checked??!1)}}return p=_,(0,n.A)(_,"\u0275fac",function(i){return new(i||p)}),(0,n.A)(_,"\u0275cmp",e.VBU({type:p,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:t.kq,useExisting:(0,e.Rfq)(()=>p),multi:!0}]),e.aNF],ngContentSelectors:b,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(i,d){1&i&&(e.NAR(),e.j41(0,"div",0)(1,"label",1),e.DNE(2,g,1,2,"input",2)(3,M,1,1,"input",3),e.j41(4,"div",4),e.SdG(5),e.k0s()()()),2&i&&(e.R7$(2),e.vxM(d.formControl?2:3))},dependencies:[T.G,t.Zm,t.BC,t.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),_})()},4978:(E,x,a)=>{a.d(x,{I:()=>p});var n=a(9842),e=a(4438),T=a(6146),t=a(5236);const b=["*",[["","footer",""]]],g=["*","[footer]"];function M(_,c){if(1&_){const i=e.RV6();e.j41(0,"button",7),e.bIt("click",function(){e.eBV(i);const h=e.XpG(2);return e.Njj(h.handleClose())}),e.nrm(1,"img",8),e.k0s()}}function u(_,c){if(1&_&&(e.j41(0,"div",4)(1,"p",5),e.EFF(2),e.nI1(3,"translate"),e.k0s()(),e.DNE(4,M,2,0,"button",6)),2&_){const i=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,i.title)),e.R7$(2),e.vxM(i.onClose.observers.length?4:-1)}}let p=(()=>{var _;class c{constructor(){(0,n.A)(this,"title",void 0),(0,n.A)(this,"onClose",new e.bkB)}handleClose(){this.onClose.emit()}}return _=c,(0,n.A)(c,"\u0275fac",function(d){return new(d||_)}),(0,n.A)(c,"\u0275cmp",e.VBU({type:_,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[e.aNF],ngContentSelectors:g,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(d,h){1&d&&(e.NAR(b),e.j41(0,"div",0)(1,"div",1),e.DNE(2,u,5,4),e.k0s(),e.j41(3,"div",2),e.SdG(4),e.k0s(),e.j41(5,"div",3),e.SdG(6,1),e.k0s()()),2&d&&(e.R7$(2),e.vxM(h.title?2:-1))},dependencies:[T.G,t.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),c})()},4592:(E,x,a)=>{a.r(x),a.d(x,{ModifyTaxesComponent:()=>I});var n=a(9842),e=a(1110),T=a(4477),t=a(4438),b=a(6146),g=a(4978),M=a(344),u=a(4006),p=a(7656),_=a(7136),c=a(9079),i=a(3492),d=a(5236);function h(f,m){if(1&f){const C=t.RV6();t.j41(0,"app-input-tax",9),t.bIt("onSelectedTax",function(s){const o=t.eBV(C).$index,r=t.XpG();return t.Njj(r.handleSelectedTax(s,o))})("onDelete",function(){const s=t.eBV(C).$index,o=t.XpG();return t.Njj(o.Delete(s))}),t.k0s()}2&f&&t.Y8G("tax",m.$implicit)}let I=(()=>{var f;class m{static getComponent(){return m}constructor(l,s){(0,n.A)(this,"dialogRef",void 0),(0,n.A)(this,"data",void 0),(0,n.A)(this,"listIndexTaxesSelected",[]),(0,n.A)(this,"onstop",new t.bkB),(0,n.A)(this,"emitTax",new t.bkB),(0,n.A)(this,"oncancel",new t.bkB),(0,n.A)(this,"ApplyAll",!1),(0,n.A)(this,"listCompanyTax",[]),(0,n.A)(this,"selectAll",!1),(0,n.A)(this,"listTax",[]),(0,n.A)(this,"originData",""),(0,n.A)(this,"toastService",(0,t.WQX)(i.f)),(0,n.A)(this,"companyTaxService",(0,t.WQX)(_.Y)),(0,n.A)(this,"_storeService",(0,t.WQX)(e.n)),(0,n.A)(this,"translate",(0,t.WQX)(d.c$)),(0,n.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,n.A)(this,"taxes",[{selected:!1,taxeNumber:"",name:"",amount:0}]),this.dialogRef=l,this.data=s,this.listTax=this.data??[]}handleClose(){this.dialogRef.close()}ngOnDestroy(){this.taxes=[]}ngOnInit(){this.GetAllCompanyTax()}GetAllCompanyTax(){this.companyTaxService.GetAllCompanyTax({Page:1,PageSize:50,Search:""}).pipe((0,c.pQ)(this.destroyRef)).subscribe({next:s=>{this.originData=JSON.stringify(s.data),this.listCompanyTax=s.data,this.taxes=s.data.slice(),this.taxes.length>0&&this.listTax.length>0&&this.taxes.forEach(r=>{r.selected=!!this.listTax.some(v=>v.companyTaxId?v.companyTaxId===r.id:v.id===r.id)});const o=this.taxes.filter(r=>r.selected);o.length>2&&(o[0].selected=!1),this.listIndexTaxesSelected=this.taxes.map((r,A)=>r.selected?A:-1).filter(r=>-1!==r)}})}stopPropagation(l){this.onstop.emit(l)}addTax(){this.taxes.push({selected:!1,taxeNumber:"",name:"",amount:0})}Delete(l){this.taxes.splice(l,1);const s=this.listIndexTaxesSelected.indexOf(l);-1!==s&&this.listIndexTaxesSelected.splice(s,1)}handleCancel(){this.oncancel.emit(!0),this.dialogRef.close()}handleSubmit(){this._storeService.set_ApplyTaxAll(this.selectAll),this.taxes=this.taxes.filter(o=>o.name||o.taxeNumber||o.amount);const l=this.taxes.reduce((o,r)=>(o[r.name]=(o[r.name]||0)+1,o),{}),s=Object.keys(l).filter(o=>l[o]>1);s.length>0?this.toastService.showWarning(this.translate.instant("TOAST.Warning"),`${this.translate.instant("TOAST.Tax")} ( ${s.toString()})`):(this.dialogRef.close({taxes:this.taxes.filter(o=>o.selected)}),JSON.stringify(this.taxes)!==JSON.stringify(this.originData)&&this.companyTaxService.Create({listItem:this.taxes}).subscribe())}handleSelectedTax(l,s){const o=this.listIndexTaxesSelected.indexOf(s);if(-1!==o)this.taxes[s].selected=!1,this.listIndexTaxesSelected.splice(o,1);else{if(2===this.listIndexTaxesSelected.length){const r=this.listIndexTaxesSelected.shift();this.taxes[r].selected=!1}this.listIndexTaxesSelected.push(s),this.taxes.forEach((r,A)=>{r.selected=this.listIndexTaxesSelected.includes(A)})}}handleChangeCheckAll(l){this.selectAll=l}}return f=m,(0,n.A)(m,"\u0275fac",function(l){return new(l||f)(t.rXU(u.CP),t.rXU(u.Vh))}),(0,n.A)(m,"\u0275cmp",t.VBU({type:f,selectors:[["app-modify-taxes"]],inputs:{ApplyAll:"ApplyAll"},outputs:{onstop:"onstop",emitTax:"emitTax",oncancel:"oncancel"},standalone:!0,features:[t.aNF],decls:15,vars:8,consts:[[3,"onClose","title"],[1,"p-[16px]","w-full","flex","flex-col","gap-[16px]"],[3,"tax"],[1,"flex","justify-start"],[1,"button-link-primary",3,"click"],["src","../../../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","mt-[10px]"],[3,"onChange","checked"],[3,"onCancel","onSubmit"],[3,"onSelectedTax","onDelete","tax"]],template:function(l,s){1&l&&(t.j41(0,"app-inno-modal-wrapper",0),t.bIt("onClose",function(){return s.handleClose()}),t.j41(1,"div",1),t.Z7z(2,h,1,1,"app-input-tax",2,t.fX1),t.j41(4,"div",3)(5,"button",4),t.bIt("click",function(){return s.addTax()}),t.nrm(6,"img",5),t.EFF(7),t.nI1(8,"translate"),t.k0s()(),t.nrm(9,"hr"),t.j41(10,"div",6)(11,"app-inno-form-checkbox",7),t.bIt("onChange",function(r){return s.handleChangeCheckAll(r)}),t.EFF(12),t.nI1(13,"translate"),t.k0s()()(),t.j41(14,"app-inno-modal-footer",8),t.bIt("onCancel",function(){return s.handleCancel()})("onSubmit",function(){return s.handleSubmit()}),t.k0s()()),2&l&&(t.Y8G("title","TAX.AddTax"),t.R7$(2),t.Dyx(s.taxes),t.R7$(5),t.SpI(" ",t.bMT(8,4,"TAX.AddAnother")," "),t.R7$(4),t.Y8G("checked",s.selectAll),t.R7$(),t.SpI(" ",t.bMT(13,6,"TAX.ApplyTax")," "))},dependencies:[b.G,d.D9,T.b,g.I,M.k,p.V]})),m})()}}]);