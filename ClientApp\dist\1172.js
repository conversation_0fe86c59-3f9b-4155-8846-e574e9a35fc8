"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1172],{1172:(M,m,l)=>{l.r(m),l.d(m,{FactorCodeComponent:()=>E});var c=l(9842),x=l(1470),I=l(3492),_=l(467),e=l(4438),C=l(177);const k=["input"];function O(o,d){if(1&o){const a=e.RV6();e.j41(0,"span")(1,"input",2,0),e.bIt("click",function(n){e.eBV(a);const i=e.XpG();return e.Njj(i.onClick(n))})("paste",function(n){const i=e.eBV(a).index,s=e.XpG();return e.Njj(s.onPaste(n,i))})("input",function(n){const i=e.eBV(a).index,s=e.XpG();return e.Njj(s.onInput(n,i))})("keydown",function(n){const i=e.eBV(a).index,s=e.XpG();return e.Njj(s.onKeydown(n,i))}),e.k0s()()}if(2&o){const a=e.XpG();e.AVh("code-hidden",a.isCodeHidden),e.R7$(),e.Y8G("type",a.inputType)("disabled",a.disabled),e.BMQ("inputmode",a.inputMode)("autocapitalize",a.autocapitalize)}}const b=new e.nKC("CodeInputComponentConfig"),v={codeLength:4,inputType:"tel",inputMode:"numeric",initialFocusField:void 0,isCharsCode:!1,isCodeHidden:!1,isPrevFocusableAfterClearing:!0,isFocusingOnLastByClickIfFilled:!1,code:void 0,disabled:!1,autocapitalize:void 0};var f=function(o){return o[o.ready=0]="ready",o[o.reset=1]="reset",o}(f||{});let S=(()=>{var o;class d{constructor(t){if(this.isNonDigitsCode=!1,this.codeChanged=new e.bkB,this.codeCompleted=new e.bkB,this.placeholders=[],this.inputs=[],this.inputsStates=[],this.state={isFocusingAfterAppearingCompleted:!1,isInitialFocusFieldEnabled:!1},Object.assign(this,v),t)for(const n in t)t.hasOwnProperty(n)&&v.hasOwnProperty(n)&&(this[n]=t[n])}ngOnInit(){this.state.isInitialFocusFieldEnabled=!this.isEmpty(this.initialFocusField),this.onCodeLengthChanges()}ngAfterViewInit(){this.inputsListSubscription=this.inputsList.changes.subscribe(this.onInputsListChanges.bind(this)),this.onInputsListChanges(this.inputsList)}ngAfterViewChecked(){this.focusOnInputAfterAppearing()}ngOnChanges(t){t.code&&this.onInputCodeChanges(),t.codeLength&&this.onCodeLengthChanges()}ngOnDestroy(){this.inputsListSubscription&&this.inputsListSubscription.unsubscribe()}reset(t=!1){this.onInputCodeChanges(),this.state.isInitialFocusFieldEnabled&&this.focusOnField(this.initialFocusField),t&&this.emitChanges()}focusOnField(t){if(t>=this._codeLength)throw new Error("The index of the focusing input box should be less than the codeLength.");this.inputs[t].focus()}onClick(t){if(!this.isFocusingOnLastByClickIfFilled)return;const i=this.inputs[this._codeLength-1];t.target===i||!(this.getCurrentFilledCode().length>=this._codeLength)||setTimeout(()=>i.focus())}onInput(t,n){const i=t.target,s=t.data||i.value;if(this.isEmpty(s))return;if(!this.canInputValue(s))return t.preventDefault(),t.stopPropagation(),this.setInputValue(i,null),void this.setStateForInput(i,f.reset);const r=s.toString().trim().split("");for(let p=0;p<r.length;p++){const h=p+n;if(h>this._codeLength-1)break;this.setInputValue(this.inputs[h],r[p])}this.emitChanges();const u=n+r.length;u>this._codeLength-1?i.blur():this.inputs[u].focus()}onPaste(t,n){t.preventDefault(),t.stopPropagation();const i=t.clipboardData?t.clipboardData.getData("text").trim():void 0;if(this.isEmpty(i))return;const s=i.split("");let r=0;for(let u=n;u<this.inputs.length&&r!==s.length;u++){const p=this.inputs[u],h=s[r];if(!this.canInputValue(h))return this.setInputValue(p,null),void this.setStateForInput(p,f.reset);this.setInputValue(p,h.toString()),r++}this.inputs[n].blur(),this.emitChanges()}onKeydown(t,n){var i=this;return(0,_.A)(function*(){const s=t.target,r=i.isEmpty(s.value),u=n-1,p=yield i.isBackspaceKey(t),h=i.isDeleteKey(t);!p&&!h||(t.preventDefault(),i.setInputValue(s,null),r||i.emitChanges(),!(u<0||h)&&(r||i.isPrevFocusableAfterClearing)&&i.inputs[u].focus())})()}onInputCodeChanges(){if(!this.inputs.length)return;if(this.isEmpty(this.code))return void this.inputs.forEach(i=>{this.setInputValue(i,null)});const t=this.code.toString().trim().split("");let n=!0;for(const i of t)if(!this.canInputValue(i)){n=!1;break}this.inputs.forEach((i,s)=>{this.setInputValue(i,n?t[s]:null)})}onCodeLengthChanges(){if(this.codeLength)if(this._codeLength=this.codeLength,this._codeLength>this.placeholders.length){const t=Array(this._codeLength-this.placeholders.length).fill(1);this.placeholders.splice(this.placeholders.length-1,0,...t)}else this._codeLength<this.placeholders.length&&this.placeholders.splice(this._codeLength)}onInputsListChanges(t){if(t.length>this.inputs.length){const n=t.filter((s,r)=>r>this.inputs.length-1);this.inputs.splice(this.inputs.length,0,...n.map(s=>s.nativeElement));const i=Array(n.length).fill(f.ready);this.inputsStates.splice(this.inputsStates.length,0,...i)}else t.length<this.inputs.length&&(this.inputs.splice(t.length),this.inputsStates.splice(t.length));this.onInputCodeChanges()}focusOnInputAfterAppearing(){this.state.isInitialFocusFieldEnabled&&(this.state.isFocusingAfterAppearingCompleted||(this.focusOnField(this.initialFocusField),this.state.isFocusingAfterAppearingCompleted=document.activeElement===this.inputs[this.initialFocusField]))}emitChanges(){setTimeout(()=>this.emitCode(),50)}emitCode(){const t=this.getCurrentFilledCode();this.codeChanged.emit(t),t.length>=this._codeLength&&this.codeCompleted.emit(t)}getCurrentFilledCode(){let t="";for(const n of this.inputs)this.isEmpty(n.value)||(t+=n.value);return t}isBackspaceKey(t){return t.key&&"backspace"===t.key.toLowerCase()||t.keyCode&&8===t.keyCode?Promise.resolve(!0):t.keyCode&&229===t.keyCode?new Promise(i=>{setTimeout(()=>{const s=t.target,r=this.getStateForInput(s)===f.reset;r&&this.setStateForInput(s,f.ready),i(0===s.selectionStart&&!r)})}):Promise.resolve(!1)}isDeleteKey(t){return t.key&&"delete"===t.key.toLowerCase()||t.keyCode&&46===t.keyCode}setInputValue(t,n){const s="has-value",r="empty";this.isEmpty(n)?(t.value="",t.classList.remove(s),t.parentElement.classList.add(r)):(t.value=n,t.classList.add(s),t.parentElement.classList.remove(r))}canInputValue(t){return!this.isEmpty(t)&&(/^[0-9]+$/.test(t.toString())||this.isCharsCode||this.isNonDigitsCode)}setStateForInput(t,n){const i=this.inputs.indexOf(t);i<0||(this.inputsStates[i]=n)}getStateForInput(t){const n=this.inputs.indexOf(t);return this.inputsStates[n]}isEmpty(t){return null==t||!t.toString().length}}return(o=d).\u0275fac=function(t){return new(t||o)(e.rXU(b,8))},o.\u0275cmp=e.VBU({type:o,selectors:[["code-input"]],viewQuery:function(t,n){if(1&t&&e.GBs(k,5),2&t){let i;e.mGM(i=e.lsd())&&(n.inputsList=i)}},inputs:{codeLength:"codeLength",inputType:"inputType",inputMode:"inputMode",initialFocusField:"initialFocusField",isNonDigitsCode:"isNonDigitsCode",isCharsCode:"isCharsCode",isCodeHidden:"isCodeHidden",isPrevFocusableAfterClearing:"isPrevFocusableAfterClearing",isFocusingOnLastByClickIfFilled:"isFocusingOnLastByClickIfFilled",code:"code",disabled:"disabled",autocapitalize:"autocapitalize"},outputs:{codeChanged:"codeChanged",codeCompleted:"codeCompleted"},features:[e.OA$],decls:1,vars:1,consts:[["input",""],[3,"code-hidden",4,"ngFor","ngForOf"],["autocomplete","one-time-code",3,"click","paste","input","keydown","type","disabled"]],template:function(t,n){1&t&&e.DNE(0,O,3,6,"span",1),2&t&&e.Y8G("ngForOf",n.placeholders)},dependencies:[C.Sq],styles:["[_nghost-%COMP%]{--text-security-type: disc;--item-spacing: 4px;--item-height: 4.375em;--item-border: 1px solid #dddddd;--item-border-bottom: 1px solid #dddddd;--item-border-has-value: 1px solid #dddddd;--item-border-bottom-has-value: 1px solid #dddddd;--item-border-focused: 1px solid #dddddd;--item-border-bottom-focused: 1px solid #dddddd;--item-shadow-focused: 0px 1px 5px rgba(221, 221, 221, 1);--item-border-radius: 5px;--item-background: transparent;--item-font-weight: 300;--color: #171516;display:flex;transform:translateZ(0);font-size:inherit;color:var(--color)}[_nghost-%COMP%]   span[_ngcontent-%COMP%]{display:block;flex:1;padding-right:var(--item-spacing)}[_nghost-%COMP%]   span[_ngcontent-%COMP%]:first-child{padding-left:var(--item-spacing)}[_nghost-%COMP%]   span.code-hidden[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{text-security:var(--text-security-type);-webkit-text-security:var(--text-security-type);-moz-text-security:var(--text-security-type)}[_nghost-%COMP%]   input[_ngcontent-%COMP%]{width:100%;height:var(--item-height);color:inherit;background:var(--item-background);text-align:center;font-size:inherit;font-weight:var(--item-font-weight);border:var(--item-border);border-bottom:var(--item-border-bottom);border-radius:var(--item-border-radius);-webkit-appearance:none;transform:translateZ(0);-webkit-transform:translate3d(0,0,0);outline:none}[_nghost-%COMP%]   input.has-value[_ngcontent-%COMP%]{border:var(--item-border-has-value);border-bottom:var(--item-border-bottom-has-value)}[_nghost-%COMP%]   input[_ngcontent-%COMP%]:focus{border:var(--item-border-focused);border-bottom:var(--item-border-bottom-focused);box-shadow:var(--item-shadow-focused)}"]}),d})(),A=(()=>{var o;class d{static forRoot(t){return{ngModule:d,providers:[{provide:b,useValue:t}]}}}return(o=d).\u0275fac=function(t){return new(t||o)},o.\u0275mod=e.$C({type:o}),o.\u0275inj=e.G2t({imports:[C.MD]}),d})();var y=l(4006),L=l(2928),F=l(9079),T=l(1342),g=l(5236);const w=["codeInput"];document.querySelectorAll(".otp-field > input");let E=(()=>{var o;class d{static getComponent(){return d}constructor(t,n){(0,c.A)(this,"data",void 0),(0,c.A)(this,"dialogRef",void 0),(0,c.A)(this,"_code",""),(0,c.A)(this,"spinnerService",(0,e.WQX)(T.D)),(0,c.A)(this,"_toastService",(0,e.WQX)(I.f)),(0,c.A)(this,"auth_services",(0,e.WQX)(L.k)),(0,c.A)(this,"translate",(0,e.WQX)(g.c$)),(0,c.A)(this,"codeInput",void 0),(0,c.A)(this,"destroyRef",(0,e.WQX)(e.abz)),this.data=t,this.dialogRef=n}ngOnInit(){}CloseDia(t){this.dialogRef?.close(t)}goBack(){this.dialogRef?.close()}ResendCode(){this.spinnerService.show(),this.auth_services.ResendCode(this.data.email).pipe((0,F.pQ)(this.destroyRef)).subscribe({next:t=>{this.spinnerService.hide(),t&&(this.clearCodeInput(),this._toastService.showSuccess(this.translate.instant("TOAST.Sent"),this.translate.instant("TOAST.SentCode")))},error:()=>{this.spinnerService.hide(),this._toastService.showError(this.translate.instant("TOAST.Error"),this.translate.instant("TOAST.ResendCodeFailed"))}})}clearCodeInput(){this._code=null,this.codeInput.reset()}Login(){this.spinnerService.show(),this.auth_services.SignIn(this.data.email,this._code).pipe((0,F.pQ)(this.destroyRef)).subscribe(t=>{t?this.CloseDia(t):this._toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})}onCodeChanged(t){this._code=t,6==this._code.length&&this.Login()}}return o=d,(0,c.A)(d,"\u0275fac",function(t){return new(t||o)(e.rXU(y.Vh),e.rXU(y.CP))}),(0,c.A)(d,"\u0275cmp",e.VBU({type:o,selectors:[["app-factor-code"]],viewQuery:function(t,n){if(1&t&&e.GBs(w,5),2&t){let i;e.mGM(i=e.lsd())&&(n.codeInput=i.first)}},standalone:!0,features:[e.aNF],decls:28,vars:14,consts:[["codeInput",""],[3,"onClose"],[1,"w-full","max-w-[400px]"],[1,"text-headline-sm-bold","text-text-primary","text-center"],[1,"mx-auto","mt-[12px]","w-[50px]","h-[50px]","flex","justify-center","items-center","bg-bg-brand-strong","rounded-full"],[1,"material-icons","!text-bg-brand-primary","!text-[30px]"],[1,"w-full","flex","flex-col","items-center","gap-[4px]","mt-[16px]"],[1,"text-text-sm-regular","text-center","text-text-tertiary"],[1,"text-text-sm-regular","text-center","font-semibold"],[1,"w-full","mt-[30px]"],[3,"codeChanged","codeLength"],[1,"button-size-md","button-primary","w-full","justify-center","mt-[34px]"],[1,"w-full","flex","flex-col","items-center","gap-[4px]","mt-[20px]"],[1,"text-text-sm-regular","cursor-pointer","text-text-brand-primary","text-center","font-semibold",3,"click"]],template:function(t,n){if(1&t){const i=e.RV6();e.j41(0,"app-innobook-modal-wrapper",1),e.bIt("onClose",function(){return e.eBV(i),e.Njj(n.goBack())}),e.j41(1,"div",2)(2,"p",3),e.EFF(3," Enter verification code "),e.k0s(),e.j41(4,"div",4)(5,"i",5),e.EFF(6,"mail"),e.k0s()(),e.j41(7,"div",6)(8,"p",7),e.EFF(9," We've sent a code to "),e.k0s(),e.j41(10,"p",8),e.EFF(11),e.k0s(),e.j41(12,"p",7),e.EFF(13),e.nI1(14,"translate"),e.k0s()(),e.j41(15,"div",9)(16,"code-input",10,0),e.bIt("codeChanged",function(r){return e.eBV(i),e.Njj(n.onCodeChanged(r))}),e.k0s()(),e.j41(18,"button",11),e.EFF(19),e.nI1(20,"translate"),e.k0s(),e.j41(21,"div",12)(22,"p",7),e.EFF(23),e.nI1(24,"translate"),e.k0s(),e.j41(25,"p",13),e.bIt("click",function(){return e.eBV(i),e.Njj(n.ResendCode())}),e.EFF(26),e.nI1(27,"translate"),e.k0s()()()()}2&t&&(e.R7$(11),e.SpI(" ",n.data.email," "),e.R7$(2),e.SpI(" ",e.bMT(14,6,"VERIFICATION.Instruction")," "),e.R7$(3),e.Y8G("codeLength",6),e.R7$(3),e.SpI(" ",e.bMT(20,8,"VERIFICATION.LoginButton")," "),e.R7$(4),e.SpI(" ",e.bMT(24,10,"VERIFICATION.NotReceived")," "),e.R7$(3),e.SpI(" ",e.bMT(27,12,"VERIFICATION.Resend")," "))},dependencies:[A,S,g.h,g.D9,x.j],styles:["code-input[_ngcontent-%COMP%]{--item-border-bottom: 2px solid #dddddd;--item-border-bottom-has-value: 2px solid #888888;--item-border-bottom-focused: 2px solid #809070;--item-height: 55px;--item-spacing: 6px;--item-border-radius: 12px;--item-font-size: 30px}@media screen and (max-width: 500px){code-input[_ngcontent-%COMP%]{--item-height: 50px;--item-spacing: 4px;--item-border-radius: 10px;--item-font-size: 24px}}p[_ngcontent-%COMP%]{margin-bottom:0}"]})),d})()}}]);