"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3325],{6196:(X,A,c)=>{c.d(A,{A:()=>R});var a=c(9842),b=c(6146),u=c(4438),e=c(177),S=c(5236);const M=(v,m,f,p,r,I)=>({"text-text-warning bg-bg-warning-primary":v,"text-text-primary bg-bg-tertiary":m,"text-text-primary bg-bg-disabled":f,"text-text-success bg-bg-success-secondary":p,"text-text-success bg-bg-secondary-subtle":r,"text-text-danger bg-bg-secondary":I});let R=(()=>{var v;class m{constructor(){(0,a.A)(this,"STATUS",{DRAFT:0,UN_BILLED:1,NON_BILLABLE:2,PAID:3,BILLED:4,SENT:5}),(0,a.A)(this,"status",void 0)}getStatusText(){switch(this.status){case this.STATUS.DRAFT:return"STATUS.Draft";case this.STATUS.UN_BILLED:return"STATUS.Unbilled";case this.STATUS.NON_BILLABLE:return"STATUS.NonBillable";case this.STATUS.PAID:return"STATUS.Paid";case this.STATUS.BILLED:return"STATUS.Billed";case this.STATUS.SENT:return"STATUS.Sent";default:return"Unknown"}}}return v=m,(0,a.A)(m,"\u0275fac",function(p){return new(p||v)}),(0,a.A)(m,"\u0275cmp",u.VBU({type:v,selectors:[["app-inno-status"]],inputs:{status:"status"},standalone:!0,features:[u.aNF],decls:3,vars:11,consts:[[1,"text-text-sm-semibold","px-[8px]","py-[2px]","rounded-md","text-center",3,"ngClass"]],template:function(p,r){1&p&&(u.j41(0,"span",0),u.EFF(1),u.nI1(2,"translate"),u.k0s()),2&p&&(u.Y8G("ngClass",u.l4e(4,M,r.status===r.STATUS.UN_BILLED,r.status===r.STATUS.NON_BILLABLE,r.status===r.STATUS.DRAFT,r.status===r.STATUS.PAID,r.status===r.STATUS.BILLED,r.status===r.STATUS.SENT)),u.R7$(),u.SpI(" ",u.bMT(2,2,r.getStatusText()),"\n"))},dependencies:[b.G,e.YU,S.D9]})),m})()},3325:(X,A,c)=>{c.r(A),c.d(A,{InvoiceComponent:()=>be});var a=c(9842),b=c(2928),u=c(4433),e=c(4438),S=c(6146),M=c(8193),R=c(4006),v=c(8600),m=c(5900),f=c(6473),p=c(1110),r=c(3492),I=c(1556),E=c(5644),D=c(2840),d=c(1448),h=c(9079),g=c(33),G=c(5245),T=c(5236),y=c(1970),w=c(3200),F=c(6196),V=c(8556),N=c(5277),_=c(6586),$=c(9424),P=c(6617),B=c(2953),x=c(177);const U=["grid"],k=i=>({"mb-28":i});function O(i,s){1&i&&(e.j41(0,"div",2),e.nrm(1,"app-inno-spin",3),e.k0s())}function Q(i,s){if(1&i){const n=e.RV6();e.j41(0,"div",15),e.bIt("click",function(){const o=e.eBV(n).$implicit,l=e.XpG(3);return e.Njj(l.handleEdit(o))}),e.j41(1,"p",16),e.EFF(2),e.k0s(),e.j41(3,"p",17),e.EFF(4),e.k0s()()}if(2&i){let n,t;const o=s.$implicit;e.R7$(2),e.SpI(" ",null!==(n=o.invoiceNumber)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" ",null!==(t=o.notes)&&void 0!==t?t:""," ")}}function z(i,s){if(1&i&&(e.j41(0,"p",17),e.EFF(1),e.k0s()),2&i){let n;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(n=null==t||null==t.client?null:t.client.emailAddress)&&void 0!==n?n:""," ")}}function L(i,s){if(1&i&&(e.j41(0,"div",18),e.nrm(1,"ngx-avatars",19),e.j41(2,"div",20)(3,"p",16),e.EFF(4),e.k0s(),e.DNE(5,z,2,1,"p",17),e.k0s()()),2&i){let n;const t=s.$implicit;e.R7$(),e.Y8G("size",40)("name",null==t||null==t.client?null:t.client.clientName),e.R7$(3),e.SpI(" ",null!==(n=null==t||null==t.client?null:t.client.clientName)&&void 0!==n?n:""," "),e.R7$(),e.vxM(null!=t&&null!=t.client&&t.client.emailAddress?5:-1)}}function W(i,s){if(1&i&&(e.j41(0,"span"),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&i){const n=s.$implicit,t=e.XpG(3);e.R7$(),e.JRh(e.i5U(2,1,n.invoiceDate,t._storeService.getdateFormat()))}}function Y(i,s){if(1&i&&(e.j41(0,"span"),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&i){const n=s.$implicit,t=e.XpG(3);e.R7$(),e.JRh(e.i5U(2,1,n.dueDate,t._storeService.getdateFormat()))}}function J(i,s){1&i&&e.nrm(0,"app-inno-status",21),2&i&&e.Y8G("status",s.$implicit.status)}function K(i,s){if(1&i&&(e.j41(0,"p",22),e.EFF(1),e.nI1(2,"decimal"),e.nI1(3,"formatNumber"),e.k0s()),2&i){const n=s.$implicit;e.R7$(),e.SpI(" $",e.bMT(3,4,e.i5U(2,1,n.totalAmount,2))," ")}}function Z(i,s){if(1&i){const n=e.RV6();e.j41(0,"app-inno-table-action",23),e.bIt("onEdit",function(){const o=e.eBV(n).$implicit,l=e.XpG(4);return e.Njj(l.handleEdit(o))})("onDelete",function(){const o=e.eBV(n).$implicit,l=e.XpG(4);return e.Njj(l.handleDelete(o))}),e.k0s()}}function H(i,s){1&i&&(e.j41(0,"e-column",13),e.DNE(1,Z,1,0,"ng-template",null,1,e.C5r),e.k0s())}function q(i,s){if(1&i){const n=e.RV6();e.j41(0,"div",4)(1,"ejs-grid",6,0),e.bIt("rowDrop",function(o){e.eBV(n);const l=e.XpG(2);return e.Njj(l.onDragStop(o))})("rowDragStart",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.onDragStart())})("actionBegin",function(o){e.eBV(n);const l=e.XpG(2);return e.Njj(l.onActionBegin(o))}),e.j41(3,"e-columns")(4,"e-column",7),e.nI1(5,"translate"),e.DNE(6,Q,5,2,"ng-template",null,1,e.C5r),e.k0s(),e.j41(8,"e-column",8),e.nI1(9,"translate"),e.DNE(10,L,6,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(12,"e-column",9),e.nI1(13,"translate"),e.DNE(14,W,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(16,"e-column",10),e.nI1(17,"translate"),e.DNE(18,Y,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(20,"e-column",11),e.nI1(21,"translate"),e.DNE(22,J,1,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(24,"e-column",12),e.nI1(25,"translate"),e.DNE(26,K,4,6,"ng-template",null,1,e.C5r),e.k0s(),e.DNE(28,H,3,0,"e-column",13),e.k0s()(),e.j41(29,"ejs-pager",14),e.bIt("click",function(o){e.eBV(n);const l=e.XpG(2);return e.Njj(l.onPageChange(o))}),e.k0s()()}if(2&i){const n=e.XpG(2);e.Y8G("ngClass",e.eq3(28,k,n._storeService.getIsRunning())),e.R7$(),e.Y8G("allowRowDragAndDrop",!0)("allowSorting",!0)("sortSettings",n.sortOptions)("dataSource",n.dataSource),e.R7$(3),e.Y8G("headerText",e.bMT(5,16,"INVOICES.GIRD.InvoiceNumber")),e.R7$(4),e.Y8G("headerText",e.bMT(9,18,"INVOICES.GIRD.Clients")),e.R7$(4),e.Y8G("headerText",e.bMT(13,20,"INVOICES.GIRD.IssuedDate")),e.R7$(4),e.Y8G("headerText",e.bMT(17,22,"INVOICES.GIRD.DueDate")),e.R7$(4),e.Y8G("headerText",e.bMT(21,24,"INVOICES.GIRD.Status")),e.R7$(4),e.Y8G("headerText",e.bMT(25,26,"INVOICES.GIRD.Amount")),e.R7$(4),e.vxM(n.authenticationService.getBusinessRole()!=n.Role.Accountant?28:-1),e.R7$(),e.Y8G("pageSize",n.pageSizesDefault)("totalRecordsCount",n.totalPages)("currentPage",n.currentPage)("pageSizes",n.pageSizes)}}function ee(i,s){1&i&&(e.j41(0,"div",5),e.nrm(1,"app-inno-empty-data",24),e.k0s())}function te(i,s){if(1&i&&e.DNE(0,q,30,30,"div",4)(1,ee,2,0,"div",5),2&i){const n=e.XpG();e.vxM(null!=n.dataSource&&n.dataSource.length?0:1)}}D.is5.Inject(D.Rav);let ne=(()=>{var i;class s{constructor(){(0,a.A)(this,"isLoading",!1),(0,a.A)(this,"isDragging",!1),(0,a.A)(this,"isMobile",!1),(0,a.A)(this,"dataSource",void 0),(0,a.A)(this,"totalPages",1),(0,a.A)(this,"currentPage",1),(0,a.A)(this,"pageSizes",[10,20,50,100]),(0,a.A)(this,"pageSizesDefault",10),(0,a.A)(this,"search",""),(0,a.A)(this,"sort",void 0),(0,a.A)(this,"sortOptions",{columns:[]}),(0,a.A)(this,"reloadCalculation",new e.bkB),(0,a.A)(this,"grid",void 0),(0,a.A)(this,"columnName",void 0),(0,a.A)(this,"direction",void 0),(0,a.A)(this,"Role",B.X),(0,a.A)(this,"translate",(0,e.WQX)(T.c$)),(0,a.A)(this,"_toastService",(0,e.WQX)(r.f)),(0,a.A)(this,"activatedRoute",(0,e.WQX)(g.nX)),(0,a.A)(this,"router",(0,e.WQX)(g.Ix)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"_invoiceService",(0,e.WQX)(E.p)),(0,a.A)(this,"layoutUtilsService",(0,e.WQX)(I.Z)),(0,a.A)(this,"dataService",(0,e.WQX)(N.u)),(0,a.A)(this,"_subscriptions",[]),(0,a.A)(this,"_storeService",(0,e.WQX)(p.n)),(0,a.A)(this,"authenticationService",(0,e.WQX)(b.k))}ngAfterViewInit(){if(this.isMobile=window.innerWidth<=768,this.isMobile){const t=document.querySelector(".e-grid");t&&(t.addEventListener("touchstart",o=>{this.isDragging=!1}),t.addEventListener("touchmove",o=>{this.isDragging&&o.preventDefault()}),t.addEventListener("touchend",()=>{setTimeout(()=>{this.isDragging=!1},200)}))}}onDragStart(){this.isDragging=!0}ngOnInit(){this._subscriptions.push(this.dataService.GetInvoiceFilter().pipe((0,G.i)(1)).subscribe(t=>{if(t?.typeView!==_.A.Created_Tab)return;const o=this.activatedRoute.snapshot.queryParams.page||1;this.search=t?.textSearch??"",this.GetAllInvoice(),this.currentPage=o})),this.activatedRoute.queryParams.pipe((0,h.pQ)(this.destroyRef)).subscribe(t=>{this.currentPage=t?.page??1,this.GetAllInvoice()})}handleDelete(t){const o=this.translate.instant("Delete Invoice !"),l=this.translate.instant("Do you want to delete?");this.layoutUtilsService.alertDelete({title:o,description:l}).then(C=>{C&&this._invoiceService.DeleteInvoice([t.id],!1).pipe((0,h.pQ)(this.destroyRef)).subscribe(j=>{j?(this.reloadCalculation.emit(!0),this.GetAllInvoice(),this._toastService.showSuccess("Delete","Success")):this._toastService.showError("Fail","Fail")})})}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize,this.GetAllInvoice()),t?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:t.currentPage},queryParamsHandling:"merge"})}ReloadData(){this.GetAllInvoice()}GetAllInvoice(){const t={Page:this.currentPage??1,Search:this.search??"",PageSize:this.pageSizesDefault,...this.sort};this.isLoading=!0,this._invoiceService.GetAllInvoice(t).subscribe({next:o=>{o&&(this.totalPages=o.totalRecords,this.dataSource=o.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))},complete:()=>{this.isLoading=!1}})}onDragStop(t){let o={fromInvoiceId:this.dataSource[t.fromIndex].id,dropInvoiceId:this.dataSource[t.dropIndex].id,fromIndex:this.dataSource[t.fromIndex].position,dropIndex:this.dataSource[t.dropIndex].position};this.isDragging=!1,this._invoiceService.ChangePosition(o).pipe((0,h.pQ)(this.destroyRef)).subscribe()}handleEdit(t){this.router.navigate(["/invoices",t.id])}onActionBegin(t){if("sorting"===t.requestType){if(this.columnName=t.columnName,this.direction=t.direction,this.sort={columnName:t.columnName,direction:t.direction},this.columnName)return void this.GetAllInvoice();this.sortOptions={columns:[]},this.sort=null,this.GetAllInvoice()}}ngOnDestroy(){this._subscriptions.forEach(t=>t.unsubscribe())}}return i=s,(0,a.A)(s,"\u0275fac",function(t){return new(t||i)}),(0,a.A)(s,"\u0275cmp",e.VBU({type:i,selectors:[["app-invoice-management"]],viewQuery:function(t,o){if(1&t&&e.GBs(U,5),2&t){let l;e.mGM(l=e.lsd())&&(o.grid=l.first)}},outputs:{reloadCalculation:"reloadCalculation"},standalone:!0,features:[e.Jv_([I.Z]),e.aNF],decls:2,vars:1,consts:[["grid",""],["template",""],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],["size","lg"],[1,"w-full","mt-[12px]",3,"ngClass"],[1,"container-full","mt-[24px]"],[1,"customTable",3,"rowDrop","rowDragStart","actionBegin","allowRowDragAndDrop","allowSorting","sortSettings","dataSource"],["width","180","field","invoiceNumber",3,"headerText"],["width","250","field","ClientName",3,"headerText"],["width","150","field","invoiceDate",3,"headerText"],["width","150","field","dueDate",3,"headerText"],["width","120","field","status",3,"headerText"],["width","120","field","totalAmount",3,"headerText"],["field","action","width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","cursor-pointer",3,"click"],[1,"text-text-primary","text-text-md-semibold"],[1,"text-text-tertiary","text-text-xs-regular"],[1,"w-full","flex","gap-[12px]","items-center"],[1,"shrink-0",3,"size","name"],[1,"w-full"],[3,"status"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],["title","No active invoice at the moment","description","You can create a new invoice"]],template:function(t,o){1&t&&e.DNE(0,O,2,0,"div",2)(1,te,2,1),2&t&&e.vxM(o.isLoading?0:1)},dependencies:[S.G,x.YU,x.vh,T.D9,F.A,w.J,V.K,g.iI,y.mC,y.fw,d.gFV,d._ab,d.eeu,d.rFS,d.LGG,d.cvh,d.iov,d.BzB,$.f,P.p,u.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),s})();const ie=["grid"],oe=i=>({"mb-28":i});function ae(i,s){1&i&&(e.j41(0,"div",2),e.nrm(1,"app-inno-spin",3),e.k0s())}function se(i,s){1&i&&(e.j41(0,"span"),e.EFF(1,"Unknown"),e.k0s())}function le(i,s){if(1&i&&(e.j41(0,"div",15)(1,"p",16),e.EFF(2),e.k0s(),e.j41(3,"p",17),e.EFF(4),e.k0s()()),2&i){let n,t;const o=s.$implicit;e.R7$(2),e.SpI(" ",null!==(n=o.invoiceNumber)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" ",null!==(t=o.notes)&&void 0!==t?t:""," ")}}function ce(i,s){if(1&i&&(e.j41(0,"p",17),e.EFF(1),e.k0s()),2&i){let n;const t=e.XpG().$implicit;e.R7$(),e.SpI(" ",null!==(n=null==t||null==t.company?null:t.company.email)&&void 0!==n?n:""," ")}}function re(i,s){if(1&i&&(e.j41(0,"div",18),e.nrm(1,"ngx-avatars",19),e.j41(2,"div",15)(3,"p",16),e.EFF(4),e.k0s(),e.DNE(5,ce,2,1,"p",17),e.k0s()()),2&i){let n;const t=s.$implicit;e.R7$(),e.Y8G("size",40)("name",null==t||null==t.client?null:t.client.clientName),e.R7$(3),e.SpI(" ",null!==(n=null==t||null==t.company?null:t.company.businessName)&&void 0!==n?n:""," "),e.R7$(),e.vxM(null!=t&&null!=t.company&&t.company.email?5:-1)}}function de(i,s){if(1&i&&(e.j41(0,"span"),e.EFF(1),e.nI1(2,"date"),e.k0s()),2&i){const n=s.$implicit,t=e.XpG(3);e.R7$(),e.JRh(e.i5U(2,1,n.invoiceDate,t._storeService.getdateFormat()))}}function ue(i,s){1&i&&e.nrm(0,"app-inno-status",20),2&i&&e.Y8G("status",s.$implicit.status)}function pe(i,s){if(1&i&&(e.j41(0,"p",21),e.EFF(1),e.nI1(2,"decimal"),e.k0s()),2&i){const n=s.$implicit;e.R7$(),e.SpI(" $",e.i5U(2,1,n.paidAmount,2)," ")}}function me(i,s){if(1&i){const n=e.RV6();e.j41(0,"app-inno-table-action",22),e.bIt("onEdit",function(){const o=e.eBV(n).$implicit,l=e.XpG(3);return e.Njj(l.handleEdit(o))})("onDelete",function(){const o=e.eBV(n).$implicit,l=e.XpG(3);return e.Njj(l.handleDelete(o))}),e.k0s()}}function _e(i,s){if(1&i){const n=e.RV6();e.j41(0,"div",4)(1,"ejs-grid",6,0),e.bIt("actionBegin",function(o){e.eBV(n);const l=e.XpG(2);return e.Njj(l.onActionBegin(o))}),e.j41(3,"e-columns")(4,"e-column",7),e.DNE(5,se,2,0,"ng-template",null,1,e.C5r),e.k0s(),e.j41(7,"e-column",8),e.DNE(8,le,5,2,"ng-template",null,1,e.C5r),e.k0s(),e.j41(10,"e-column",9),e.DNE(11,re,6,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(13,"e-column",10),e.DNE(14,de,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(16,"e-column",11),e.DNE(17,ue,1,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(19,"e-column",12),e.DNE(20,pe,3,4,"ng-template",null,1,e.C5r),e.k0s(),e.j41(22,"e-column",13),e.DNE(23,me,1,0,"ng-template",null,1,e.C5r),e.k0s()()(),e.j41(25,"ejs-pager",14),e.bIt("click",function(o){e.eBV(n);const l=e.XpG(2);return e.Njj(l.onPageChange(o))}),e.k0s()()}if(2&i){const n=e.XpG(2);e.Y8G("ngClass",e.eq3(8,oe,n._storeService.getIsRunning())),e.R7$(),e.Y8G("dataSource",n.dataSource)("sortSettings",n.sortOptions)("allowSorting",!0),e.R7$(24),e.Y8G("pageSize",n.pageSizesDefault)("totalRecordsCount",n.totalPages)("currentPage",n.currentPage)("pageSizes",n.pageSizes)}}function ve(i,s){1&i&&(e.j41(0,"div",5),e.nrm(1,"app-inno-empty-data",23),e.k0s())}function he(i,s){if(1&i&&e.DNE(0,_e,26,10,"div",4)(1,ve,2,0,"div",5),2&i){const n=e.XpG();e.vxM(null!=n.dataSource&&n.dataSource.length?0:1)}}D.is5.Inject(D.Rav);let ge=(()=>{var i;class s{constructor(){(0,a.A)(this,"isLoading",!1),(0,a.A)(this,"dataSource",void 0),(0,a.A)(this,"totalPages",1),(0,a.A)(this,"currentPage",1),(0,a.A)(this,"pageSizes",[10,20,50,100]),(0,a.A)(this,"pageSizesDefault",10),(0,a.A)(this,"reloadCalculation",new e.bkB),(0,a.A)(this,"grid",void 0),(0,a.A)(this,"columnName",void 0),(0,a.A)(this,"direction",void 0),(0,a.A)(this,"sort",void 0),(0,a.A)(this,"sortOptions",{columns:[]}),(0,a.A)(this,"search",""),(0,a.A)(this,"activatedRoute",(0,e.WQX)(g.nX)),(0,a.A)(this,"router",(0,e.WQX)(g.Ix)),(0,a.A)(this,"layoutUtilsService",(0,e.WQX)(I.Z)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"_toastService",(0,e.WQX)(r.f)),(0,a.A)(this,"translate",(0,e.WQX)(T.c$)),(0,a.A)(this,"_invoiceService",(0,e.WQX)(E.p)),(0,a.A)(this,"dataService",(0,e.WQX)(N.u)),(0,a.A)(this,"_storeService",(0,e.WQX)(p.n)),(0,a.A)(this,"_subscriptions",[])}ngOnInit(){this._subscriptions.push(this.dataService.GetInvoiceFilter().pipe((0,G.i)(1)).subscribe(t=>{t?.typeView===_.A.Created_Tab&&(this.search=t?.textSearch??"",this.GetInvoiceSendToMe())})),this.activatedRoute.queryParams.pipe((0,h.pQ)(this.destroyRef)).subscribe(t=>{this.currentPage=t?.page??1,this.GetInvoiceSendToMe()})}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize,this.GetInvoiceSendToMe()),t?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:t.currentPage},queryParamsHandling:"merge"})}ReloadData(){this.GetInvoiceSendToMe()}GetInvoiceSendToMe(){const t={Page:this.currentPage??1,Search:this.search??"",PageSize:this.pageSizesDefault,...this.sort};this.isLoading=!0,this._invoiceService.GetAllInvoiceSendToMe(t).subscribe({next:o=>{o&&(this.totalPages=o.totalRecords,this.dataSource=o.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))},complete:()=>{this.isLoading=!1}})}handleEdit(t){this.router.navigate(["/invoices",t.id])}handleDelete(t){const o=this.translate.instant("Delete Invoice !"),l=this.translate.instant("Do you want to delete?");this.layoutUtilsService.alertDelete({title:o,description:l}).then(C=>{C&&this._invoiceService.DeleteInvoice([t.id],!1).pipe((0,h.pQ)(this.destroyRef)).subscribe(j=>{j?(this.reloadCalculation.emit(!0),this.GetInvoiceSendToMe(),this._toastService.showSuccess("Delete","Success")):this._toastService.showError("Fail","Fail")})})}onActionBegin(t){if("sorting"===t.requestType){if(this.columnName=t.columnName,this.direction=t.direction,this.sort={columnName:t.columnName,direction:t.direction},this.columnName)return void this.GetInvoiceSendToMe();this.sortOptions={columns:[]},this.sort=null,this.GetInvoiceSendToMe()}}ngOnDestroy(){this._subscriptions.forEach(t=>t.unsubscribe())}}return i=s,(0,a.A)(s,"\u0275fac",function(t){return new(t||i)}),(0,a.A)(s,"\u0275cmp",e.VBU({type:i,selectors:[["app-invoice-send-to-me"]],viewQuery:function(t,o){if(1&t&&e.GBs(ie,5),2&t){let l;e.mGM(l=e.lsd())&&(o.grid=l.first)}},outputs:{reloadCalculation:"reloadCalculation"},standalone:!0,features:[e.Jv_([I.Z]),e.aNF],decls:2,vars:1,consts:[["grid",""],["template",""],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],["size","lg"],[1,"w-full","mt-[12px]",3,"ngClass"],[1,"container-full","mt-[24px]"],[1,"customTable",3,"actionBegin","dataSource","sortSettings","allowSorting"],["headerText","Sent Date","width","150"],["headerText","Invoice Number","width","180","field","invoiceNumber"],["headerText","Sender","width","250"],["headerText","Issued Date","width","150","field","invoiceDate"],["headerText","Status","width","120"],["headerText","Amount","width","120","field","paidAmount"],["field","action","headerText","","width","100"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full"],[1,"text-text-primary","text-text-md-semibold"],[1,"text-text-tertiary","text-text-xs-regular"],[1,"w-full","flex","gap-[12px]","items-center"],[1,"shrink-0",3,"size","name"],[3,"status"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],["title","No active invoice at the moment","description","You can create a new invoice"]],template:function(t,o){1&t&&e.DNE(0,ae,2,0,"div",2)(1,he,2,1),2&t&&e.vxM(o.isLoading?0:1)},dependencies:[S.G,x.YU,x.vh,F.A,w.J,V.K,g.iI,y.mC,y.fw,d.gFV,d._ab,d.eeu,d.rFS,d.LGG,d.cvh,d.iov,d.BzB,$.f,u.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),s})();const Ce=["gridManagement"],fe=["gridSendToMe"],Ie=(i,s)=>({"grid-cols-3":i,"grid-cols-2":s});function Se(i,s){if(1&i){const n=e.RV6();e.j41(0,"button",16),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.NewInvoice())}),e.nrm(1,"img",17),e.EFF(2),e.nI1(3,"translate"),e.k0s()}2&i&&(e.R7$(2),e.SpI(" ",e.bMT(3,1,"INVOICES.Buttons.NewInvoice")," "))}function Te(i,s){if(1&i&&(e.j41(0,"div",13)(1,"p",14),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"p",4),e.EFF(5),e.nI1(6,"decimal"),e.nI1(7,"formatNumber"),e.k0s()()),2&i){const n=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,"INVOICES.Summary.TotalInDraft")),e.R7$(3),e.JRh(e.bMT(7,7,e.i5U(6,4,null==n.calculationInvoice?null:n.calculationInvoice.totalDraft,2)))}}function xe(i,s){if(1&i){const n=e.RV6();e.j41(0,"app-invoice-management",18,0),e.bIt("reloadCalculation",function(o){e.eBV(n);const l=e.XpG();return e.Njj(l.reloadCalculation(o))}),e.k0s()}}function Ae(i,s){if(1&i){const n=e.RV6();e.j41(0,"app-invoice-send-to-me",18,1),e.bIt("reloadCalculation",function(o){e.eBV(n);const l=e.XpG();return e.Njj(l.reloadCalculation(o))}),e.k0s()}}let be=(()=>{var i;class s{constructor(){(0,a.A)(this,"tab1",void 0),(0,a.A)(this,"tab2",void 0),(0,a.A)(this,"Role",B.X),(0,a.A)(this,"totalDraft",0),(0,a.A)(this,"invoiceView",_.A),(0,a.A)(this,"currentTypeView",void 0),(0,a.A)(this,"calculationInvoice",null),(0,a.A)(this,"textSearch",""),(0,a.A)(this,"listTypeView",[]),(0,a.A)(this,"router",(0,e.WQX)(g.Ix)),(0,a.A)(this,"dialog",(0,e.WQX)(R.bZ)),(0,a.A)(this,"_subscriptions",[]),(0,a.A)(this,"dataService",(0,e.WQX)(N.u)),(0,a.A)(this,"_storeService",(0,e.WQX)(p.n)),(0,a.A)(this,"translate",(0,e.WQX)(T.c$)),(0,a.A)(this,"authenticationService",(0,e.WQX)(b.k)),(0,a.A)(this,"_invoiceService",(0,e.WQX)(E.p)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"handleSearch",(0,f.gC)((t="")=>{this.dataService.SetNewInvoiceFilter({textSearch:t})},1e3))}ngOnInit(){this.listTypeView=[{label:this.translate.instant("INVOICES.Tabs.Created"),value:_.A.Created_Tab},{label:this.translate.instant("INVOICES.Tabs.SentToMe"),value:_.A.Sent_To_Me_Tab}],this._subscriptions.push(this.dataService.GetInvoiceFilter().subscribe(t=>{this.currentTypeView=t?.typeView,this.currentTypeView!=_.A.Created_Tab?this.CalculationInvoiceSendToMe():this.CalculateInvoice()}))}reloadCalculation(t){this.currentTypeView!=_.A.Created_Tab?this.CalculationInvoiceSendToMe():this.CalculateInvoice()}CalculateInvoice(){this._invoiceService.CalculationInvoice(0).pipe((0,h.pQ)(this.destroyRef)).subscribe({next:t=>{this.calculationInvoice=t}})}CalculationInvoiceSendToMe(){this._invoiceService.CalculationInvoiceSendToMe(0).pipe((0,h.pQ)(this.destroyRef)).subscribe({next:t=>{this.calculationInvoice=t}})}NewInvoice(){this.dialog.open(M.NewInvoiceComponent,{width:"80vw",maxWidth:"100%",maxHeight:"100%",panelClass:"custom_dialog",disableClose:!0}).afterClosed().subscribe(t=>{t&&(this.currentTypeView==_.A.Created_Tab?this.tab1.ReloadData():this.tab2.ReloadData())})}removeQueryParams(){this.router.navigate([],{queryParams:{},replaceUrl:!0})}handleChangeTypeView(t){this.removeQueryParams(),this.dataService.GetInvoiceFilterValue().typeView!==t&&this.dataService.SetNewInvoiceFilter({typeView:t})}ngOnDestroy(){this._subscriptions.forEach(t=>t.unsubscribe())}}return i=s,(0,a.A)(s,"\u0275fac",function(t){return new(t||i)}),(0,a.A)(s,"\u0275cmp",e.VBU({type:i,selectors:[["app-invoice"]],viewQuery:function(t,o){if(1&t&&(e.GBs(Ce,5),e.GBs(fe,5)),2&t){let l;e.mGM(l=e.lsd())&&(o.tab1=l.first),e.mGM(l=e.lsd())&&(o.tab2=l.first)}},standalone:!0,features:[e.Jv_([]),e.aNF],decls:33,vars:31,consts:[["gridManagement",""],["gridSendToMe",""],[1,"w-full","py-[24px]","border-b","border-border-primary","bg-bg-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"button-size-md","button-primary"],[1,"container-full","mt-[24px]","flex","items-center","justify-between","flex-wrap","gap-2"],[1,"flex","w-full","items-center","gap-[14px]","flex-wrap","justify-between"],[3,"onChange","tabs","value"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","w-[340px]","mxw600:w-[240px]"],["height","36px",3,"onChange","value"],[1,"container-full","grid","grid-cols-3","mt-[24px]","mxw1100:grid-cols-1",3,"ngClass"],[1,"w-full","min-h-[112px]","border-[4px]","border-border-primary","flex","flex-col","justify-end","gap-[8px]","pb-[16px]","px-[24px]","bg-bg-primary"],[1,"text-text-tertiary","text-text-md-semibold"],[1,"w-full","mt-[24px]"],[1,"button-size-md","button-primary",3,"click"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"],[3,"reloadCalculation"]],template:function(t,o){1&t&&(e.j41(0,"div",2)(1,"div",3)(2,"p",4),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.DNE(5,Se,4,3,"button",5),e.k0s()(),e.j41(6,"div",6)(7,"div",7)(8,"app-inno-tabs",8),e.bIt("onChange",function(C){return o.handleChangeTypeView(C)}),e.k0s(),e.j41(9,"div",9)(10,"div",10)(11,"app-inno-input-search",11),e.bIt("onChange",function(C){return o.handleSearch(C)}),e.k0s()()()()(),e.j41(12,"div",12)(13,"div",13)(14,"p",14),e.EFF(15),e.nI1(16,"translate"),e.k0s(),e.j41(17,"p",4),e.EFF(18),e.nI1(19,"decimal"),e.nI1(20,"formatNumber"),e.k0s()(),e.DNE(21,Te,8,9,"div",13),e.j41(22,"div",13)(23,"p",14),e.EFF(24),e.nI1(25,"translate"),e.k0s(),e.j41(26,"p",4),e.EFF(27),e.nI1(28,"decimal"),e.nI1(29,"formatNumber"),e.k0s()()(),e.j41(30,"div",15),e.DNE(31,xe,2,0,"app-invoice-management")(32,Ae,2,0,"app-invoice-send-to-me"),e.k0s()),2&t&&(e.R7$(3),e.SpI(" ",e.bMT(4,12,"INVOICES.Title")," "),e.R7$(2),e.vxM(o.authenticationService.getBusinessRole()!=o.Role.Accountant?5:-1),e.R7$(3),e.Y8G("tabs",o.listTypeView)("value",o.currentTypeView),e.R7$(3),e.Y8G("value",o.textSearch),e.R7$(),e.Y8G("ngClass",e.l_i(28,Ie,o.currentTypeView===o.invoiceView.Created_Tab,o.currentTypeView===o.invoiceView.Sent_To_Me_Tab)),e.R7$(3),e.SpI(" ",e.bMT(16,14,"INVOICES.Summary.TotalOverdue"),""),e.R7$(3),e.SpI("$",e.bMT(20,19,e.i5U(19,16,null==o.calculationInvoice?null:o.calculationInvoice.totalOverdue,2)),""),e.R7$(3),e.vxM(o.currentTypeView===o.invoiceView.Created_Tab?21:-1),e.R7$(3),e.JRh(e.bMT(25,21,"INVOICES.Summary.TotalOutstanding")),e.R7$(3),e.SpI("$",e.bMT(29,26,e.i5U(28,23,null==o.calculationInvoice?null:o.calculationInvoice.totalAmount,2)),""),e.R7$(4),e.vxM(o.currentTypeView===o.invoiceView.Created_Tab?31:32))},dependencies:[S.G,x.YU,T.D9,v.k,m.M,ne,ge,P.p,u.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),s})()}}]);