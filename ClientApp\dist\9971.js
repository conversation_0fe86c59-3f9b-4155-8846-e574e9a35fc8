"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9971],{3200:(T,v,a)=>{a.d(v,{J:()=>x});var r=a(9842),i=a(177),e=a(5236),l=a(4438);function b(I,C){if(1&I&&(l.j41(0,"p",4),l.<PERSON>(1),l.nI1(2,"translate"),l.k0s()),2&I){const _=l.XpG();l.R7$(),l.SpI(" ",l.bMT(2,1,_.description)," ")}}let x=(()=>{var I;class C{constructor(){(0,r.A)(this,"title",""),(0,r.A)(this,"description",""),(0,r.A)(this,"icon",""),(0,r.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return I=C,(0,r.A)(C,"\u0275fac",function(h){return new(h||I)}),(0,r.A)(C,"\u0275cmp",l.VBU({type:I,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[l.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(h,d){1&h&&(l.j41(0,"div",0),l.nrm(1,"img",1),l.j41(2,"div",2)(3,"p",3),l.EFF(4),l.nI1(5,"translate"),l.nI1(6,"translate"),l.k0s(),l.DNE(7,b,3,3,"p",4),l.k0s()()),2&h&&(l.R7$(),l.Y8G("src",d.icon||d.defaultIcon,l.B4B),l.R7$(3),l.SpI(" ",d.title?l.bMT(5,3,d.title):l.bMT(6,5,"COMMON.EmptyData")," "),l.R7$(3),l.vxM(d.description?7:-1))},dependencies:[i.MD,e.h,e.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),C})()},7656:(T,v,a)=>{a.d(v,{V:()=>C});var r=a(9842),i=a(4438),e=a(6146),l=a(9417);const b=["*"];function x(_,h){if(1&_){const d=i.RV6();i.j41(0,"input",5),i.bIt("change",function(g){i.eBV(d);const f=i.XpG();return i.Njj(f.handleChange(g))}),i.k0s()}if(2&_){const d=i.XpG();i.Y8G("checked",d.checked)("formControl",d.formControl)}}function I(_,h){if(1&_){const d=i.RV6();i.j41(0,"input",6),i.bIt("change",function(g){i.eBV(d);const f=i.XpG();return i.Njj(f.handleChange(g))}),i.k0s()}if(2&_){const d=i.XpG();i.Y8G("checked",d.checked)}}let C=(()=>{var _;class h{constructor(){(0,r.A)(this,"checked",void 0),(0,r.A)(this,"onChange",new i.bkB),(0,r.A)(this,"formControl",void 0),(0,r.A)(this,"errorMessages",void 0)}registerOnChange(u){}registerOnTouched(u){}setDisabledState(u){}writeValue(u){}handleChange(u){this.onChange.emit(u?.target?.checked??!1)}}return _=h,(0,r.A)(h,"\u0275fac",function(u){return new(u||_)}),(0,r.A)(h,"\u0275cmp",i.VBU({type:_,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[i.Jv_([{provide:l.kq,useExisting:(0,i.Rfq)(()=>_),multi:!0}]),i.aNF],ngContentSelectors:b,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(u,g){1&u&&(i.NAR(),i.j41(0,"div",0)(1,"label",1),i.DNE(2,x,1,2,"input",2)(3,I,1,1,"input",3),i.j41(4,"div",4),i.SdG(5),i.k0s()()()),2&u&&(i.R7$(2),i.vxM(g.formControl?2:3))},dependencies:[e.G,l.Zm,l.BC,l.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),h})()},4978:(T,v,a)=>{a.d(v,{I:()=>_});var r=a(9842),i=a(4438),e=a(6146),l=a(5236);const b=["*",[["","footer",""]]],x=["*","[footer]"];function I(h,d){if(1&h){const u=i.RV6();i.j41(0,"button",7),i.bIt("click",function(){i.eBV(u);const f=i.XpG(2);return i.Njj(f.handleClose())}),i.nrm(1,"img",8),i.k0s()}}function C(h,d){if(1&h&&(i.j41(0,"div",4)(1,"p",5),i.EFF(2),i.nI1(3,"translate"),i.k0s()(),i.DNE(4,I,2,0,"button",6)),2&h){const u=i.XpG();i.R7$(2),i.JRh(i.bMT(3,2,u.title)),i.R7$(2),i.vxM(u.onClose.observers.length?4:-1)}}let _=(()=>{var h;class d{constructor(){(0,r.A)(this,"title",void 0),(0,r.A)(this,"onClose",new i.bkB)}handleClose(){this.onClose.emit()}}return h=d,(0,r.A)(d,"\u0275fac",function(g){return new(g||h)}),(0,r.A)(d,"\u0275cmp",i.VBU({type:h,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[i.aNF],ngContentSelectors:x,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(g,f){1&g&&(i.NAR(b),i.j41(0,"div",0)(1,"div",1),i.DNE(2,C,5,4),i.k0s(),i.j41(3,"div",2),i.SdG(4),i.k0s(),i.j41(5,"div",3),i.SdG(6,1),i.k0s()()),2&g&&(i.R7$(2),i.vxM(f.title?2:-1))},dependencies:[e.G,l.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),d})()},5402:(T,v,a)=>{a.d(v,{Q:()=>r});var r=function(i){return i.Item="Item",i.Service="Service",i}(r||{})},5272:(T,v,a)=>{a.d(v,{N:()=>I});var r=a(9842),i=a(5312),e=a(1626),l=a(4438),b=a(6473);const x=i.c.HOST_API+"/api";let I=(()=>{var C;class _{constructor(){(0,r.A)(this,"http",(0,l.WQX)(e.Qq))}GetAllService(d){const u=(0,b.yU)(d,!1);return this.http.get(x+"/Service/GetAllService",{params:u})}CreateService(d){return this.http.post(x+"/Service/CreateService",d)}GetServiceById(d){return this.http.get(x+`/Service/GetServiceById?id=${d}`)}DeleteServices(d){return this.http.post(x+"/Service/DeleteServices",d)}UpdateArchive(d){return this.http.post(x+"/Service/UpdateArchive",d)}Update(d){return this.http.post(x+"/Service/Update",d)}}return C=_,(0,r.A)(_,"\u0275fac",function(d){return new(d||C)}),(0,r.A)(_,"\u0275prov",l.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})),_})()},9971:(T,v,a)=>{a.r(v),a.d(v,{AddNewItemComponent:()=>Q});var r=a(9842),i=a(8600),e=a(4438),l=a(4006),b=a(9424),x=a(5402),I=a(6146),C=a(1110),_=a(1328),h=a(344),d=a(7656),u=a(822),g=a(3200),f=a(9079),A=a(2840),k=a(1448),M=a(4978),y=a(5909),E=a(177),j=a(5236);const R=(c,p)=>p.id,D=c=>({"font-bold text-black":c});function P(c,p){1&c&&(e.j41(0,"div",0),e.nrm(1,"app-inno-empty-data",1),e.k0s()),2&c&&(e.R7$(),e.Y8G("title","EMPTY.NoResult"))}function B(c,p){if(1&c&&(e.j41(0,"span",12),e.EFF(1),e.k0s()),2&c){const s=e.XpG(2);e.R7$(),e.SpI(" ","Ascending"===s.sortDirection?"arrow_upward":"arrow_downward"," ")}}function N(c,p){if(1&c){const s=e.RV6();e.j41(0,"div",4)(1,"div",5)(2,"div",6)(3,"app-inno-form-checkbox",7),e.bIt("onChange",function(){const n=e.eBV(s).$index,o=e.XpG(2);return e.Njj(o.handleToggleCheckedIndex(n))}),e.k0s()(),e.j41(4,"p",16),e.EFF(5),e.k0s()(),e.j41(6,"p",17),e.EFF(7),e.k0s(),e.j41(8,"app-inno-form-input",18),e.bIt("onChange",function(n){const o=e.eBV(s).$index,m=e.XpG(2);return e.Njj(m.handleQtyIndex(n,o))}),e.k0s(),e.j41(9,"p",19),e.EFF(10),e.k0s(),e.j41(11,"p",19),e.EFF(12),e.k0s(),e.j41(13,"p",20),e.EFF(14),e.nI1(15,"date"),e.k0s()()}if(2&c){let s,t,n;const o=p.$implicit,m=p.$index,S=e.XpG(2);e.R7$(3),e.Y8G("checked",S.isCheckedIndex(m)),e.R7$(2),e.SpI(" ",null!==(s=o.itemName)&&void 0!==s?s:""," "),e.R7$(2),e.SpI(" ",null!==(t=o.rate)&&void 0!==t?t:"0"," "),e.R7$(),e.Y8G("isTable",!0),e.R7$(2),e.SpI(" ",null!==(n=o.description)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" ",S.getNameSelectedTaxes(null==o?null:o.taxes)," "),e.R7$(2),e.SpI(" ",e.i5U(15,7,o.createdAt,S._storeService.getdateFormat())," ")}}function L(c,p){if(1&c){const s=e.RV6();e.j41(0,"app-inno-modal-wrapper"),e.nrm(1,"div",2),e.j41(2,"div",3)(3,"div",4)(4,"div",5)(5,"div",6)(6,"app-inno-form-checkbox",7),e.bIt("onChange",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.handleCheckedAll(n))}),e.k0s()(),e.j41(7,"p",8),e.EFF(8),e.nI1(9,"translate"),e.k0s()(),e.j41(10,"p",9),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"p",9),e.EFF(14),e.nI1(15,"translate"),e.k0s(),e.j41(16,"p",10),e.EFF(17),e.nI1(18,"translate"),e.k0s(),e.j41(19,"p",10),e.EFF(20),e.nI1(21,"translate"),e.k0s(),e.j41(22,"p",11),e.bIt("click",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.sortDates("createdAt"))}),e.EFF(23),e.nI1(24,"translate"),e.DNE(25,B,2,1,"span",12),e.k0s()(),e.Z7z(26,N,16,10,"div",4,R),e.k0s(),e.j41(28,"ejs-pager",13),e.bIt("click",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.onPageChange(n))}),e.k0s(),e.j41(29,"div",14)(30,"app-inno-modal-footer",15),e.bIt("onCancel",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleCancel())})("onSubmit",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleSubmit())}),e.k0s()()()}if(2&c){const s=e.XpG();e.R7$(6),e.Y8G("checked",s.listIndexInvoiceSelected.length===s.listItem.length),e.R7$(2),e.SpI(" ",e.bMT(9,14,"SELECTTIMETRACKING.Table.ItemName")," "),e.R7$(3),e.SpI(" ",e.bMT(12,16,"SELECTTIMETRACKING.Table.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(15,18,"SELECTTIMETRACKING.Table.Qty")," "),e.R7$(3),e.SpI(" ",e.bMT(18,20,"SELECTTIMETRACKING.Table.Description")," "),e.R7$(3),e.SpI(" ",e.bMT(21,22,"SELECTTIMETRACKING.Table.Taxes")," "),e.R7$(2),e.Y8G("ngClass",e.eq3(26,D,"createdAt"===s.sortColumn)),e.R7$(),e.SpI(" ",e.bMT(24,24,"SELECTTIMETRACKING.Table.Date")," "),e.R7$(2),e.vxM("createdAt"==s.sortColumn?25:-1),e.R7$(),e.Dyx(s.listItem),e.R7$(2),e.Y8G("pageSize",s.pageSizesDefault)("totalRecordsCount",s.totalPages)("currentPage",s.currentPage)("pageSizes",s.pageSizes),e.R7$(2),e.Y8G("isDisableSubmit",!s.listIndexInvoiceSelected.length)}}A.is5.Inject(A.Rav);let F=(()=>{var c;class p{constructor(){(0,r.A)(this,"cancel",new e.bkB),(0,r.A)(this,"submit",new e.bkB),(0,r.A)(this,"getNameSelectedTaxes",y.Xj),(0,r.A)(this,"currentPage",1),(0,r.A)(this,"totalPages",1),(0,r.A)(this,"pageSizes",[10,20,50,100]),(0,r.A)(this,"pageSizesDefault",20),(0,r.A)(this,"listItem",[]),(0,r.A)(this,"listIndexInvoiceSelected",[]),(0,r.A)(this,"sort",void 0),(0,r.A)(this,"sortDirection","Ascending"),(0,r.A)(this,"sortColumn",""),(0,r.A)(this,"_storeService",(0,e.WQX)(C.n)),(0,r.A)(this,"itemService",(0,e.WQX)(u.b)),(0,r.A)(this,"destroyRef",(0,e.WQX)(e.abz))}handleSort(t){const n=JSON.stringify(this.sort),o=this.currentPage;switch(t){case"Descending":case"Ascending":this.LoadAllItem({page:o,textSearch:"",sort:n})}}sortDates(t){this.sortColumn===t?(this.sortDirection="Ascending"===this.sortDirection?"Descending":"Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)):(this.sortColumn=t,this.sortDirection="Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection))}LoadAllItem(t){this.itemService.GetAllItem({Page:t?.page??1,Search:t?.textSearch??"",PageSize:this.pageSizesDefault,Filter:{Sort:t.sort}}).pipe((0,f.pQ)(this.destroyRef)).subscribe({next:o=>{this.totalPages=o.totalRecords,this.listItem=o.data.map(m=>({...m,isServices:!1}))}})}handleCheckedAll(t){this.listIndexInvoiceSelected=t?this.listItem.map((n,o)=>o):[]}onPageChange(t){if(t?.newProp?.pageSize){const n=this.currentPage,o="";this.pageSizesDefault=t.newProp.pageSize,this.sort?this.handleSort(this.sortDirection):this.LoadAllItem({page:n,textSearch:o})}if(t?.currentPage){this.currentPage=t.currentPage;const n=this.currentPage;this.sort?this.handleSort(this.sortDirection):this.LoadAllItem({page:n,textSearch:""})}}ngOnInit(){this.sortDirection="Ascending",this.sortColumn="createdAt",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)}isCheckedIndex(t){return this.listIndexInvoiceSelected.includes(t)}handleToggleCheckedIndex(t){const n=this.isCheckedIndex(t);let o=[...this.listIndexInvoiceSelected];n?o=o.filter(m=>m!==t):o.push(t),this.listIndexInvoiceSelected=o}handleQtyIndex(t,n){this.listItem[n].qty=t}handleCancel(){this.cancel.emit()}handleSubmit(){const t=this.listItem.filter((n,o)=>this.listIndexInvoiceSelected.includes(o));this.submit.emit(t)}}return c=p,(0,r.A)(p,"\u0275fac",function(t){return new(t||c)}),(0,r.A)(p,"\u0275cmp",e.VBU({type:c,selectors:[["app-load-item"]],outputs:{cancel:"cancel",submit:"submit"},standalone:!0,features:[e.aNF],decls:2,vars:1,consts:[[1,"w-full"],[3,"title"],[1,"container-full","flex","items-center","justify-between","flex-wrap","gap-2"],[1,"overflow-auto","w-full","pr-3","p-[16px]"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right","cursor-pointer",3,"click","ngClass"],[1,"material-icons","pl-1","!text-[15px]"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],["footer",""],[3,"onCancel","onSubmit","isDisableSubmit"],[1,"text-text-primary","text-text-sm-regular"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],["type","number","placeholder","Enter the qty",1,"addBorderIsTable",3,"onChange","isTable"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-right"]],template:function(t,n){1&t&&e.DNE(0,P,2,1,"div",0)(1,L,31,28,"app-inno-modal-wrapper"),2&t&&e.vxM(0==n.listItem.length?0:1)},dependencies:[k.iov,k.BzB,M.I,g.J,_.a,h.k,d.V,I.G,E.YU,E.vh,j.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(170px,1fr) 130px 130px 100px 120px 120px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.addBorderIsTable[_ngcontent-%COMP%]{border-bottom:1px solid;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),p})();var G=a(5272);const $=(c,p)=>p.id,w=c=>({"font-bold text-black":c});function O(c,p){1&c&&(e.j41(0,"div",0),e.nrm(1,"app-inno-empty-data",1),e.k0s()),2&c&&(e.R7$(),e.Y8G("title","EMPTY.NoResult"))}function V(c,p){if(1&c&&(e.j41(0,"span",12),e.EFF(1),e.k0s()),2&c){const s=e.XpG(2);e.R7$(),e.SpI(" ","Ascending"===s.sortDirection?"arrow_upward":"arrow_downward"," ")}}function X(c,p){if(1&c){const s=e.RV6();e.j41(0,"div",4)(1,"div",5)(2,"div",6)(3,"app-inno-form-checkbox",7),e.bIt("onChange",function(){const n=e.eBV(s).$index,o=e.XpG(2);return e.Njj(o.handleToggleCheckedIndex(n))}),e.k0s()(),e.j41(4,"p",16),e.EFF(5),e.k0s()(),e.j41(6,"p",17),e.EFF(7),e.k0s(),e.j41(8,"app-inno-form-input",18),e.bIt("onChange",function(n){const o=e.eBV(s).$index,m=e.XpG(2);return e.Njj(m.handleQtyIndex(n,o))}),e.k0s(),e.j41(9,"p",19),e.EFF(10),e.k0s(),e.j41(11,"p",19),e.EFF(12),e.k0s(),e.j41(13,"p",20),e.EFF(14),e.nI1(15,"date"),e.k0s()()}if(2&c){let s,t,n;const o=p.$implicit,m=p.$index,S=e.XpG(2);e.R7$(3),e.Y8G("checked",S.isCheckedIndex(m)),e.R7$(2),e.SpI(" ",null!==(s=o.serviceName)&&void 0!==s?s:""," "),e.R7$(2),e.SpI(" ",null!==(t=o.rate)&&void 0!==t?t:"0"," "),e.R7$(),e.Y8G("isTable",!0),e.R7$(2),e.SpI(" ",null!==(n=o.description)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" ",S.getNameSelectedTaxes(null==o?null:o.taxes)," "),e.R7$(2),e.SpI(" ",e.i5U(15,7,o.createdAt,S._storeService.getdateFormat())," ")}}function U(c,p){if(1&c){const s=e.RV6();e.j41(0,"app-inno-modal-wrapper"),e.nrm(1,"div",2),e.j41(2,"div",3)(3,"div",4)(4,"div",5)(5,"div",6)(6,"app-inno-form-checkbox",7),e.bIt("onChange",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.handleCheckedAll(n))}),e.k0s()(),e.j41(7,"p",8),e.EFF(8),e.nI1(9,"translate"),e.k0s()(),e.j41(10,"p",9),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"p",9),e.EFF(14),e.nI1(15,"translate"),e.k0s(),e.j41(16,"p",10),e.EFF(17),e.nI1(18,"translate"),e.k0s(),e.j41(19,"p",10),e.EFF(20),e.nI1(21,"translate"),e.k0s(),e.j41(22,"p",11),e.bIt("click",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.sortDates("createdAt"))}),e.EFF(23),e.nI1(24,"translate"),e.DNE(25,V,2,1,"span",12),e.k0s()(),e.Z7z(26,X,16,10,"div",4,$),e.k0s(),e.j41(28,"ejs-pager",13),e.bIt("click",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.onPageChange(n))}),e.k0s(),e.j41(29,"div",14)(30,"app-inno-modal-footer",15),e.bIt("onCancel",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleCancel())})("onSubmit",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleSubmit())}),e.k0s()()()}if(2&c){const s=e.XpG();e.R7$(6),e.Y8G("checked",s.listIndexInvoiceSelected.length===s.listService.length),e.R7$(2),e.SpI(" ",e.bMT(9,14,"SELECTTIMETRACKING.Table.ServiceName")," "),e.R7$(3),e.SpI(" ",e.bMT(12,16,"SELECTTIMETRACKING.Table.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(15,18,"SELECTTIMETRACKING.Table.Qty")," "),e.R7$(3),e.SpI(" ",e.bMT(18,20,"SELECTTIMETRACKING.Table.Description")," "),e.R7$(3),e.SpI(" ",e.bMT(21,22,"SELECTTIMETRACKING.Table.Taxes")," "),e.R7$(2),e.Y8G("ngClass",e.eq3(26,w,"createdAt"===s.sortColumn)),e.R7$(),e.SpI(" ",e.bMT(24,24,"SELECTTIMETRACKING.Table.Date")," "),e.R7$(2),e.vxM("createdAt"==s.sortColumn?25:-1),e.R7$(),e.Dyx(s.listService),e.R7$(2),e.Y8G("pageSize",s.pageSizesDefault)("totalRecordsCount",s.totalPages)("currentPage",s.currentPage)("pageSizes",s.pageSizes),e.R7$(2),e.Y8G("isDisableSubmit",!s.listIndexInvoiceSelected.length)}}A.is5.Inject(A.Rav);let z=(()=>{var c;class p{constructor(){(0,r.A)(this,"cancel",new e.bkB),(0,r.A)(this,"submit",new e.bkB),(0,r.A)(this,"getNameSelectedTaxes",y.Xj),(0,r.A)(this,"listService",[]),(0,r.A)(this,"listIndexInvoiceSelected",[]),(0,r.A)(this,"projectId",void 0),(0,r.A)(this,"projectName",void 0),(0,r.A)(this,"currentPage",1),(0,r.A)(this,"totalPages",1),(0,r.A)(this,"pageSizes",[10,20,50,100]),(0,r.A)(this,"pageSizesDefault",20),(0,r.A)(this,"sort",void 0),(0,r.A)(this,"sortDirection","Ascending"),(0,r.A)(this,"sortColumn",""),(0,r.A)(this,"_storeService",(0,e.WQX)(C.n)),(0,r.A)(this,"serviceService",(0,e.WQX)(G.N)),(0,r.A)(this,"destroyRef",(0,e.WQX)(e.abz))}LoadAllService(t){const n={Page:t?.page??1,Search:t?.textSearch??"",PageSize:this.pageSizesDefault,...this.sort};this.serviceService.GetAllService(n).pipe((0,f.pQ)(this.destroyRef)).subscribe({next:o=>{this.totalPages=o.totalRecords,this.listService=o.data.map(m=>({...m,dateSelectItem:m.createdAt,projectName:m.project?.projectName,isServices:!0}))}})}handleCheckedAll(t){this.listIndexInvoiceSelected=t?this.listService.map((n,o)=>o):[]}handleQtyIndex(t,n){this.listService[n].qty=t}ngOnInit(){this.sortDirection="Ascending",this.sortColumn="createdAt",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)}isCheckedIndex(t){return this.listIndexInvoiceSelected.includes(t)}handleToggleCheckedIndex(t){const n=this.isCheckedIndex(t);let o=[...this.listIndexInvoiceSelected];n?o=o.filter(m=>m!==t):o.push(t),this.listIndexInvoiceSelected=o}onPageChange(t){if(t?.newProp?.pageSize){this.pageSizesDefault=t.newProp.pageSize;const n=this.currentPage;this.sort?this.handleSort(this.sortDirection):this.LoadAllService({page:n,textSearch:""})}if(t?.currentPage){this.currentPage=t.currentPage;const n=this.currentPage;this.sort?this.handleSort(this.sortDirection):this.LoadAllService({page:n,textSearch:""})}}sortDates(t){this.sortColumn===t?(this.sortDirection="Ascending"===this.sortDirection?"Descending":"Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)):(this.sortColumn=t,this.sortDirection="Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection))}handleSort(t){const n=JSON.stringify(this.sort),o=this.currentPage;switch(t){case"Descending":case"Ascending":this.LoadAllService({page:o,textSearch:"",sort:n})}}handleCancel(){this.cancel.emit()}handleSubmit(){const t=this.listService.filter((n,o)=>this.listIndexInvoiceSelected.includes(o));this.submit.emit(t.map(({project:n,...o})=>o))}}return c=p,(0,r.A)(p,"\u0275fac",function(t){return new(t||c)}),(0,r.A)(p,"\u0275cmp",e.VBU({type:c,selectors:[["app-load-service"]],outputs:{cancel:"cancel",submit:"submit"},standalone:!0,features:[e.aNF],decls:2,vars:1,consts:[[1,"w-full"],[3,"title"],[1,"container-full","flex","items-center","justify-between","flex-wrap","gap-2"],[1,"overflow-auto","w-full","p-[16px]"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right","cursor-pointer",3,"click","ngClass"],[1,"material-icons","pl-1","!text-[15px]"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],["footer",""],[3,"onCancel","onSubmit","isDisableSubmit"],[1,"text-text-primary","text-text-sm-regular","text-wrap"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],["type","number","placeholder","Enter the qty",1,"addBorderIsTable",3,"onChange","isTable"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center","text-wrap"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center"]],template:function(t,n){1&t&&e.DNE(0,O,2,1,"div",0)(1,U,31,28,"app-inno-modal-wrapper"),2&t&&e.vxM(0==n.listService.length?0:1)},dependencies:[k.iov,k.BzB,M.I,g.J,_.a,h.k,d.V,I.G,E.YU,E.vh,j.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(170px,1fr) 130px 130px 100px 120px 120px}.addBorderIsTable[_ngcontent-%COMP%]{border-bottom:1px solid;padding-bottom:8px;border-color:var(--border-primary)}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),p})();function W(c,p){1&c&&(e.j41(0,"div",11),e.nrm(1,"app-inno-spin"),e.k0s())}function K(c,p){if(1&c){const s=e.RV6();e.j41(0,"app-load-item",12),e.bIt("submit",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.handleSubmitItem(n))})("cancel",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleCancel())}),e.k0s()}}function Y(c,p){if(1&c){const s=e.RV6();e.j41(0,"app-load-service",12),e.bIt("submit",function(n){e.eBV(s);const o=e.XpG();return e.Njj(o.handleSubmitItem(n))})("cancel",function(){e.eBV(s);const n=e.XpG();return e.Njj(n.handleCancel())}),e.k0s()}}let Q=(()=>{var c;class p{static getComponent(){return p}constructor(t,n){(0,r.A)(this,"dialogRef",void 0),(0,r.A)(this,"data",void 0),(0,r.A)(this,"currentTypeView",x.Q.Item),(0,r.A)(this,"itemServiceView",x.Q),(0,r.A)(this,"listTypeView",[]),(0,r.A)(this,"clientName",""),(0,r.A)(this,"listInvoiceItem",[]),(0,r.A)(this,"listIndexInvoiceSelected",[]),(0,r.A)(this,"isFetchingProject",!1),(0,r.A)(this,"translate",(0,e.WQX)(j.c$)),this.dialogRef=t,this.data=n,this.clientName=n?.client?.clientName??"",this.listTypeView=[{label:this.translate.instant("SELECTTIMETRACKING.Tabs.Items"),value:x.Q.Item},{label:this.translate.instant("SELECTTIMETRACKING.Tabs.Services"),value:x.Q.Service}]}handleClose(){this.dialogRef.close()}handleChangeTypeView(t){this.currentTypeView!==t&&(this.currentTypeView=t)}ngOnInit(){}handleCheckedAll(t){this.listIndexInvoiceSelected=t?this.listInvoiceItem.map((n,o)=>o):[]}isCheckedIndex(t){return this.listIndexInvoiceSelected.includes(t)}handleToggleCheckedIndex(t){const n=this.isCheckedIndex(t);let o=[...this.listIndexInvoiceSelected];n?o=o.filter(m=>m!==t):o.push(t),this.listIndexInvoiceSelected=o}totalAmount(){const t=(0,y.yo)(this.listInvoiceItem);return t.subtotal+t.grandTotalTax}handleCancel(){this.dialogRef.close()}handleSubmitItem(t){this.dialogRef.close(t)}handleSubmit(){const t=this.listInvoiceItem.filter((n,o)=>this.listIndexInvoiceSelected.includes(o));this.dialogRef.close(t)}}return c=p,(0,r.A)(p,"\u0275fac",function(t){return new(t||c)(e.rXU(l.CP),e.rXU(l.Vh))}),(0,r.A)(p,"\u0275cmp",e.VBU({type:c,selectors:[["app-add-new-item"]],standalone:!0,features:[e.aNF],decls:18,vars:7,consts:[[1,"w-full"],[1,"p-[16px]"],[1,"w-full","sticky","top-0"],[1,"w-full","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"],[1,"flex","w-full","items-center","gap-[14px]","flex-wrap","justify-between","mt-[8px]"],[3,"onChange","tabs","value"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","flex","flex-col","relative"],[1,"w-full","py-2","flex","justify-center","items-center"],[3,"submit","cancel"]],template:function(t,n){1&t&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"p",4),e.EFF(5),e.nI1(6,"translate"),e.k0s()(),e.j41(7,"button",5),e.bIt("click",function(){return n.handleClose()}),e.nrm(8,"img",6),e.k0s()(),e.j41(9,"div",7)(10,"app-inno-tabs",8),e.bIt("onChange",function(m){return n.handleChangeTypeView(m)}),e.k0s(),e.nrm(11,"div",9),e.k0s()(),e.j41(12,"div",10)(13,"div",0),e.DNE(14,W,2,0,"div",11),e.j41(15,"div",0),e.DNE(16,K,1,0,"app-load-item")(17,Y,1,0,"app-load-service"),e.k0s()()()()),2&t&&(e.R7$(5),e.SpI(" ",e.bMT(6,5,"SELECTTIMETRACKING.Title")," "),e.R7$(5),e.Y8G("tabs",n.listTypeView)("value",n.currentTypeView),e.R7$(4),e.vxM(n.isFetchingProject?14:-1),e.R7$(2),e.vxM(n.currentTypeView===n.itemServiceView.Item?16:17))},dependencies:[I.G,j.D9,i.k,b.f,F,z],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(200px,1fr) 130px 100px 120px 100px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),p})()}}]);