"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8133],{8133:(D,u,s)=>{s.r(u),s.d(u,{ProfileComponent:()=>C});var o=s(9842),e=s(4438),_=s(9079),i=s(9417),g=s(3719),f=s(33),b=s(2928),E=s(6146),v=s(1342),P=s(3492),I=s(2603),p=s(4088),O=s(5861),h=s(5236),F=s(177);const R=["ddlelement"],M=(n,l)=>({"bg-green-700 hover:bg-green-800":n,"bg-gray-400":l});let C=(()=>{var n;class l{constructor(){(0,o.A)(this,"tzNames",void 0),(0,o.A)(this,"businessObject",void 0),(0,o.A)(this,"companyId",void 0),(0,o.A)(this,"selectedTz",void 0),(0,o.A)(this,"profileForm",void 0),(0,o.A)(this,"spiner_services",(0,e.WQX)(v.D)),(0,o.A)(this,"auth_services",(0,e.WQX)(b.k)),(0,o.A)(this,"router",(0,e.WQX)(f.Ix)),(0,o.A)(this,"_toastService",(0,e.WQX)(P.f)),(0,o.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,o.A)(this,"formBuilder",(0,e.WQX)(i.ze)),(0,o.A)(this,"translate",(0,e.WQX)(h.c$)),(0,o.A)(this,"fields",{text:"Name",value:"Code"}),(0,o.A)(this,"height","220px"),(0,o.A)(this,"watermark","Select time zone"),(0,o.A)(this,"filterPlaceholder","Search"),(0,o.A)(this,"onFiltering",t=>{let r=new I.XK;r=""!==t.text?r.where("Name","startswith",t.text,!0):r,t.updateData(this.tzNames,r)}),(0,o.A)(this,"dropDownListObject",void 0),this.profileForm=this.formBuilder.group({firstname:["",i.k0.compose([i.k0.required])],lastname:["",i.k0.compose([i.k0.required])],email:["",i.k0.compose([i.k0.required,i.k0.email])],username:[""],timezone:[""]}),this.tzNames=O.A}get f(){return this.profileForm.controls}ngOnInit(){this.GetUser()}_handleData(t){this.f.firstname.setValue(t.firstName),this.f.lastname.setValue(t.lastName),this.f.email.setValue(t.email),this.f.username.setValue(t.username),this.f.timezone.setValue(t.timeZoneId),this.f.email.disable()}GetUser(){this.auth_services.GetUser().pipe((0,_.pQ)(this.destroyRef)).subscribe(t=>{t&&this._handleData(t)})}onSubmit(){this.spiner_services.show(),this.auth_services.UpdateUserProfile({firstName:this.profileForm.controls.firstname.value,lastName:this.profileForm.controls.lastname.value,email:this.profileForm.controls.email.value,username:this.profileForm.controls.username.value,timeZoneId:this.profileForm.controls.timezone.value}).pipe((0,_.pQ)(this.destroyRef)).subscribe(r=>{r&&(this.spiner_services.hide(),this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this._handleData(r))})}onChange(t){this.profileForm.controls.timezone.setValue(t.value)}}return n=l,(0,o.A)(l,"\u0275fac",function(t){return new(t||n)}),(0,o.A)(l,"\u0275cmp",e.VBU({type:n,selectors:[["app-profile"]],viewQuery:function(t,r){if(1&t&&e.GBs(R,5),2&t){let a;e.mGM(a=e.lsd())&&(r.dropDownListObject=a.first)}},standalone:!0,features:[e.aNF],decls:41,vars:42,consts:[["ddlelement",""],[1,"input-content","rounded-md","w-full","border","border-gray-300"],[1,"p-3",3,"ngSubmit","formGroup"],[1,"mb-4"],[1,"fw-bold","m-0"],[1,"grid","gap-6","mb-6","md:grid-cols-2"],["for","_name",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["type","text","formControlName","firstname","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"placeholder"],["type","text","id","last_name","formControlName","lastname","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"placeholder"],[1,"mb-6"],["type","text","id","_name","formControlName","username",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","block","w-full","p-2.5",3,"placeholder"],["for","name",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["formControlName","timezone","id","ddlelement",3,"change","filtering","dataSource","fields","filterBarPlaceholder","popupHeight","allowFiltering","placeholder"],[1,"form-group","mb-3"],["type","email","id","_email","formControlName","email","placeholder","Email","required","",1,"bg-gray-200","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"w-full","text-center"],["type","submit",1,"text-white","font-medium","rounded-lg","text-sm","w-full","sm:w-auto","px-5","py-2.5","text-center",3,"disabled","ngClass"]],template:function(t,r){if(1&t){const a=e.RV6();e.j41(0,"div",1)(1,"form",2),e.bIt("ngSubmit",function(){return e.eBV(a),e.Njj(r.onSubmit())}),e.j41(2,"div",3)(3,"h5",4),e.EFF(4),e.nI1(5,"translate"),e.k0s()(),e.j41(6,"div",5)(7,"div")(8,"label",6),e.EFF(9),e.nI1(10,"translate"),e.k0s(),e.nrm(11,"input",7),e.nI1(12,"translate"),e.k0s(),e.j41(13,"div")(14,"label",6),e.EFF(15),e.nI1(16,"translate"),e.k0s(),e.nrm(17,"input",8),e.nI1(18,"translate"),e.k0s()(),e.j41(19,"div",9)(20,"label",6),e.EFF(21),e.nI1(22,"translate"),e.k0s(),e.nrm(23,"input",10),e.nI1(24,"translate"),e.k0s(),e.j41(25,"div",9)(26,"label",11),e.EFF(27),e.nI1(28,"translate"),e.k0s(),e.j41(29,"ejs-dropdownlist",12,0),e.bIt("change",function(d){return e.eBV(a),e.Njj(r.onChange(d))})("filtering",function(d){return e.eBV(a),e.Njj(r.onFiltering(d))}),e.k0s()(),e.j41(31,"div",13)(32,"div")(33,"label",6),e.EFF(34),e.nI1(35,"translate"),e.k0s(),e.nrm(36,"input",14),e.k0s()(),e.j41(37,"div",15)(38,"button",16),e.EFF(39),e.nI1(40,"translate"),e.k0s()()()()}2&t&&(e.R7$(),e.Y8G("formGroup",r.profileForm),e.R7$(3),e.JRh(e.bMT(5,19,"PROFILE.Title")),e.R7$(5),e.SpI("",e.bMT(10,21,"PROFILE.FirstName")," "),e.R7$(2),e.Y8G("placeholder",e.bMT(12,23,"PROFILE.FirstName")),e.R7$(4),e.SpI("",e.bMT(16,25,"PROFILE.LastName")," "),e.R7$(2),e.Y8G("placeholder",e.bMT(18,27,"PROFILE.LastName")),e.R7$(4),e.SpI("",e.bMT(22,29,"PROFILE.UserName")," "),e.R7$(2),e.Y8G("placeholder",e.bMT(24,31,"PROFILE.UserName")),e.R7$(4),e.SpI("",e.bMT(28,33,"PROFILE.TimeZone")," "),e.R7$(2),e.Y8G("dataSource",r.tzNames)("fields",r.fields)("filterBarPlaceholder",r.filterPlaceholder)("popupHeight",r.height)("allowFiltering",!0)("placeholder",r.watermark),e.R7$(5),e.SpI("",e.bMT(35,35,"PROFILE.Email")," "),e.R7$(4),e.Y8G("disabled",!r.profileForm.valid)("ngClass",e.l_i(39,M,r.profileForm.valid,!r.profileForm.valid)),e.R7$(),e.JRh(e.bMT(40,37,"BUTTON.Save")))},dependencies:[f.iI,E.G,F.YU,i.qT,i.me,i.BC,i.cb,i.YS,i.j4,i.JD,h.D9,g.RG,p.yi,p.V9]})),l})()}}]);