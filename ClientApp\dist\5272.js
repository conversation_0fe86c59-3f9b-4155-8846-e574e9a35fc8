"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5272],{359:(I,v,t)=>{t.d(v,{l:()=>h});var a=t(9842),g=t(1626),b=t(4438),e=t(33);const d=t(5312).c.HOST_API+"/api";let h=(()=>{var p;class m{constructor(){(0,a.A)(this,"http",(0,b.WQX)(g.Qq)),(0,a.A)(this,"router",(0,b.WQX)(e.Ix))}CreateUserBusiness(n){return this.http.post(d+"/Business/user-business",n)}GetUserBusiness(){return this.http.get(d+"/Business/user-business").pipe(n=>n)}GetBusinessById(n){return this.http.get(d+`/Business/user-business-byid?businessId=${n}`)}GetInfoCompany(){return this.http.get(d+"/Business/GetInfoCompany")}GetAllUserBusiness(n){const u={...n};return Object.keys(u).forEach(M=>null==u[M]&&delete u[M]),this.http.get(d+"/Business/GetAllUserBusiness",{params:u})}AddMemberBusiness(n){return this.http.post(d+"/Business/AddMemberBusiness",n)}DeleteMemberInBusiness(n){return this.http.post(d+`/Business/DeleteMemberInBusiness?memberId=${n}`,null)}userBusinessById(n){return this.http.get(d+`/Business/userBusinessById?UserId=${n}`)}UpdateRoleMember(n,u){return this.http.get(d+`/Business/UpdateRoleMember?UserId=${n}&role=${u}`)}UpdateStatus(n){return this.http.post(d+"/Business/UpdateStatus",n)}SendMailAddMember(n){return this.http.post(d+"/Business/SendMailAddMember",n)}}return p=m,(0,a.A)(m,"\u0275fac",function(n){return new(n||p)}),(0,a.A)(m,"\u0275prov",b.jDH({token:p,factory:p.\u0275fac,providedIn:"root"})),m})()},2891:(I,v,t)=>{t.r(v),t.d(v,{DetailMemberComponent:()=>x});var a=t(9842),g=t(359),b=t(1110),e=t(4438),c=t(33),d=t(6146),h=t(9079),p=t(1970),m=t(467),E=t(2716),n=t(7987);let u=(()=>{var i;class l extends E.H{open(o){var r=this;return(0,m.A)(function*(){const f=yield Promise.all([t.e(2076),t.e(4185)]).then(t.bind(t,4185));return r.matDialog.open(f.DialogUpdateRoleComponent.getComponent(),{panelClass:"custom_dialog",data:o,disableClose:!0,scrollStrategy:new n.t0})})()}}return i=l,(0,a.A)(l,"\u0275fac",(()=>{let s;return function(r){return(s||(s=e.xGo(i)))(r||i)}})()),(0,a.A)(l,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),l})(),M=(()=>{var i;class l extends E.H{open(o){var r=this;return(0,m.A)(function*(){const f=yield Promise.all([t.e(1328),t.e(3719),t.e(4225)]).then(t.bind(t,4225));return r.matDialog.open(f.EditProfileTeamMembersComponent.getComponent(),{panelClass:"custom_dialog",width:"450px",data:o,disableClose:!0,scrollStrategy:new n.t0})})()}}return i=l,(0,a.A)(l,"\u0275fac",(()=>{let s;return function(r){return(s||(s=e.xGo(i)))(r||i)}})()),(0,a.A)(l,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),l})();var R=t(5236);const D=()=>["/members"];function B(i,l){if(1&i&&e.nrm(0,"ngx-avatars",9),2&i){const s=e.XpG(2);e.Y8G("size",50)("name",s.objectInfor.user.firstName.slice(0,1))}}function j(i,l){if(1&i&&e.nrm(0,"ngx-avatars",9),2&i){const s=e.XpG(2);e.Y8G("size",50)("name",s.objectInfor.user.email.slice(0,1))}}function F(i,l){if(1&i){const s=e.RV6();e.nrm(0,"hr"),e.j41(1,"div",3)(2,"div")(3,"div",4)(4,"span",5),e.EFF(5),e.nI1(6,"translate"),e.k0s(),e.j41(7,"div",6)(8,"div",7)(9,"div",8),e.DNE(10,B,1,2,"ngx-avatars",9)(11,j,1,2,"ngx-avatars",9),e.k0s(),e.j41(12,"div",10)(13,"div",11)(14,"span",12),e.EFF(15),e.k0s(),e.j41(16,"div",13)(17,"button",14),e.bIt("click",function(){e.eBV(s);const r=e.XpG();return e.Njj(r.handleEditMember(null==r.objectInfor.user?null:r.objectInfor.user.id))}),e.nrm(18,"img",15),e.k0s()()(),e.nrm(19,"hr"),e.j41(20,"div",16)(21,"span",2),e.EFF(22," person "),e.k0s(),e.j41(23,"span",17),e.EFF(24),e.k0s()()()()()(),e.j41(25,"div",4)(26,"span",18),e.EFF(27),e.nI1(28,"translate"),e.k0s(),e.j41(29,"div",4)(30,"div",19)(31,"div",8)(32,"div",20)(33,"span",21),e.EFF(34),e.nI1(35,"translate"),e.k0s(),e.j41(36,"span",22),e.EFF(37),e.k0s()()(),e.j41(38,"div",23)(39,"button",14),e.bIt("click",function(){e.eBV(s);const r=e.XpG();return e.Njj(r.OpenDialog())}),e.nrm(40,"img",15),e.k0s()()()()()(),e.j41(41,"div",4)(42,"span",5),e.EFF(43),e.nI1(44,"translate"),e.k0s(),e.nrm(45,"hr"),e.j41(46,"div",24)(47,"div",25)(48,"div",26)(49,"span",27)(50,"span",28),e.EFF(51," schedule "),e.k0s(),e.EFF(52),e.nI1(53,"translate"),e.k0s(),e.j41(54,"span",21),e.EFF(55),e.nI1(56,"translate"),e.k0s()(),e.j41(57,"span",2),e.EFF(58," chevron_right "),e.k0s()()(),e.j41(59,"div",24)(60,"div",25)(61,"div",26)(62,"span",27)(63,"span",28),e.EFF(64," pending_actions "),e.k0s(),e.EFF(65),e.nI1(66,"translate"),e.k0s(),e.j41(67,"span",21),e.EFF(68),e.nI1(69,"translate"),e.k0s()(),e.j41(70,"span",2),e.EFF(71," chevron_right "),e.k0s()()(),e.j41(72,"div",24)(73,"div",25)(74,"div",26)(75,"span",27)(76,"span",28),e.EFF(77," hourglass_empty "),e.k0s(),e.EFF(78),e.nI1(79,"translate"),e.k0s(),e.j41(80,"span",21),e.EFF(81),e.nI1(82,"translate"),e.k0s()(),e.j41(83,"span",2),e.EFF(84," chevron_right "),e.k0s()()()()()}if(2&i){const s=e.XpG();e.R7$(5),e.JRh(e.bMT(6,15,"TEAMMEMBERS.Detail.BasicInfo")),e.R7$(5),e.vxM(null!=s.objectInfor.user&&s.objectInfor.user.firstName&&null!=s.objectInfor.user&&s.objectInfor.user.lastName?10:11),e.R7$(5),e.Lme(" ",null==s.objectInfor.user?null:s.objectInfor.user.firstName," ",null==s.objectInfor.user?null:s.objectInfor.user.lastName,""),e.R7$(9),e.JRh(s.objectInfor.user.email),e.R7$(3),e.JRh(e.bMT(28,17,"TEAMMEMBERS.Detail.RolePermissions")),e.R7$(7),e.SpI("",e.bMT(35,19,"TEAMMEMBERS.Detail.RoleLabel"),":"),e.R7$(3),e.SpI(" ",s.objectInfor.role,""),e.R7$(6),e.JRh(e.bMT(44,21,"TEAMMEMBERS.Detail.SettingsTitle")),e.R7$(9),e.SpI(" ",e.bMT(53,23,"TEAMMEMBERS.Detail.SetBillableRate"),""),e.R7$(3),e.SpI(" ",e.bMT(56,25,"TEAMMEMBERS.Detail.BillableRateDescription"),""),e.R7$(10),e.SpI(" ",e.bMT(66,27,"TEAMMEMBERS.Detail.SetCostRate"),""),e.R7$(3),e.JRh(e.bMT(69,29,"TEAMMEMBERS.Detail.CostRateDescription")),e.R7$(10),e.SpI(" ",e.bMT(79,31,"TEAMMEMBERS.Detail.SetCapacity"),""),e.R7$(3),e.SpI(" ",e.bMT(82,33,"TEAMMEMBERS.Detail.CapacityUnit"),"")}}let x=(()=>{var i;class l{constructor(){(0,a.A)(this,"objectInfor",void 0),(0,a.A)(this,"_id",void 0),(0,a.A)(this,"activatedRoute",(0,e.WQX)(c.nX)),(0,a.A)(this,"_storeService",(0,e.WQX)(b.n)),(0,a.A)(this,"userbusiness_services",(0,e.WQX)(g.l)),(0,a.A)(this,"editMemberDialog",(0,e.WQX)(M)),(0,a.A)(this,"updateRoleDialog",(0,e.WQX)(u)),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"router",(0,e.WQX)(c.Ix))}ngOnInit(){this.activatedRoute.params.pipe((0,h.pQ)(this.destroyRef)).subscribe(o=>{o?.id&&(this._id=o?.id,this.GetInforUser(o?.id))})}GetInforUser(o){this.userbusiness_services.userBusinessById(o).pipe((0,h.pQ)(this.destroyRef)).subscribe(r=>{r&&(this.objectInfor=r)})}handleEditMember(o){this.editMemberDialog.open(o).then(f=>{f.afterClosed().subscribe(C=>{C&&this.GetInforUser(this._id)})})}OpenDialog(){this.updateRoleDialog.open(this.objectInfor).then(r=>{r.afterClosed().subscribe(f=>{f&&this.GetInforUser(this._id)})})}}return i=l,(0,a.A)(l,"\u0275fac",function(o){return new(o||i)}),(0,a.A)(l,"\u0275cmp",e.VBU({type:i,selectors:[["app-detail-member"]],standalone:!0,features:[e.aNF],decls:8,vars:6,consts:[[1,"ml-5","mr-5"],["routerLinkActive","router-link-active",1,"navigate-link","flex","items-center","text-blue-400","p-2",3,"routerLink"],[1,"material-icons"],[1,"grid","grid-cols-2","gap-4"],[1,"mt-4"],[1,"text-lg","font-bold"],[1,"mt-4","bg-bg-primary"],[1,"rounded-md","border","flex"],[1,"p-4","mr-3"],[3,"size","name"],[1,"w-full"],[1,"flex","justify-between","items-center"],[1,"text-lg","font-bold","w-full"],[1,"w-full","p-4","flex","justify-end","items-center","cursor-pointer"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],[1,"flex","items-center","pt-3","pb-3"],[1,"ml-2"],[1,"text-lg","font-bold","mb-4"],[1,"rounded-md","border","flex","bg-bg-primary"],[1,"w-full","flex","items-center"],[1,"text-gray-400"],[1,"ml-1"],[1,"w-full","p-4","flex","justify-end","items-center","text-blue-400","cursor-pointer"],[1,"rounded-md","w-full","cursor-pointer","mt-4","bg-bg-primary"],[1,"p-3","flex","justify-between","items-center"],[1,"flex","flex-col","w-full"],[1,"flex"],[1,"material-icons","mr-2"]],template:function(o,r){1&o&&(e.j41(0,"div",0)(1,"a",1)(2,"span",2),e.EFF(3," arrow_back "),e.k0s(),e.j41(4,"span"),e.EFF(5),e.nI1(6,"translate"),e.k0s()(),e.DNE(7,F,85,35),e.k0s()),2&o&&(e.R7$(),e.Y8G("routerLink",e.lJ4(5,D)),e.R7$(4),e.JRh(e.bMT(6,3,"TEAMMEMBERS.Title")),e.R7$(2),e.vxM(r.objectInfor?7:-1))},dependencies:[d.G,R.D9,c.iI,c.Wk,c.wQ,p.mC,p.fw],styles:[".navigate-link[_ngcontent-%COMP%]{text-decoration:none;font-size:15px!important;display:flex;flex-direction:row;width:200px;gap:5px;-webkit-user-select:none;user-select:none}[_nghost-%COMP%]   .e-ddl.e-input-group[_ngcontent-%COMP%]{font-size:15px!important}"]})),l})()}}]);