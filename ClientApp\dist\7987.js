"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7987],{7786:(Q,X,p)=>{p.d(X,{h:()=>j});var z=p(6365),S=p(8750),h=p(983),C=p(9326),V=p(6648);function j(...Y){const K=(0,C.lI)(Y),B=(0,C.R0)(Y,1/0),f=Y;return f.length?1===f.length?(0,S.Tg)(f[0]):(0,z.U)(B)((0,V.H)(f,K)):h.w}},536:(Q,X,p)=>{p.d(X,{X:()=>Y});var z=p(6780),S=p(8359);const h={schedule(B){let f=requestAnimationFrame,k=cancelAnimationFrame;const{delegate:E}=h;E&&(f=E.requestAnimationFrame,k=E.cancelAnimationFrame);const L=f(N=>{k=void 0,B(N)});return new S.yU(()=>k?.(L))},requestAnimationFrame(...B){const{delegate:f}=h;return(f?.requestAnimationFrame||requestAnimationFrame)(...B)},cancelAnimationFrame(...B){const{delegate:f}=h;return(f?.cancelAnimationFrame||cancelAnimationFrame)(...B)},delegate:void 0};var V=p(9687);const Y=new class j extends V.q{flush(f){this._active=!0;const k=this._scheduled;this._scheduled=void 0;const{actions:E}=this;let L;f=f||E.shift();do{if(L=f.execute(f.state,f.delay))break}while((f=E[0])&&f.id===k&&E.shift());if(this._active=!1,L){for(;(f=E[0])&&f.id===k&&E.shift();)f.unsubscribe();throw L}}}(class C extends z.R{constructor(f,k){super(f,k),this.scheduler=f,this.work=k}requestAsyncId(f,k,E=0){return null!==E&&E>0?super.requestAsyncId(f,k,E):(f.actions.push(this),f._scheduled||(f._scheduled=h.requestAnimationFrame(()=>f.flush(void 0))))}recycleAsyncId(f,k,E=0){var L;if(null!=E?E>0:this.delay>0)return super.recycleAsyncId(f,k,E);const{actions:N}=f;null!=k&&(null===(L=N[N.length-1])||void 0===L?void 0:L.id)!==k&&(h.cancelAnimationFrame(k),f._scheduled=void 0)}})},5007:(Q,X,p)=>{p.d(X,{$:()=>N});var z=p(6780);let h,S=1;const C={};function V(A){return A in C&&(delete C[A],!0)}const j={setImmediate(A){const v=S++;return C[v]=!0,h||(h=Promise.resolve()),h.then(()=>V(v)&&A()),v},clearImmediate(A){V(A)}},{setImmediate:K,clearImmediate:B}=j,f={setImmediate(...A){const{delegate:v}=f;return(v?.setImmediate||K)(...A)},clearImmediate(A){const{delegate:v}=f;return(v?.clearImmediate||B)(A)},delegate:void 0};var E=p(9687);const N=new class L extends E.q{flush(v){this._active=!0;const w=this._scheduled;this._scheduled=void 0;const{actions:P}=this;let W;v=v||P.shift();do{if(W=v.execute(v.state,v.delay))break}while((v=P[0])&&v.id===w&&P.shift());if(this._active=!1,W){for(;(v=P[0])&&v.id===w&&P.shift();)v.unsubscribe();throw W}}}(class k extends z.R{constructor(v,w){super(v,w),this.scheduler=v,this.work=w}requestAsyncId(v,w,P=0){return null!==P&&P>0?super.requestAsyncId(v,w,P):(v.actions.push(this),v._scheduled||(v._scheduled=f.setImmediate(v.flush.bind(v,void 0))))}recycleAsyncId(v,w,P=0){var W;if(null!=P?P>0:this.delay>0)return super.recycleAsyncId(v,w,P);const{actions:U}=v;null!=w&&(null===(W=U[U.length-1])||void 0===W?void 0:W.id)!==w&&(f.clearImmediate(w),v._scheduled===w&&(v._scheduled=void 0))}})},7987:(Q,X,p)=>{p.d(X,{Sf:()=>tt,hJ:()=>l,rR:()=>it,t0:()=>v,yY:()=>st,z_:()=>gt});var z=p(7333),S=p(177),h=p(4438),C=p(4085),V=p(6860),j=p(5964),Y=p(6977),K=p(8203),B=p(6939),f=p(1413),k=p(8359),E=p(7786);const L=(0,V.CZ)();class N{constructor(t,r){this._viewportRuler=t,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=r}attach(){}enable(){if(this._canBeEnabled()){const t=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=t.style.left||"",this._previousHTMLStyles.top=t.style.top||"",t.style.left=(0,C.a1)(-this._previousScrollPosition.left),t.style.top=(0,C.a1)(-this._previousScrollPosition.top),t.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){const t=this._document.documentElement,e=t.style,n=this._document.body.style,o=e.scrollBehavior||"",a=n.scrollBehavior||"";this._isEnabled=!1,e.left=this._previousHTMLStyles.left,e.top=this._previousHTMLStyles.top,t.classList.remove("cdk-global-scrollblock"),L&&(e.scrollBehavior=n.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),L&&(e.scrollBehavior=o,n.scrollBehavior=a)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;const r=this._document.body,e=this._viewportRuler.getViewportSize();return r.scrollHeight>e.height||r.scrollWidth>e.width}}class A{constructor(t,r,e,n){this._scrollDispatcher=t,this._ngZone=r,this._viewportRuler=e,this._config=n,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(t){this._overlayRef=t}enable(){if(this._scrollSubscription)return;const t=this._scrollDispatcher.scrolled(0).pipe((0,j.p)(r=>!r||!this._overlayRef.overlayElement.contains(r.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=t.subscribe(()=>{const r=this._viewportRuler.getViewportScrollPosition().top;Math.abs(r-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=t.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}class v{enable(){}disable(){}attach(){}}function w(s,t){return t.some(r=>s.bottom<r.top||s.top>r.bottom||s.right<r.left||s.left>r.right)}function P(s,t){return t.some(r=>s.top<r.top||s.bottom>r.bottom||s.left<r.left||s.right>r.right)}class W{constructor(t,r,e,n){this._scrollDispatcher=t,this._viewportRuler=r,this._ngZone=e,this._config=n,this._scrollSubscription=null}attach(t){this._overlayRef=t}enable(){this._scrollSubscription||(this._scrollSubscription=this._scrollDispatcher.scrolled(this._config?this._config.scrollThrottle:0).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){const r=this._overlayRef.overlayElement.getBoundingClientRect(),{width:e,height:n}=this._viewportRuler.getViewportSize();w(r,[{width:e,height:n,bottom:n,right:e,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}}))}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}}let U=(()=>{var s;class t{constructor(e,n,o,a){this._scrollDispatcher=e,this._viewportRuler=n,this._ngZone=o,this.noop=()=>new v,this.close=_=>new A(this._scrollDispatcher,this._ngZone,this._viewportRuler,_),this.block=()=>new N(this._viewportRuler,this._document),this.reposition=_=>new W(this._scrollDispatcher,this._viewportRuler,this._ngZone,_),this._document=a}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(z.R),h.KVO(z.Xj),h.KVO(h.SKi),h.KVO(S.qQ))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();class it{constructor(t){if(this.scrollStrategy=new v,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,t){const r=Object.keys(t);for(const e of r)void 0!==t[e]&&(this[e]=t[e])}}}class ht{constructor(t,r){this.connectionPair=t,this.scrollableViewProperties=r}}let G=(()=>{var s;class t{constructor(e){this._attachedOverlays=[],this._document=e}ngOnDestroy(){this.detach()}add(e){this.remove(e),this._attachedOverlays.push(e)}remove(e){const n=this._attachedOverlays.indexOf(e);n>-1&&this._attachedOverlays.splice(n,1),0===this._attachedOverlays.length&&this.detach()}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(S.qQ))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),rt=(()=>{var s;class t extends G{constructor(e,n){super(e),this._ngZone=n,this._keydownListener=o=>{const a=this._attachedOverlays;for(let _=a.length-1;_>-1;_--)if(a[_]._keydownEvents.observers.length>0){const b=a[_]._keydownEvents;this._ngZone?this._ngZone.run(()=>b.next(o)):b.next(o);break}}}add(e){super.add(e),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(S.qQ),h.KVO(h.SKi,8))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),nt=(()=>{var s;class t extends G{constructor(e,n,o){super(e),this._platform=n,this._ngZone=o,this._cursorStyleIsSet=!1,this._pointerDownListener=a=>{this._pointerDownEventTarget=(0,V.Fb)(a)},this._clickListener=a=>{const _=(0,V.Fb)(a),b="click"===a.type&&this._pointerDownEventTarget?this._pointerDownEventTarget:_;this._pointerDownEventTarget=null;const O=this._attachedOverlays.slice();for(let y=O.length-1;y>-1;y--){const R=O[y];if(R._outsidePointerEvents.observers.length<1||!R.hasAttached())continue;if(q(R.overlayElement,_)||q(R.overlayElement,b))break;const T=R._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>T.next(a)):T.next(a)}}}add(e){if(super.add(e),!this._isAttached){const n=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(n)):this._addEventListeners(n),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=n.style.cursor,n.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){const e=this._document.body;e.removeEventListener("pointerdown",this._pointerDownListener,!0),e.removeEventListener("click",this._clickListener,!0),e.removeEventListener("auxclick",this._clickListener,!0),e.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(e.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(e){e.addEventListener("pointerdown",this._pointerDownListener,!0),e.addEventListener("click",this._clickListener,!0),e.addEventListener("auxclick",this._clickListener,!0),e.addEventListener("contextmenu",this._clickListener,!0)}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(S.qQ),h.KVO(V.OD),h.KVO(h.SKi,8))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();function q(s,t){const r=typeof ShadowRoot<"u"&&ShadowRoot;let e=t;for(;e;){if(e===s)return!0;e=r&&e instanceof ShadowRoot?e.host:e.parentNode}return!1}let tt=(()=>{var s;class t{constructor(e,n){this._platform=n,this._document=e}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){const e="cdk-overlay-container";if(this._platform.isBrowser||(0,V.v8)()){const o=this._document.querySelectorAll(`.${e}[platform="server"], .${e}[platform="test"]`);for(let a=0;a<o.length;a++)o[a].remove()}const n=this._document.createElement("div");n.classList.add(e),(0,V.v8)()?n.setAttribute("platform","test"):this._platform.isBrowser||n.setAttribute("platform","server"),this._document.body.appendChild(n),this._containerElement=n}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(S.qQ),h.KVO(V.OD))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();class st{constructor(t,r,e,n,o,a,_,b,O,y=!1,R){this._portalOutlet=t,this._host=r,this._pane=e,this._config=n,this._ngZone=o,this._keyboardDispatcher=a,this._document=_,this._location=b,this._outsideClickDispatcher=O,this._animationsDisabled=y,this._injector=R,this._backdropElement=null,this._backdropClick=new f.B,this._attachments=new f.B,this._detachments=new f.B,this._locationChanges=k.yU.EMPTY,this._backdropClickHandler=T=>this._backdropClick.next(T),this._backdropTransitionendHandler=T=>{this._disposeBackdrop(T.target)},this._keydownEvents=new f.B,this._outsidePointerEvents=new f.B,this._renders=new f.B,n.scrollStrategy&&(this._scrollStrategy=n.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=n.positionStrategy,this._afterRenderRef=(0,h.O8t)(()=>(0,h.Tzd)(()=>{this._renders.next()},{injector:this._injector}))}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(t){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);const r=this._portalOutlet.attach(t);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._afterNextRenderRef?.destroy(),this._afterNextRenderRef=(0,h.mal)(()=>{this.hasAttached()&&this.updatePosition()},{injector:this._injector}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),"function"==typeof r?.onDestroy&&r.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),r}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();const t=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenEmpty(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),t}dispose(){const t=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._afterNextRenderRef?.destroy(),this._previousHostParent=this._pane=this._host=null,t&&this._detachments.next(),this._detachments.complete(),this._afterRenderRef.destroy(),this._renders.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(t){t!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=t,this.hasAttached()&&(t.attach(this),this.updatePosition()))}updateSize(t){this._config={...this._config,...t},this._updateElementSize()}setDirection(t){this._config={...this._config,direction:t},this._updateElementDirection()}addPanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!0)}removePanelClass(t){this._pane&&this._toggleClasses(this._pane,t,!1)}getDirection(){const t=this._config.direction;return t?"string"==typeof t?t:t.value:"ltr"}updateScrollStrategy(t){t!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=t,this.hasAttached()&&(t.attach(this),t.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;const t=this._pane.style;t.width=(0,C.a1)(this._config.width),t.height=(0,C.a1)(this._config.height),t.minWidth=(0,C.a1)(this._config.minWidth),t.minHeight=(0,C.a1)(this._config.minHeight),t.maxWidth=(0,C.a1)(this._config.maxWidth),t.maxHeight=(0,C.a1)(this._config.maxHeight)}_togglePointerEvents(t){this._pane.style.pointerEvents=t?"":"none"}_attachBackdrop(){const t="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(t)})}):this._backdropElement.classList.add(t)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){const t=this._backdropElement;if(t){if(this._animationsDisabled)return void this._disposeBackdrop(t);t.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{t.addEventListener("transitionend",this._backdropTransitionendHandler)}),t.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(t)},500))}}_toggleClasses(t,r,e){const n=(0,C.FG)(r||[]).filter(o=>!!o);n.length&&(e?t.classList.add(...n):t.classList.remove(...n))}_detachContentWhenEmpty(){this._ngZone.runOutsideAngular(()=>{const t=this._renders.pipe((0,Y.Q)((0,E.h)(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||0===this._pane.children.length)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),t.unsubscribe())})})}_disposeScrollStrategy(){const t=this._scrollStrategy;t&&(t.disable(),t.detach&&t.detach())}_disposeBackdrop(t){t&&(t.removeEventListener("click",this._backdropClickHandler),t.removeEventListener("transitionend",this._backdropTransitionendHandler),t.remove(),this._backdropElement===t&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}}const ot="cdk-overlay-connected-position-bounding-box",dt=/([A-Za-z%]+)$/;class ut{get positions(){return this._preferredPositions}constructor(t,r,e,n,o){this._viewportRuler=r,this._document=e,this._platform=n,this._overlayContainer=o,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new f.B,this._resizeSubscription=k.yU.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(t)}attach(t){this._validatePositions(),t.hostElement.classList.add(ot),this._overlayRef=t,this._boundingBox=t.hostElement,this._pane=t.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition)return void this.reapplyLastPosition();this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const t=this._originRect,r=this._overlayRect,e=this._viewportRect,n=this._containerRect,o=[];let a;for(let _ of this._preferredPositions){let b=this._getOriginPoint(t,n,_),O=this._getOverlayPoint(b,r,_),y=this._getOverlayFit(O,r,e,_);if(y.isCompletelyWithinViewport)return this._isPushed=!1,void this._applyPosition(_,b);this._canFitWithFlexibleDimensions(y,O,e)?o.push({position:_,origin:b,overlayRect:r,boundingBoxRect:this._calculateBoundingBoxRect(b,_)}):(!a||a.overlayFit.visibleArea<y.visibleArea)&&(a={overlayFit:y,overlayPoint:O,originPoint:b,position:_,overlayRect:r})}if(o.length){let _=null,b=-1;for(const O of o){const y=O.boundingBoxRect.width*O.boundingBoxRect.height*(O.position.weight||1);y>b&&(b=y,_=O)}return this._isPushed=!1,void this._applyPosition(_.position,_.origin)}if(this._canPush)return this._isPushed=!0,void this._applyPosition(a.position,a.originPoint);this._applyPosition(a.position,a.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&Z(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(ot),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;const t=this._lastPosition;if(t){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();const r=this._getOriginPoint(this._originRect,this._containerRect,t);this._applyPosition(t,r)}else this.apply()}withScrollableContainers(t){return this._scrollables=t,this}withPositions(t){return this._preferredPositions=t,-1===t.indexOf(this._lastPosition)&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(t){return this._viewportMargin=t,this}withFlexibleDimensions(t=!0){return this._hasFlexibleDimensions=t,this}withGrowAfterOpen(t=!0){return this._growAfterOpen=t,this}withPush(t=!0){return this._canPush=t,this}withLockedPosition(t=!0){return this._positionLocked=t,this}setOrigin(t){return this._origin=t,this}withDefaultOffsetX(t){return this._offsetX=t,this}withDefaultOffsetY(t){return this._offsetY=t,this}withTransformOriginOn(t){return this._transformOriginSelector=t,this}_getOriginPoint(t,r,e){let n,o;if("center"==e.originX)n=t.left+t.width/2;else{const a=this._isRtl()?t.right:t.left,_=this._isRtl()?t.left:t.right;n="start"==e.originX?a:_}return r.left<0&&(n-=r.left),o="center"==e.originY?t.top+t.height/2:"top"==e.originY?t.top:t.bottom,r.top<0&&(o-=r.top),{x:n,y:o}}_getOverlayPoint(t,r,e){let n,o;return n="center"==e.overlayX?-r.width/2:"start"===e.overlayX?this._isRtl()?-r.width:0:this._isRtl()?0:-r.width,o="center"==e.overlayY?-r.height/2:"top"==e.overlayY?0:-r.height,{x:t.x+n,y:t.y+o}}_getOverlayFit(t,r,e,n){const o=at(r);let{x:a,y:_}=t,b=this._getOffset(n,"x"),O=this._getOffset(n,"y");b&&(a+=b),O&&(_+=O);let T=0-_,I=_+o.height-e.height,M=this._subtractOverflows(o.width,0-a,a+o.width-e.width),H=this._subtractOverflows(o.height,T,I),pt=M*H;return{visibleArea:pt,isCompletelyWithinViewport:o.width*o.height===pt,fitsInViewportVertically:H===o.height,fitsInViewportHorizontally:M==o.width}}_canFitWithFlexibleDimensions(t,r,e){if(this._hasFlexibleDimensions){const n=e.bottom-r.y,o=e.right-r.x,a=lt(this._overlayRef.getConfig().minHeight),_=lt(this._overlayRef.getConfig().minWidth);return(t.fitsInViewportVertically||null!=a&&a<=n)&&(t.fitsInViewportHorizontally||null!=_&&_<=o)}return!1}_pushOverlayOnScreen(t,r,e){if(this._previousPushAmount&&this._positionLocked)return{x:t.x+this._previousPushAmount.x,y:t.y+this._previousPushAmount.y};const n=at(r),o=this._viewportRect,a=Math.max(t.x+n.width-o.width,0),_=Math.max(t.y+n.height-o.height,0),b=Math.max(o.top-e.top-t.y,0),O=Math.max(o.left-e.left-t.x,0);let y=0,R=0;return y=n.width<=o.width?O||-a:t.x<this._viewportMargin?o.left-e.left-t.x:0,R=n.height<=o.height?b||-_:t.y<this._viewportMargin?o.top-e.top-t.y:0,this._previousPushAmount={x:y,y:R},{x:t.x+y,y:t.y+R}}_applyPosition(t,r){if(this._setTransformOrigin(t),this._setOverlayElementStyles(r,t),this._setBoundingBoxStyles(r,t),t.panelClass&&this._addPanelClasses(t.panelClass),this._positionChanges.observers.length){const e=this._getScrollVisibility();if(t!==this._lastPosition||!this._lastScrollVisibility||!function _t(s,t){return s===t||s.isOriginClipped===t.isOriginClipped&&s.isOriginOutsideView===t.isOriginOutsideView&&s.isOverlayClipped===t.isOverlayClipped&&s.isOverlayOutsideView===t.isOverlayOutsideView}(this._lastScrollVisibility,e)){const n=new ht(t,e);this._positionChanges.next(n)}this._lastScrollVisibility=e}this._lastPosition=t,this._isInitialRender=!1}_setTransformOrigin(t){if(!this._transformOriginSelector)return;const r=this._boundingBox.querySelectorAll(this._transformOriginSelector);let e,n=t.overlayY;e="center"===t.overlayX?"center":this._isRtl()?"start"===t.overlayX?"right":"left":"start"===t.overlayX?"left":"right";for(let o=0;o<r.length;o++)r[o].style.transformOrigin=`${e} ${n}`}_calculateBoundingBoxRect(t,r){const e=this._viewportRect,n=this._isRtl();let o,a,_,y,R,T;if("top"===r.overlayY)a=t.y,o=e.height-a+this._viewportMargin;else if("bottom"===r.overlayY)_=e.height-t.y+2*this._viewportMargin,o=e.height-_+this._viewportMargin;else{const I=Math.min(e.bottom-t.y+e.top,t.y),M=this._lastBoundingBoxSize.height;o=2*I,a=t.y-I,o>M&&!this._isInitialRender&&!this._growAfterOpen&&(a=t.y-M/2)}if("end"===r.overlayX&&!n||"start"===r.overlayX&&n)T=e.width-t.x+2*this._viewportMargin,y=t.x-this._viewportMargin;else if("start"===r.overlayX&&!n||"end"===r.overlayX&&n)R=t.x,y=e.right-t.x;else{const I=Math.min(e.right-t.x+e.left,t.x),M=this._lastBoundingBoxSize.width;y=2*I,R=t.x-I,y>M&&!this._isInitialRender&&!this._growAfterOpen&&(R=t.x-M/2)}return{top:a,left:R,bottom:_,right:T,width:y,height:o}}_setBoundingBoxStyles(t,r){const e=this._calculateBoundingBoxRect(t,r);!this._isInitialRender&&!this._growAfterOpen&&(e.height=Math.min(e.height,this._lastBoundingBoxSize.height),e.width=Math.min(e.width,this._lastBoundingBoxSize.width));const n={};if(this._hasExactPosition())n.top=n.left="0",n.bottom=n.right=n.maxHeight=n.maxWidth="",n.width=n.height="100%";else{const o=this._overlayRef.getConfig().maxHeight,a=this._overlayRef.getConfig().maxWidth;n.height=(0,C.a1)(e.height),n.top=(0,C.a1)(e.top),n.bottom=(0,C.a1)(e.bottom),n.width=(0,C.a1)(e.width),n.left=(0,C.a1)(e.left),n.right=(0,C.a1)(e.right),n.alignItems="center"===r.overlayX?"center":"end"===r.overlayX?"flex-end":"flex-start",n.justifyContent="center"===r.overlayY?"center":"bottom"===r.overlayY?"flex-end":"flex-start",o&&(n.maxHeight=(0,C.a1)(o)),a&&(n.maxWidth=(0,C.a1)(a))}this._lastBoundingBoxSize=e,Z(this._boundingBox.style,n)}_resetBoundingBoxStyles(){Z(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){Z(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(t,r){const e={},n=this._hasExactPosition(),o=this._hasFlexibleDimensions,a=this._overlayRef.getConfig();if(n){const y=this._viewportRuler.getViewportScrollPosition();Z(e,this._getExactOverlayY(r,t,y)),Z(e,this._getExactOverlayX(r,t,y))}else e.position="static";let _="",b=this._getOffset(r,"x"),O=this._getOffset(r,"y");b&&(_+=`translateX(${b}px) `),O&&(_+=`translateY(${O}px)`),e.transform=_.trim(),a.maxHeight&&(n?e.maxHeight=(0,C.a1)(a.maxHeight):o&&(e.maxHeight="")),a.maxWidth&&(n?e.maxWidth=(0,C.a1)(a.maxWidth):o&&(e.maxWidth="")),Z(this._pane.style,e)}_getExactOverlayY(t,r,e){let n={top:"",bottom:""},o=this._getOverlayPoint(r,this._overlayRect,t);return this._isPushed&&(o=this._pushOverlayOnScreen(o,this._overlayRect,e)),"bottom"===t.overlayY?n.bottom=this._document.documentElement.clientHeight-(o.y+this._overlayRect.height)+"px":n.top=(0,C.a1)(o.y),n}_getExactOverlayX(t,r,e){let a,n={left:"",right:""},o=this._getOverlayPoint(r,this._overlayRect,t);return this._isPushed&&(o=this._pushOverlayOnScreen(o,this._overlayRect,e)),a=this._isRtl()?"end"===t.overlayX?"left":"right":"end"===t.overlayX?"right":"left","right"===a?n.right=this._document.documentElement.clientWidth-(o.x+this._overlayRect.width)+"px":n.left=(0,C.a1)(o.x),n}_getScrollVisibility(){const t=this._getOriginRect(),r=this._pane.getBoundingClientRect(),e=this._scrollables.map(n=>n.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:P(t,e),isOriginOutsideView:w(t,e),isOverlayClipped:P(r,e),isOverlayOutsideView:w(r,e)}}_subtractOverflows(t,...r){return r.reduce((e,n)=>e-Math.max(n,0),t)}_getNarrowedViewportRect(){const t=this._document.documentElement.clientWidth,r=this._document.documentElement.clientHeight,e=this._viewportRuler.getViewportScrollPosition();return{top:e.top+this._viewportMargin,left:e.left+this._viewportMargin,right:e.left+t-this._viewportMargin,bottom:e.top+r-this._viewportMargin,width:t-2*this._viewportMargin,height:r-2*this._viewportMargin}}_isRtl(){return"rtl"===this._overlayRef.getDirection()}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(t,r){return"x"===r?null==t.offsetX?this._offsetX:t.offsetX:null==t.offsetY?this._offsetY:t.offsetY}_validatePositions(){}_addPanelClasses(t){this._pane&&(0,C.FG)(t).forEach(r=>{""!==r&&-1===this._appliedPanelClasses.indexOf(r)&&(this._appliedPanelClasses.push(r),this._pane.classList.add(r))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(t=>{this._pane.classList.remove(t)}),this._appliedPanelClasses=[])}_getOriginRect(){const t=this._origin;if(t instanceof h.aKT)return t.nativeElement.getBoundingClientRect();if(t instanceof Element)return t.getBoundingClientRect();const r=t.width||0,e=t.height||0;return{top:t.y,bottom:t.y+e,left:t.x,right:t.x+r,height:e,width:r}}}function Z(s,t){for(let r in t)t.hasOwnProperty(r)&&(s[r]=t[r]);return s}function lt(s){if("number"!=typeof s&&null!=s){const[t,r]=s.split(dt);return r&&"px"!==r?null:parseFloat(t)}return s||null}function at(s){return{top:Math.floor(s.top),right:Math.floor(s.right),bottom:Math.floor(s.bottom),left:Math.floor(s.left),width:Math.floor(s.width),height:Math.floor(s.height)}}const g="cdk-global-overlay-wrapper";class m{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(t){const r=t.getConfig();this._overlayRef=t,this._width&&!r.width&&t.updateSize({width:this._width}),this._height&&!r.height&&t.updateSize({height:this._height}),t.hostElement.classList.add(g),this._isDisposed=!1}top(t=""){return this._bottomOffset="",this._topOffset=t,this._alignItems="flex-start",this}left(t=""){return this._xOffset=t,this._xPosition="left",this}bottom(t=""){return this._topOffset="",this._bottomOffset=t,this._alignItems="flex-end",this}right(t=""){return this._xOffset=t,this._xPosition="right",this}start(t=""){return this._xOffset=t,this._xPosition="start",this}end(t=""){return this._xOffset=t,this._xPosition="end",this}width(t=""){return this._overlayRef?this._overlayRef.updateSize({width:t}):this._width=t,this}height(t=""){return this._overlayRef?this._overlayRef.updateSize({height:t}):this._height=t,this}centerHorizontally(t=""){return this.left(t),this._xPosition="center",this}centerVertically(t=""){return this.top(t),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;const t=this._overlayRef.overlayElement.style,r=this._overlayRef.hostElement.style,e=this._overlayRef.getConfig(),{width:n,height:o,maxWidth:a,maxHeight:_}=e,b=!("100%"!==n&&"100vw"!==n||a&&"100%"!==a&&"100vw"!==a),O=!("100%"!==o&&"100vh"!==o||_&&"100%"!==_&&"100vh"!==_),y=this._xPosition,R=this._xOffset,T="rtl"===this._overlayRef.getConfig().direction;let I="",M="",H="";b?H="flex-start":"center"===y?(H="center",T?M=R:I=R):T?"left"===y||"end"===y?(H="flex-end",I=R):("right"===y||"start"===y)&&(H="flex-start",M=R):"left"===y||"start"===y?(H="flex-start",I=R):("right"===y||"end"===y)&&(H="flex-end",M=R),t.position=this._cssPosition,t.marginLeft=b?"0":I,t.marginTop=O?"0":this._topOffset,t.marginBottom=this._bottomOffset,t.marginRight=b?"0":M,r.justifyContent=H,r.alignItems=O?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;const t=this._overlayRef.overlayElement.style,r=this._overlayRef.hostElement,e=r.style;r.classList.remove(g),e.justifyContent=e.alignItems=t.marginTop=t.marginBottom=t.marginLeft=t.marginRight=t.position="",this._overlayRef=null,this._isDisposed=!0}}let c=(()=>{var s;class t{constructor(e,n,o,a){this._viewportRuler=e,this._document=n,this._platform=o,this._overlayContainer=a}global(){return new m}flexibleConnectedTo(e){return new ut(e,this._viewportRuler,this._document,this._platform,this._overlayContainer)}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(z.Xj),h.KVO(S.qQ),h.KVO(V.OD),h.KVO(tt))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})(),i=0,l=(()=>{var s;class t{constructor(e,n,o,a,_,b,O,y,R,T,I,M){this.scrollStrategies=e,this._overlayContainer=n,this._componentFactoryResolver=o,this._positionBuilder=a,this._keyboardDispatcher=_,this._injector=b,this._ngZone=O,this._document=y,this._directionality=R,this._location=T,this._outsideClickDispatcher=I,this._animationsModuleType=M}create(e){const n=this._createHostElement(),o=this._createPaneElement(n),a=this._createPortalOutlet(o),_=new it(e);return _.direction=_.direction||this._directionality.value,new st(a,n,o,_,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,"NoopAnimations"===this._animationsModuleType,this._injector.get(h.uvJ))}position(){return this._positionBuilder}_createPaneElement(e){const n=this._document.createElement("div");return n.id="cdk-overlay-"+i++,n.classList.add("cdk-overlay-pane"),e.appendChild(n),n}_createHostElement(){const e=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(e),e}_createPortalOutlet(e){return this._appRef||(this._appRef=this._injector.get(h.o8S)),new B.aI(e,this._componentFactoryResolver,this._appRef,this._injector,this._document)}}return(s=t).\u0275fac=function(e){return new(e||s)(h.KVO(U),h.KVO(tt),h.KVO(h.OM3),h.KVO(c),h.KVO(rt),h.KVO(h.zZn),h.KVO(h.SKi),h.KVO(S.qQ),h.KVO(K.dS),h.KVO(S.aZ),h.KVO(nt),h.KVO(h.bc$,8))},s.\u0275prov=h.jDH({token:s,factory:s.\u0275fac,providedIn:"root"}),t})();const $={provide:new h.nKC("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{const s=(0,h.WQX)(l);return()=>s.scrollStrategies.reposition()}}),deps:[l],useFactory:function D(s){return()=>s.scrollStrategies.reposition()}};let gt=(()=>{var s;class t{}return(s=t).\u0275fac=function(e){return new(e||s)},s.\u0275mod=h.$C({type:s}),s.\u0275inj=h.G2t({providers:[l,$],imports:[K.jI,B.jc,z.E9,z.E9]}),t})()},7333:(Q,X,p)=>{p.d(X,{uv:()=>rt,Gj:()=>et,R:()=>G,E9:()=>ft,Xj:()=>q});var z=p(4085),S=p(4438),h=p(1413),C=p(7673),V=p(1985),j=p(3726),B=(p(536),p(5007),p(3236)),f=p(9974),k=p(8750),E=p(4360),N=p(1584);function J(g,m=B.E){return function L(g){return(0,f.N)((m,c)=>{let i=!1,l=null,d=null,u=!1;const x=()=>{if(d?.unsubscribe(),d=null,i){i=!1;const D=l;l=null,c.next(D)}u&&c.complete()},F=()=>{d=null,u&&c.complete()};m.subscribe((0,E._)(c,D=>{i=!0,l=D,d||(0,k.Tg)(g(D)).subscribe(d=(0,E._)(c,x,F))},()=>{u=!0,(!i||!d||d.closed)&&c.complete()}))})}(()=>(0,N.O)(g,m))}var A=p(5964),v=p(6977),w=p(6860),P=p(177),W=p(8203);let G=(()=>{var g;class m{constructor(i,l,d){this._ngZone=i,this._platform=l,this._scrolled=new h.B,this._globalSubscription=null,this._scrolledCount=0,this.scrollContainers=new Map,this._document=d}register(i){this.scrollContainers.has(i)||this.scrollContainers.set(i,i.elementScrolled().subscribe(()=>this._scrolled.next(i)))}deregister(i){const l=this.scrollContainers.get(i);l&&(l.unsubscribe(),this.scrollContainers.delete(i))}scrolled(i=20){return this._platform.isBrowser?new V.c(l=>{this._globalSubscription||this._addGlobalListener();const d=i>0?this._scrolled.pipe(J(i)).subscribe(l):this._scrolled.subscribe(l);return this._scrolledCount++,()=>{d.unsubscribe(),this._scrolledCount--,this._scrolledCount||this._removeGlobalListener()}}):(0,C.of)()}ngOnDestroy(){this._removeGlobalListener(),this.scrollContainers.forEach((i,l)=>this.deregister(l)),this._scrolled.complete()}ancestorScrolled(i,l){const d=this.getAncestorScrollContainers(i);return this.scrolled(l).pipe((0,A.p)(u=>!u||d.indexOf(u)>-1))}getAncestorScrollContainers(i){const l=[];return this.scrollContainers.forEach((d,u)=>{this._scrollableContainsElement(u,i)&&l.push(u)}),l}_getWindow(){return this._document.defaultView||window}_scrollableContainsElement(i,l){let d=(0,z.i8)(l),u=i.getElementRef().nativeElement;do{if(d==u)return!0}while(d=d.parentElement);return!1}_addGlobalListener(){this._globalSubscription=this._ngZone.runOutsideAngular(()=>{const i=this._getWindow();return(0,j.R)(i.document,"scroll").subscribe(()=>this._scrolled.next())})}_removeGlobalListener(){this._globalSubscription&&(this._globalSubscription.unsubscribe(),this._globalSubscription=null)}}return(g=m).\u0275fac=function(i){return new(i||g)(S.KVO(S.SKi),S.KVO(w.OD),S.KVO(P.qQ,8))},g.\u0275prov=S.jDH({token:g,factory:g.\u0275fac,providedIn:"root"}),m})(),rt=(()=>{var g;class m{constructor(i,l,d,u){this.elementRef=i,this.scrollDispatcher=l,this.ngZone=d,this.dir=u,this._destroyed=new h.B,this._elementScrolled=new V.c(x=>this.ngZone.runOutsideAngular(()=>(0,j.R)(this.elementRef.nativeElement,"scroll").pipe((0,v.Q)(this._destroyed)).subscribe(x)))}ngOnInit(){this.scrollDispatcher.register(this)}ngOnDestroy(){this.scrollDispatcher.deregister(this),this._destroyed.next(),this._destroyed.complete()}elementScrolled(){return this._elementScrolled}getElementRef(){return this.elementRef}scrollTo(i){const l=this.elementRef.nativeElement,d=this.dir&&"rtl"==this.dir.value;null==i.left&&(i.left=d?i.end:i.start),null==i.right&&(i.right=d?i.start:i.end),null!=i.bottom&&(i.top=l.scrollHeight-l.clientHeight-i.bottom),d&&(0,w.BD)()!=w.r5.NORMAL?(null!=i.left&&(i.right=l.scrollWidth-l.clientWidth-i.left),(0,w.BD)()==w.r5.INVERTED?i.left=i.right:(0,w.BD)()==w.r5.NEGATED&&(i.left=i.right?-i.right:i.right)):null!=i.right&&(i.left=l.scrollWidth-l.clientWidth-i.right),this._applyScrollToOptions(i)}_applyScrollToOptions(i){const l=this.elementRef.nativeElement;(0,w.CZ)()?l.scrollTo(i):(null!=i.top&&(l.scrollTop=i.top),null!=i.left&&(l.scrollLeft=i.left))}measureScrollOffset(i){const l="left",d="right",u=this.elementRef.nativeElement;if("top"==i)return u.scrollTop;if("bottom"==i)return u.scrollHeight-u.clientHeight-u.scrollTop;const x=this.dir&&"rtl"==this.dir.value;return"start"==i?i=x?d:l:"end"==i&&(i=x?l:d),x&&(0,w.BD)()==w.r5.INVERTED?i==l?u.scrollWidth-u.clientWidth-u.scrollLeft:u.scrollLeft:x&&(0,w.BD)()==w.r5.NEGATED?i==l?u.scrollLeft+u.scrollWidth-u.clientWidth:-u.scrollLeft:i==l?u.scrollLeft:u.scrollWidth-u.clientWidth-u.scrollLeft}}return(g=m).\u0275fac=function(i){return new(i||g)(S.rXU(S.aKT),S.rXU(G),S.rXU(S.SKi),S.rXU(W.dS,8))},g.\u0275dir=S.FsC({type:g,selectors:[["","cdk-scrollable",""],["","cdkScrollable",""]],standalone:!0}),m})(),q=(()=>{var g;class m{constructor(i,l,d){this._platform=i,this._change=new h.B,this._changeListener=u=>{this._change.next(u)},this._document=d,l.runOutsideAngular(()=>{if(i.isBrowser){const u=this._getWindow();u.addEventListener("resize",this._changeListener),u.addEventListener("orientationchange",this._changeListener)}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){if(this._platform.isBrowser){const i=this._getWindow();i.removeEventListener("resize",this._changeListener),i.removeEventListener("orientationchange",this._changeListener)}this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();const i={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),i}getViewportRect(){const i=this.getViewportScrollPosition(),{width:l,height:d}=this.getViewportSize();return{top:i.top,left:i.left,bottom:i.top+d,right:i.left+l,height:d,width:l}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};const i=this._document,l=this._getWindow(),d=i.documentElement,u=d.getBoundingClientRect();return{top:-u.top||i.body.scrollTop||l.scrollY||d.scrollTop||0,left:-u.left||i.body.scrollLeft||l.scrollX||d.scrollLeft||0}}change(i=20){return i>0?this._change.pipe(J(i)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){const i=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:i.innerWidth,height:i.innerHeight}:{width:0,height:0}}}return(g=m).\u0275fac=function(i){return new(i||g)(S.KVO(w.OD),S.KVO(S.SKi),S.KVO(P.qQ,8))},g.\u0275prov=S.jDH({token:g,factory:g.\u0275fac,providedIn:"root"}),m})(),et=(()=>{var g;class m{}return(g=m).\u0275fac=function(i){return new(i||g)},g.\u0275mod=S.$C({type:g}),g.\u0275inj=S.G2t({}),m})(),ft=(()=>{var g;class m{}return(g=m).\u0275fac=function(i){return new(i||g)},g.\u0275mod=S.$C({type:g}),g.\u0275inj=S.G2t({imports:[W.jI,et,W.jI,et]}),m})()}}]);