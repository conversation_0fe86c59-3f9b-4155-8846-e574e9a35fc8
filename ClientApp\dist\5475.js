"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5475],{4800:(R,v,r)=>{r.d(v,{j:()=>B});var d=r(467),e=r(9842),f=r(1110),t=r(4438),b=r(6146),C=r(5599),T=r(3652),g=r(1970),x=r(6463),I=r(177),S=r(5236);function O(o,p){1&o&&t.eu8(0,4)}function A(o,p){if(1&o&&t.DNE(0,O,1,0,"ng-container",6),2&o){const i=t.XpG();t.Y8G("ngTemplateOutlet",i.templateTrigger)}}function E(o,p){1&o&&(t.j41(0,"button",5),t.EFF(1," Select user"),t.nrm(2,"img",7),t.k0s())}function D(o,p){if(1&o&&t.nrm(0,"ngx-avatars",11),2&o){const i=t.XpG().$implicit,l=t.XpG(2);t.FS9("bgColor",l._storeService.getBgColor(i.label.slice(0,1))),t.Y8G("size",32)("name",i.label)}}function a(o,p){if(1&o&&t.nrm(0,"ngx-avatars",11),2&o){const i=t.XpG().$implicit,l=t.XpG(2);t.FS9("bgColor",l._storeService.getBgColor(i.metadata.email.slice(0,1))),t.Y8G("size",32)("name",i.metadata.email)}}function m(o,p){if(1&o&&t.EFF(0),2&o){const i=t.XpG().$implicit;t.SpI(" ",i.label," ")}}function _(o,p){if(1&o&&t.EFF(0),2&o){const i=t.XpG().$implicit;t.SpI(" ",i.metadata.email," ")}}function u(o,p){if(1&o&&(t.j41(0,"p",14),t.EFF(1),t.k0s()),2&o){const i=t.XpG().$implicit;t.R7$(),t.SpI(" ",i.description," ")}}function s(o,p){if(1&o){const i=t.RV6();t.j41(0,"div",10),t.bIt("click",function(){const n=t.eBV(i).$implicit,c=t.XpG(2);return t.Njj(c.handleChooseOption(n))}),t.DNE(1,D,1,3,"ngx-avatars",11)(2,a,1,3,"ngx-avatars",11),t.j41(3,"div",12)(4,"p",13),t.DNE(5,m,1,1)(6,_,1,1),t.k0s(),t.DNE(7,u,2,1,"p",14),t.k0s()()}if(2&o){const i=p.$implicit,l=t.XpG(2);t.AVh("selected",i.value===l.value),t.R7$(),t.vxM(""!=i.label?1:2),t.R7$(4),t.vxM(""!=i.label?5:6),t.R7$(2),t.vxM(i.description?7:-1)}}function M(o,p){if(1&o){const i=t.RV6();t.j41(0,"button",15),t.bIt("click",function(){t.eBV(i);const n=t.XpG(2);return t.Njj(n.handleCreateNew())}),t.nrm(1,"img",16),t.EFF(2," Create a new client "),t.k0s()}}function P(o,p){if(1&o){const i=t.RV6();t.j41(0,"div",8)(1,"app-inno-input-search-result",9),t.nI1(2,"translate"),t.bIt("onChange",function(n){t.eBV(i);const c=t.XpG();return t.Njj(c.handleSearch(n))}),t.DNE(3,s,8,5,"ng-template",null,1,t.C5r)(5,M,3,0,"ng-template",null,2,t.C5r),t.k0s()()}if(2&o){const i=t.sdS(4),l=t.sdS(6),n=t.XpG();t.R7$(),t.Y8G("placeholder",t.bMT(2,8,"COMMON.Search"))("data",n.listOptionPreview)("isNotFound",!n.listOptionPreview.length)("isEmptyData",!n.listOptionOriginal.length)("isLoading",n.isLoading)("optionTemplate",i)("defaultValue",n.defaultTextSearch)("footerTemplate",n.isShowCreateUserButton?l:null)}}let B=(()=>{var o;class p{constructor(){(0,e.A)(this,"templateTrigger",null),(0,e.A)(this,"value",void 0),(0,e.A)(this,"isAll",void 0),(0,e.A)(this,"defaultTextSearch",""),(0,e.A)(this,"isShowCreateUserButton",!1),(0,e.A)(this,"onSelect",new t.bkB),(0,e.A)(this,"onGetInfoSelectedValue",new t.bkB),(0,e.A)(this,"listOptionPreview",[]),(0,e.A)(this,"listOptionOriginal",[]),(0,e.A)(this,"isLoading",!1),(0,e.A)(this,"dropdownOptionService",(0,t.WQX)(x.R)),(0,e.A)(this,"_storeService",(0,t.WQX)(f.n)),(0,e.A)(this,"unsubscribe",[]),(0,e.A)(this,"searchResultComponent",void 0)}ngOnInit(){this.value&&this.loadData(!0)}loadData(l){var n=this;return(0,d.A)(function*(){n.isLoading=!0;const c=yield n.dropdownOptionService.getDropdownOptionsUser();if(n.listOptionOriginal=c,n.isAll&&n.listOptionOriginal.unshift({label:"All",value:"all",metadata:{}}),n.handleSearch(n.defaultTextSearch),l&&n.value){const h=n.listOptionOriginal.find(j=>j.value==n.value);n.onGetInfoSelectedValue.emit(h)}n.isLoading=!1})()}handleSearch(l){if(l=l?.trim()?.toLowerCase(),!l?.length)return void(this.listOptionPreview=this.listOptionOriginal);this.listOptionPreview=this.listOptionOriginal.filter(c=>c.label.toLowerCase().indexOf(l)>-1);const n=[];this.listOptionPreview.forEach(c=>{n.push(c),this.listOptionOriginal.filter(h=>"project"==h.metadata.type).forEach(h=>{c.value==h.metadata.objectClient.id&&n.push(h)})}),this.listOptionPreview=n}handleCloseSearchResult(){this.searchResultComponent&&this.searchResultComponent.handleHideContent()}handleChooseOption(l){l.value!=this.value&&(this.onSelect.emit(l),this.handleCloseSearchResult())}handleCreateNew(){this.handleCloseSearchResult()}ngOnDestroy(){this.unsubscribe.forEach(l=>l.unsubscribe())}}return o=p,(0,e.A)(p,"\u0275fac",function(l){return new(l||o)}),(0,e.A)(p,"\u0275cmp",t.VBU({type:o,selectors:[["app-inno-select-search-user"]],viewQuery:function(l,n){if(1&l&&t.GBs(C.x,5),2&l){let c;t.mGM(c=t.lsd())&&(n.searchResultComponent=c.first)}},inputs:{templateTrigger:"templateTrigger",value:"value",isAll:"isAll",defaultTextSearch:"defaultTextSearch",isShowCreateUserButton:"isShowCreateUserButton"},outputs:{onSelect:"onSelect",onGetInfoSelectedValue:"onGetInfoSelectedValue"},standalone:!0,features:[t.aNF],decls:5,vars:4,consts:[["templateSearchProject",""],["optionTemplate",""],["buttonCreateNew",""],["position","bottom-start",3,"onOpen","content","isClickOnContentToClose","isClearPadding"],["target",""],["target","",1,"dropdown-invisible","flex","gap-[4px]"],["target","",4,"ngTemplateOutlet"],["src","../../../assets/img/icon/ic_arrow_down_gray.svg","alt","Icon",1,"w-[16px]","translate-y-[2px]"],[1,"min-w-[320px]"],[3,"onChange","placeholder","data","isNotFound","isEmptyData","isLoading","optionTemplate","defaultValue","footerTemplate"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","hover:bg-gray-100","rounded-md","cursor-pointer",3,"click"],[3,"size","bgColor","name"],[1,"w-full"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular"],[1,"p-[12px]","gap-[12px]","text-text-brand-primary","text-text-sm-semibold","w-full","flex","items-center","hover:bg-bg-brand-primary","rounded-md","cursor-pointer",3,"click"],["src","../../../assets/img/icon/ic_add_green.svg","alt","Icon"]],template:function(l,n){if(1&l){const c=t.RV6();t.j41(0,"app-inno-popover",3),t.bIt("onOpen",function(){return t.eBV(c),t.Njj(n.loadData())}),t.DNE(1,A,1,1,"ng-container",4)(2,E,3,0,"button",5)(3,P,7,10,"ng-template",null,0,t.C5r),t.k0s()}if(2&l){const c=t.sdS(4);t.Y8G("content",c)("isClickOnContentToClose",!1)("isClearPadding",!0),t.R7$(),t.vxM(n.templateTrigger?1:2)}},dependencies:[b.G,I.T3,S.D9,C.x,T.t,g.mC,g.fw],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selected[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary)}"]})),p})()},8556:(R,v,r)=>{r.d(v,{K:()=>D});var d=r(9842),e=r(4438),f=r(5599),t=r(6146),b=r(4823),C=r(5236);function T(a,m){if(1&a){const _=e.RV6();e.j41(0,"button",4),e.bIt("click",function(){e.eBV(_);const s=e.XpG();return e.Njj(s.handleResume())}),e.nrm(1,"img",5),e.k0s()}}function g(a,m){if(1&a){const _=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(_);const s=e.XpG();return e.Njj(s.handleEdit())}),e.nrm(1,"img",7),e.k0s()}}function x(a,m){if(1&a){const _=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(_);const s=e.XpG();return e.Njj(s.handleDowload())}),e.nrm(1,"img",8),e.k0s()}}function I(a,m){if(1&a){const _=e.RV6();e.j41(0,"div",12)(1,"button",13),e.bIt("click",function(){e.eBV(_);const s=e.XpG(2);return e.Njj(s.handleArchive())}),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"button",14),e.bIt("click",function(){e.eBV(_);const s=e.XpG(2);return e.Njj(s.handleDelete())}),e.EFF(5),e.nI1(6,"translate"),e.k0s()()}2&a&&(e.R7$(2),e.SpI(" ",e.bMT(3,2,"COMMON.Archive")," "),e.R7$(3),e.SpI(" ",e.bMT(6,4,"COMMON.Delete")," "))}function S(a,m){if(1&a&&(e.j41(0,"app-inno-popover",9)(1,"button",10),e.nrm(2,"img",11),e.k0s()(),e.DNE(3,I,7,6,"ng-template",null,0,e.C5r)),2&a){const _=e.sdS(4);e.Y8G("content",_)}}function O(a,m){if(1&a){const _=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(_);const s=e.XpG(2);return e.Njj(s.handleArchive())}),e.nrm(1,"img",15),e.k0s()}}function A(a,m){if(1&a){const _=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(_);const s=e.XpG(2);return e.Njj(s.handleDelete())}),e.nrm(1,"img",16),e.k0s()}}function E(a,m){if(1&a&&e.DNE(0,O,2,0,"button",3)(1,A,2,0,"button",3),2&a){const _=e.XpG();e.vxM(_.onArchive.observed?0:-1),e.R7$(),e.vxM(_.onDelete.observed?1:-1)}}let D=(()=>{var a;class m{constructor(){(0,d.A)(this,"onEdit",new e.bkB),(0,d.A)(this,"onResume",new e.bkB),(0,d.A)(this,"onDelete",new e.bkB),(0,d.A)(this,"onArchive",new e.bkB),(0,d.A)(this,"onDowload",new e.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return a=m,(0,d.A)(m,"\u0275fac",function(u){return new(u||a)}),(0,d.A)(m,"\u0275cmp",e.VBU({type:a,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[e.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(u,s){1&u&&(e.j41(0,"div",1),e.DNE(1,T,2,0,"button",2)(2,g,2,0,"button",3)(3,x,2,0,"button",3)(4,S,5,1)(5,E,2,2),e.k0s()),2&u&&(e.R7$(),e.vxM(s.onResume.observed?1:-1),e.R7$(),e.vxM(s.onEdit.observed?2:-1),e.R7$(),e.vxM(s.onDowload.observed?3:-1),e.R7$(),e.vxM(s.onArchive.observed&&s.onDelete.observed?4:5))},dependencies:[t.G,C.D9,b.oV,f.x]})),m})()}}]);