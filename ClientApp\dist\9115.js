"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9115],{9115:(ae,N,r)=>{r.d(N,{Cn:()=>se,Cp:()=>ie,fb:()=>C,kk:()=>g});var n=r(4438),h=r(687),u=r(7336),P=r(1413),D=r(7786),R=r(8359),w=r(7673),A=r(5007),T=r(9172),x=r(5558),k=r(6977),y=r(5964),B=r(6697),S=r(1986),L=r(177),E=r(6600),X=r(6939),m=r(9969),K=r(8203),p=r(7987),Y=r(6860),H=r(7333);const W=["mat-menu-item",""],G=[[["mat-icon"],["","matMenuItemIcon",""]],"*"],z=["mat-icon, [matMenuItemIcon]","*"];function Q(a,c){1&a&&(n.qSk(),n.j41(0,"svg",2),n.nrm(1,"polygon",3),n.k0s())}const V=["*"];function Z(a,c){if(1&a){const o=n.RV6();n.j41(0,"div",0),n.bIt("keydown",function(t){n.eBV(o);const i=n.XpG();return n.Njj(i._handleKeydown(t))})("click",function(){n.eBV(o);const t=n.XpG();return n.Njj(t.closed.emit("click"))})("@transformMenu.start",function(t){n.eBV(o);const i=n.XpG();return n.Njj(i._onAnimationStart(t))})("@transformMenu.done",function(t){n.eBV(o);const i=n.XpG();return n.Njj(i._onAnimationDone(t))}),n.j41(1,"div",1),n.SdG(2),n.k0s()()}if(2&a){const o=n.XpG();n.HbH(o._classList),n.Y8G("id",o.panelId)("@transformMenu",o._panelAnimationState),n.BMQ("aria-label",o.ariaLabel||null)("aria-labelledby",o.ariaLabelledby||null)("aria-describedby",o.ariaDescribedby||null)}}const O=new n.nKC("MAT_MENU_PANEL");let C=(()=>{var a;class c{constructor(e,t,i,s,l){this._elementRef=e,this._document=t,this._focusMonitor=i,this._parentMenu=s,this._changeDetectorRef=l,this.role="menuitem",this.disabled=!1,this.disableRipple=!1,this._hovered=new P.B,this._focused=new P.B,this._highlighted=!1,this._triggersSubmenu=!1,s?.addItem?.(this)}focus(e,t){this._focusMonitor&&e?this._focusMonitor.focusVia(this._getHostElement(),e,t):this._getHostElement().focus(t),this._focused.next(this)}ngAfterViewInit(){this._focusMonitor&&this._focusMonitor.monitor(this._elementRef,!1)}ngOnDestroy(){this._focusMonitor&&this._focusMonitor.stopMonitoring(this._elementRef),this._parentMenu&&this._parentMenu.removeItem&&this._parentMenu.removeItem(this),this._hovered.complete(),this._focused.complete()}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._elementRef.nativeElement}_checkDisabled(e){this.disabled&&(e.preventDefault(),e.stopPropagation())}_handleMouseEnter(){this._hovered.next(this)}getLabel(){const e=this._elementRef.nativeElement.cloneNode(!0),t=e.querySelectorAll("mat-icon, .material-icons");for(let i=0;i<t.length;i++)t[i].remove();return e.textContent?.trim()||""}_setHighlighted(e){this._highlighted=e,this._changeDetectorRef?.markForCheck()}_setTriggersSubmenu(e){this._triggersSubmenu=e,this._changeDetectorRef?.markForCheck()}_hasFocus(){return this._document&&this._document.activeElement===this._getHostElement()}}return(a=c).\u0275fac=function(e){return new(e||a)(n.rXU(n.aKT),n.rXU(L.qQ),n.rXU(h.FN),n.rXU(O,8),n.rXU(n.gRc))},a.\u0275cmp=n.VBU({type:a,selectors:[["","mat-menu-item",""]],hostAttrs:[1,"mat-mdc-menu-item","mat-mdc-focus-indicator"],hostVars:8,hostBindings:function(e,t){1&e&&n.bIt("click",function(s){return t._checkDisabled(s)})("mouseenter",function(){return t._handleMouseEnter()}),2&e&&(n.BMQ("role",t.role)("tabindex",t._getTabIndex())("aria-disabled",t.disabled)("disabled",t.disabled||null),n.AVh("mat-mdc-menu-item-highlighted",t._highlighted)("mat-mdc-menu-item-submenu-trigger",t._triggersSubmenu))},inputs:{role:"role",disabled:[2,"disabled","disabled",n.L39],disableRipple:[2,"disableRipple","disableRipple",n.L39]},exportAs:["matMenuItem"],standalone:!0,features:[n.GFd,n.aNF],attrs:W,ngContentSelectors:z,decls:5,vars:3,consts:[[1,"mat-mdc-menu-item-text"],["matRipple","",1,"mat-mdc-menu-ripple",3,"matRippleDisabled","matRippleTrigger"],["viewBox","0 0 5 10","focusable","false","aria-hidden","true",1,"mat-mdc-menu-submenu-icon"],["points","0,0 5,5 0,10"]],template:function(e,t){1&e&&(n.NAR(G),n.SdG(0),n.j41(1,"span",0),n.SdG(2,1),n.k0s(),n.nrm(3,"div",1),n.DNE(4,Q,2,0,":svg:svg",2)),2&e&&(n.R7$(3),n.Y8G("matRippleDisabled",t.disableRipple||t.disabled)("matRippleTrigger",t._getHostElement()),n.R7$(),n.vxM(t._triggersSubmenu?4:-1))},dependencies:[E.r6],encapsulation:2,changeDetection:0}),c})();const $=new n.nKC("MatMenuContent"),I={transformMenu:(0,m.hZ)("transformMenu",[(0,m.wk)("void",(0,m.iF)({opacity:0,transform:"scale(0.8)"})),(0,m.kY)("void => enter",(0,m.i0)("120ms cubic-bezier(0, 0, 0.2, 1)",(0,m.iF)({opacity:1,transform:"scale(1)"}))),(0,m.kY)("* => void",(0,m.i0)("100ms 25ms linear",(0,m.iF)({opacity:0})))]),fadeInItems:(0,m.hZ)("fadeInItems",[(0,m.wk)("showing",(0,m.iF)({opacity:1})),(0,m.kY)("void => *",[(0,m.iF)({opacity:0}),(0,m.i0)("400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)")])])};let J=0;const q=new n.nKC("mat-menu-default-options",{providedIn:"root",factory:function ee(){return{overlapTrigger:!1,xPosition:"after",yPosition:"below",backdropClass:"cdk-overlay-transparent-backdrop"}}});let g=(()=>{var a;class c{get xPosition(){return this._xPosition}set xPosition(e){this._xPosition=e,this.setPositionClasses()}get yPosition(){return this._yPosition}set yPosition(e){this._yPosition=e,this.setPositionClasses()}set panelClass(e){const t=this._previousPanelClass,i={...this._classList};t&&t.length&&t.split(" ").forEach(s=>{i[s]=!1}),this._previousPanelClass=e,e&&e.length&&(e.split(" ").forEach(s=>{i[s]=!0}),this._elementRef.nativeElement.className=""),this._classList=i}get classList(){return this.panelClass}set classList(e){this.panelClass=e}constructor(e,t,i,s){this._elementRef=e,this._changeDetectorRef=s,this._elevationPrefix="mat-elevation-z",this._baseElevation=null,this._directDescendantItems=new n.rOR,this._classList={},this._panelAnimationState="void",this._animationDone=new P.B,this.closed=new n.bkB,this.close=this.closed,this.panelId="mat-menu-panel-"+J++,this._injector=(0,n.WQX)(n.zZn),this.overlayPanelClass=i.overlayPanelClass||"",this._xPosition=i.xPosition,this._yPosition=i.yPosition,this.backdropClass=i.backdropClass,this.overlapTrigger=i.overlapTrigger,this.hasBackdrop=i.hasBackdrop}ngOnInit(){this.setPositionClasses()}ngAfterContentInit(){this._updateDirectDescendants(),this._keyManager=new h.Bu(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd(),this._keyManager.tabOut.subscribe(()=>this.closed.emit("tab")),this._directDescendantItems.changes.pipe((0,T.Z)(this._directDescendantItems),(0,x.n)(e=>(0,D.h)(...e.map(t=>t._focused)))).subscribe(e=>this._keyManager.updateActiveItem(e)),this._directDescendantItems.changes.subscribe(e=>{const t=this._keyManager;if("enter"===this._panelAnimationState&&t.activeItem?._hasFocus()){const i=e.toArray(),s=Math.max(0,Math.min(i.length-1,t.activeItemIndex||0));i[s]&&!i[s].disabled?t.setActiveItem(s):t.setNextItemActive()}})}ngOnDestroy(){this._keyManager?.destroy(),this._directDescendantItems.destroy(),this.closed.complete(),this._firstItemFocusRef?.destroy()}_hovered(){return this._directDescendantItems.changes.pipe((0,T.Z)(this._directDescendantItems),(0,x.n)(t=>(0,D.h)(...t.map(i=>i._hovered))))}addItem(e){}removeItem(e){}_handleKeydown(e){const t=e.keyCode,i=this._keyManager;switch(t){case u._f:(0,u.rp)(e)||(e.preventDefault(),this.closed.emit("keydown"));break;case u.UQ:this.parentMenu&&"ltr"===this.direction&&this.closed.emit("keydown");break;case u.LE:this.parentMenu&&"rtl"===this.direction&&this.closed.emit("keydown");break;default:return(t===u.i7||t===u.n6)&&i.setFocusOrigin("keyboard"),void i.onKeydown(e)}e.stopPropagation()}focusFirstItem(e="program"){this._firstItemFocusRef?.destroy(),this._firstItemFocusRef=(0,n.mal)(()=>{let t=null;if(this._directDescendantItems.length&&(t=this._directDescendantItems.first._getHostElement().closest('[role="menu"]')),!t||!t.contains(document.activeElement)){const i=this._keyManager;i.setFocusOrigin(e).setFirstItemActive(),!i.activeItem&&t&&t.focus()}},{injector:this._injector})}resetActiveItem(){this._keyManager.setActiveItem(-1)}setElevation(e){if(null===this._baseElevation){const d=("function"==typeof getComputedStyle?getComputedStyle(this._elementRef.nativeElement):null)?.getPropertyValue("--mat-menu-base-elevation-level")||"8";this._baseElevation=parseInt(d)}const t=Math.min(this._baseElevation+e,24),i=`${this._elevationPrefix}${t}`,s=Object.keys(this._classList).find(l=>l.startsWith(this._elevationPrefix));if(!s||s===this._previousElevation){const l={...this._classList};this._previousElevation&&(l[this._previousElevation]=!1),l[i]=!0,this._previousElevation=i,this._classList=l}}setPositionClasses(e=this.xPosition,t=this.yPosition){this._classList={...this._classList,"mat-menu-before":"before"===e,"mat-menu-after":"after"===e,"mat-menu-above":"above"===t,"mat-menu-below":"below"===t},this._changeDetectorRef?.markForCheck()}_startAnimation(){this._panelAnimationState="enter"}_resetAnimation(){this._panelAnimationState="void"}_onAnimationDone(e){this._animationDone.next(e),this._isAnimating=!1}_onAnimationStart(e){this._isAnimating=!0,"enter"===e.toState&&0===this._keyManager.activeItemIndex&&(e.element.scrollTop=0)}_updateDirectDescendants(){this._allItems.changes.pipe((0,T.Z)(this._allItems)).subscribe(e=>{this._directDescendantItems.reset(e.filter(t=>t._parentMenu===this)),this._directDescendantItems.notifyOnChanges()})}}return(a=c).\u0275fac=function(e){return new(e||a)(n.rXU(n.aKT),n.rXU(n.SKi),n.rXU(q),n.rXU(n.gRc))},a.\u0275cmp=n.VBU({type:a,selectors:[["mat-menu"]],contentQueries:function(e,t,i){if(1&e&&(n.wni(i,$,5),n.wni(i,C,5),n.wni(i,C,4)),2&e){let s;n.mGM(s=n.lsd())&&(t.lazyContent=s.first),n.mGM(s=n.lsd())&&(t._allItems=s),n.mGM(s=n.lsd())&&(t.items=s)}},viewQuery:function(e,t){if(1&e&&n.GBs(n.C4Q,5),2&e){let i;n.mGM(i=n.lsd())&&(t.templateRef=i.first)}},hostVars:3,hostBindings:function(e,t){2&e&&n.BMQ("aria-label",null)("aria-labelledby",null)("aria-describedby",null)},inputs:{backdropClass:"backdropClass",ariaLabel:[0,"aria-label","ariaLabel"],ariaLabelledby:[0,"aria-labelledby","ariaLabelledby"],ariaDescribedby:[0,"aria-describedby","ariaDescribedby"],xPosition:"xPosition",yPosition:"yPosition",overlapTrigger:[2,"overlapTrigger","overlapTrigger",n.L39],hasBackdrop:[2,"hasBackdrop","hasBackdrop",o=>null==o?null:(0,n.L39)(o)],panelClass:[0,"class","panelClass"],classList:"classList"},outputs:{closed:"closed",close:"close"},exportAs:["matMenu"],standalone:!0,features:[n.Jv_([{provide:O,useExisting:a}]),n.GFd,n.aNF],ngContentSelectors:V,decls:1,vars:0,consts:[["tabindex","-1","role","menu",1,"mat-mdc-menu-panel","mat-mdc-elevation-specific",3,"keydown","click","id"],[1,"mat-mdc-menu-content"]],template:function(e,t){1&e&&(n.NAR(),n.DNE(0,Z,3,7,"ng-template"))},styles:['mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-app-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-app-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-app-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape, var(--mat-app-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-app-surface-container));will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.mat-mdc-menu-panel.ng-animating:has(.mat-mdc-menu-content:empty){display:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-app-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}[dir=rtl] .mat-mdc-menu-item{padding-right:var(--mat-menu-item-leading-spacing);padding-left:var(--mat-menu-item-trailing-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-right:var(--mat-menu-item-with-icon-leading-spacing);padding-left:var(--mat-menu-item-with-icon-trailing-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-app-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:"";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}'],encapsulation:2,data:{animation:[I.transformMenu,I.fadeInItems]},changeDetection:0}),c})();const F=new n.nKC("mat-menu-scroll-strategy",{providedIn:"root",factory:()=>{const a=(0,n.WQX)(p.hJ);return()=>a.scrollStrategies.reposition()}}),ne={provide:F,deps:[p.hJ],useFactory:function te(a){return()=>a.scrollStrategies.reposition()}},U=(0,Y.BQ)({passive:!0});let ie=(()=>{var a;class c{get _deprecatedMatMenuTriggerFor(){return this.menu}set _deprecatedMatMenuTriggerFor(e){this.menu=e}get menu(){return this._menu}set menu(e){e!==this._menu&&(this._menu=e,this._menuCloseSubscription.unsubscribe(),e&&(this._menuCloseSubscription=e.close.subscribe(t=>{this._destroyMenu(t),("click"===t||"tab"===t)&&this._parentMaterialMenu&&this._parentMaterialMenu.closed.emit(t)})),this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu()))}constructor(e,t,i,s,l,d,_,f,M){this._overlay=e,this._element=t,this._viewContainerRef=i,this._menuItemInstance=d,this._dir=_,this._focusMonitor=f,this._ngZone=M,this._overlayRef=null,this._menuOpen=!1,this._closingActionsSubscription=R.yU.EMPTY,this._hoverSubscription=R.yU.EMPTY,this._menuCloseSubscription=R.yU.EMPTY,this._changeDetectorRef=(0,n.WQX)(n.gRc),this._handleTouchStart=v=>{(0,h.w6)(v)||(this._openedBy="touch")},this._openedBy=void 0,this.restoreFocus=!0,this.menuOpened=new n.bkB,this.onMenuOpen=this.menuOpened,this.menuClosed=new n.bkB,this.onMenuClose=this.menuClosed,this._scrollStrategy=s,this._parentMaterialMenu=l instanceof g?l:void 0,t.nativeElement.addEventListener("touchstart",this._handleTouchStart,U)}ngAfterContentInit(){this._handleHover()}ngOnDestroy(){this._overlayRef&&(this._overlayRef.dispose(),this._overlayRef=null),this._element.nativeElement.removeEventListener("touchstart",this._handleTouchStart,U),this._menuCloseSubscription.unsubscribe(),this._closingActionsSubscription.unsubscribe(),this._hoverSubscription.unsubscribe()}get menuOpen(){return this._menuOpen}get dir(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}triggersSubmenu(){return!!(this._menuItemInstance&&this._parentMaterialMenu&&this.menu)}toggleMenu(){return this._menuOpen?this.closeMenu():this.openMenu()}openMenu(){const e=this.menu;if(this._menuOpen||!e)return;const t=this._createOverlay(e),i=t.getConfig(),s=i.positionStrategy;this._setPosition(e,s),i.hasBackdrop=null==e.hasBackdrop?!this.triggersSubmenu():e.hasBackdrop,t.attach(this._getPortal(e)),e.lazyContent&&e.lazyContent.attach(this.menuData),this._closingActionsSubscription=this._menuClosingActions().subscribe(()=>this.closeMenu()),this._initMenu(e),e instanceof g&&(e._startAnimation(),e._directDescendantItems.changes.pipe((0,k.Q)(e.close)).subscribe(()=>{s.withLockedPosition(!1).reapplyLastPosition(),s.withLockedPosition(!0)}))}closeMenu(){this.menu?.close.emit()}focus(e,t){this._focusMonitor&&e?this._focusMonitor.focusVia(this._element,e,t):this._element.nativeElement.focus(t)}updatePosition(){this._overlayRef?.updatePosition()}_destroyMenu(e){if(!this._overlayRef||!this.menuOpen)return;const t=this.menu;this._closingActionsSubscription.unsubscribe(),this._overlayRef.detach(),this.restoreFocus&&("keydown"===e||!this._openedBy||!this.triggersSubmenu())&&this.focus(this._openedBy),this._openedBy=void 0,t instanceof g?(t._resetAnimation(),t.lazyContent?t._animationDone.pipe((0,y.p)(i=>"void"===i.toState),(0,B.s)(1),(0,k.Q)(t.lazyContent._attached)).subscribe({next:()=>t.lazyContent.detach(),complete:()=>this._setIsMenuOpen(!1)}):this._setIsMenuOpen(!1)):(this._setIsMenuOpen(!1),t?.lazyContent?.detach())}_initMenu(e){e.parentMenu=this.triggersSubmenu()?this._parentMaterialMenu:void 0,e.direction=this.dir,this._setMenuElevation(e),e.focusFirstItem(this._openedBy||"program"),this._setIsMenuOpen(!0)}_setMenuElevation(e){if(e.setElevation){let t=0,i=e.parentMenu;for(;i;)t++,i=i.parentMenu;e.setElevation(t)}}_setIsMenuOpen(e){e!==this._menuOpen&&(this._menuOpen=e,this._menuOpen?this.menuOpened.emit():this.menuClosed.emit(),this.triggersSubmenu()&&this._menuItemInstance._setHighlighted(e),this._changeDetectorRef.markForCheck())}_createOverlay(e){if(!this._overlayRef){const t=this._getOverlayConfig(e);this._subscribeToPositions(e,t.positionStrategy),this._overlayRef=this._overlay.create(t),this._overlayRef.keydownEvents().subscribe()}return this._overlayRef}_getOverlayConfig(e){return new p.rR({positionStrategy:this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn(".mat-menu-panel, .mat-mdc-menu-panel"),backdropClass:e.backdropClass||"cdk-overlay-transparent-backdrop",panelClass:e.overlayPanelClass,scrollStrategy:this._scrollStrategy(),direction:this._dir})}_subscribeToPositions(e,t){e.setPositionClasses&&t.positionChanges.subscribe(i=>{const s="start"===i.connectionPair.overlayX?"after":"before",l="top"===i.connectionPair.overlayY?"below":"above";this._ngZone?this._ngZone.run(()=>e.setPositionClasses(s,l)):e.setPositionClasses(s,l)})}_setPosition(e,t){let[i,s]="before"===e.xPosition?["end","start"]:["start","end"],[l,d]="above"===e.yPosition?["bottom","top"]:["top","bottom"],[_,f]=[l,d],[M,v]=[i,s],b=0;if(this.triggersSubmenu()){if(v=i="before"===e.xPosition?"start":"end",s=M="end"===i?"start":"end",this._parentMaterialMenu){if(null==this._parentInnerPadding){const j=this._parentMaterialMenu.items.first;this._parentInnerPadding=j?j._getHostElement().offsetTop:0}b="bottom"===l?this._parentInnerPadding:-this._parentInnerPadding}}else e.overlapTrigger||(_="top"===l?"bottom":"top",f="top"===d?"bottom":"top");t.withPositions([{originX:i,originY:_,overlayX:M,overlayY:l,offsetY:b},{originX:s,originY:_,overlayX:v,overlayY:l,offsetY:b},{originX:i,originY:f,overlayX:M,overlayY:d,offsetY:-b},{originX:s,originY:f,overlayX:v,overlayY:d,offsetY:-b}])}_menuClosingActions(){const e=this._overlayRef.backdropClick(),t=this._overlayRef.detachments(),i=this._parentMaterialMenu?this._parentMaterialMenu.closed:(0,w.of)(),s=this._parentMaterialMenu?this._parentMaterialMenu._hovered().pipe((0,y.p)(l=>l!==this._menuItemInstance),(0,y.p)(()=>this._menuOpen)):(0,w.of)();return(0,D.h)(e,i,s,t)}_handleMousedown(e){(0,h._G)(e)||(this._openedBy=0===e.button?"mouse":void 0,this.triggersSubmenu()&&e.preventDefault())}_handleKeydown(e){const t=e.keyCode;(t===u.Fm||t===u.t6)&&(this._openedBy="keyboard"),this.triggersSubmenu()&&(t===u.LE&&"ltr"===this.dir||t===u.UQ&&"rtl"===this.dir)&&(this._openedBy="keyboard",this.openMenu())}_handleClick(e){this.triggersSubmenu()?(e.stopPropagation(),this.openMenu()):this.toggleMenu()}_handleHover(){!this.triggersSubmenu()||!this._parentMaterialMenu||(this._hoverSubscription=this._parentMaterialMenu._hovered().pipe((0,y.p)(e=>e===this._menuItemInstance&&!e.disabled),(0,S.c)(0,A.$)).subscribe(()=>{this._openedBy="mouse",this.menu instanceof g&&this.menu._isAnimating?this.menu._animationDone.pipe((0,B.s)(1),(0,S.c)(0,A.$),(0,k.Q)(this._parentMaterialMenu._hovered())).subscribe(()=>this.openMenu()):this.openMenu()}))}_getPortal(e){return(!this._portal||this._portal.templateRef!==e.templateRef)&&(this._portal=new X.VA(e.templateRef,this._viewContainerRef)),this._portal}}return(a=c).\u0275fac=function(e){return new(e||a)(n.rXU(p.hJ),n.rXU(n.aKT),n.rXU(n.c1b),n.rXU(F),n.rXU(O,8),n.rXU(C,10),n.rXU(K.dS,8),n.rXU(h.FN),n.rXU(n.SKi))},a.\u0275dir=n.FsC({type:a,selectors:[["","mat-menu-trigger-for",""],["","matMenuTriggerFor",""]],hostAttrs:[1,"mat-mdc-menu-trigger"],hostVars:3,hostBindings:function(e,t){1&e&&n.bIt("click",function(s){return t._handleClick(s)})("mousedown",function(s){return t._handleMousedown(s)})("keydown",function(s){return t._handleKeydown(s)}),2&e&&n.BMQ("aria-haspopup",t.menu?"menu":null)("aria-expanded",t.menuOpen)("aria-controls",t.menuOpen?t.menu.panelId:null)},inputs:{_deprecatedMatMenuTriggerFor:[0,"mat-menu-trigger-for","_deprecatedMatMenuTriggerFor"],menu:[0,"matMenuTriggerFor","menu"],menuData:[0,"matMenuTriggerData","menuData"],restoreFocus:[0,"matMenuTriggerRestoreFocus","restoreFocus"]},outputs:{menuOpened:"menuOpened",onMenuOpen:"onMenuOpen",menuClosed:"menuClosed",onMenuClose:"onMenuClose"},exportAs:["matMenuTrigger"],standalone:!0}),c})(),se=(()=>{var a;class c{}return(a=c).\u0275fac=function(e){return new(e||a)},a.\u0275mod=n.$C({type:a}),a.\u0275inj=n.G2t({providers:[ne],imports:[L.MD,E.pZ,E.yE,p.z_,H.Gj,E.yE]}),c})()}}]);