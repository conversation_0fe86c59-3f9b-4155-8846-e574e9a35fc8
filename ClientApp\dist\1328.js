"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1328],{8192:(ne,G,v)=>{v.d(G,{Y:()=>D});var k=v(9842),l=v(6146),C=v(4438);function L(w,I){if(1&w&&(C.j41(0,"p",0),C.EFF(1),C.k0s()),2&w){const R=C.XpG();C.R7$(),C.JRh(R.message)}}let D=(()=>{var w;class I{constructor(){(0,k.A)(this,"message","")}}return w=I,(0,k.A)(I,"\u0275fac",function(H){return new(H||w)}),(0,k.A)(I,"\u0275cmp",C.VBU({type:w,selectors:[["app-inno-error-message"]],inputs:{message:"message"},standalone:!0,features:[C.aNF],decls:1,vars:1,consts:[[1,"errorMessage"]],template:function(H,X){1&H&&C.DNE(0,L,2,1,"p",0),2&H&&C.vxM(X.message?0:-1)},dependencies:[l.G],styles:['.errorMessage[_ngcontent-%COMP%]{font-size:12px;line-height:18px;margin-top:4px;margin-bottom:0;color:var(--text-danger)}.errorMessage[_ngcontent-%COMP%]:empty{display:none}.errorMessage[_ngcontent-%COMP%]:before{content:"* "}']})),I})()},1328:(ne,G,v)=>{v.d(G,{a:()=>ie});var k=v(9842),l=v(4438),C=v(9417),L=v(6146),D=v(9830),w=v(8192),I=v(177);const R=["inputElement"],H=(x,P)=>({"border-2 border-border-primary":x,"border-none  text-text-sm-regular":P});function X(x,P){if(1&x&&(l.j41(0,"label",4),l.EFF(1),l.k0s()),2&x){const _=l.XpG();l.AVh("required",_.isRequired),l.R7$(),l.JRh(_.label)}}function se(x,P){if(1&x){const _=l.RV6();l.j41(0,"input",5,0),l.bIt("change",function(o){l.eBV(_);const g=l.XpG();return l.Njj(g.handleChange(o))}),l.k0s(),l.nrm(2,"app-inno-error-message",6)}if(2&x){const _=l.XpG();l.HbH(_.inputClassName),l.Y8G("type",_.type)("placeholder",_.placeholder)("formControl",_.formControl)("autocomplete",_.autocomplete)("pattern",_.pattern)("mask",_.mask),l.R7$(2),l.Y8G("message",_.getErrorMessage())}}function U(x,P){if(1&x){const _=l.RV6();l.j41(0,"input",7,0),l.bIt("change",function(o){l.eBV(_);const g=l.XpG();return l.Njj(g.handleChange(o))})("blur",function(o){l.eBV(_);const g=l.XpG();return l.Njj(g.removeLeadingZeros(o))}),l.k0s()}if(2&x){const _=l.XpG();l.HbH(_.inputClassName),l.Y8G("type",_.type)("pattern",_.pattern)("mask",null)("autocomplete",_.autocomplete)("placeholder",_.placeholder)("value",_.value)("ngClass",l.l_i(9,H,!_.isTable,_.isTable))}}let ie=(()=>{var x;class P{constructor(){(0,k.A)(this,"isRequired",void 0),(0,k.A)(this,"isTable",void 0),(0,k.A)(this,"type","text"),(0,k.A)(this,"label",""),(0,k.A)(this,"autocomplete","off"),(0,k.A)(this,"removeZeros",void 0),(0,k.A)(this,"placeholder",""),(0,k.A)(this,"value",""),(0,k.A)(this,"pattern",""),(0,k.A)(this,"mask",""),(0,k.A)(this,"formControl",void 0),(0,k.A)(this,"errorMessages",void 0),(0,k.A)(this,"inputClassName",""),(0,k.A)(this,"onChange",new l.bkB),(0,k.A)(this,"inputElement",void 0)}ngAfterViewInit(){this.inputElement&&setTimeout(()=>{this.inputElement.nativeElement.value=this.value},10)}registerOnChange(S){}registerOnTouched(S){}setDisabledState(S){}writeValue(S){}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const S in this.formControl.errors)if(this.errorMessages[S])return this.errorMessages[S];return""}removeLeadingZeros(S){const o=S.target;let g=o.value;"number"===o.type&&this.removeZeros&&(o.value=/^0\d+/.test(g)&&!/^0\.\d+$/.test(g)?o.value.replace(/^0+/,""):o.value)}handleChange(S){this.onChange.emit(S?.target?.value??"")}}return x=P,(0,k.A)(P,"\u0275fac",function(S){return new(S||x)}),(0,k.A)(P,"\u0275cmp",l.VBU({type:x,selectors:[["app-inno-form-input"]],viewQuery:function(S,o){if(1&S&&l.GBs(R,5),2&S){let g;l.mGM(g=l.lsd())&&(o.inputElement=g.first)}},inputs:{isRequired:"isRequired",isTable:"isTable",type:"type",label:"label",autocomplete:"autocomplete",removeZeros:"removeZeros",placeholder:"placeholder",value:"value",pattern:"pattern",mask:"mask",formControl:"formControl",errorMessages:"errorMessages",inputClassName:"inputClassName"},outputs:{onChange:"onChange"},standalone:!0,features:[l.Jv_([{provide:C.kq,useExisting:(0,l.Rfq)(()=>x),multi:!0},(0,D.Dw)()]),l.aNF],decls:4,vars:4,consts:[["inputElement",""],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],[1,"w-full","h-[40px]","rounded-md","px-[12px]","text-text-md-regular","placeholder-text-placeholder",3,"type","pattern","mask","autocomplete","placeholder","value","ngClass","class"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","h-[40px]","rounded-md","border-2","border-border-primary","px-[12px]","text-text-md-regular","placeholder-text-placeholder",3,"change","type","placeholder","formControl","autocomplete","pattern","mask"],[3,"message"],[1,"w-full","h-[40px]","rounded-md","px-[12px]","text-text-md-regular","placeholder-text-placeholder",3,"change","blur","type","pattern","mask","autocomplete","placeholder","value","ngClass"]],template:function(S,o){1&S&&(l.j41(0,"div",1),l.DNE(1,X,2,3,"label",2)(2,se,3,9)(3,U,2,12,"input",3),l.k0s()),2&S&&(l.AVh("error",o.hasError()),l.R7$(),l.vxM(o.label?1:-1),l.R7$(),l.vxM(o.formControl?2:3))},dependencies:[L.G,I.YU,C.me,C.BC,C.R_,C.l_,D.Zr,w.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}']})),P})()},9830:(ne,G,v)=>{v.d(G,{Dw:()=>x,Zr:()=>_});var k=v(467),l=v(4438),C=v(177),L=v(9417);const D=new l.nKC("ngx-mask config"),w=new l.nKC("new ngx-mask config"),I=new l.nKC("initial ngx-mask config"),R={suffix:"",prefix:"",thousandSeparator:" ",decimalMarker:[".",","],clearIfNotMatch:!1,showTemplate:!1,showMaskTyped:!1,placeHolderCharacter:"_",dropSpecialCharacters:!0,hiddenInput:!1,shownMaskExpression:"",separatorLimit:"",allowNegativeNumbers:!1,validation:!0,specialCharacters:["-","/","(",")",".",":"," ","+",",","@","[","]",'"',"'"],leadZeroDateTime:!1,apm:!1,leadZero:!1,keepCharacterPositions:!1,triggerOnMaskChange:!1,inputTransformFn:o=>o,outputTransformFn:o=>o,maskFilled:new l.bkB,patterns:{0:{pattern:new RegExp("\\d")},9:{pattern:new RegExp("\\d"),optional:!0},X:{pattern:new RegExp("\\d"),symbol:"*"},A:{pattern:new RegExp("[a-zA-Z0-9]")},S:{pattern:new RegExp("[a-zA-Z]")},U:{pattern:new RegExp("[A-Z]")},L:{pattern:new RegExp("[a-z]")},d:{pattern:new RegExp("\\d")},m:{pattern:new RegExp("\\d")},M:{pattern:new RegExp("\\d")},H:{pattern:new RegExp("\\d")},h:{pattern:new RegExp("\\d")},s:{pattern:new RegExp("\\d")}}},H=["Hh:m0:s0","Hh:m0","m0:s0"],X=["percent","Hh","s0","m0","separator","d0/M0/0000","d0/M0","d0","M0"];let se=(()=>{var o;class g{constructor(){this._config=(0,l.WQX)(D),this.dropSpecialCharacters=this._config.dropSpecialCharacters,this.hiddenInput=this._config.hiddenInput,this.clearIfNotMatch=this._config.clearIfNotMatch,this.specialCharacters=this._config.specialCharacters,this.patterns=this._config.patterns,this.prefix=this._config.prefix,this.suffix=this._config.suffix,this.thousandSeparator=this._config.thousandSeparator,this.decimalMarker=this._config.decimalMarker,this.showMaskTyped=this._config.showMaskTyped,this.placeHolderCharacter=this._config.placeHolderCharacter,this.validation=this._config.validation,this.separatorLimit=this._config.separatorLimit,this.allowNegativeNumbers=this._config.allowNegativeNumbers,this.leadZeroDateTime=this._config.leadZeroDateTime,this.leadZero=this._config.leadZero,this.apm=this._config.apm,this.inputTransformFn=this._config.inputTransformFn,this.outputTransformFn=this._config.outputTransformFn,this.keepCharacterPositions=this._config.keepCharacterPositions,this._shift=new Set,this.plusOnePosition=!1,this.maskExpression="",this.actualValue="",this.showKeepCharacterExp="",this.shownMaskExpression="",this.deletedSpecialCharacter=!1,this._formatWithSeparators=(t,e,r,s)=>{let h=[],u="";if(Array.isArray(r)){const y=new RegExp(r.map(b=>"[\\^$.|?*+()".indexOf(b)>=0?`\\${b}`:b).join("|"));h=t.split(y),u=t.match(y)?.[0]??""}else h=t.split(r),u=r;const a=h.length>1?`${u}${h[1]}`:"";let n=h[0]??"";const p=this.separatorLimit.replace(/\s/g,"");p&&+p&&(n="-"===n[0]?`-${n.slice(1,n.length).slice(0,p.length)}`:n.slice(0,p.length));const d=/(\d+)(\d{3})/;for(;e&&d.test(n);)n=n.replace(d,"$1"+e+"$2");return typeof s>"u"?n+a:0===s?n:n+a.substring(0,s+1)},this.percentage=t=>{const e=t.replace(",","."),r=Number(this.allowNegativeNumbers&&t.includes("-")?e.slice(1,t.length):e);return!isNaN(r)&&r>=0&&r<=100},this.getPrecision=t=>{const e=t.split(".");return e.length>1?Number(e[e.length-1]):1/0},this.checkAndRemoveSuffix=t=>{for(let e=this.suffix?.length-1;e>=0;e--){const r=this.suffix.substring(e,this.suffix?.length);if(t.includes(r)&&e!==this.suffix?.length-1&&(e-1<0||!t.includes(this.suffix.substring(e-1,this.suffix?.length))))return t.replace(r,"")}return t},this.checkInputPrecision=(t,e,r)=>{let s=t,h=r;if(e<1/0){if(Array.isArray(h)){const p=h.find(d=>d!==this.thousandSeparator);h=p||h[0]}const u=new RegExp(this._charToRegExpExpression(h)+`\\d{${e}}.*$`),a=s.match(u),n=(a&&a[0]?.length)??0;n-1>e&&(s=s.substring(0,s.length-(n-1-e))),0===e&&this._compareOrIncludes(s[s.length-1],h,this.thousandSeparator)&&(s=s.substring(0,s.length-1))}return s}}applyMaskWithPattern(t,e){const[r,s]=e;return this.customPattern=s,this.applyMask(t,r)}applyMask(t,e,r=0,s=!1,h=!1,u=()=>{}){if(!e||"string"!=typeof t)return"";let a=0,n="",p=!1,d=!1,y=1,b=!1,i=t,m=r;i.slice(0,this.prefix.length)===this.prefix&&(i=i.slice(this.prefix.length,i.length)),this.suffix&&i.length>0&&(i=this.checkAndRemoveSuffix(i)),"("===i&&this.prefix&&(i="");const E=i.toString().split("");if(this.allowNegativeNumbers&&"-"===i.slice(a,a+1)&&(n+=i.slice(a,a+1)),"IP"===e){const f=i.split(".");this.ipError=this._validIP(f),e="***************"}const O=[];for(let f=0;f<i.length;f++)i[f]?.match("\\d")&&O.push(i[f]??"");if("CPF_CNPJ"===e&&(this.cpfCnpjError=11!==O.length&&14!==O.length,e=O.length>11?"00.000.000/0000-00":"000.000.000-00"),e.startsWith("percent")){if(i.match("[a-z]|[A-Z]")||i.match(/[-!$%^&*()_+|~=`{}\[\]:";'<>?,\/.]/)&&!h){i=this._stripToDecimal(i);const N=this.getPrecision(e);i=this.checkInputPrecision(i,N,this.decimalMarker)}const f="string"==typeof this.decimalMarker?this.decimalMarker:".";if(i.indexOf(f)>0&&!this.percentage(i.substring(0,i.indexOf(f)))){let N=i.substring(0,i.indexOf(f)-1);this.allowNegativeNumbers&&"-"===i.slice(a,a+1)&&!h&&(N=i.substring(0,i.indexOf(f))),i=`${N}${i.substring(i.indexOf(f),i.length)}`}let c="";c=this.allowNegativeNumbers&&"-"===i.slice(a,a+1)?`-${i.slice(a+1,a+i.length)}`:i,n=this.percentage(c)?this._splitPercentZero(i):this._splitPercentZero(i.substring(0,i.length-1))}else if(e.startsWith("separator")){(i.match("[w\u0430-\u044f\u0410-\u042f]")||i.match("[\u0401\u0451\u0410-\u044f]")||i.match("[a-z]|[A-Z]")||i.match(/[-@#!$%\\^&*()_\xa3\xac'+|~=`{}\]:";<>.?/]/)||i.match("[^A-Za-z0-9,]"))&&(i=this._stripToDecimal(i));const f=this.getPrecision(e),c=Array.isArray(this.decimalMarker)?"."===this.thousandSeparator?",":".":this.decimalMarker;if(0===f?i=this.allowNegativeNumbers?i.length>2&&"-"===i[0]&&"0"===i[1]&&i[2]!==this.thousandSeparator&&","!==i[2]&&"."!==i[2]?"-"+i.slice(2,i.length):"0"===i[0]&&i.length>1&&i[1]!==this.thousandSeparator&&","!==i[1]&&"."!==i[1]?i.slice(1,i.length):i:i.length>1&&"0"===i[0]&&i[1]!==this.thousandSeparator&&","!==i[1]&&"."!==i[1]?i.slice(1,i.length):i:(i[0]===c&&i.length>1&&(i="0"+i.slice(0,i.length+1),this.plusOnePosition=!0),"0"===i[0]&&i[1]!==c&&i[1]!==this.thousandSeparator&&(i=i.length>1?i.slice(0,1)+c+i.slice(1,i.length+1):i,this.plusOnePosition=!0),this.allowNegativeNumbers&&"-"===i[0]&&(i[1]===c||"0"===i[1])&&(i=i[1]===c&&i.length>2?i.slice(0,1)+"0"+i.slice(1,i.length):"0"===i[1]&&i.length>2&&i[2]!==c?i.slice(0,2)+c+i.slice(2,i.length):i,this.plusOnePosition=!0)),h){const M=i.slice(this._findFirstNonZeroDigitIndex(i),i.length),j="0"===i[m]||i[m]===c,Z="0"===i[0],K="-"===i[0],ee=i[0]===this.thousandSeparator,te="0"===i[1],ae=i[2]===c;Z&&i[1]===c&&j&&m<2&&(i=M),K&&te&&ae&&j&&m<3&&(i="-"+M),"-"!==M&&(0===m&&(Z||ee)||this.allowNegativeNumbers&&1===m&&K&&!te)&&(i=K?"-"+M:M)}const N=this._charToRegExpExpression(this.thousandSeparator);let V='@#!$%^&*()_+|~=`{}\\[\\]:\\s,\\.";<>?\\/'.replace(N,"");if(Array.isArray(this.decimalMarker))for(const M of this.decimalMarker)V=V.replace(this._charToRegExpExpression(M),"");else V=V.replace(this._charToRegExpExpression(this.decimalMarker),"");const A=new RegExp("["+V+"]");i.match(A)&&(i=i.substring(0,i.length-1)),i=this.checkInputPrecision(i,f,this.decimalMarker);const F=i.replace(new RegExp(N,"g"),"");n=this._formatWithSeparators(F,this.thousandSeparator,this.decimalMarker,f);const re=n.indexOf(",")-i.indexOf(","),$=n.length-i.length;if((n[m-1]===this.thousandSeparator||n[m-this.prefix.length])&&this.prefix&&h)m-=1;else if($>0&&n[m]!==this.thousandSeparator||h&&n.length<t.length&&this.separatorLimit){d=!0;let M=0;do{this._shift.add(m+M),M++}while(M<$)}else n[m-1]===this.thousandSeparator||-4===$||-3===$||n[m]===this.thousandSeparator?(this._shift.clear(),this._shift.add(m-1)):0!==re&&m>0&&!(n.indexOf(",")>=m&&m>3)||!(n.indexOf(".")>=m&&m>3)&&$<=0?(this._shift.clear(),d=!0,y=$,m+=$,this._shift.add(m)):this._shift.clear()}else for(let f=0,c=E[0];f<E.length&&a!==e.length;f++,c=E[f]??""){const N="*"in this.patterns;if(this._checkSymbolMask(c,e[a]??"")&&"?"===e[a+1])n+=c,a+=2;else if("*"===e[a+1]&&p&&this._checkSymbolMask(c,e[a+2]??""))n+=c,a+=3,p=!1;else if(this._checkSymbolMask(c,e[a]??"")&&"*"===e[a+1]&&!N)n+=c,p=!0;else if("?"===e[a+1]&&this._checkSymbolMask(c,e[a+2]??""))n+=c,a+=3;else if(this._checkSymbolMask(c,e[a]??"")){if("H"===e[a]&&(this.apm?Number(c)>9:Number(c)>2)){m=this.leadZeroDateTime?m:m+1,a+=1,this._shiftStep(e,a,E.length),f--,this.leadZeroDateTime&&(n+="0");continue}if("h"===e[a]&&(this.apm?1===n.length&&Number(n)>1||"1"===n&&Number(c)>2||1===i.slice(a-1,a).length&&Number(i.slice(a-1,a))>2||"1"===i.slice(a-1,a)&&Number(c)>2:"2"===n&&Number(c)>3||("2"===n.slice(a-2,a)||"2"===n.slice(a-3,a)||"2"===n.slice(a-4,a)||"2"===n.slice(a-1,a))&&Number(c)>3&&a>10)){m+=1,a+=1,f--;continue}if(("m"===e[a]||"s"===e[a])&&Number(c)>5){m=this.leadZeroDateTime?m:m+1,a+=1,this._shiftStep(e,a,E.length),f--,this.leadZeroDateTime&&(n+="0");continue}const V=31,A=i[a],F=i[a+1],re=i[a+2],$=i[a-1],q=i[a-2],M=i.slice(a-3,a-1),j=i.slice(a-1,a+1),Z=i.slice(a,a+2),K=i.slice(a-2,a);if("d"===e[a]){const ee="M0"===e.slice(0,2),J="M0"===e.slice(0,2)&&this.specialCharacters.includes(q);if(Number(c)>3&&this.leadZeroDateTime||!ee&&(Number(Z)>V||Number(j)>V||this.specialCharacters.includes(F))||(J?Number(j)>V||!this.specialCharacters.includes(A)&&this.specialCharacters.includes(re)||this.specialCharacters.includes(A):Number(Z)>V||this.specialCharacters.includes(F)&&!h)){m=this.leadZeroDateTime?m:m+1,a+=1,this._shiftStep(e,a,E.length),f--,this.leadZeroDateTime&&(n+="0");continue}}if("M"===e[a]){const J=0===a&&(Number(c)>2||Number(Z)>12||this.specialCharacters.includes(F)&&!h),te=e.slice(a+2,a+3),ae=M.includes(te)&&e.includes("d0")&&(this.specialCharacters.includes(q)&&Number(j)>12&&!this.specialCharacters.includes(A)||this.specialCharacters.includes(A)),le=Number(M)<=V&&!this.specialCharacters.includes(M)&&this.specialCharacters.includes($)&&(Number(Z)>12||this.specialCharacters.includes(F)),he=Number(Z)>12&&5===a||this.specialCharacters.includes(F)&&5===a,oe=Number(M)>V&&!this.specialCharacters.includes(M)&&!this.specialCharacters.includes(K)&&Number(K)>12&&e.includes("d0"),ce=Number(M)<=V&&!this.specialCharacters.includes(M)&&!this.specialCharacters.includes($)&&Number(j)>12;if(Number(c)>1&&this.leadZeroDateTime||J||ae||ce||oe||le||he&&!this.leadZeroDateTime){m=this.leadZeroDateTime?m:m+1,a+=1,this._shiftStep(e,a,E.length),f--,this.leadZeroDateTime&&(n+="0");continue}}n+=c,a++}else this.specialCharacters.includes(c)&&e[a]===c?(n+=c,a++):-1!==this.specialCharacters.indexOf(e[a]??"")?(n+=e[a],a++,this._shiftStep(e,a,E.length),f--):"9"===e[a]&&this.showMaskTyped?this._shiftStep(e,a,E.length):this.patterns[e[a]??""]&&this.patterns[e[a]??""]?.optional?(E[a]&&"***************"!==e&&"000.000.000-00"!==e&&"00.000.000/0000-00"!==e&&!e.match(/^9+\.0+$/)&&!this.patterns[e[a]??""]?.optional&&(n+=E[a]),e.includes("9*")&&e.includes("0*")&&a++,a++,f--):"*"===this.maskExpression[a+1]&&this._findSpecialChar(this.maskExpression[a+2]??"")&&this._findSpecialChar(c)===this.maskExpression[a+2]&&p||"?"===this.maskExpression[a+1]&&this._findSpecialChar(this.maskExpression[a+2]??"")&&this._findSpecialChar(c)===this.maskExpression[a+2]&&p?(a+=3,n+=c):this.showMaskTyped&&this.specialCharacters.indexOf(c)<0&&c!==this.placeHolderCharacter&&1===this.placeHolderCharacter.length&&(b=!0)}n.length+1===e.length&&-1!==this.specialCharacters.indexOf(e[e.length-1]??"")&&(n+=e[e.length-1]);let B=m+1;for(;this._shift.has(B);)y++,B++;let Q=s&&!e.startsWith("separator")?a:this._shift.has(m)?y:0;b&&Q--,u(Q,d),y<0&&this._shift.clear();let z=!1;h&&(z=E.every(f=>this.specialCharacters.includes(f)));let W=`${this.prefix}${z?"":n}${this.showMaskTyped?"":this.suffix}`;0===n.length&&(W=this.dropSpecialCharacters?`${n}`:`${this.prefix}${n}`);const Y=1===i.length&&this.specialCharacters.includes(e[0])&&i!==e[0];if(!this._checkSymbolMask(i,e[1])&&Y)return"";if(n.includes("-")&&this.prefix&&this.allowNegativeNumbers){if(h&&"-"===n)return"";W=`-${this.prefix}${n.split("-").join("")}${this.suffix}`}return W}_findDropSpecialChar(t){return Array.isArray(this.dropSpecialCharacters)?this.dropSpecialCharacters.find(e=>e===t):this._findSpecialChar(t)}_findSpecialChar(t){return this.specialCharacters.find(e=>e===t)}_checkSymbolMask(t,e){return this.patterns=this.customPattern?this.customPattern:this.patterns,(this.patterns[e]?.pattern&&this.patterns[e]?.pattern.test(t))??!1}_stripToDecimal(t){return t.split("").filter((e,r)=>{const s="string"==typeof this.decimalMarker?e===this.decimalMarker:this.decimalMarker.includes(e);return e.match("^-?\\d")||e===this.thousandSeparator||s||"-"===e&&0===r&&this.allowNegativeNumbers}).join("")}_charToRegExpExpression(t){return t&&(" "===t?"\\s":"[\\^$.|?*+()".indexOf(t)>=0?`\\${t}`:t)}_shiftStep(t,e,r){const s=/[*?]/g.test(t.slice(0,e))?r:e;this._shift.add(s+this.prefix.length||0)}_compareOrIncludes(t,e,r){return Array.isArray(e)?e.filter(s=>s!==r).includes(t):t===e}_validIP(t){return!(4===t.length&&!t.some((e,r)=>t.length!==r+1?""===e||Number(e)>255:""===e||Number(e.substring(0,3))>255))}_splitPercentZero(t){if("-"===t&&this.allowNegativeNumbers)return t;const e=t.indexOf("string"==typeof this.decimalMarker?this.decimalMarker:"."),r=this.allowNegativeNumbers&&t.includes("-")?"-":"";if(-1===e){const s=parseInt(r?t.slice(1,t.length):t,10);return isNaN(s)?"":`${r}${s}`}{const s=parseInt(t.replace("-","").substring(0,e),10),h=t.substring(e+1),u=isNaN(s)?"":s.toString();return""===u?"":`${r}${u}${"string"==typeof this.decimalMarker?this.decimalMarker:"."}${h}`}}_findFirstNonZeroDigitIndex(t){for(let e=0;e<t.length;e++){const r=t[e];if(r&&r>="1"&&r<="9")return e}return-1}}return(o=g).\u0275fac=function(t){return new(t||o)},o.\u0275prov=l.jDH({token:o,factory:o.\u0275fac}),g})(),U=(()=>{var o;class g extends se{constructor(){super(...arguments),this.isNumberValue=!1,this.maskIsShown="",this.selStart=null,this.selEnd=null,this.writingValue=!1,this.maskChanged=!1,this._maskExpressionArray=[],this.triggerOnMaskChange=!1,this._previousValue="",this._currentValue="",this._emitValue=!1,this.onChange=t=>{},this._elementRef=(0,l.WQX)(l.aKT,{optional:!0}),this.document=(0,l.WQX)(C.qQ),this._config=(0,l.WQX)(D),this._renderer=(0,l.WQX)(l.sFG,{optional:!0})}applyMask(t,e,r=0,s=!1,h=!1,u=()=>{}){if(!e)return t!==this.actualValue?this.actualValue:t;if(this.maskIsShown=this.showMaskTyped?this.showMaskInInput():"","IP"===this.maskExpression&&this.showMaskTyped&&(this.maskIsShown=this.showMaskInInput(t||"#")),"CPF_CNPJ"===this.maskExpression&&this.showMaskTyped&&(this.maskIsShown=this.showMaskInInput(t||"#")),!t&&this.showMaskTyped)return this.formControlResult(this.prefix),`${this.prefix}${this.maskIsShown}${this.suffix}`;const a=t&&"number"==typeof this.selStart?t[this.selStart]??"":"";let n="",p=r;if(this.hiddenInput&&!this.writingValue){let i=t&&1===t.length?t.split(""):this.actualValue.split("");"object"==typeof this.selStart&&"object"==typeof this.selEnd?(this.selStart=Number(this.selStart),this.selEnd=Number(this.selEnd)):""!==t&&i.length?"number"==typeof this.selStart&&"number"==typeof this.selEnd&&(t.length>i.length?i.splice(this.selStart,0,a):t.length<i.length&&(i.length-t.length==1?i.splice(h?this.selStart-1:t.length-1,1):i.splice(this.selStart,this.selEnd-this.selStart))):i=[],this.showMaskTyped&&!this.hiddenInput&&(n=this.removeMask(t)),n=this.actualValue.length&&i.length<=t.length?this.shiftTypedSymbols(i.join("")):t}if(s&&(this.hiddenInput||!this.hiddenInput)&&(n=t),h&&-1!==this.specialCharacters.indexOf(this.maskExpression[p]??"")&&this.showMaskTyped&&!this.prefix&&(n=this._currentValue),this.deletedSpecialCharacter&&p&&(this.specialCharacters.includes(this.actualValue.slice(p,p+1))?p+=1:"M0"!==e.slice(p-1,p+1)&&(p-=2),this.deletedSpecialCharacter=!1),this.showMaskTyped&&1===this.placeHolderCharacter.length&&!this.leadZeroDateTime&&(n=this.removeMask(t)),n=this.maskChanged?t:n&&n.length?n:t,this.showMaskTyped&&this.keepCharacterPositions&&this.actualValue&&!s&&!this.writingValue){const i=this.dropSpecialCharacters?this.removeMask(this.actualValue):this.actualValue;return this.formControlResult(i),this.actualValue?this.actualValue:`${this.prefix}${this.maskIsShown}${this.suffix}`}const d=super.applyMask(n,e,p,s,h,u);if(this.actualValue=this.getActualValue(d),"."===this.thousandSeparator&&"."===this.decimalMarker&&(this.decimalMarker=","),this.maskExpression.startsWith("separator")&&!0===this.dropSpecialCharacters&&(this.specialCharacters=this.specialCharacters.filter(i=>!this._compareOrIncludes(i,this.decimalMarker,this.thousandSeparator))),(d||""===d)&&(this._previousValue=this._currentValue,this._currentValue=d,this._emitValue=this._previousValue!==this._currentValue||this.maskChanged||this.writingValue||this._previousValue===this._currentValue&&s),this._emitValue&&(this.writingValue&&this.triggerOnMaskChange?requestAnimationFrame(()=>this.formControlResult(d)):this.formControlResult(d)),!this.showMaskTyped||this.showMaskTyped&&this.hiddenInput)return this.hiddenInput?h?this.hideInput(d,this.maskExpression):`${this.hideInput(d,this.maskExpression)}${this.maskIsShown.slice(d.length)}`:d;const y=d.length,b=`${this.prefix}${this.maskIsShown}${this.suffix}`;if(this.maskExpression.includes("H")){const i=this._numberSkipedSymbols(d);return`${d}${b.slice(y+i)}`}return"IP"===this.maskExpression||"CPF_CNPJ"===this.maskExpression?`${d}${b}`:`${d}${b.slice(y)}`}_numberSkipedSymbols(t){const e=/(^|\D)(\d\D)/g;let r=e.exec(t),s=0;for(;null!=r;)s+=1,r=e.exec(t);return s}applyValueChanges(t,e,r,s=()=>{}){const h=this._elementRef?.nativeElement;h&&(h.value=this.applyMask(h.value,this.maskExpression,t,e,r,s),h!==this._getActiveElement()&&this.clearIfNotMatchFn())}hideInput(t,e){return t.split("").map((r,s)=>this.patterns&&this.patterns[e[s]??""]&&this.patterns[e[s]??""]?.symbol?this.patterns[e[s]??""]?.symbol:r).join("")}getActualValue(t){const e=t.split("").filter((r,s)=>{const h=this.maskExpression[s]??"";return this._checkSymbolMask(r,h)||this.specialCharacters.includes(h)&&r===h});return e.join("")===t?e.join(""):t}shiftTypedSymbols(t){let e="";return(t&&t.split("").map((s,h)=>{if(this.specialCharacters.includes(t[h+1]??"")&&t[h+1]!==this.maskExpression[h+1])return e=s,t[h+1];if(e.length){const u=e;return e="",u}return s})||[]).join("")}numberToString(t){return!t&&0!==t||this.maskExpression.startsWith("separator")&&(this.leadZero||!this.dropSpecialCharacters)||this.maskExpression.startsWith("separator")&&this.separatorLimit.length>14&&String(t).length>14?String(t):Number(t).toLocaleString("fullwide",{useGrouping:!1,maximumFractionDigits:20}).replace("/-/","-")}showMaskInInput(t){if(this.showMaskTyped&&this.shownMaskExpression){if(this.maskExpression.length!==this.shownMaskExpression.length)throw new Error("Mask expression must match mask placeholder length");return this.shownMaskExpression}if(this.showMaskTyped){if(t){if("IP"===this.maskExpression)return this._checkForIp(t);if("CPF_CNPJ"===this.maskExpression)return this._checkForCpfCnpj(t)}return this.placeHolderCharacter.length===this.maskExpression.length?this.placeHolderCharacter:this.maskExpression.replace(/\w/g,this.placeHolderCharacter)}return""}clearIfNotMatchFn(){const t=this._elementRef?.nativeElement;t&&this.clearIfNotMatch&&this.prefix.length+this.maskExpression.length+this.suffix.length!==t.value.replace(this.placeHolderCharacter,"").length&&(this.formElementProperty=["value",""],this.applyMask("",this.maskExpression))}set formElementProperty([t,e]){!this._renderer||!this._elementRef||Promise.resolve().then(()=>this._renderer?.setProperty(this._elementRef?.nativeElement,t,e))}checkDropSpecialCharAmount(t){return t.split("").filter(r=>this._findDropSpecialChar(r)).length}removeMask(t){return this._removeMask(this._removeSuffix(this._removePrefix(t)),this.specialCharacters.concat("_").concat(this.placeHolderCharacter))}_checkForIp(t){if("#"===t)return`${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`;const e=[];for(let r=0;r<t.length;r++){const s=t[r]??"";s&&s.match("\\d")&&e.push(s)}return e.length<=3?`${this.placeHolderCharacter}.${this.placeHolderCharacter}.${this.placeHolderCharacter}`:e.length>3&&e.length<=6?`${this.placeHolderCharacter}.${this.placeHolderCharacter}`:e.length>6&&e.length<=9?this.placeHolderCharacter:""}_checkForCpfCnpj(t){const e=`${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}-${this.placeHolderCharacter}${this.placeHolderCharacter}`,r=`${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}.${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}/${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}${this.placeHolderCharacter}-${this.placeHolderCharacter}${this.placeHolderCharacter}`;if("#"===t)return e;const s=[];for(let h=0;h<t.length;h++){const u=t[h]??"";u&&u.match("\\d")&&s.push(u)}return s.length<=3?e.slice(s.length,e.length):s.length>3&&s.length<=6?e.slice(s.length+1,e.length):s.length>6&&s.length<=9?e.slice(s.length+2,e.length):s.length>9&&s.length<11?e.slice(s.length+3,e.length):11===s.length?"":12===s.length?r.slice(17===t.length?16:15,r.length):s.length>12&&s.length<=14?r.slice(s.length+4,r.length):""}_getActiveElement(t=this.document){const e=t?.activeElement?.shadowRoot;return e?.activeElement?this._getActiveElement(e):t.activeElement}formControlResult(t){if(!this.writingValue||t)return this.writingValue||!this.triggerOnMaskChange&&this.maskChanged?(this.triggerOnMaskChange&&this.maskChanged&&this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeSuffix(this._removePrefix(t)))))),void(this.maskChanged=!1)):void(Array.isArray(this.dropSpecialCharacters)?this.onChange(this.outputTransformFn(this._toNumber(this._checkSymbols(this._removeMask(this._removeSuffix(this._removePrefix(t)),this.dropSpecialCharacters))))):this.onChange(this.outputTransformFn(this._toNumber(this.dropSpecialCharacters||!this.dropSpecialCharacters&&this.prefix===t?this._checkSymbols(this._removeSuffix(this._removePrefix(t))):t))));this.onChange("")}_toNumber(t){if(!this.isNumberValue||""===t||this.maskExpression.startsWith("separator")&&(this.leadZero||!this.dropSpecialCharacters))return t;if(String(t).length>14&&this.maskExpression.startsWith("separator"))return String(t);const e=Number(t);if(this.maskExpression.startsWith("separator")&&Number.isNaN(e)){const r=String(t).replace(",",".");return Number(r)}return Number.isNaN(e)?t:e}_removeMask(t,e){return this.maskExpression.startsWith("percent")&&t.includes(".")?t:t&&t.replace(this._regExpForRemove(e),"")}_removePrefix(t){return this.prefix?t&&t.replace(this.prefix,""):t}_removeSuffix(t){return this.suffix?t&&t.replace(this.suffix,""):t}_retrieveSeparatorValue(t){let e=Array.isArray(this.dropSpecialCharacters)?this.specialCharacters.filter(r=>this.dropSpecialCharacters.includes(r)):this.specialCharacters;return!this.deletedSpecialCharacter&&this._checkPatternForSpace()&&t.includes(" ")&&this.maskExpression.includes("*")&&(e=e.filter(r=>" "!==r)),this._removeMask(t,e)}_regExpForRemove(t){return new RegExp(t.map(e=>`\\${e}`).join("|"),"gi")}_replaceDecimalMarkerToDot(t){const e=Array.isArray(this.decimalMarker)?this.decimalMarker:[this.decimalMarker];return t.replace(this._regExpForRemove(e),".")}_checkSymbols(t){let e=t;if(""===e)return e;this.maskExpression.startsWith("percent")&&","===this.decimalMarker&&(e=e.replace(",","."));const r=this._retrieveSeparatorPrecision(this.maskExpression),s=this._replaceDecimalMarkerToDot(this._retrieveSeparatorValue(e));return this.isNumberValue&&r?e===this.decimalMarker?null:s.length>14?String(s):this._checkPrecision(this.maskExpression,s):s}_checkPatternForSpace(){for(const t in this.patterns)if(this.patterns[t]&&this.patterns[t]?.hasOwnProperty("pattern")){const e=this.patterns[t]?.pattern.toString(),r=this.patterns[t]?.pattern;if(e?.includes(" ")&&r?.test(this.maskExpression))return!0}return!1}_retrieveSeparatorPrecision(t){const e=t.match(new RegExp("^separator\\.([^d]*)"));return e?Number(e[1]):null}_checkPrecision(t,e){const r=this.getPrecision(t);let s=e;return t.indexOf("2")>0||this.leadZero&&Number(r)>0?(","===this.decimalMarker&&this.leadZero&&(s=s.replace(",",".")),this.leadZero?Number(s).toFixed(Number(r)):Number(s).toFixed(2)):this.numberToString(s)}_repeatPatternSymbols(t){return t.match(/{[0-9]+}/)&&t.split("").reduce((e,r,s)=>{if(this._start="{"===r?s:this._start,"}"!==r)return this._findSpecialChar(r)?e+r:e;this._end=s;const h=Number(t.slice(this._start+1,this._end)),u=new Array(h+1).join(t[this._start-1]);if(t.slice(0,this._start).length>1&&t.includes("S")){const a=t.slice(0,this._start-1);return a.includes("{")?e+u:a+e+u}return e+u},"")||t}currentLocaleDecimalMarker(){return 1.1.toLocaleString().substring(1,2)}}return(o=g).\u0275fac=(()=>{let T;return function(e){return(T||(T=l.xGo(o)))(e||o)}})(),o.\u0275prov=l.jDH({token:o,factory:o.\u0275fac}),g})();function ie(){const o=(0,l.WQX)(I),g=(0,l.WQX)(w);return g instanceof Function?{...o,...g()}:{...o,...g}}function x(o){return[{provide:w,useValue:o},{provide:I,useValue:R},{provide:D,useFactory:ie},U]}let _=(()=>{var o;class g{constructor(){this.maskExpression="",this.specialCharacters=[],this.patterns={},this.prefix="",this.suffix="",this.thousandSeparator=" ",this.decimalMarker=".",this.dropSpecialCharacters=null,this.hiddenInput=null,this.showMaskTyped=null,this.placeHolderCharacter=null,this.shownMaskExpression=null,this.showTemplate=null,this.clearIfNotMatch=null,this.validation=null,this.separatorLimit=null,this.allowNegativeNumbers=null,this.leadZeroDateTime=null,this.leadZero=null,this.triggerOnMaskChange=null,this.apm=null,this.inputTransformFn=null,this.outputTransformFn=null,this.keepCharacterPositions=null,this.maskFilled=new l.bkB,this._maskValue="",this._position=null,this._maskExpressionArray=[],this._allowFewMaskChangeMask=!1,this._justPasted=!1,this._isFocused=!1,this._isComposing=!1,this.document=(0,l.WQX)(C.qQ),this._maskService=(0,l.WQX)(U,{self:!0}),this._config=(0,l.WQX)(D),this.onChange=t=>{},this.onTouch=()=>{}}ngOnChanges(t){const{maskExpression:e,specialCharacters:r,patterns:s,prefix:h,suffix:u,thousandSeparator:a,decimalMarker:n,dropSpecialCharacters:p,hiddenInput:d,showMaskTyped:y,placeHolderCharacter:b,shownMaskExpression:i,showTemplate:m,clearIfNotMatch:E,validation:O,separatorLimit:B,allowNegativeNumbers:Q,leadZeroDateTime:z,leadZero:W,triggerOnMaskChange:Y,apm:f,inputTransformFn:c,outputTransformFn:N,keepCharacterPositions:V}=t;if(e&&(e.currentValue!==e.previousValue&&!e.firstChange&&(this._maskService.maskChanged=!0),e.currentValue&&e.currentValue.split("||").length>1?(this._maskExpressionArray=e.currentValue.split("||").sort((A,F)=>A.length-F.length),this._setMask()):(this._maskExpressionArray=[],this._maskValue=e.currentValue||"",this._maskService.maskExpression=this._maskValue)),r){if(!r.currentValue||!Array.isArray(r.currentValue))return;this._maskService.specialCharacters=r.currentValue||[]}Q&&(this._maskService.allowNegativeNumbers=Q.currentValue,this._maskService.allowNegativeNumbers&&(this._maskService.specialCharacters=this._maskService.specialCharacters.filter(A=>"-"!==A))),s&&s.currentValue&&(this._maskService.patterns=s.currentValue),f&&f.currentValue&&(this._maskService.apm=f.currentValue),h&&(this._maskService.prefix=h.currentValue),u&&(this._maskService.suffix=u.currentValue),a&&(this._maskService.thousandSeparator=a.currentValue),n&&(this._maskService.decimalMarker=n.currentValue),p&&(this._maskService.dropSpecialCharacters=p.currentValue),d&&(this._maskService.hiddenInput=d.currentValue),y&&(this._maskService.showMaskTyped=y.currentValue,!1===y.previousValue&&!0===y.currentValue&&this._isFocused&&requestAnimationFrame(()=>{this._maskService._elementRef?.nativeElement.click()})),b&&(this._maskService.placeHolderCharacter=b.currentValue),i&&(this._maskService.shownMaskExpression=i.currentValue),m&&(this._maskService.showTemplate=m.currentValue),E&&(this._maskService.clearIfNotMatch=E.currentValue),O&&(this._maskService.validation=O.currentValue),B&&(this._maskService.separatorLimit=B.currentValue),z&&(this._maskService.leadZeroDateTime=z.currentValue),W&&(this._maskService.leadZero=W.currentValue),Y&&(this._maskService.triggerOnMaskChange=Y.currentValue),c&&(this._maskService.inputTransformFn=c.currentValue),N&&(this._maskService.outputTransformFn=N.currentValue),V&&(this._maskService.keepCharacterPositions=V.currentValue),this._applyMask()}validate({value:t}){const e="number"==typeof t?String(t):t;if(!this._maskService.validation||!this._maskValue)return null;if(this._maskService.ipError)return this._createValidationError(e);if(this._maskService.cpfCnpjError)return this._createValidationError(e);if(this._maskValue.startsWith("separator")||X.includes(this._maskValue)||this._maskService.clearIfNotMatch)return null;if(H.includes(this._maskValue))return this._validateTime(e);if("A*@A*.A*"===this._maskValue)return!/^[^@]+@[^@]+\.[^@]+$/.test(e)&&e?this._createValidationError(e):null;if(e&&e.length>=1){let r=0;if(this._maskValue.includes("{")&&this._maskValue.includes("}"))return this._maskValue.slice(this._maskValue.indexOf("{")+1,this._maskValue.indexOf("}"))===String(e.length)?null:this._createValidationError(e);if(this._maskValue.startsWith("percent"))return null;for(const s in this._maskService.patterns)if(this._maskService.patterns[s]?.optional&&(this._maskValue.indexOf(s)!==this._maskValue.lastIndexOf(s)?r+=this._maskValue.split("").filter(u=>u===s).join("").length:-1!==this._maskValue.indexOf(s)&&r++,-1!==this._maskValue.indexOf(s)&&e.length>=this._maskValue.indexOf(s)||r===this._maskValue.length))return null;if(this._maskValue.indexOf("*")>1&&e.length<this._maskValue.indexOf("*")||this._maskValue.indexOf("?")>1&&e.length<this._maskValue.indexOf("?"))return this._createValidationError(e);if(-1===this._maskValue.indexOf("*")||-1===this._maskValue.indexOf("?")){const s=this._maskValue.split("*"),h=this._maskService.dropSpecialCharacters?this._maskValue.length-this._maskService.checkDropSpecialCharAmount(this._maskValue)-r:this.prefix?this._maskValue.length+this.prefix.length-r:this._maskValue.length-r;if(1===s.length&&e.length<h)return this._createValidationError(e);if(s.length>1){const u=s[s.length-1];if(u&&this._maskService.specialCharacters.includes(u[0])&&String(e).includes(u[0]??"")&&!this.dropSpecialCharacters){const a=t.split(u[0]);return a[a.length-1].length===u.length-1?null:this._createValidationError(e)}return(u&&!this._maskService.specialCharacters.includes(u[0])||!u||this._maskService.dropSpecialCharacters)&&e.length>=h-1?null:this._createValidationError(e)}}if(1===this._maskValue.indexOf("*")||1===this._maskValue.indexOf("?"))return null}return t&&this.maskFilled.emit(),null}onPaste(){this._justPasted=!0}onFocus(){this._isFocused=!0}onModelChange(t){(""===t||null===t||typeof t>"u")&&this._maskService.actualValue&&(this._maskService.actualValue=this._maskService.getActualValue(""))}onInput(t){if(this._isComposing)return;const e=t.target,r=this._maskService.inputTransformFn(e.value);if("number"!==e.type)if("string"==typeof r||"number"==typeof r){if(e.value=r.toString(),this._inputValue=e.value,this._setMask(),!this._maskValue)return void this.onChange(e.value);let s=1===e.selectionStart?e.selectionStart+this._maskService.prefix.length:e.selectionStart;if(this.showMaskTyped&&this.keepCharacterPositions&&1===this._maskService.placeHolderCharacter.length){const n=e.value.slice(s-1,s),p=this.prefix.length,d=this._maskService._checkSymbolMask(n,this._maskService.maskExpression[s-1-p]??""),y=this._maskService._checkSymbolMask(n,this._maskService.maskExpression[s+1-p]??""),b=this._maskService.selStart===this._maskService.selEnd,i=Number(this._maskService.selStart)-p,m=Number(this._maskService.selEnd)-p,E="Backspace"===this._code||"Delete"===this._code;if(E){if(b){if(!this._maskService.specialCharacters.includes(this._maskService.maskExpression.slice(s-this.prefix.length,s+1-this.prefix.length))&&b)if(1===i&&this.prefix)this._maskService.actualValue=`${this.prefix}${this._maskService.placeHolderCharacter}${e.value.split(this.prefix).join("").split(this.suffix).join("")}${this.suffix}`,s-=1;else{const O=e.value.substring(0,s),B=e.value.substring(s);this._maskService.actualValue=`${O}${this._maskService.placeHolderCharacter}${B}`}}else this._maskService.actualValue=this._maskService.selStart===p?`${this.prefix}${this._maskService.maskIsShown.slice(0,m)}${this._inputValue.split(this.prefix).join("")}`:this._maskService.selStart===this._maskService.maskIsShown.length+p?`${this._inputValue}${this._maskService.maskIsShown.slice(i,m)}`:`${this.prefix}${this._inputValue.split(this.prefix).join("").slice(0,i)}${this._maskService.maskIsShown.slice(i,m)}${this._maskService.actualValue.slice(m+p,this._maskService.maskIsShown.length+p)}${this.suffix}`;s="Delete"===this._code?s+1:s}E||(d||y||!b?this._maskService.specialCharacters.includes(e.value.slice(s,s+1))&&y&&!this._maskService.specialCharacters.includes(e.value.slice(s+1,s+2))?(this._maskService.actualValue=`${e.value.slice(0,s-1)}${e.value.slice(s,s+1)}${n}${e.value.slice(s+2)}`,s+=1):d?this._maskService.actualValue=1===e.value.length&&1===s?`${this.prefix}${n}${this._maskService.maskIsShown.slice(1,this._maskService.maskIsShown.length)}${this.suffix}`:`${e.value.slice(0,s-1)}${n}${e.value.slice(s+1).split(this.suffix).join("")}${this.suffix}`:this.prefix&&1===e.value.length&&s-p==1&&this._maskService._checkSymbolMask(e.value,this._maskService.maskExpression[s-1-p]??"")&&(this._maskService.actualValue=`${this.prefix}${e.value}${this._maskService.maskIsShown.slice(1,this._maskService.maskIsShown.length)}${this.suffix}`):s=Number(e.selectionStart)-1)}let h=0,u=!1;if("Delete"===this._code&&(this._maskService.deletedSpecialCharacter=!0),this._inputValue.length>=this._maskService.maskExpression.length-1&&"Backspace"!==this._code&&"d0/M0/0000"===this._maskService.maskExpression&&s<10){const n=this._inputValue.slice(s-1,s);e.value=this._inputValue.slice(0,s-1)+n+this._inputValue.slice(s+1)}if("d0/M0/0000"===this._maskService.maskExpression&&this.leadZeroDateTime&&(s<3&&Number(e.value)>31&&Number(e.value)<40||5===s&&Number(e.value.slice(3,5))>12)&&(s+=2),"Hh:m0:s0"===this._maskService.maskExpression&&this.apm&&(this._justPasted&&"00"===e.value.slice(0,2)&&(e.value=e.value.slice(1,2)+e.value.slice(2,e.value.length)),e.value="00"===e.value?"0":e.value),this._maskService.applyValueChanges(s,this._justPasted,"Backspace"===this._code||"Delete"===this._code,(n,p)=>{this._justPasted=!1,h=n,u=p}),this._getActiveElement()!==e)return;if(this._maskService.plusOnePosition&&(s+=1,this._maskService.plusOnePosition=!1),this._maskExpressionArray.length)if("Backspace"===this._code){const n=this.specialCharacters.includes(this._maskService.actualValue.slice(s-1,s)),p=this.specialCharacters.includes(this._maskService.actualValue.slice(s,s+1));this._allowFewMaskChangeMask&&!p?(s=e.selectionStart+1,this._allowFewMaskChangeMask=!1):s=n?s-1:s}else s=1===e.selectionStart?e.selectionStart+this._maskService.prefix.length:e.selectionStart;this._position=1===this._position&&1===this._inputValue.length?null:this._position;let a=this._position?this._inputValue.length+s+h:s+("Backspace"!==this._code||u?h:0);a>this._getActualInputLength()&&(a=e.value===this._maskService.decimalMarker&&1===e.value.length?this._getActualInputLength()+1:this._getActualInputLength()),a<0&&(a=0),e.setSelectionRange(a,a),this._position=null}else console.warn("Ngx-mask writeValue work with string | number, your current value:",typeof r);else{if(!this._maskValue)return void this.onChange(e.value);this._maskService.applyValueChanges(e.value.length,this._justPasted,"Backspace"===this._code||"Delete"===this._code)}}onCompositionStart(){this._isComposing=!0}onCompositionEnd(t){this._isComposing=!1,this._justPasted=!0,this.onInput(t)}onBlur(t){if(this._maskValue){const e=t.target;if(this._maskService.leadZero&&e.value.length>0&&"string"==typeof this._maskService.decimalMarker){const r=this._maskService.maskExpression,s=Number(this._maskService.maskExpression.slice(r.length-1,r.length));if(s>0){e.value=this._maskService.suffix?e.value.split(this._maskService.suffix).join(""):e.value;const h=e.value.split(this._maskService.decimalMarker)[1];e.value=e.value.includes(this._maskService.decimalMarker)?e.value+"0".repeat(s-h.length)+this._maskService.suffix:e.value+this._maskService.decimalMarker+"0".repeat(s)+this._maskService.suffix,this._maskService.actualValue=e.value}}this._maskService.clearIfNotMatchFn()}this._isFocused=!1,this.onTouch()}onClick(t){if(!this._maskValue)return;const e=t.target;null!==e&&null!==e.selectionStart&&e.selectionStart===e.selectionEnd&&e.selectionStart>this._maskService.prefix.length&&38!==t.keyCode&&this._maskService.showMaskTyped&&!this.keepCharacterPositions&&(this._maskService.maskIsShown=this._maskService.showMaskInInput(),e.setSelectionRange&&this._maskService.prefix+this._maskService.maskIsShown===e.value?(e.focus(),e.setSelectionRange(0,0)):e.selectionStart>this._maskService.actualValue.length&&e.setSelectionRange(this._maskService.actualValue.length,this._maskService.actualValue.length));const h=e&&(e.value===this._maskService.prefix?this._maskService.prefix+this._maskService.maskIsShown:e.value);if(e&&e.value!==h&&(e.value=h),e&&"number"!==e.type&&(e.selectionStart||e.selectionEnd)<=this._maskService.prefix.length){const u=this._maskService.maskExpression.match(new RegExp(`^[${this._maskService.specialCharacters.map(a=>`\\${a}`).join("")}]+`))?.[0].length||0;e.selectionStart=this._maskService.prefix.length+u}else e&&e.selectionEnd>this._getActualInputLength()&&(e.selectionEnd=this._getActualInputLength())}onKeyDown(t){if(!this._maskValue)return;if(this._isComposing)return void("Enter"===t.key&&this.onCompositionEnd(t));this._code=t.code?t.code:t.key;const e=t.target;if(this._inputValue=e.value,this._setMask(),"number"!==e.type){if("ArrowUp"===t.key&&t.preventDefault(),"ArrowLeft"===t.key||"Backspace"===t.key||"Delete"===t.key){if("Backspace"===t.key&&0===e.value.length&&(e.selectionStart=e.selectionEnd),"Backspace"===t.key&&0!==e.selectionStart)if(this.specialCharacters=this.specialCharacters?.length?this.specialCharacters:this._config.specialCharacters,this.prefix.length>1&&e.selectionStart<=this.prefix.length)e.setSelectionRange(this.prefix.length,e.selectionEnd);else if(this._inputValue.length!==e.selectionStart&&1!==e.selectionStart)for(;this.specialCharacters.includes((this._inputValue[e.selectionStart-1]??"").toString())&&(this.prefix.length>=1&&e.selectionStart>this.prefix.length||0===this.prefix.length);)e.setSelectionRange(e.selectionStart-1,e.selectionEnd);this.checkSelectionOnDeletion(e),this._maskService.prefix.length&&e.selectionStart<=this._maskService.prefix.length&&e.selectionEnd<=this._maskService.prefix.length&&t.preventDefault(),"Backspace"===t.key&&!e.readOnly&&0===e.selectionStart&&e.selectionEnd===e.value.length&&0!==e.value.length&&(this._position=this._maskService.prefix?this._maskService.prefix.length:0,this._maskService.applyMask(this._maskService.prefix,this._maskService.maskExpression,this._position))}this.suffix&&this.suffix.length>1&&this._inputValue.length-this.suffix.length<e.selectionStart?e.setSelectionRange(this._inputValue.length-this.suffix.length,this._inputValue.length):("KeyA"===t.code&&t.ctrlKey||"KeyA"===t.code&&t.metaKey)&&(e.setSelectionRange(0,this._getActualInputLength()),t.preventDefault()),this._maskService.selStart=e.selectionStart,this._maskService.selEnd=e.selectionEnd}}writeValue(t){var e=this;return(0,k.A)(function*(){let r=t;if("object"==typeof r&&null!==r&&"value"in r&&("disable"in r&&e.setDisabledState(!!r.disable),r=r.value),null!==r&&(r=e.inputTransformFn?e.inputTransformFn(r):r),"string"==typeof r||"number"==typeof r||null===r||typeof r>"u"){(null===r||typeof r>"u"||""===r)&&(e._maskService._currentValue="",e._maskService._previousValue="");let s=r;if("number"==typeof s||e._maskValue.startsWith("separator")){s=String(s);const h=e._maskService.currentLocaleDecimalMarker();Array.isArray(e._maskService.decimalMarker)||(s=e._maskService.decimalMarker!==h?s.replace(h,e._maskService.decimalMarker):s),e._maskService.leadZero&&s&&e.maskExpression&&!1!==e.dropSpecialCharacters&&(s=e._maskService._checkPrecision(e._maskService.maskExpression,s)),(","===e._maskService.decimalMarker||Array.isArray(e._maskService.decimalMarker)&&"."===e._maskService.thousandSeparator)&&(s=s.toString().replace(".",",")),e.maskExpression?.startsWith("separator")&&e.leadZero&&requestAnimationFrame(()=>{e._maskService.applyMask(s?.toString()??"",e._maskService.maskExpression)}),e._maskService.isNumberValue=!0}"string"!=typeof s&&(s=""),e._inputValue=s,e._setMask(),s&&e._maskService.maskExpression||e._maskService.maskExpression&&(e._maskService.prefix||e._maskService.showMaskTyped)?("function"!=typeof e.inputTransformFn&&(e._maskService.writingValue=!0),e._maskService.formElementProperty=["value",e._maskService.applyMask(s,e._maskService.maskExpression)],"function"!=typeof e.inputTransformFn&&(e._maskService.writingValue=!1)):e._maskService.formElementProperty=["value",s],e._inputValue=s}else console.warn("Ngx-mask writeValue work with string | number, your current value:",typeof r)})()}registerOnChange(t){this._maskService.onChange=this.onChange=t}registerOnTouched(t){this.onTouch=t}_getActiveElement(t=this.document){const e=t?.activeElement?.shadowRoot;return e?.activeElement?this._getActiveElement(e):t.activeElement}checkSelectionOnDeletion(t){t.selectionStart=Math.min(Math.max(this.prefix.length,t.selectionStart),this._inputValue.length-this.suffix.length),t.selectionEnd=Math.min(Math.max(this.prefix.length,t.selectionEnd),this._inputValue.length-this.suffix.length)}setDisabledState(t){this._maskService.formElementProperty=["disabled",t]}_applyMask(){this._maskService.maskExpression=this._maskService._repeatPatternSymbols(this._maskValue||""),this._maskService.formElementProperty=["value",this._maskService.applyMask(this._inputValue,this._maskService.maskExpression)]}_validateTime(t){const e=this._maskValue.split("").filter(r=>":"!==r).length;return t&&(0==+(t[t.length-1]??-1)&&t.length<e||t.length<=e-2)?this._createValidationError(t):null}_getActualInputLength(){return this._maskService.actualValue.length||this._maskService.actualValue.length+this._maskService.prefix.length}_createValidationError(t){return{mask:{requiredMask:this._maskValue,actualValue:t}}}_setMask(){this._maskExpressionArray.some(t=>{if(t.split("").some(r=>this._maskService.specialCharacters.includes(r))&&this._inputValue&&this._areAllCharactersInEachStringSame(this._maskExpressionArray)||t.includes("{")){const r=this._maskService.removeMask(this._inputValue)?.length<=this._maskService.removeMask(t)?.length;if(r)return this._maskValue=this.maskExpression=this._maskService.maskExpression=t.includes("{")?this._maskService._repeatPatternSymbols(t):t,r;{"Backspace"===this._code&&(this._allowFewMaskChangeMask=!0);const s=this._maskExpressionArray[this._maskExpressionArray.length-1]??"";this._maskValue=this.maskExpression=this._maskService.maskExpression=s.includes("{")?this._maskService._repeatPatternSymbols(s):s}}else{const r=this._maskService.removeMask(this._inputValue)?.split("").every((s,h)=>{const u=t.charAt(h);return this._maskService._checkSymbolMask(s,u)});if(r||this._justPasted)return this._maskValue=this.maskExpression=this._maskService.maskExpression=t,r}})}_areAllCharactersInEachStringSame(t){const e=this._maskService.specialCharacters;return t.map(function r(h){const u=new RegExp(`[${e.map(a=>`\\${a}`).join("")}]`,"g");return h.replace(u,"")}).every(h=>1===new Set(h).size)}}return(o=g).\u0275fac=function(t){return new(t||o)},o.\u0275dir=l.FsC({type:o,selectors:[["input","mask",""],["textarea","mask",""]],hostBindings:function(t,e){1&t&&l.bIt("paste",function(){return e.onPaste()})("focus",function(s){return e.onFocus(s)})("ngModelChange",function(s){return e.onModelChange(s)})("input",function(s){return e.onInput(s)})("compositionstart",function(s){return e.onCompositionStart(s)})("compositionend",function(s){return e.onCompositionEnd(s)})("blur",function(s){return e.onBlur(s)})("click",function(s){return e.onClick(s)})("keydown",function(s){return e.onKeyDown(s)})},inputs:{maskExpression:[0,"mask","maskExpression"],specialCharacters:"specialCharacters",patterns:"patterns",prefix:"prefix",suffix:"suffix",thousandSeparator:"thousandSeparator",decimalMarker:"decimalMarker",dropSpecialCharacters:"dropSpecialCharacters",hiddenInput:"hiddenInput",showMaskTyped:"showMaskTyped",placeHolderCharacter:"placeHolderCharacter",shownMaskExpression:"shownMaskExpression",showTemplate:"showTemplate",clearIfNotMatch:"clearIfNotMatch",validation:"validation",separatorLimit:"separatorLimit",allowNegativeNumbers:"allowNegativeNumbers",leadZeroDateTime:"leadZeroDateTime",leadZero:"leadZero",triggerOnMaskChange:"triggerOnMaskChange",apm:"apm",inputTransformFn:"inputTransformFn",outputTransformFn:"outputTransformFn",keepCharacterPositions:"keepCharacterPositions"},outputs:{maskFilled:"maskFilled"},exportAs:["mask","ngxMask"],standalone:!0,features:[l.Jv_([{provide:L.kq,useExisting:o,multi:!0},{provide:L.cz,useExisting:o,multi:!0},U]),l.OA$]}),g})()}}]);