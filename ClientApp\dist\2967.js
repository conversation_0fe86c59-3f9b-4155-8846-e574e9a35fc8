"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2967],{2967:(k,g,a)=>{a.r(g),a.d(g,{AddCategoryExpensesComponent:()=>x});var s=a(9842),_=a(9097),p=a(3492),y=a(1342),C=a(1470),u=a(6146),e=a(4438),c=a(4006),m=a(9079),h=a(177),d=a(9417);const v=(n,i)=>({"bg-green-700 hover:bg-green-800 text-white":n,"bg-gray-400":i});function f(n,i){if(1&n&&(e.j41(0,"option",5)(1,"label"),e.EFF(2),e.k0s()()),2&n){const r=i.$implicit;e.Y8G("value",r.id),e.R7$(2),e.<PERSON>h(r.categoryName)}}function b(n,i){1&n&&(e.j41(0,"span",14),e.EFF(1," remove "),e.k0s(),e.j41(2,"span",18),e.EFF(3,"Remove All"),e.k0s())}function I(n,i){if(1&n){const r=e.RV6();e.j41(0,"div",9)(1,"div",19),e.EFF(2),e.j41(3,"div",20),e.bIt("click",function(){const t=e.eBV(r).$index,l=e.XpG();return e.Njj(l.RemoveService(t))}),e.j41(4,"span",14),e.EFF(5," delete_forever "),e.k0s()()()()}if(2&n){const r=i.$implicit;e.R7$(2),e.SpI(" ",r.itemName," ")}}function E(n,i){if(1&n){const r=e.RV6();e.j41(0,"input",21),e.mxI("ngModelChange",function(t){e.eBV(r);const l=e.XpG();return e.DH7(l.categoryNameItem,t)||(l.categoryNameItem=t),e.Njj(t)}),e.k0s()}if(2&n){const r=e.XpG();e.R50("ngModel",r.categoryNameItem)}}function A(n,i){if(1&n){const r=e.RV6();e.j41(0,"div",22),e.bIt("click",function(){e.eBV(r);const t=e.XpG();return e.Njj(t.CreatedCategoryItem())}),e.j41(1,"span",14),e.EFF(2," add "),e.k0s(),e.EFF(3," Created Category Item "),e.j41(4,"span"),e.EFF(5),e.k0s()()}if(2&n){const r=e.XpG();e.R7$(5),e.SpI('" ',r.categoryNameItem,' "')}}let x=(()=>{var n;class i{static getComponent(){return i}constructor(o,t){(0,s.A)(this,"dialogRef",void 0),(0,s.A)(this,"data",void 0),(0,s.A)(this,"_spinnerService",(0,e.WQX)(y.D)),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"_toastService",(0,e.WQX)(p.f)),(0,s.A)(this,"_categoryService",(0,e.WQX)(_.M)),(0,s.A)(this,"categoryId",""),(0,s.A)(this,"isAddSvcMode",!1),(0,s.A)(this,"listCategory",[]),(0,s.A)(this,"listCategoryItem",[]),(0,s.A)(this,"listItemCategoryRemove",[]),(0,s.A)(this,"categoryName",void 0),(0,s.A)(this,"categoryNameItem",void 0),this.dialogRef=o,this.data=t}ngOnInit(){this.GetAllCategoryAsync()}GetAllCategoryAsync(){this._categoryService.GetAllCategoryAsync({Page:1,PageSize:40,Search:""}).pipe((0,m.pQ)(this.destroyRef)).subscribe(t=>{t&&(this.listCategory=t.data,this.listCategory.length>0&&(this.listCategoryItem.push({itemName:this.data.item,categoryId:this.listCategory[0].id}),this.categoryId=this.listCategory[0].id))})}handleSelectCategory(o){this.categoryId=o.target.value.toString()}RemoveItem(o,t){this.listCategoryItem.splice(t,1),this.listItemCategoryRemove.push(o)}RemoveAll(){this.listCategoryItem=[]}RemoveService(o){this.listCategoryItem.splice(o,1)}CreatedCategoryItem(){this.isAddSvcMode=!1,this.listCategoryItem.push({itemName:this.categoryNameItem,categoryId:this.categoryId}),this.categoryNameItem=""}AddCategoryItem(){this.isAddSvcMode=!0}closeDialog(){this.dialogRef.close()}Edit(){}Save(){this.listCategoryItem.length>0&&(this.listCategoryItem.map(o=>o.categoryId=this.categoryId),this._spinnerService.show(),this._categoryService.CreateCategoryItem(this.listCategoryItem).pipe((0,m.pQ)(this.destroyRef)).subscribe(o=>{o&&(this._toastService.showSuccess("Save","Success"),this._spinnerService.hide(),this.dialogRef.close(o))}))}}return n=i,(0,s.A)(i,"\u0275fac",function(o){return new(o||n)(e.rXU(c.CP),e.rXU(c.Vh))}),(0,s.A)(i,"\u0275cmp",e.VBU({type:n,selectors:[["app-add-category-expenses"]],standalone:!0,features:[e.aNF],decls:29,vars:8,consts:[[3,"onClose"],[1,"relative","group","w-full","rounded-md","p-2"],[1,"text-start"],["for","client",1,"block","mb-2","text-sm","font-medium","text-gray-900"],[1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"change"],[3,"value"],[1,"text-start","pl-3"],[1,"p-3"],[1,"flex","items-center","text-blue-500",3,"click"],[1,"relative","group","w-full","border","rounded-md","p-2","mb-2"],[1,"mb-2"],["type","text","placeholder","Category Item",1,"bg-white","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModel"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500"],["type","button",1,"btn","btn-outline-secondary","btn-dashed",3,"click"],[1,"material-icons"],[1,"w-full","flex","justify-around","mt-3"],["type","button",1,"text-gray-900","bg-white","border","border-gray-300","focus:outline-none","hover:bg-gray-100","focus:ring-4","focus:ring-gray-100","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["type","button",1,"text-gray-900","focus:outline-none","hover:bg-gray-300","focus:ring-4","focus:ring-gray-100","font-medium","cursor-pointer","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click","disabled","ngClass"],[1,"ml-2","cursor-pointer"],[1,"flex","items-center","w-full","justify-between"],[1,"hidden","group-hover:block","text-end","cursor-pointer",3,"click"],["type","text","placeholder","Category Item",1,"bg-white","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","ngModel"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500",3,"click"]],template:function(o,t){1&o&&(e.j41(0,"app-innobook-modal-wrapper",0),e.bIt("onClose",function(){return t.closeDialog()}),e.j41(1,"div",1)(2,"div",2)(3,"label",3),e.EFF(4,"Select Category "),e.k0s(),e.j41(5,"select",4),e.bIt("change",function(R){return t.handleSelectCategory(R)}),e.Z7z(6,f,3,2,"option",5,e.fX1),e.k0s()()(),e.j41(8,"h6",6),e.EFF(9,"Category Item"),e.k0s(),e.j41(10,"div",7)(11,"div",8),e.bIt("click",function(){return t.RemoveAll()}),e.DNE(12,b,4,0),e.k0s(),e.nrm(13,"hr"),e.Z7z(14,I,6,1,"div",9,e.fX1),e.j41(16,"div")(17,"div",10),e.DNE(18,E,1,1,"input",11)(19,A,6,1,"div",12),e.k0s(),e.j41(20,"button",13),e.bIt("click",function(){return t.AddCategoryItem()}),e.j41(21,"span",14),e.EFF(22,"add"),e.k0s(),e.EFF(23," Add a new item Category "),e.k0s()(),e.j41(24,"div",15)(25,"button",16),e.bIt("click",function(){return t.closeDialog()}),e.EFF(26,"Cancel"),e.k0s(),e.j41(27,"button",17),e.bIt("click",function(){return t.Save()}),e.EFF(28,"Save"),e.k0s()()()()),2&o&&(e.R7$(6),e.Dyx(t.listCategory),e.R7$(6),e.vxM(t.listCategoryItem.length>0?12:-1),e.R7$(2),e.Dyx(t.listCategoryItem),e.R7$(4),e.vxM(t.isAddSvcMode?18:-1),e.R7$(),e.vxM(t.categoryNameItem?19:-1),e.R7$(8),e.Y8G("disabled",0===t.listCategoryItem.length&&0===t.listItemCategoryRemove.length&&""==t.categoryId)("ngClass",e.l_i(5,v,(t.listCategoryItem.length>0||t.listItemCategoryRemove.length>0)&&t.categoryId,0==t.listCategoryItem.length||0==t.listItemCategoryRemove.length||""==t.categoryId)))},dependencies:[u.G,h.YU,d.xH,d.y7,d.me,d.BC,d.vS,C.j]})),i})()}}]);