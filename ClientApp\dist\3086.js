"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3086],{3086:(k,l,s)=>{s.r(l),s.d(l,{ShareLinkComponent:()=>b});var a=s(9842),p=s(4006),u=s(1470),C=s(6146),c=s(177),h=s(5312),t=s(4438);class m{constructor(o,r){this._document=r;const e=this._textarea=this._document.createElement("textarea"),i=e.style;i.position="fixed",i.top=i.opacity="0",i.left="-999em",e.setAttribute("aria-hidden","true"),e.value=o,e.readOnly=!0,(this._document.fullscreenElement||this._document.body).appendChild(e)}copy(){const o=this._textarea;let r=!1;try{if(o){const e=this._document.activeElement;o.select(),o.setSelectionRange(0,o.value.length),r=this._document.execCommand("copy"),e&&e.focus()}}catch{}return r}destroy(){const o=this._textarea;o&&(o.remove(),this._textarea=void 0)}}let _=(()=>{var n;class o{constructor(e){this._document=e}copy(e){const i=this.beginCopy(e),d=i.copy();return i.destroy(),d}beginCopy(e){return new m(e,this._document)}}return(n=o).\u0275fac=function(e){return new(e||n)(t.KVO(c.qQ))},n.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"}),o})();function f(n,o){if(1&n&&(t.j41(0,"div",3),t.nrm(1,"img",7),t.j41(2,"div",8)(3,"span"),t.EFF(4),t.k0s()()()),2&n){const r=t.XpG();t.R7$(4),t.JRh(r.linkShare)}}function y(n,o){1&n&&(t.j41(0,"div",3)(1,"div",9)(2,"span"),t.EFF(3,"Copied!"),t.k0s()()())}function g(n,o){if(1&n){const r=t.RV6();t.j41(0,"div",4)(1,"span",10),t.bIt("click",function(){t.eBV(r);const i=t.XpG();return t.Njj(i.Copy())}),t.EFF(2,"Copy to Clipboard"),t.k0s()()}}let b=(()=>{var n;class o{static getComponent(){return o}constructor(e,i,d){(0,a.A)(this,"clipboard",void 0),(0,a.A)(this,"dialogRef",void 0),(0,a.A)(this,"data",void 0),(0,a.A)(this,"isCopy",!1),(0,a.A)(this,"linkShare",h.c.HOST+"/link/"),this.clipboard=e,this.dialogRef=i,this.data=d}ngOnInit(){this.data&&(this.linkShare=this.linkShare+this.data)}Copy(){this.isCopy=!0,this.clipboard.copy(this.linkShare)}closeDialog(){this.dialogRef.close()}}return n=o,(0,a.A)(o,"\u0275fac",function(e){return new(e||n)(t.rXU(_),t.rXU(p.CP),t.rXU(p.Vh))}),(0,a.A)(o,"\u0275cmp",t.VBU({type:n,selectors:[["app-share-link"]],standalone:!0,features:[t.aNF],decls:12,vars:2,consts:[[3,"onClose"],[1,"flex","flex-col","mb-1"],[1,"text-center"],[1,"bg-blue-200","rounded-sm","flex","items-center"],[1,"w-full","text-center"],[1,"w-full","text-center","mt-4"],["type","button",1,"focus:outline-none","text-white","bg-green-700","hover:bg-green-800","focus:ring-4","focus:ring-green-300","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["src","../../../../../assets//img/link.png",1,"w-6","ml-3"],[1,"p-3","text-start","w-full"],[1,"pt-3","pb-3","text-center","w-full"],[1,"text-blue-400","cursor-pointer",3,"click"]],template:function(e,i){1&e&&(t.j41(0,"app-innobook-modal-wrapper",0),t.bIt("onClose",function(){return i.closeDialog()}),t.j41(1,"div",1)(2,"h4",2),t.EFF(3,"Here's your link"),t.k0s(),t.j41(4,"span",2),t.EFF(5," You can copy, paste and share this link with your client. "),t.k0s()(),t.DNE(6,f,5,1,"div",3)(7,y,4,0,"div",3)(8,g,3,0,"div",4),t.j41(9,"div",5)(10,"button",6),t.bIt("click",function(){return i.closeDialog()}),t.EFF(11," Done"),t.k0s()()()),2&e&&(t.R7$(6),t.vxM(i.isCopy?7:6),t.R7$(2),t.vxM(i.isCopy?-1:8))},dependencies:[C.G,c.MD,u.j]})),o})()}}]);