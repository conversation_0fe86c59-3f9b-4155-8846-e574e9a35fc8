"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7371],{7656:(O,g,i)=>{i.d(g,{V:()=>T});var o=i(9842),e=i(4438),f=i(6146),M=i(9417);const b=["*"];function t(C,m){if(1&C){const c=e.RV6();e.j41(0,"input",5),e.bIt("change",function(r){e.eBV(c);const n=e.XpG();return e.Njj(n.handleChange(r))}),e.k0s()}if(2&C){const c=e.XpG();e.Y8G("checked",c.checked)("formControl",c.formControl)}}function p(C,m){if(1&C){const c=e.RV6();e.j41(0,"input",6),e.bIt("change",function(r){e.eBV(c);const n=e.XpG();return e.Njj(n.handleChange(r))}),e.k0s()}if(2&C){const c=e.XpG();e.Y8G("checked",c.checked)}}let T=(()=>{var C;class m{constructor(){(0,o.A)(this,"checked",void 0),(0,o.A)(this,"onChange",new e.bkB),(0,o.A)(this,"formControl",void 0),(0,o.A)(this,"errorMessages",void 0)}registerOnChange(a){}registerOnTouched(a){}setDisabledState(a){}writeValue(a){}handleChange(a){this.onChange.emit(a?.target?.checked??!1)}}return C=m,(0,o.A)(m,"\u0275fac",function(a){return new(a||C)}),(0,o.A)(m,"\u0275cmp",e.VBU({type:C,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:M.kq,useExisting:(0,e.Rfq)(()=>C),multi:!0}]),e.aNF],ngContentSelectors:b,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(a,r){1&a&&(e.NAR(),e.j41(0,"div",0)(1,"label",1),e.DNE(2,t,1,2,"input",2)(3,p,1,1,"input",3),e.j41(4,"div",4),e.SdG(5),e.k0s()()()),2&a&&(e.R7$(2),e.vxM(r.formControl?2:3))},dependencies:[f.G,M.Zm,M.BC,M.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),m})()},7086:(O,g,i)=>{i.d(g,{C:()=>c});var o=i(9842),e=i(4438),f=i(9417),M=i(6146),b=i(8192),t=i(177);const p=a=>({"resize-none":a});function T(a,r){if(1&a&&(e.j41(0,"label",3),e.EFF(1),e.k0s()),2&a){const n=e.XpG();e.AVh("required",n.isRequired),e.R7$(),e.JRh(n.label)}}function C(a,r){if(1&a){const n=e.RV6();e.j41(0,"textarea",4),e.bIt("keyup",function(u){e.eBV(n);const A=e.XpG();return e.Njj(A.handleChange(u))}),e.k0s(),e.nrm(1,"app-inno-error-message",5)}if(2&a){const n=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",n.class,""),e.Y8G("placeholder",n.placeholder)("formControl",n.formControl)("ngClass",e.eq3(7,p,!n.isAbleResize)),e.R7$(),e.Y8G("message",n.getErrorMessage())}}function m(a,r){if(1&a){const n=e.RV6();e.j41(0,"textarea",6),e.bIt("keyup",function(u){e.eBV(n);const A=e.XpG();return e.Njj(A.handleChange(u))}),e.k0s()}if(2&a){const n=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",n.class,""),e.Y8G("value",n.value)("placeholder",n.placeholder)("ngClass",e.eq3(6,p,!n.isAbleResize))}}let c=(()=>{var a;class r{constructor(){(0,o.A)(this,"class",""),(0,o.A)(this,"isAbleResize",!1),(0,o.A)(this,"isRequired",void 0),(0,o.A)(this,"label",""),(0,o.A)(this,"placeholder",""),(0,o.A)(this,"value",""),(0,o.A)(this,"onChange",new e.bkB),(0,o.A)(this,"formControl",void 0),(0,o.A)(this,"errorMessages",void 0)}registerOnChange(_){}registerOnTouched(_){}setDisabledState(_){}writeValue(_){}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const _ in this.formControl.errors)if(this.errorMessages[_])return this.errorMessages[_];return""}handleChange(_){this.onChange.emit(_?.target?.value??"")}}return a=r,(0,o.A)(r,"\u0275fac",function(_){return new(_||a)}),(0,o.A)(r,"\u0275cmp",e.VBU({type:a,selectors:[["app-inno-form-textarea"]],inputs:{class:"class",isAbleResize:"isAbleResize",isRequired:"isRequired",label:"label",placeholder:"placeholder",value:"value",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:f.kq,useExisting:(0,e.Rfq)(()=>a),multi:!0}]),e.aNF],decls:4,vars:4,consts:[[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],[3,"value","placeholder","class","ngClass"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[3,"keyup","placeholder","formControl","ngClass"],[3,"message"],[3,"keyup","value","placeholder","ngClass"]],template:function(_,u){1&_&&(e.j41(0,"div",0),e.DNE(1,T,2,3,"label",1)(2,C,2,9)(3,m,1,8,"textarea",2),e.k0s()),2&_&&(e.AVh("error",u.hasError()),e.R7$(),e.vxM(u.label?1:-1),e.R7$(),e.vxM(u.formControl?2:3))},dependencies:[M.G,t.YU,f.me,f.BC,f.l_,b.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}']})),r})()},344:(O,g,i)=>{i.d(g,{k:()=>c});var o=i(9842),e=i(4438),f=i(6146),M=i(177),b=i(5236);function t(a,r){if(1&a&&e.eu8(0,1),2&a){const n=e.XpG();e.Y8G("ngTemplateOutlet",n.leftAction)}}function p(a,r){if(1&a&&e.eu8(0,1),2&a){const n=e.XpG();e.Y8G("ngTemplateOutlet",n.customCancelButton)}}function T(a,r){if(1&a){const n=e.RV6();e.j41(0,"button",5),e.bIt("click",function(){e.eBV(n);const u=e.XpG();return e.Njj(u.handleCancel())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}if(2&a){const n=e.XpG();e.HbH(n.classNameCancelButton),e.R7$(),e.SpI(" ",e.bMT(2,3,n.textCancel||"BUTTON.Cancel")," ")}}function C(a,r){if(1&a&&e.eu8(0,1),2&a){const n=e.XpG();e.Y8G("ngTemplateOutlet",n.customSubmitButton)}}function m(a,r){if(1&a){const n=e.RV6();e.j41(0,"button",6),e.bIt("click",function(){e.eBV(n);const u=e.XpG();return e.Njj(u.handleSubmit())}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}if(2&a){const n=e.XpG();e.HbH(n.classNameSubmitButton),e.Y8G("disabled",n.isDisableSubmit),e.R7$(),e.SpI(" ",e.bMT(2,4,n.textSubmit||"BUTTON.Save")," ")}}let c=(()=>{var a;class r{constructor(){(0,o.A)(this,"classNameSubmitButton",""),(0,o.A)(this,"classNameCancelButton",""),(0,o.A)(this,"leftAction",void 0),(0,o.A)(this,"customSubmitButton",void 0),(0,o.A)(this,"customCancelButton",void 0),(0,o.A)(this,"textSubmit",void 0),(0,o.A)(this,"textCancel",void 0),(0,o.A)(this,"idSubmit",""),(0,o.A)(this,"idCancel",""),(0,o.A)(this,"isDisableSubmit",!1),(0,o.A)(this,"onSubmit",new e.bkB),(0,o.A)(this,"onCancel",new e.bkB)}handleSubmit(){this.onSubmit.emit()}handleCancel(){this.onCancel.emit()}}return a=r,(0,o.A)(r,"\u0275fac",function(_){return new(_||a)}),(0,o.A)(r,"\u0275cmp",e.VBU({type:a,selectors:[["app-inno-modal-footer"]],inputs:{classNameSubmitButton:"classNameSubmitButton",classNameCancelButton:"classNameCancelButton",leftAction:"leftAction",customSubmitButton:"customSubmitButton",customCancelButton:"customCancelButton",textSubmit:"textSubmit",textCancel:"textCancel",idSubmit:"idSubmit",idCancel:"idCancel",isDisableSubmit:"isDisableSubmit"},outputs:{onSubmit:"onSubmit",onCancel:"onCancel"},standalone:!0,features:[e.aNF],decls:7,vars:3,consts:[[1,"w-full","p-[16px]","border-t","border-border-primary","flex","items-center","gap-[12px]"],[3,"ngTemplateOutlet"],[1,"ml-auto","flex","gap-[12px]","items-center"],["type","button",1,"button-outline","button-size-md",3,"class"],["type","button",1,"button-primary","button-size-md",3,"class","disabled"],["type","button",1,"button-outline","button-size-md",3,"click"],["type","button",1,"button-primary","button-size-md",3,"click","disabled"]],template:function(_,u){1&_&&(e.j41(0,"div",0),e.DNE(1,t,1,1,"ng-container",1),e.j41(2,"div",2),e.DNE(3,p,1,1,"ng-container",1)(4,T,3,5,"button",3)(5,C,1,1,"ng-container",1)(6,m,3,6,"button",4),e.k0s()()),2&_&&(e.R7$(),e.vxM(u.leftAction?1:-1),e.R7$(2),e.vxM(u.customCancelButton?3:u.onCancel.observers.length?4:-1),e.R7$(2),e.vxM(u.customSubmitButton?5:u.onSubmit.observers.length?6:-1))},dependencies:[f.G,M.T3,b.D9],styles:[".zipplexActionModal[_ngcontent-%COMP%]{width:100%;margin-top:16px}.leftAction[_ngcontent-%COMP%]:empty, .rightAction[_ngcontent-%COMP%]:empty{display:none!important}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%], .zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .leftAction[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%]{flex:1;display:flex;gap:8px}.zipplexActionModal[_ngcontent-%COMP%]   .listButton[_ngcontent-%COMP%]   .rightAction[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]{width:100%}"]})),r})()},4978:(O,g,i)=>{i.d(g,{I:()=>C});var o=i(9842),e=i(4438),f=i(6146),M=i(5236);const b=["*",[["","footer",""]]],t=["*","[footer]"];function p(m,c){if(1&m){const a=e.RV6();e.j41(0,"button",7),e.bIt("click",function(){e.eBV(a);const n=e.XpG(2);return e.Njj(n.handleClose())}),e.nrm(1,"img",8),e.k0s()}}function T(m,c){if(1&m&&(e.j41(0,"div",4)(1,"p",5),e.EFF(2),e.nI1(3,"translate"),e.k0s()(),e.DNE(4,p,2,0,"button",6)),2&m){const a=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,a.title)),e.R7$(2),e.vxM(a.onClose.observers.length?4:-1)}}let C=(()=>{var m;class c{constructor(){(0,o.A)(this,"title",void 0),(0,o.A)(this,"onClose",new e.bkB)}handleClose(){this.onClose.emit()}}return m=c,(0,o.A)(c,"\u0275fac",function(r){return new(r||m)}),(0,o.A)(c,"\u0275cmp",e.VBU({type:m,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[e.aNF],ngContentSelectors:t,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(r,n){1&r&&(e.NAR(b),e.j41(0,"div",0)(1,"div",1),e.DNE(2,T,5,4),e.k0s(),e.j41(3,"div",2),e.SdG(4),e.k0s(),e.j41(5,"div",3),e.SdG(6,1),e.k0s()()),2&r&&(e.R7$(2),e.vxM(n.title?2:-1))},dependencies:[f.G,M.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),c})()},2508:(O,g,i)=>{i.r(g),i.d(g,{DialogModifyItemServiceComponent:()=>y});var o=i(9842),e=i(7136),f=i(9079),M=i(822),b=i(5272),t=i(4438),p=i(9417),T=i(4006),C=i(1328),m=i(7086),c=i(344),a=i(4978),r=i(1556),n=i(5402),_=i(6146),u=i(3492),A=i(4477),D=i(5236);const R=E=>({required:E});function P(E,I){if(1&E){const S=t.RV6();t.j41(0,"app-input-tax",16),t.bIt("onSelectedTax",function(l){const h=t.eBV(S).$index,s=t.XpG();return t.Njj(s.handleSelectedTax(l,h))})("onDelete",function(){const l=t.eBV(S).$index,h=t.XpG();return t.Njj(h.Delete(l))}),t.k0s()}2&E&&t.Y8G("tax",I.$implicit)("isHideDeleteButton",!0)}let y=(()=>{var E;class I{constructor(d,l){(0,o.A)(this,"dialogRef",void 0),(0,o.A)(this,"data",void 0),(0,o.A)(this,"enumService",n.Q.Service),(0,o.A)(this,"itemForm",void 0),(0,o.A)(this,"titlePopup",""),(0,o.A)(this,"taxes",[{selected:!1,taxeNumber:null,name:"",amount:null}]),(0,o.A)(this,"layoutUtilsService",(0,t.WQX)(r.Z)),(0,o.A)(this,"toastService",(0,t.WQX)(u.f)),(0,o.A)(this,"serviceService",(0,t.WQX)(b.N)),(0,o.A)(this,"itemService",(0,t.WQX)(M.b)),(0,o.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,o.A)(this,"translate",(0,t.WQX)(D.c$)),(0,o.A)(this,"companyTaxService",(0,t.WQX)(e.Y)),(0,o.A)(this,"listIndexTaxesSelected",[]),(0,o.A)(this,"formBuilder",(0,t.WQX)(p.ze)),(0,o.A)(this,"originData",""),this.dialogRef=d,this.data=l;const{mode:h,serviceInfo:s}=l;switch(h){case n.Q.Item:this.titlePopup=s?.id?"ITEMS_SERVICES.EditItem":"ITEMS_SERVICES.NEW_ITEM_FORM.Title",this.itemForm=this.formBuilder.group({name:[s?.itemName??"",p.k0.compose([p.k0.required])],description:[s?.description??""],rate:[s?.rate??"",[]],taxes:[[]]}),this.taxes=s?.taxes??[];break;case n.Q.Service:this.titlePopup=s?.id?"ITEMS_SERVICES.EditService":"ITEMS_SERVICES.CreateNewService",this.itemForm=this.formBuilder.group({name:[s?.serviceName??"",p.k0.compose([p.k0.required])],description:[s?.description??""],rate:[s?.rate??"",[]],taxes:[[]]}),this.taxes=s?.taxes??[]}this.originData=JSON.stringify({name:this.itemForm.get("name")?.value??"",description:this.itemForm.get("description")?.value??"",rate:this.itemForm.get("rate")?.value??"",taxes:this.itemForm.get("taxes")?.value??""})}ngOnInit(){this.GetAllCompanyTax()}GetAllCompanyTax(){this.companyTaxService.GetAllCompanyTax({Page:1,PageSize:50,Search:""}).pipe((0,f.pQ)(this.destroyRef)).subscribe({next:l=>{const h=l.data.map(s=>{const x=this.taxes.find(v=>v.companyTaxId===s.id);return{...s,companyTaxId:s.id,selected:!!x}});this.taxes=h,this.listIndexTaxesSelected=this.taxes.map((s,x)=>s.selected?x:-1).filter(s=>-1!==s)}})}handleClose(){this.dialogRef.close()}static getComponent(){return I}get f(){return this.itemForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(d=>{d.markAsTouched()})}Delete(d){this.taxes.splice(d,1);const l=this.listIndexTaxesSelected.indexOf(d);-1!==l&&this.listIndexTaxesSelected.splice(l,1)}addTax(){this.taxes.push({selected:!1,taxeNumber:null,name:"",amount:null})}handleSelectedTax(d,l){const h=()=>{const x=this.listIndexTaxesSelected.map(v=>this.taxes[v]);this.itemForm.get("taxes")?.setValue(x)},s=this.listIndexTaxesSelected.indexOf(l);if(-1!==s)return this.taxes[s].selected=!1,this.listIndexTaxesSelected.splice(s,1),void h();if(2===this.listIndexTaxesSelected.length){const x=this.listIndexTaxesSelected.shift();this.taxes[x].selected=!1}this.listIndexTaxesSelected.push(l),this.taxes.forEach((x,v)=>{x.selected=this.listIndexTaxesSelected.includes(v)}),h()}handleCancel(){JSON.stringify({name:this.itemForm.get("name")?.value??"",description:this.itemForm.get("description")?.value??"",rate:this.itemForm.get("rate")?.value??"",taxes:this.itemForm.get("taxes")?.value??""})!==this.originData?this.layoutUtilsService.alertConfirm({title:this.translate.instant("TOAST.DiscardChanges"),description:this.translate.instant("TOAST.DescriptionChanges")}).then(h=>{h&&this.dialogRef.close()}):this.dialogRef.close()}handleSubmit(){if(this.itemForm.invalid)return void this.markAllControlsAsTouched();const d=this.taxes.map(s=>s.name?.toLowerCase().trim()).filter(s=>s),l=d.filter((s,x)=>d.indexOf(s)!==x);if(l.length>0)return void this.toastService.showWarning(this.translate.instant("TOAST.Warning"),`${this.translate.instant("TOAST.DuplicateTax")} ${l.join(", ")}. ${this.translate.instant("TOAST.TaxName")}`);const h=this.taxes.filter(s=>!0===s.selected);switch(this.data.mode){case n.Q.Item:const s={itemName:this.f.name.value,description:this.f.description.value,rate:this.f.rate.value,taxes:h,id:this.data?.serviceInfo?.id};this.data?.serviceInfo?.id?this.itemService.Update(s).pipe((0,f.pQ)(this.destroyRef)).subscribe(v=>{v&&(this.toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.UpdateItem")),this.dialogRef.close({viewRefresh:n.Q.Item}))}):this.itemService.CreateItem(s).pipe((0,f.pQ)(this.destroyRef)).subscribe({next:v=>{v&&(this.toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this.dialogRef.close({viewRefresh:n.Q.Item}))}});break;case n.Q.Service:const x={serviceName:this.itemForm.get("name")?.value??"",description:this.itemForm.get("description")?.value??"",rate:this.itemForm.get("rate")?.value??"",taxes:h,id:this.data?.serviceInfo?.id};this.data?.serviceInfo?.id?this.serviceService.Update(x).pipe((0,f.pQ)(this.destroyRef)).subscribe(v=>{v&&(this.toastService.showSuccess(this.translate.instant("TOAST.Success"),this.translate.instant("TOAST.UpdateService")),this.dialogRef.close({viewRefresh:n.Q.Service}))}):(this.serviceService.CreateService(x).pipe((0,f.pQ)(this.destroyRef)).subscribe(v=>{v&&(this.toastService.showSuccess(this.translate.instant("TOAST.Success"),this.translate.instant("TOAST.CreateService")),this.dialogRef.close({viewRefresh:n.Q.Service}))}),0==this.data.isShowProject&&this.dialogRef.close(x))}}}return E=I,(0,o.A)(I,"\u0275fac",function(d){return new(d||E)(t.rXU(T.CP),t.rXU(T.Vh))}),(0,o.A)(I,"\u0275cmp",t.VBU({type:E,selectors:[["app-dialog-modify-item-service"]],standalone:!0,features:[t.Jv_([r.Z]),t.aNF],decls:28,vars:37,consts:[[3,"onClose","title"],[3,"formGroup"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","gap-[16px]"],[3,"label","placeholder","formControl","value","errorMessages"],[3,"label","formControl","value","placeholder"],["type","number",3,"label","placeholder","formControl","value"],[1,"w-full","mt-[16px]"],[1,"text-text-secondary","text-text-sm-semibold","!mb-[10px]"],[1,"w-full","flex","flex-col","gap-[6px]"],[3,"tax","isHideDeleteButton"],[1,"flex","justify-start","mt-[10px]"],[1,"button-link-primary",3,"click"],["src","../../../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],["footer",""],[3,"onCancel","onSubmit"],[3,"onSelectedTax","onDelete","tax","isHideDeleteButton"]],template:function(d,l){1&d&&(t.j41(0,"app-inno-modal-wrapper",0),t.bIt("onClose",function(){return l.handleClose()}),t.j41(1,"form",1)(2,"div",2)(3,"div",3),t.nrm(4,"app-inno-form-input",4),t.nI1(5,"translate"),t.nI1(6,"translate"),t.nI1(7,"translate"),t.nrm(8,"app-inno-form-textarea",5),t.nI1(9,"translate"),t.nI1(10,"translate"),t.nrm(11,"app-inno-form-input",6),t.nI1(12,"translate"),t.nI1(13,"translate"),t.k0s(),t.j41(14,"div",7)(15,"p",8),t.EFF(16),t.nI1(17,"translate"),t.k0s(),t.j41(18,"div",9),t.Z7z(19,P,1,2,"app-input-tax",10,t.fX1),t.k0s(),t.j41(21,"div",11)(22,"button",12),t.bIt("click",function(){return l.addTax()}),t.nrm(23,"img",13),t.EFF(24),t.nI1(25,"translate"),t.k0s()()()()(),t.j41(26,"div",14)(27,"app-inno-modal-footer",15),t.bIt("onCancel",function(){return l.handleCancel()})("onSubmit",function(){return l.handleSubmit()}),t.k0s()()()),2&d&&(t.Y8G("title",l.titlePopup),t.R7$(),t.Y8G("formGroup",l.itemForm),t.R7$(3),t.Y8G("label",t.bMT(5,17,"ITEMS_SERVICES.NEW_ITEM_FORM.Name"))("placeholder",t.bMT(6,19,"ITEMS_SERVICES.NEW_ITEM_FORM.NamePlaceholder"))("formControl",l.f.name)("value",l.f.name.value)("errorMessages",t.eq3(35,R,t.bMT(7,21,"ITEMS_SERVICES.NEW_ITEM_FORM.NameRequired"))),t.R7$(4),t.Y8G("label",t.bMT(9,23,"ITEMS_SERVICES.NEW_ITEM_FORM.Description"))("formControl",l.f.description)("value",l.f.description.value)("placeholder",t.bMT(10,25,"ITEMS_SERVICES.NEW_ITEM_FORM.DescriptionPlaceholder")),t.R7$(3),t.Y8G("label",t.bMT(12,27,"ITEMS_SERVICES.NEW_ITEM_FORM.Rate"))("placeholder",t.bMT(13,29,"ITEMS_SERVICES.NEW_ITEM_FORM.RatePlaceholder"))("formControl",l.f.rate)("value",l.f.rate.value),t.R7$(5),t.SpI(" ",t.bMT(17,31,"ITEMS_SERVICES.NEW_ITEM_FORM.Taxes"),""),t.R7$(3),t.Dyx(l.taxes),t.R7$(5),t.SpI(" ",t.bMT(25,33,"ITEMS_SERVICES.NEW_ITEM_FORM.AddTax")," "))},dependencies:[_.G,p.qT,p.BC,p.cb,p.l_,p.j4,D.D9,a.I,c.k,C.a,m.C,A.b],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),I})()}}]);