"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8026],{8026:(O,_,n)=>{n.r(_),n.d(_,{SendEstimateComponent:()=>A});var o=n(9842),v=n(1342),E=n(5644),e=n(4438),c=n(4006),h=n(344),f=n(4978),S=n(6473),C=n(6146),b=n(1110),g=n(3492),p=n(9079),y=n(274),a=n(9417),u=n(3719),I=n(177);function D(s,r){if(1&s){const m=e.RV6();e.j41(0,"div",16),e.EFF(1),e.j41(2,"div",17),e.bIt("click",function(t){const l=e.eBV(m).$index,d=e.XpG(2);return e.Njj(d.handleRemoveEmailClient(t,l))}),e.nrm(3,"img",18),e.k0s()()}if(2&s){const m=r.$implicit;e.R7$(),e.SpI(" ",m," ")}}function x(s,r){if(1&s&&(e.j41(0,"div",4)(1,"div",15),e.Z7z(2,D,4,1,"div",16,e.fX1),e.k0s()()),2&s){const m=e.XpG();e.R7$(2),e.Dyx(m.listEmailSelected)}}function M(s,r){1&s&&(e.j41(0,"mat-error",8),e.EFF(1,"Email is required"),e.k0s())}function P(s,r){1&s&&(e.j41(0,"mat-error",8),e.EFF(1,"Invalid email"),e.k0s())}let A=(()=>{var s;class r{static getComponent(){return r}constructor(i,t){(0,o.A)(this,"dialogRef",void 0),(0,o.A)(this,"data",void 0),(0,o.A)(this,"sendInvoiceForm",void 0),(0,o.A)(this,"listEmailSelected",[]),(0,o.A)(this,"invoice",null),(0,o.A)(this,"_storeService",(0,e.WQX)(b.n)),(0,o.A)(this,"_toastService",(0,e.WQX)(g.f)),(0,o.A)(this,"_invoiceService",(0,e.WQX)(E.p)),(0,o.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,o.A)(this,"spinnerService",(0,e.WQX)(v.D)),(0,o.A)(this,"formBuilder",(0,e.WQX)(a.ze)),this.dialogRef=i,this.data=t,this.sendInvoiceForm=this.formBuilder.group({email:["",a.k0.compose([a.k0.required,a.k0.email])]})}get f(){return this.sendInvoiceForm.controls}ngOnInit(){this.invoice=this.data??{}}handleCancel(){this.dialogRef.close()}get listSelectedIdClient(){return this.listEmailSelected.map(i=>i.id)}get businessInfo(){const i=this._storeService.get_UserBusiness();return{businessName:i?.company?.businessName??"",businessPhoneNumber:i?.company?.phone??"",businessAddress:(0,S.Aw)({addressLine1:i?.company?.adress??"",addressLine2:i?.company?.adress2??"",stateProvince:i?.company?.province??"",postalCode:i?.company?.postalCode??"",country:i?.company?.country??""})}}handleSelectClient(i){const t=i?.metadata?.client;!t||this.listEmailSelected.some(d=>d.id===t.id)||this.listEmailSelected.push(t)}handleRemoveEmailClient(i,t){i.stopPropagation(),this.listEmailSelected.splice(t,1)}handleShare(){this._toastService.showInfo("The feature is in development."," ")}formatDate(i){return new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}onSubmit(){this.listEmailSelected.push(this.f.email.value),this.f.email.setValue("")}handleSave(){this.listEmailSelected.length>0?(this.spinnerService.show(),this._invoiceService.CreatedInvoice(this.invoice).pipe((0,p.pQ)(this.destroyRef),(0,y.H)(i=>i?this._invoiceService.CreatedInvoiceSend({invoiceId:i.id,clientId:this.data.clientId,isEstimate:!0,ListEmail:this.listEmailSelected}).pipe((0,p.pQ)(this.destroyRef)):[])).subscribe(i=>{if(i){const t=this.listEmailSelected;let l={id:i.id,businessName:this._storeService.get_UserBusiness().businessName,invoiceNumber:i.invoiceNumber,payAmount:i.paidAmount,date:this.formatDate(i.invoiceDate),listEmail:t};this._invoiceService.SendMailInvoice(l).subscribe(),this.spinnerService.hide(),this.dialogRef.close(i),this._toastService.showSuccess("Send","Success")}},i=>{console.error("Error:",i)})):this._toastService.showInfo("Infor","Enter to confirm the email you want to send")}handleClose(){this.dialogRef.close()}}return s=r,(0,o.A)(r,"\u0275fac",function(i){return new(i||s)(e.rXU(c.CP),e.rXU(c.Vh))}),(0,o.A)(r,"\u0275cmp",e.VBU({type:s,selectors:[["app-send-estimate"]],standalone:!0,features:[e.aNF],decls:23,vars:13,consts:[["title","Send Estimate",3,"onClose"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"w-full","min-h-[46px]","cursor-pointer","rounded-md","border-2","border-border-primary","py-[6px]","px-[4px]","flex","justify-between","gap-[4px]"],[1,"mt-3",3,"ngSubmit","formGroup"],[1,"mb-6"],["type","email","id","_email","formControlName","email","placeholder","Email","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"matError"],[1,"bg-bg-brand-primary","rounded-md","p-[16px]","mt-[16px]"],[1,"text-text-primary","text-text-md-semibold","mb-[4px]"],[1,"text-text-secondary","text-text-sm-regular"],[1,"button-link-primary","mt-[16px]",3,"click"],["src","../../../../../../assets/img/icon/ic_link.svg","alt","Icon"],["textCancel","Back to Estimate","textSubmit","Send Email",3,"onCancel","onSubmit"],[1,"grow","flex","flex-wrap","gap-[4px]"],[1,"rounded-[100px]","text-text-secondary","text-text-sm-medium","border","border-border-primary","bg-bg-secondary","flex","gap-[4px]","items-center","py-[4px]","px-[8px]"],[1,"w-[16px]","shrink-0","h-[16px]","hover:bg-bg-danger-secondary","flex","justify-center","items-center","rounded-xs",3,"click"],["src","../../../../../../assets/img/icon/ic_remove.svg","alt","Icon",1,"w-[16px]"]],template:function(i,t){1&i&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return t.handleClose()}),e.j41(1,"div",1)(2,"div",2)(3,"label",3),e.EFF(4,"To Email"),e.k0s(),e.DNE(5,x,4,0,"div",4),e.j41(6,"form",5),e.bIt("ngSubmit",function(){return t.onSubmit()}),e.j41(7,"div",6),e.nrm(8,"input",7),e.DNE(9,M,2,0,"mat-error",8)(10,P,2,0,"mat-error",8),e.k0s()()(),e.j41(11,"div",9)(12,"p",10),e.EFF(13),e.k0s(),e.j41(14,"p",11),e.EFF(15),e.nI1(16,"date"),e.nrm(17,"br"),e.EFF(18," Thank you! "),e.k0s()(),e.j41(19,"button",12),e.bIt("click",function(){return t.handleShare()}),e.nrm(20,"img",13),e.EFF(21," Share by the link "),e.k0s()(),e.j41(22,"app-inno-modal-footer",14),e.bIt("onCancel",function(){return t.handleCancel()})("onSubmit",function(){return t.handleSave()}),e.k0s()()),2&i&&(e.R7$(5),e.vxM(t.listEmailSelected.length>0?5:-1),e.R7$(),e.Y8G("formGroup",t.sendInvoiceForm),e.R7$(3),e.vxM((t.f.email.dirty||t.f.email.touched)&&t.f.email.hasError("required")?9:-1),e.R7$(),e.vxM((t.f.email.dirty||t.f.email.touched)&&t.f.email.hasError("email")?10:-1),e.R7$(3),e.Lme(" ",t.businessInfo.businessName," sent you an estimate (",null==t.invoice?null:t.invoice.invoiceNumber,") "),e.R7$(2),e.LHq(" ",t.businessInfo.businessName," has sent you an estimate (",null==t.invoice?null:t.invoice.invoiceNumber,") with an amount of $",null==t.invoice?null:t.invoice.totalAmount,", due on ",e.i5U(16,10,null==t.invoice?null:t.invoice.dueDate,"MMM d, yyyy"),". If you have any questions or require further details, please feel free to reach out. "))},dependencies:[C.G,a.qT,a.me,a.BC,a.cb,a.YS,a.j4,a.JD,I.vh,u.RG,u.TL,f.I,h.k]})),r})()}}]);