"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[945],{945:(j,c,l)=>{l.r(c),l.d(c,{StripeTestComponent:()=>f});var a=l(9842),e=l(4438),m=l(177),u=l(6146),b=l(7152),p=l(9417),h=l(1626),_=l(5312);function g(s,d){if(1&s){const i=e.RV6();e.j41(0,"div",6)(1,"div",38)(2,"input",39),e.mxI("ngModelChange",function(t){e.eBV(i);const r=e.XpG();return e.DH7(r.billingInterval,t)||(r.billingInterval=t),e.Njj(t)}),e.k0s(),e.j41(3,"label",40),e.<PERSON><PERSON>(4,"Monthly"),e.k0s()(),e.j41(5,"div",38)(6,"input",41),e.mxI("ngModelChange",function(t){e.eBV(i);const r=e.XpG();return e.DH7(r.billingInterval,t)||(r.billingInterval=t),e.Njj(t)}),e.k0s(),e.j41(7,"label",42),e.EFF(8,"Yearly"),e.k0s()()()}if(2&s){const i=e.XpG();e.R7$(2),e.R50("ngModel",i.billingInterval),e.R7$(4),e.R50("ngModel",i.billingInterval)}}function y(s,d){if(1&s&&(e.j41(0,"div",43)(1,"p")(2,"strong"),e.EFF(3,"Note:"),e.k0s(),e.EFF(4),e.k0s(),e.j41(5,"p"),e.EFF(6,"For testing, subscriptions will not actually charge your test card repeatedly."),e.k0s()()),2&s){const i=e.XpG();e.R7$(4),e.SpI(" Subscription payments will recur ","month"===i.billingInterval?"monthly":"yearly"," until cancelled.")}}function F(s,d){if(1&s&&(e.j41(0,"div",44)(1,"div",45)(2,"span",46),e.EFF(3),e.k0s(),e.j41(4,"span",47),e.EFF(5),e.nI1(6,"date"),e.k0s()(),e.j41(7,"pre",48),e.EFF(8),e.nI1(9,"json"),e.k0s()()),2&s){const i=d.$implicit;e.R7$(3),e.JRh(i.type),e.R7$(2),e.JRh(e.i5U(6,3,i.timestamp,"medium")),e.R7$(3),e.JRh(e.bMT(9,6,i.data))}}function E(s,d){1&s&&(e.j41(0,"div",49),e.EFF(1," No webhook events received yet "),e.k0s())}function v(s,d){if(1&s&&(e.j41(0,"div",50),e.EFF(1),e.k0s()),2&s){const i=e.XpG();e.R7$(),e.SpI(" ",i.error," ")}}function k(s,d){if(1&s&&(e.j41(0,"div",51),e.EFF(1),e.k0s()),2&s){const i=e.XpG();e.R7$(),e.SpI(" ",i.success," ")}}let f=(()=>{var s;class d{constructor(){(0,a.A)(this,"amount",55),(0,a.A)(this,"isLoading",!1),(0,a.A)(this,"error",""),(0,a.A)(this,"success",""),(0,a.A)(this,"webhookEvents",[]),(0,a.A)(this,"selectedEventType","checkout.session.completed"),(0,a.A)(this,"paymentType","one_time"),(0,a.A)(this,"billingInterval","month"),(0,a.A)(this,"stripeService",(0,e.WQX)(b.d)),(0,a.A)(this,"http",(0,e.WQX)(h.Qq))}ngOnInit(){const o=localStorage.getItem("stripe-webhook-events");o&&(this.webhookEvents=JSON.parse(o))}createPayment(){this.isLoading=!0,this.error="",this.success="",this.stripeService.createPaymentSession(this.amount,"USD","subscription"===this.paymentType?("month"===this.billingInterval?"Monthly":"Yearly")+" Subscription":"One-time Payment",this.paymentType,this.billingInterval).subscribe({next:t=>{this.isLoading=!1,t&&t.url?(this.success=("subscription"===this.paymentType?"Subscription":"Payment")+" session created! Redirecting to Stripe...",window.open(t.url,"_blank")):this.error=`Failed to create ${"subscription"===this.paymentType?"subscription":"payment"} session`},error:t=>{this.isLoading=!1,this.error=`Error: ${t.message||"Unknown error"}`}})}simulateWebhook(){this.isLoading=!0,this.error="",this.success="";let o="payment_intent",t=`pi_${Math.random().toString(36).substring(2,15)}`;this.selectedEventType.includes("checkout")?(o="checkout.session",t=`cs_${Math.random().toString(36).substring(2,15)}`):this.selectedEventType.includes("subscription")?(o="subscription",t=`sub_${Math.random().toString(36).substring(2,15)}`):this.selectedEventType.includes("invoice")&&(o="invoice",t=`in_${Math.random().toString(36).substring(2,15)}`);const r={id:`evt_${Math.random().toString(36).substring(2,15)}`,type:this.selectedEventType,created:Math.floor(Date.now()/1e3),data:{object:{id:t,object:o,amount:100*this.amount,currency:"usd",status:this.selectedEventType.includes("failed")?"failed":"succeeded",metadata:{companyId:"test_company",userId:"test_user"}}}};if("subscription"===o){const n=r.data.object;n.current_period_start=Math.floor(Date.now()/1e3),n.current_period_end=Math.floor(Date.now()/1e3)+("month"===this.billingInterval?2592e3:31536e3),n.plan={interval:this.billingInterval,amount:100*this.amount}}if("invoice"===o){const n=r.data.object;n.subscription=`sub_${Math.random().toString(36).substring(2,15)}`,n.total=100*this.amount,n.billing_reason="subscription_cycle"}this.http.post(`${_.c.HOST_API}/api/Stripe/Webhook`,r).subscribe({next:n=>{this.isLoading=!1,this.success=`Webhook event ${this.selectedEventType} simulated successfully`,this.webhookEvents.unshift({type:this.selectedEventType,timestamp:new Date,data:r}),localStorage.setItem("stripe-webhook-events",JSON.stringify(this.webhookEvents.slice(0,10)))},error:n=>{this.isLoading=!1,this.error=`Error: ${n.message||"Unknown error"}`,this.webhookEvents.unshift({type:this.selectedEventType,timestamp:new Date,data:r,error:n.message}),localStorage.setItem("stripe-webhook-events",JSON.stringify(this.webhookEvents.slice(0,10)))}})}}return s=d,(0,a.A)(d,"\u0275fac",function(o){return new(o||s)}),(0,a.A)(d,"\u0275cmp",e.VBU({type:s,selectors:[["app-stripe-test"]],standalone:!0,features:[e.aNF],decls:125,vars:12,consts:[[1,"container-full","mt-8"],[1,"border","border-border-primary","rounded-md","p-6"],[1,"text-text-primary","text-headline-lg-bold","mb-4"],[1,"mb-6"],[1,"text-text-primary","text-headline-sm-bold","mb-2"],[1,"mb-4"],[1,"flex","items-center","gap-4","mb-2"],["type","number","placeholder","Amount",1,"p-2","border","border-border-primary","rounded-md",3,"ngModelChange","ngModel"],[1,"p-2","border","border-border-primary","rounded-md",3,"ngModelChange","ngModel"],["value","one_time"],["value","subscription"],["class","flex items-center gap-4 mb-2",4,"ngIf"],[1,"mt-3"],[1,"button-size-md","button-primary",3,"click","disabled"],["class","p-3 bg-blue-50 text-blue-700 rounded-md text-sm",4,"ngIf"],[1,"border","border-border-primary","rounded-md","p-4","bg-gray-50","h-64","overflow-auto"],["class","mb-2 p-2 border-b border-border-primary",4,"ngFor","ngForOf"],["class","text-center text-text-secondary py-8",4,"ngIf"],[1,"flex","items-center","gap-4","mb-4"],["value","checkout.session.completed"],["value","payment_intent.succeeded"],["value","payment_intent.payment_failed"],["value","customer.subscription.created"],["value","customer.subscription.updated"],["value","invoice.payment_succeeded"],["class","p-4 bg-red-100 text-red-700 rounded-md mb-4",4,"ngIf"],["class","p-4 bg-green-100 text-green-700 rounded-md mb-4",4,"ngIf"],[1,"mt-6"],[1,"overflow-x-auto"],[1,"min-w-full","border","border-border-primary"],[1,"bg-gray-50"],[1,"p-2","border-b","border-border-primary","text-left"],[1,"p-2","border-b","border-border-primary"],[1,"p-2","border-b","border-border-primary","font-mono"],[1,"p-2","border-b","border-border-primary","text-green-600"],[1,"p-2","border-b","border-border-primary","text-red-600"],[1,"text-text-secondary","text-sm","mt-2"],["href","https://stripe.com/docs/testing","target","_blank",1,"text-blue-600","hover:underline"],[1,"flex","items-center"],["type","radio","id","monthly","name","billingInterval","value","month",1,"mr-2",3,"ngModelChange","ngModel"],["for","monthly"],["type","radio","id","yearly","name","billingInterval","value","year",1,"mr-2",3,"ngModelChange","ngModel"],["for","yearly"],[1,"p-3","bg-blue-50","text-blue-700","rounded-md","text-sm"],[1,"mb-2","p-2","border-b","border-border-primary"],[1,"flex","justify-between"],[1,"font-bold"],[1,"text-text-secondary","text-sm"],[1,"text-sm","mt-1","overflow-auto"],[1,"text-center","text-text-secondary","py-8"],[1,"p-4","bg-red-100","text-red-700","rounded-md","mb-4"],[1,"p-4","bg-green-100","text-green-700","rounded-md","mb-4"]],template:function(o,t){1&o&&(e.j41(0,"div",0)(1,"div",1)(2,"h1",2),e.EFF(3,"Stripe Webhook Test"),e.k0s(),e.j41(4,"div",3)(5,"h2",4),e.EFF(6,"Create Test Payment"),e.k0s(),e.j41(7,"div",5)(8,"div",6)(9,"input",7),e.mxI("ngModelChange",function(n){return e.DH7(t.amount,n)||(t.amount=n),n}),e.k0s(),e.j41(10,"select",8),e.mxI("ngModelChange",function(n){return e.DH7(t.paymentType,n)||(t.paymentType=n),n}),e.j41(11,"option",9),e.EFF(12,"One-time Payment"),e.k0s(),e.j41(13,"option",10),e.EFF(14,"Subscription"),e.k0s()()(),e.DNE(15,g,9,2,"div",11),e.j41(16,"div",12)(17,"button",13),e.bIt("click",function(){return t.createPayment()}),e.EFF(18),e.k0s()()(),e.DNE(19,y,7,1,"div",14),e.k0s(),e.j41(20,"div",3)(21,"h2",4),e.EFF(22,"Webhook Events"),e.k0s(),e.j41(23,"div",15),e.DNE(24,F,10,8,"div",16)(25,E,2,0,"div",17),e.k0s()(),e.j41(26,"div",3)(27,"h2",4),e.EFF(28,"Simulate Webhook Event"),e.k0s(),e.j41(29,"div",18)(30,"select",8),e.mxI("ngModelChange",function(n){return e.DH7(t.selectedEventType,n)||(t.selectedEventType=n),n}),e.j41(31,"option",19),e.EFF(32,"checkout.session.completed"),e.k0s(),e.j41(33,"option",20),e.EFF(34,"payment_intent.succeeded"),e.k0s(),e.j41(35,"option",21),e.EFF(36,"payment_intent.payment_failed"),e.k0s(),e.j41(37,"option",22),e.EFF(38,"customer.subscription.created"),e.k0s(),e.j41(39,"option",23),e.EFF(40,"customer.subscription.updated"),e.k0s(),e.j41(41,"option",24),e.EFF(42,"invoice.payment_succeeded"),e.k0s()(),e.j41(43,"button",13),e.bIt("click",function(){return t.simulateWebhook()}),e.EFF(44," Simulate Event "),e.k0s()()(),e.DNE(45,v,2,1,"div",25)(46,k,2,1,"div",26),e.j41(47,"div",27)(48,"h2",4),e.EFF(49,"Test Card Numbers"),e.k0s(),e.j41(50,"div",28)(51,"table",29)(52,"thead")(53,"tr",30)(54,"th",31),e.EFF(55,"Card Type"),e.k0s(),e.j41(56,"th",31),e.EFF(57,"Number"),e.k0s(),e.j41(58,"th",31),e.EFF(59,"CVC"),e.k0s(),e.j41(60,"th",31),e.EFF(61,"Expiry"),e.k0s(),e.j41(62,"th",31),e.EFF(63,"Result"),e.k0s()()(),e.j41(64,"tbody")(65,"tr")(66,"td",32),e.EFF(67,"Visa"),e.k0s(),e.j41(68,"td",33),e.EFF(69,"4242 4242 4242 4242"),e.k0s(),e.j41(70,"td",32),e.EFF(71,"Any 3 digits"),e.k0s(),e.j41(72,"td",32),e.EFF(73,"Any future date"),e.k0s(),e.j41(74,"td",34),e.EFF(75,"Success"),e.k0s()(),e.j41(76,"tr")(77,"td",32),e.EFF(78,"Visa"),e.k0s(),e.j41(79,"td",33),e.EFF(80,"4000 0025 0000 3155"),e.k0s(),e.j41(81,"td",32),e.EFF(82,"Any 3 digits"),e.k0s(),e.j41(83,"td",32),e.EFF(84,"Any future date"),e.k0s(),e.j41(85,"td",32),e.EFF(86,"Requires authentication"),e.k0s()(),e.j41(87,"tr")(88,"td",32),e.EFF(89,"Visa"),e.k0s(),e.j41(90,"td",33),e.EFF(91,"4000 0000 0000 0002"),e.k0s(),e.j41(92,"td",32),e.EFF(93,"Any 3 digits"),e.k0s(),e.j41(94,"td",32),e.EFF(95,"Any future date"),e.k0s(),e.j41(96,"td",35),e.EFF(97,"Declined"),e.k0s()(),e.j41(98,"tr")(99,"td",32),e.EFF(100,"Mastercard"),e.k0s(),e.j41(101,"td",33),e.EFF(102,"5555 5555 5555 4444"),e.k0s(),e.j41(103,"td",32),e.EFF(104,"Any 3 digits"),e.k0s(),e.j41(105,"td",32),e.EFF(106,"Any future date"),e.k0s(),e.j41(107,"td",34),e.EFF(108,"Success"),e.k0s()(),e.j41(109,"tr")(110,"td",32),e.EFF(111,"Link"),e.k0s(),e.j41(112,"td",33),e.EFF(113,"4242 4242 4242 4242"),e.k0s(),e.j41(114,"td",32),e.EFF(115,"Any 3 digits"),e.k0s(),e.j41(116,"td",32),e.EFF(117,"Any future date"),e.k0s(),e.j41(118,"td",32),e.EFF(119,"Use <NAME_EMAIL>"),e.k0s()()()()(),e.j41(120,"p",36),e.EFF(121," For more test cards, see the "),e.j41(122,"a",37),e.EFF(123,"Stripe testing documentation"),e.k0s(),e.EFF(124,". "),e.k0s()()()()),2&o&&(e.R7$(9),e.R50("ngModel",t.amount),e.R7$(),e.R50("ngModel",t.paymentType),e.R7$(5),e.Y8G("ngIf","subscription"===t.paymentType),e.R7$(2),e.Y8G("disabled",t.isLoading),e.R7$(),e.SpI(" ","subscription"===t.paymentType?"Create Subscription":"Create Payment"," "),e.R7$(),e.Y8G("ngIf","subscription"===t.paymentType),e.R7$(5),e.Y8G("ngForOf",t.webhookEvents),e.R7$(),e.Y8G("ngIf",0===t.webhookEvents.length),e.R7$(5),e.R50("ngModel",t.selectedEventType),e.R7$(13),e.Y8G("disabled",t.isLoading),e.R7$(2),e.Y8G("ngIf",t.error),e.R7$(),e.Y8G("ngIf",t.success))},dependencies:[m.MD,m.Sq,m.bT,m.TG,m.vh,u.G,p.xH,p.y7,p.me,p.Q0,p.wz,p.Fm,p.BC,p.vS,p.YN],encapsulation:2})),d})()}}]);