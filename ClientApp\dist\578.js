"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[578],{4978:(P,f,s)=>{s.d(f,{I:()=>b});var n=s(9842),o=s(4438),T=s(6146),j=s(5236);const e=["*",[["","footer",""]]],I=["*","[footer]"];function E(p,m){if(1&p){const h=o.RV6();o.j41(0,"button",7),o.bIt("click",function(){o.eBV(h);const C=o.XpG(2);return o.Njj(C.handleClose())}),o.nrm(1,"img",8),o.k0s()}}function v(p,m){if(1&p&&(o.j41(0,"div",4)(1,"p",5),o.<PERSON>(2),o.nI1(3,"translate"),o.k0s()(),o.DNE(4,E,2,0,"button",6)),2&p){const h=o.XpG();o.R7$(2),o.JRh(o.bMT(3,2,h.title)),o.R7$(2),o.vxM(h.onClose.observers.length?4:-1)}}let b=(()=>{var p;class m{constructor(){(0,n.A)(this,"title",void 0),(0,n.A)(this,"onClose",new o.bkB)}handleClose(){this.onClose.emit()}}return p=m,(0,n.A)(m,"\u0275fac",function(u){return new(u||p)}),(0,n.A)(m,"\u0275cmp",o.VBU({type:p,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[o.aNF],ngContentSelectors:I,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(u,C){1&u&&(o.NAR(e),o.j41(0,"div",0)(1,"div",1),o.DNE(2,v,5,4),o.k0s(),o.j41(3,"div",2),o.SdG(4),o.k0s(),o.j41(5,"div",3),o.SdG(6,1),o.k0s()()),2&u&&(o.R7$(2),o.vxM(C.title?2:-1))},dependencies:[T.G,j.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),m})()},578:(P,f,s)=>{s.r(f),s.d(f,{DialogAddTimeComponent:()=>G});var n=s(9842),o=s(4978),T=s(4262),j=s(4805),e=s(4438),I=s(9115),E=s(1110),v=s(4412),b=s(9079),p=s(6146),m=s(3924),h=s(4088),u=s(3719),C=s(4006),N=s(6508),D=s.n(N),O=s(8194),A=s(177),g=s(9417),x=s(5236);const M=()=>({standalone:!0}),R=a=>({"text-text-primary":a});function k(a,_){if(1&a&&e.EFF(0),2&a){const c=e.XpG(2);e.Lme(" ",c.clientName," - ",c.projectName," ")}}function S(a,_){if(1&a&&e.EFF(0),2&a){let c;const t=e.XpG(2);e.Lme(" ",null==t.previewWorkingInfo||null==t.previewWorkingInfo.metadata||null==t.previewWorkingInfo.metadata.objectClient?null:t.previewWorkingInfo.metadata.objectClient.clientName," - ",null!==(c=null==t.previewWorkingInfo?null:t.previewWorkingInfo.label)&&void 0!==c?c:"What are you working on?"," ")}}function B(a,_){if(1&a&&(e.j41(0,"button",21),e.DNE(1,k,1,2)(2,S,1,2),e.k0s()),2&a){const c=e.XpG();e.Y8G("ngClass",e.eq3(2,R,c.previewWorkingInfo||c.clientName)),e.R7$(),e.vxM(c.clientName?1:2)}}function y(a,_){if(1&a&&(e.j41(0,"button",22),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&a){const c=e.XpG();e.Y8G("ngClass",e.eq3(4,R,""!=c.servicesName)),e.R7$(),e.SpI(" ",""==c.servicesName?e.bMT(2,2,"TIMETRACKING.ChooseService"):c.servicesName," ")}}function W(a,_){1&a&&(e.j41(0,"mat-error",9),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&a&&(e.R7$(),e.JRh(e.bMT(2,1,"TIMETRACKING.ProjectRequired")))}function U(a,_){1&a&&(e.j41(0,"mat-error",9),e.EFF(1),e.nI1(2,"translate"),e.k0s()),2&a&&(e.R7$(),e.JRh(e.bMT(2,1,"TIMETRACKING.TimeRequired")))}function K(a,_){1&a&&(e.j41(0,"div",16)(1,"span",23),e.EFF(2,"Billed"),e.k0s()())}function w(a,_){if(1&a){const c=e.RV6();e.j41(0,"div",17)(1,"input",24),e.mxI("ngModelChange",function(i){e.eBV(c);const r=e.XpG();return e.DH7(r.checked,i)||(r.checked=i),e.Njj(i)}),e.k0s(),e.j41(2,"label",25),e.EFF(3),e.nI1(4,"translate"),e.k0s()()}if(2&a){const c=e.XpG();e.R7$(),e.R50("ngModel",c.checked),e.Y8G("ngModelOptions",e.lJ4(5,M)),e.R7$(2),e.JRh(e.bMT(4,3,"TIMETRACKING.Billable"))}}let G=(()=>{var a;class _{static getComponent(){return _}constructor(t,i){(0,n.A)(this,"dialogRef",void 0),(0,n.A)(this,"data",void 0),(0,n.A)(this,"_id",""),(0,n.A)(this,"memberId",void 0),(0,n.A)(this,"isCloseMenu",!1),(0,n.A)(this,"checked",!0),(0,n.A)(this,"objectClient",void 0),(0,n.A)(this,"objectProject",void 0),(0,n.A)(this,"objectServices",void 0),(0,n.A)(this,"searchServices",""),(0,n.A)(this,"clientName",""),(0,n.A)(this,"projectName",""),(0,n.A)(this,"servicesName",""),(0,n.A)(this,"nameService",void 0),(0,n.A)(this,"projectId",void 0),(0,n.A)(this,"timeTrackingId",void 0),(0,n.A)(this,"serviceId",""),(0,n.A)(this,"previewWorkingInfo",void 0),(0,n.A)(this,"_subscriptions",[]),(0,n.A)(this,"localMoment",D().tz(new Date,D().tz.guess())),(0,n.A)(this,"searchSubject",new v.t("")),(0,n.A)(this,"searchSubjectName",new v.t("")),(0,n.A)(this,"search",""),(0,n.A)(this,"timeEnd",void 0),(0,n.A)(this,"description",void 0),(0,n.A)(this,"searchMember",""),(0,n.A)(this,"name",void 0),(0,n.A)(this,"isrequiredProject",!1),(0,n.A)(this,"isrequiredTime",!1),(0,n.A)(this,"selectedDate",new Date),(0,n.A)(this,"project",void 0),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"changeDetectorRefs",(0,e.WQX)(e.gRc)),(0,n.A)(this,"_storeService",(0,e.WQX)(E.n)),(0,n.A)(this,"_timeTrackingService",(0,e.WQX)(j.y)),this.dialogRef=t,this.data=i}closeDialog(){this.dialogRef.close()}getNowUTC(){const t=new Date;return new Date(t.getTime()+6e4*t.getTimezoneOffset())}handleSearch(t){this.searchSubject.next(t)}handleSearchName(t){this.searchSubjectName.next(t)}onBlur(t){if(""!=this.timeEnd){let i=this.timeEnd;if(i=i.replace(/[^0-9:]/g,""),-1!==i.indexOf(":")){const l=i.split(":");2===l.length&&""===l[1]?i=l[0]+":00":l.length>2&&(i=l[0]+":"+(l[1]||"00"))}let[r,d]=i.split(":");d||(d="00"),r&&Number(r)>23?r="23":r&&Number(r)<10&&1===r.length&&(r="0"+r),d&&Number(d)>59?d="59":d&&Number(d)<10&&1===d.length&&(d="0"+d),this.timeEnd=`${r}:${d}`}}Update(){(!this.timeEnd||""==this.timeEnd)&&(this.isrequiredTime=!0),this.timeEnd&&this._timeTrackingService.UpdateTimeTracking({id:this.timeTrackingId,memberId:this.memberId,date:this.selectedDate,serviceId:this.serviceId,clientId:this.objectClient?.id,service:null,description:this.description,projectId:this.projectId,userId:this._id,startTime:null,endTime:this.timeEnd,billable:this.checked}).pipe((0,b.pQ)(this.destroyRef)).subscribe(i=>{i&&this.dialogRef.close(i)})}confirm(){this.objectProject||(this.isrequiredProject=!0),(!this.timeEnd||""==this.timeEnd)&&(this.isrequiredTime=!0),this.objectProject&&this.timeEnd&&this._timeTrackingService.CreateTimeTracking({memberId:null,date:this.selectedDate,serviceId:this.serviceId,clientId:this.objectClient?.id,service:null,description:this.description,projectId:this.objectProject.id,userId:this._id,startTime:null,endTime:this.timeEnd,billable:this.checked}).pipe((0,b.pQ)(this.destroyRef)).subscribe(i=>{i&&this.dialogRef.close(i)})}handleSelectServices(t){this.serviceId=t.value,this.servicesName=t.label}ngOnInit(){if(this.data?.new)this.memberId=this.data.data.memberId,this.timeTrackingId=this.data.data.id,this.selectedDate=this.data.data.date,this.timeEnd=this.data.data.endTime,this.description=this.data.data.description,this.objectClient=this.data.data?.client,this.projectId=this.data.data?.projectId,this.serviceId=this.data.data?.serviceId,this.clientName=this.data.data?.client?.clientName,this.projectName=this.data.data?.project?.projectName,this.checked=this.data.data.billable,this.servicesName=this.data.data.service?this.data.data.service.serviceName:"",this.project=this.clientName+"/"+this.projectName+"/"+this.servicesName,this.changeDetectorRefs.detectChanges();else{const t=this.localMoment.utcOffset()/60,i=new Date(this.data),r=i.getHours();i.setHours(r+t),this.selectedDate=i}this.name=this._storeService._InforUser.firstName+" "+this._storeService._InforUser.lastName,this._id=this._storeService._InforUser.id}ngOnDestroy(){this._subscriptions&&this._subscriptions.forEach(t=>t.unsubscribe())}cancel(){this.dialogRef.close()}stopPropagation(t){t.stopPropagation()}ItemSelected(t){this.objectProject=t.data,setTimeout(()=>{this.project=this.project+"/"+t.data.projectName,this.projectName=t.data.projectName,this.project=this.clientName+"/"+this.projectName,this.changeDetectorRefs.detectChanges()},100),this.projectId=this.objectProject.id}ItemSelectedMember(t){this.name=t.firstName+" "+t.lastName,this._id=t.id}ItemSelectedClient(t){this.objectClient=t,this.clientName=t.clientName,this.project=this.clientName}onSelectedDateChange(t){this.selectedDate=t}handleSelectProject(t){this.previewWorkingInfo=t,this.projectName=t.label,this.clientName=t.metadata.objectClient.clientName,this.projectId=t.value,this.objectClient=t.metadata.objectClient,this.servicesName="",this.serviceId=""}menuOpened(){this.isCloseMenu=!1}menuClosed(){this.isCloseMenu=!0}clearInput(){this.project="",this.objectClient=null,this.objectProject=null,this.objectServices=null}}return a=_,(0,n.A)(_,"\u0275fac",function(t){return new(t||a)(e.rXU(C.CP),e.rXU(C.Vh))}),(0,n.A)(_,"\u0275cmp",e.VBU({type:a,selectors:[["app-dialog-add-time"]],standalone:!0,features:[e.aNF],decls:32,vars:30,consts:[["templateTriggerSelectProject",""],["templateTriggerSelectService",""],[3,"onClose","title"],[1,"p-4"],[1,"w-full"],[1,"flex","flex-col","gap-2","w-full"],[1,"flex"],[3,"onSelect","templateTrigger","isOnlySelectProject","value"],[3,"onSelect","templateTrigger","value"],[1,"matError"],[1,"bg-white","border","border-gray-400","w-32","rounded-lg","flex","items-center","mb-2"],[1,"fw-semibold","mx-2","mt-1"],[1,"datepicker-single",3,"valueChange","value","showClearButton"],[1,"flex","gap-2","mb-2"],["type","text","placeholder","HH:MM",1,"bg-white","border","border-gray-400","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","blur","ngModel","ngModelOptions"],["id","message","rows","3",1,"block","p-2.5","w-full","text-sm","text-gray-900","bg-white","rounded-lg","border","border-gray-400","focus:ring-blue-500","focus:border-blue-500",3,"ngModelChange","ngModel","ngModelOptions","placeholder"],[1,"mt-2"],[1,"flex","items-center","text-start","mt-2"],[1,"flex","mt-2"],["type","button",1,"button-primary","button-size-md",3,"click"],["type","button",1,"button-outline","button-size-md","ml-3",3,"click"],[1,"px-[12px]","py-[5px]","text-headline-sm-semibold","text-text-placeholder-slight","cursor-pointer","border-2","rounded-md","border-border-primary","transition-all",3,"ngClass"],[1,"px-[12px]","py-[5px]","text-text-sm-bold","text-text-placeholder-slight","cursor-pointer","border-2","rounded-md","border-border-primary","transition-all",3,"ngClass"],[1,"text-text-sm-semibold","px-[8px]","py-[2px]","rounded-md","text-center","ms-2","text-sm","font-medium","text-text-success","bg-bg-secondary-subtle"],["checked","","id","checked-checkbox","type","checkbox","value","",1,"w-6","h-6","text-blue-600","bg-gray-100","border-gray-300","rounded","focus:ring-blue-500",3,"ngModelChange","ngModel","ngModelOptions"],["for","checked-checkbox",1,"ms-2","text-sm","font-medium","text-gray-900"]],template:function(t,i){if(1&t){const r=e.RV6();e.j41(0,"app-inno-modal-wrapper",2),e.bIt("onClose",function(){return e.eBV(r),e.Njj(i.closeDialog())}),e.j41(1,"div",3)(2,"form",4)(3,"div",5)(4,"div",6)(5,"app-inno-select-search-project",7),e.bIt("onSelect",function(l){return e.eBV(r),e.Njj(i.handleSelectProject(l))}),e.k0s(),e.DNE(6,B,3,4,"ng-template",null,0,e.C5r),e.k0s(),e.j41(8,"app-inno-select-search-service",8),e.bIt("onSelect",function(l){return e.eBV(r),e.Njj(i.handleSelectServices(l))}),e.k0s(),e.DNE(9,y,3,6,"ng-template",null,1,e.C5r)(11,W,3,3,"mat-error",9),e.j41(12,"div",10)(13,"span",11),e.EFF(14),e.nI1(15,"date"),e.k0s(),e.j41(16,"ejs-datepicker",12),e.mxI("valueChange",function(l){return e.eBV(r),e.DH7(i.selectedDate,l)||(i.selectedDate=l),e.Njj(l)}),e.bIt("valueChange",function(l){return e.eBV(r),e.Njj(i.onSelectedDateChange(l))}),e.k0s()()(),e.j41(17,"div",13)(18,"div")(19,"input",14),e.mxI("ngModelChange",function(l){return e.eBV(r),e.DH7(i.timeEnd,l)||(i.timeEnd=l),e.Njj(l)}),e.bIt("blur",function(l){return e.eBV(r),e.Njj(i.onBlur(l))}),e.k0s(),e.DNE(20,U,3,3,"mat-error",9),e.k0s()(),e.j41(21,"textarea",15),e.nI1(22,"translate"),e.mxI("ngModelChange",function(l){return e.eBV(r),e.DH7(i.description,l)||(i.description=l),e.Njj(l)}),e.k0s(),e.DNE(23,K,3,0,"div",16)(24,w,5,6,"div",17),e.j41(25,"div",18)(26,"button",19),e.bIt("click",function(){return e.eBV(r),e.Njj(i.Update())}),e.EFF(27),e.nI1(28,"translate"),e.k0s(),e.j41(29,"button",20),e.bIt("click",function(){return e.eBV(r),e.Njj(i.cancel())}),e.EFF(30),e.nI1(31,"translate"),e.k0s()()()()()}if(2&t){const r=e.sdS(7),d=e.sdS(10);e.Y8G("title","TIMETRACKING.EditEntry"),e.R7$(5),e.Y8G("templateTrigger",r)("isOnlySelectProject",!0)("value",null==i.previewWorkingInfo?null:i.previewWorkingInfo.value),e.R7$(3),e.Y8G("templateTrigger",d)("value",i.projectId),e.R7$(3),e.vxM(i.isrequiredProject?11:-1),e.R7$(3),e.JRh(e.i5U(15,19,i.selectedDate,i._storeService.getdateFormat())),e.R7$(2),e.R50("value",i.selectedDate),e.Y8G("showClearButton",!1),e.R7$(3),e.R50("ngModel",i.timeEnd),e.Y8G("ngModelOptions",e.lJ4(28,M)),e.R7$(),e.vxM(i.isrequiredTime?20:-1),e.R7$(),e.R50("ngModel",i.description),e.Y8G("ngModelOptions",e.lJ4(29,M))("placeholder",e.bMT(22,22,"TIMETRACKING.Write")),e.R7$(2),e.vxM(i.data.data.isBilled?23:24),e.R7$(4),e.SpI(" ",e.bMT(28,24,"BUTTON.Update")," "),e.R7$(3),e.SpI(" ",e.bMT(31,26,"BUTTON.Cancel")," ")}},dependencies:[m.tZ,m.I,p.G,A.YU,g.qT,g.me,g.Zm,g.BC,g.cb,g.vS,g.cV,A.vh,x.D9,I.Cn,h.$$,m.Y3,m.ap,u.RG,u.TL,O.H,o.I,T.B],styles:[".datepicker-single[_ngcontent-%COMP%]{margin-top:.12rem}.e-input-group[_ngcontent-%COMP%]{width:20px!important;border:0!important}.e-input-group[_ngcontent-%COMP%]   .e-input[_ngcontent-%COMP%]{line-height:35px}.e-input-group[_ngcontent-%COMP%]:before{display:none}.e-input-group[_ngcontent-%COMP%]:after{display:none}"]})),_})()}}]);