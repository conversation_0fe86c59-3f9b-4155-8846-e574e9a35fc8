using InnoBook.DTO.CoreModel;

namespace InnoBook.DTO.Expenses
{
    public class DTOItemExpense
    {
        public Guid Id { get; set; }
        public Guid ExpensesId { get; set; }
        public decimal Rate { get; set; }
        public decimal Qty { get; set; }
        public decimal Total { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
        public List<TaxItem>? Taxes { get; set; } = new();
    }
}
