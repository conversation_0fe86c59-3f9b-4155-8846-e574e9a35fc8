"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8222],{5644:(E,g,i)=>{i.d(g,{p:()=>S});var a=i(9842),A=i(1342),P=i(5312),C=i(1626),y=i(4438),f=i(6473);const o=P.c.HOST_API+"/api";let S=(()=>{var u;class e{constructor(){(0,a.A)(this,"http",(0,y.WQX)(C.Qq)),(0,a.A)(this,"spinnerService",(0,y.WQX)(A.D))}CreatedInvoice(t){return this.http.post(o+"/Invoices/CreatedInvoice",t)}CreatedInvoiceSend(t){return this.http.post(o+"/Invoices/CreatedInvoiceSend",t)}CountInvoiceByCompany(){return this.http.get(o+"/Invoices/CountInvoiceByCompany")}CountInvoiceByContractor(){return this.http.get(o+"/Invoices/CountInvoiceByContractor")}CountEstimate(){return this.http.get(o+"/Invoices/CountEstimate")}SendMailInvoice(t){return this.http.post(o+"/Invoices/SendMailInvoice",t)}GetAllEstimate(t){const s=(0,f.yU)(t);return this.http.get(o+"/Invoices/GetAllEstimate",{params:s})}GetAllInvoice(t){const s=(0,f.yU)(t);return this.http.get(o+"/Invoices/GetAllInvoice",{params:s})}GetAllInvoiceSendToMe(t){const s=(0,f.yU)(t);return this.http.get(o+"/Invoices/GetAllInvoiceSendToMe",{params:s})}GetAllEstimateSendToMe(t){return this.http.post(o+"/Invoices/GetAllEstimateSendToMe",t)}DeleteInvoice(t,s){return this.http.post(o+`/Invoices/DeleteInvoice?isActive=${s}`,t)}ChangePosition(t){return this.http.post(o+"/Invoices/ChangePosition?",t)}GetRevenueChart(t){return this.http.get(o+`/Invoices/GetRevenueChart?year=${t}`)}GraphicsChart(t){return this.http.get(o+`/Invoices/GraphicsChart?year=${t}`)}MarkAsPaid(t){return this.http.post(o+`/Invoices/MarkAsPaid?Id=${t}`,null)}MarkAsSent(t){return this.http.post(o+`/Invoices/MarkAsSent?Id=${t}`,null)}UpdateInvoice(t){return t?.itemInvoices.forEach(s=>{s.user=null}),this.http.post(o+"/Invoices/UpdateInvoice",t)}UpdateArchive(t,s){return this.http.post(o+`/Invoices/UpdateArchive?invoiceId=${t}&isArchive=${s}`,{})}ConvertToInvoice(t){return this.http.post(o+`/Invoices/ConvertToInvoice?invoiceId=${t}`,{})}GetInvoiceById(t){return this.http.get(o+`/Invoices/GetInvoiceById?InvoiceId=${t}`)}PrintInvoiceById(t,s){this.http.get(`${o}/Invoices/PrintInvoice?InvoiceId=${t}`,{responseType:"blob"}).subscribe({next:v=>{const F=new Blob([v],{type:"application/pdf"}),I=window.URL.createObjectURL(F),h=document.createElement("a");h.href=I,h.download=`Invoice_${s}.pdf`,document.body.appendChild(h),h.click(),this.spinnerService.hide(),window.URL.revokeObjectURL(I)},error:v=>{console.error("Error downloading the invoice:",v)}})}GetInvoiceByIdLink(t){return this.http.get(o+`/Invoices/GetInvoiceByIdLink?InvoiceId=${t}`)}CalculationInvoice(t){return this.http.get(o+`/Invoices/CalculationInvoice?status=${t}`)}CalculationInvoiceSendToMe(t){return this.http.get(o+`/Invoices/CalculationInvoiceSendToMe?status=${t}`)}CalculationEstimateSendToMe(t){return this.http.get(o+`/Invoices/CalculationEstimateSendToMe?status=${t}`)}CalculationEstimate(t){return this.http.get(o+`/Invoices/CalculationEstimate?status=${t}`)}uploadFile(t){const s=new FormData;return s.append("formFile",t),this.http.post(o+"/Invoices/UploadFile",s)}}return u=e,(0,a.A)(e,"\u0275fac",function(t){return new(t||u)}),(0,a.A)(e,"\u0275prov",y.jDH({token:u,factory:u.\u0275fac,providedIn:"root"})),e})()},8222:(E,g,i)=>{i.r(g),i.d(g,{AddPaymentComponent:()=>T});var a=i(9842),A=i(5644),P=i(8897),C=i(1342),y=i(3492),f=i(1110),o=i(1470),S=i(6146),u=i(177),e=i(4438),b=i(3719),t=i(9417),s=i(33),v=i(4006);const F=[{name:"Visa",value:"Visa"},{name:"MasterCard",value:"Card"},{name:"PayPal",value:"PayPal"},{name:"Stripe",value:"Stripe"}];var I=i(9079),h=i(6508),M=i.n(h);const x=(l,m)=>({"bg-green-700 hover:bg-green-800":l,"bg-gray-400":m});function D(l,m){if(1&l&&(e.j41(0,"option",15)(1,"div",28)(2,"label"),e.EFF(3),e.k0s(),e.j41(4,"span",29),e.EFF(5),e.k0s()()()),2&l){const c=m.$implicit;e.Y8G("value",c.id),e.R7$(3),e.SpI("",c.invoiceNumber," - "),e.R7$(2),e.JRh(c.client.clientName)}}function k(l,m){if(1&l){const c=e.RV6();e.j41(0,"div",4)(1,"label",25),e.EFF(2,"Select Invoices "),e.k0s(),e.j41(3,"select",26),e.bIt("change",function(n){e.eBV(c);const d=e.XpG();return e.Njj(d.handleSelectInvoice(n))}),e.j41(4,"option",27),e.EFF(5,"Choose a invoice"),e.k0s(),e.Z7z(6,D,6,3,"option",15,e.fX1),e.k0s()()}if(2&l){const c=e.XpG();e.R7$(6),e.Dyx(c.listInvoice)}}function _(l,m){1&l&&(e.j41(0,"mat-error",9),e.EFF(1,"Payment Amount is required"),e.k0s())}function R(l,m){1&l&&(e.j41(0,"mat-error",9),e.EFF(1,"Payment Date is required"),e.k0s())}function $(l,m){if(1&l&&(e.j41(0,"option",15)(1,"label"),e.EFF(2),e.k0s()()),2&l){const c=m.$implicit;e.Y8G("value",c.value),e.R7$(2),e.JRh(c.name)}}let T=(()=>{var l;class m{static getComponent(){return m}constructor(r,n){(0,a.A)(this,"dialogRef",void 0),(0,a.A)(this,"data",void 0),(0,a.A)(this,"invoiceId",void 0),(0,a.A)(this,"listInvoice",[]),(0,a.A)(this,"newPaymentForm",void 0),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"router",(0,e.WQX)(s.Ix)),(0,a.A)(this,"_spinnerService",(0,e.WQX)(C.D)),(0,a.A)(this,"_storeService",(0,e.WQX)(f.n)),(0,a.A)(this,"_toastService",(0,e.WQX)(y.f)),(0,a.A)(this,"_paymentService",(0,e.WQX)(P.W)),(0,a.A)(this,"formBuilder",(0,e.WQX)(t.ze)),(0,a.A)(this,"currencyPipe",(0,e.WQX)(u.oe)),(0,a.A)(this,"_invoiceService",(0,e.WQX)(A.p)),(0,a.A)(this,"PaymentMethod",F),this.dialogRef=r,this.data=n,this.newPaymentForm=this.formBuilder.group({paymentAmount:["",t.k0.compose([t.k0.required])],paymentDate:[this.formatDate(new Date),t.k0.compose([t.k0.required])],paymentMethod:["",t.k0.compose([t.k0.required])],paymentNote:[""],paymentSendNotifi:[!1]})}handleSelectInvoice(r){let n=this.listInvoice.findIndex(d=>d.id.toString()==r.target.value.toString());n>=0&&(this.invoiceId=this.listInvoice[n].id,this.newPaymentForm.controls.paymentAmount.setValue(this.listInvoice[n].paidAmount))}transformAmount(r){const n=this.newPaymentForm.controls.paymentAmount,p=parseFloat(n.value.toString().replace(/[^0-9.-]+/g,""));isNaN(p)||n.setValue(this.currencyPipe.transform(p,"$"))}formatDate(r){return`${r.getFullYear()}-${String(r.getMonth()+1).padStart(2,"0")}-${String(r.getDate()).padStart(2,"0")}`}handleSelectMethod(r){let n=this.PaymentMethod.findIndex(d=>d.value.toString()==r.target.value.toString());n>=0&&this.newPaymentForm.controls.paymentMethod.setValue(this.PaymentMethod[n].value)}onSubmit(){this._spinnerService.show();let r={note:this.newPaymentForm.controls.paymentNote.value,datePayment:M().utc(this.newPaymentForm.controls.paymentDate.value).toDate(),idPaymentMethod:this.newPaymentForm.controls.paymentMethod.value,notificationSent:this.newPaymentForm.controls.paymentSendNotifi.value,paidAmount:this.newPaymentForm.controls.paymentAmount.value,invoiceId:this.invoiceId};this.invoiceId?this._paymentService.CreatedPayment(r).pipe((0,I.pQ)(this.destroyRef)).subscribe(n=>{if(n)return this.dialogRef.close(n),this._spinnerService.hide(),void this._toastService.showSuccess("Save","Create Success");this._spinnerService.hide()}):(this._spinnerService.hide(),this.dialogRef.close(r))}_handleData(r){r&&this.newPaymentForm.controls.paymentAmount.setValue(this.data.id?r.paidAmount:this.data)}GetAllInvoice(){this._invoiceService.GetAllInvoice({Page:0,PageSize:100,Search:""}).pipe((0,I.pQ)(this.destroyRef)).subscribe({next:n=>{n&&(this.listInvoice=n.data)}})}ngOnInit(){this.data?(this.invoiceId=this.data.id,this._handleData(this.data),this.newPaymentForm.controls.paymentMethod.setValue(this.PaymentMethod[0].value)):(this.newPaymentForm.controls.paymentMethod.setValue(this.PaymentMethod[0].value),this.GetAllInvoice())}get f(){return this.newPaymentForm.controls}closeDialog(){this.dialogRef.close()}}return l=m,(0,a.A)(m,"\u0275fac",function(r){return new(r||l)(e.rXU(v.CP),e.rXU(v.Vh))}),(0,a.A)(m,"\u0275cmp",e.VBU({type:l,selectors:[["app-add-payment"]],standalone:!0,features:[e.Jv_([u.oe]),e.aNF],decls:38,vars:9,consts:[[3,"onClose"],[1,"text-start","p-3","customform",3,"ngSubmit","formGroup"],[1,"mb-4"],[1,"fw-bold","m-0"],[1,"mb-3"],[1,"grid","gap-4","sm:grid-cols-2","mb-6"],[1,"w-full"],["for","paymentAmount",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["type","text","name","paymentAmount","id","paymentAmount","formControlName","paymentAmount","placeholder","Payment Amount","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-primary-600","focus:border-primary-600","block","w-full","p-2.5",3,"blur"],[1,"matError"],["for","paymentDate",1,"block","mb-2","text-sm","font-medium","text-gray-900"],["type","date","formControlName","paymentDate","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"mb-6"],["for","client",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["id","client",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5","dark:bg-gray-700",3,"change"],[3,"value"],["for","_email",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["type","text","id","paymentNote","formControlName","paymentNote","placeholder","Payment Note",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"mb-6","flex","w-full","items-center"],[1,"text-start"],["type","checkbox","id","checkbox","formControlName","paymentSendNotifi",1,"bg-gray-50","border","w-5","h-5","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","p-2.5"],[1,"ml-2"],[1,"w-full","text-center","mt-6","flex","justify-around"],["type","button",1,"font-medium","rounded-lg","text-sm","w-full","sm:w-auto","px-5","py-2.5","text-cente","hover:","border",3,"click"],["type","submit",1,"text-white","font-medium","rounded-lg","text-sm","w-full","sm:w-auto","px-5","py-2.5","text-center",3,"disabled","ngClass"],["for","client",1,"block","mb-2","text-sm","font-medium","text-gray-900"],["id","inoive",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"change"],["selected",""],[1,"flex","flex-col"],[1,"text-gray-300"]],template:function(r,n){1&r&&(e.j41(0,"app-innobook-modal-wrapper",0),e.bIt("onClose",function(){return n.closeDialog()}),e.j41(1,"form",1),e.bIt("ngSubmit",function(){return n.onSubmit()}),e.j41(2,"div",2)(3,"h5",3),e.EFF(4,"Add a Payment"),e.k0s()(),e.DNE(5,k,8,0,"div",4),e.j41(6,"div",5)(7,"div",6)(8,"label",7),e.EFF(9,"Payment Amount"),e.k0s(),e.j41(10,"input",8),e.bIt("blur",function(p){return n.transformAmount(p)}),e.k0s(),e.DNE(11,_,2,0,"mat-error",9),e.k0s(),e.j41(12,"div",6)(13,"label",10),e.EFF(14,"Payment Date"),e.k0s(),e.nrm(15,"input",11),e.DNE(16,R,2,0,"mat-error",9),e.k0s()(),e.j41(17,"div",12)(18,"label",13),e.EFF(19,"Payment Method "),e.k0s(),e.j41(20,"select",14),e.bIt("change",function(p){return n.handleSelectMethod(p)}),e.Z7z(21,$,3,2,"option",15,e.fX1),e.k0s()(),e.j41(23,"div",12)(24,"label",16),e.EFF(25,"Payment Note "),e.k0s(),e.nrm(26,"input",17),e.k0s(),e.j41(27,"div",18)(28,"div",19),e.nrm(29,"input",20),e.k0s(),e.j41(30,"span",21),e.EFF(31," Send client a payment notification email "),e.k0s()(),e.nrm(32,"hr"),e.j41(33,"div",22)(34,"button",23),e.bIt("click",function(){return n.closeDialog()}),e.EFF(35,"Cancel"),e.k0s(),e.j41(36,"button",24),e.EFF(37,"Save"),e.k0s()()()()),2&r&&(e.R7$(),e.Y8G("formGroup",n.newPaymentForm),e.R7$(4),e.vxM(n.data?-1:5),e.R7$(6),e.vxM((n.f.paymentAmount.dirty||n.f.paymentAmount.touched)&&n.f.paymentAmount.hasError("required")?11:-1),e.R7$(5),e.vxM((n.f.paymentDate.dirty||n.f.paymentDate.touched)&&n.f.paymentDate.hasError("required")?16:-1),e.R7$(5),e.Dyx(n.PaymentMethod),e.R7$(15),e.Y8G("disabled",!n.newPaymentForm.valid)("ngClass",e.l_i(6,x,n.newPaymentForm.valid,!n.newPaymentForm.valid)))},dependencies:[S.G,u.YU,t.qT,t.xH,t.y7,t.me,t.Zm,t.BC,t.cb,t.YS,t.j4,t.JD,u.MD,b.RG,b.TL,o.j]})),m})()}}]);