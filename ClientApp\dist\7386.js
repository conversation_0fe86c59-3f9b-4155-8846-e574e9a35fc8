"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7386],{7386:(O,h,n)=>{n.r(h),n.d(h,{ModifyExpenseItemComponent:()=>g});var i=n(9842),x=n(4433),e=n(4438),s=n(9417),M=n(4006),C=n(1328),T=n(344),v=n(4978),I=n(6146),D=n(6617),p=n(5909),y=n(2387),R=n(5236);const m=a=>({required:a});function b(a,r){if(1&a){const _=e.RV6();e.j41(0,"app-inno-form-input",12),e.nI1(1,"translate"),e.bIt("onChange",function(t){const l=e.eBV(_).$index,d=e.XpG(2);return e.Njj(d.onChangeTax(t,l))}),e.k0s()}if(2&a){const _=r.$implicit,o=e.XpG(2);e.Y8G("label",_.companyTax.name)("value",o.calculateTotalTax(_.companyTax.amount))("errorMessages",e.eq3(5,m,e.bMT(1,3,"EXPENSES.NEW_ADD_FORM.ValidationQuantityRequired")))}}function A(a,r){if(1&a&&e.Z7z(0,b,2,7,"app-inno-form-input",11,e.fX1),2&a){const _=e.XpG();e.Dyx(_.data.taxes)}}function P(a,r){if(1&a&&(e.j41(0,"div",6)(1,"label",7),e.EFF(2,"Taxes"),e.k0s(),e.j41(3,"p",13),e.EFF(4),e.k0s()()),2&a){const _=e.XpG();e.R7$(4),e.SpI(" ",_.listTaxName," ")}}let g=(()=>{var a;class r{static getComponent(){return r}constructor(o,t,l){(0,i.A)(this,"dialogRef",void 0),(0,i.A)(this,"modifyTaxesDialog",void 0),(0,i.A)(this,"data",void 0),(0,i.A)(this,"invoiceItemForm",void 0),(0,i.A)(this,"today",new Date),(0,i.A)(this,"total",void 0),(0,i.A)(this,"listTaxName",""),(0,i.A)(this,"rate",void 0),(0,i.A)(this,"quantity",void 0),(0,i.A)(this,"formBuilder",(0,e.WQX)(s.ze)),this.dialogRef=o,this.modifyTaxesDialog=t,this.data=l;const{description:d="",rate:E,qty:f,taxes:u=[]}=l||{};this.rate=E,this.quantity=f,this.invoiceItemForm=this.formBuilder.group({description:[d,[]],rate:[E,s.k0.compose([s.k0.required])],qty:[f,s.k0.compose([s.k0.required])],taxes:[u]}),u&&(this.listTaxName=(0,p.Xj)(u)),this.total=(0,p.R2)(E,f),this.invoiceItemForm.valueChanges.subscribe(c=>{this.total=(0,p.R2)(c.rate,c.qty),this.listTaxName=(0,p.Xj)(c.taxes)})}onChangeTax(o,t){}calculateTotalTax(o){return Number((this.rate*this.quantity*(o/100)).toFixed(2))}get f(){return this.invoiceItemForm.controls}handleClose(){this.dialogRef.close()}markAllControlsAsTouched(){Object.values(this.f).forEach(o=>{o.markAsTouched()})}handleCancel(){this.dialogRef.close()}handleSubmit(){if(this.invoiceItemForm.invalid)return void this.markAllControlsAsTouched();const o={description:this.f.description.value,rate:this.f.rate.value,qty:this.f.qty.value,total:this.total??0,taxes:this.f.taxes.value,date:this.today,dateSelectItem:this.today};this.data?.id&&(o.id=this.data.id),this.dialogRef.close(o)}handleModifyTaxes(){this.modifyTaxesDialog.open(this.f.taxes?.value??[]).then(t=>{t.afterClosed().subscribe(l=>{l?.taxes&&this.f.taxes.setValue(l.taxes)})})}}return a=r,(0,i.A)(r,"\u0275fac",function(o){return new(o||a)(e.rXU(M.CP),e.rXU(y.I),e.rXU(M.Vh))}),(0,i.A)(r,"\u0275cmp",e.VBU({type:a,selectors:[["app-modify-expense-item"]],standalone:!0,features:[e.aNF],decls:26,vars:44,consts:[[3,"onClose","title"],[3,"formGroup"],[1,"w-full","flex","flex-col","gap-[16px]","px-[16px]","pb-[16px]"],[3,"label","placeholder","formControl","value"],["type","number","placeholder","0.00",3,"label","formControl","value","errorMessages"],["type","number",3,"label","placeholder","formControl","value","errorMessages"],[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-primary","text-headline-md-bold"],[1,"button-link-primary",3,"click"],[3,"onCancel","onSubmit"],["placeholder","0.00 ",3,"label","value","errorMessages"],["placeholder","0.00 ",3,"onChange","label","value","errorMessages"],[1,"text-text-primary","text-text-sm-bold"]],template:function(o,t){1&o&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return t.handleClose()}),e.j41(1,"form",1)(2,"div",2),e.nrm(3,"app-inno-form-input",3),e.nI1(4,"translate"),e.nI1(5,"translate"),e.nrm(6,"app-inno-form-input",4),e.nI1(7,"translate"),e.nI1(8,"translate"),e.nrm(9,"app-inno-form-input",5),e.nI1(10,"translate"),e.nI1(11,"translate"),e.nI1(12,"translate"),e.DNE(13,A,2,0)(14,P,5,1,"div",6),e.j41(15,"div",6)(16,"label",7),e.EFF(17,"Total"),e.k0s(),e.j41(18,"p",8),e.EFF(19),e.nI1(20,"decimal"),e.nI1(21,"formatNumber"),e.k0s()(),e.j41(22,"button",9),e.bIt("click",function(){return t.handleModifyTaxes()}),e.EFF(23),e.nI1(24,"translate"),e.k0s()(),e.j41(25,"app-inno-modal-footer",10),e.bIt("onCancel",function(){return t.handleCancel()})("onSubmit",function(){return t.handleSubmit()}),e.k0s()()()),2&o&&(e.Y8G("title",t.data?"EXPENSES.DescriptionEditExpensesItem":"EXPENSES.NewExpensesItem"),e.R7$(),e.Y8G("formGroup",t.invoiceItemForm),e.R7$(2),e.Y8G("label",e.bMT(4,19,"EXPENSES.NEW_ADD_FORM.Description"))("placeholder",e.bMT(5,21,"EXPENSES.NEW_ADD_FORM.Description"))("formControl",t.f.description)("value",t.f.description.value),e.R7$(3),e.Y8G("label",e.bMT(7,23,"EXPENSES.NEW_ADD_FORM.Rate"))("formControl",t.f.rate)("value",t.f.rate.value)("errorMessages",e.eq3(40,m,e.bMT(8,25,"EXPENSES.NEW_ADD_FORM.ValidationRateRequired"))),e.R7$(3),e.Y8G("label",e.bMT(10,27,"EXPENSES.NEW_ADD_FORM.Quantity"))("placeholder",e.bMT(11,29,"EXPENSES.NEW_ADD_FORM.Quantity"))("formControl",t.f.qty)("value",t.f.qty.value)("errorMessages",e.eq3(42,m,e.bMT(12,31,"EXPENSES.NEW_ADD_FORM.ValidationQuantityRequired"))),e.R7$(4),e.vxM(t.data?13:-1),e.R7$(),e.vxM(null!=t.listTaxName&&t.listTaxName.length?14:-1),e.R7$(5),e.SpI(" $",e.bMT(21,36,e.i5U(20,33,t.total||0,2))," "),e.R7$(4),e.SpI(" ",e.bMT(24,38,"EXPENSES.NEW_ADD_FORM.AddUpdateTaxes")," "))},dependencies:[I.G,s.qT,s.BC,s.cb,s.l_,s.j4,R.D9,v.I,T.k,C.a,D.p,x.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),r})()}}]);