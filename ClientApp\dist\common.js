"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2076],{828:(g,u,e)=>{e.d(u,{G:()=>y});var t=e(9842),n=e(4438);let o=(()=>{var C;class A{constructor(){(0,t.A)(this,"loginWithGoogle",new n.bkB),(0,t.A)(this,"createFakeGoogleWrapper",()=>{const _=document.createElement("div");_.style.display="none",_.classList.add("custom-google-button"),document.body.appendChild(_),window.google.accounts.id.renderButton(_,{type:"icon",width:"200"});const v=_.querySelector("div[role=button]");return{click:()=>{v?.click()}}})}handleGoogleLogin(){this.loginWithGoogle.emit(this.createFakeGoogleWrapper())}}return C=A,(0,t.A)(A,"\u0275fac",function(_){return new(_||C)}),(0,t.A)(A,"\u0275cmp",n.VBU({type:C,selectors:[["app-google-signin"]],outputs:{loginWithGoogle:"loginWithGoogle"},standalone:!0,features:[n.aNF],decls:3,vars:0,consts:[[1,"button-outline","w-full","justify-center",3,"click"],[1,"icon-google","shrink-0"]],template:function(_,v){1&_&&(n.j41(0,"button",0),n.bIt("click",function(){return v.handleGoogleLogin()}),n.nrm(1,"div",1),n.EFF(2," Google\n"),n.k0s())},styles:[".icon-google[_ngcontent-%COMP%]{width:22px;height:22px;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMTcuNiA5LjJsLS4xLTEuOEg5djMuNGg0LjhDMTMuNiAxMiAxMyAxMyAxMiAxMy42djIuMmgzYTguOCA4LjggMCAwIDAgMi42LTYuNnoiIGZpbGw9IiM0Mjg1RjQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjxwYXRoIGQ9Ik05IDE4YzIuNCAwIDQuNS0uOCA2LTIuMmwtMy0yLjJhNS40IDUuNCAwIDAgMS04LTIuOUgxVjEzYTkgOSAwIDAgMCA4IDV6IiBmaWxsPSIjMzRBODUzIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNCAxMC43YTUuNCA1LjQgMCAwIDEgMC0zLjRWNUgxYTkgOSAwIDAgMCAwIDhsMy0yLjN6IiBmaWxsPSIjRkJCQzA1IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNOSAzLjZjMS4zIDAgMi41LjQgMy40IDEuM0wxNSAyLjNBOSA5IDAgMCAwIDEgNWwzIDIuNGE1LjQgNS40IDAgMCAxIDUtMy43eiIgZmlsbD0iI0VBNDMzNSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PHBhdGggZD0iTTAgMGgxOHYxOEgweiIvPjwvZz48L3N2Zz4=);background-repeat:no-repeat;background-size:cover}"]})),A})();var p=e(467);let d=(()=>{var C;class A{loginApple(){return(0,p.A)(function*(){try{AppleID.auth.init({clientId:"VRSignIn",scope:"name email",redirectURI:"https://angular-apple-signin.stackblitz.io/apple-callback",state:"init",nonce:"test",usePopup:!0}),yield AppleID.auth.signIn()}catch(_){console.log(_)}})()}parseJwt(_){var T=_.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),P=decodeURIComponent(atob(T).split("").map(function(S){return"%"+("00"+S.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(P)}}return C=A,(0,t.A)(A,"\u0275fac",function(_){return new(_||C)}),(0,t.A)(A,"\u0275cmp",n.VBU({type:C,selectors:[["app-apple-signin"]],standalone:!0,features:[n.aNF],decls:3,vars:0,consts:[[1,"button-outline","w-full","justify-center",3,"click"],["src","../../../assets/img/icon/ic_apple.svg","alt","Icon Apple",1,"h-[22px]","shrink-0"]],template:function(_,v){1&_&&(n.j41(0,"button",0),n.bIt("click",function(){return v.loginApple()}),n.nrm(1,"img",1),n.EFF(2," Apple\n"),n.k0s())},styles:[".login-with-apple-btn[_ngcontent-%COMP%]{transition:background-color .3s,box-shadow .3s;cursor:pointer;padding:12px 16px 12px 13px;border-radius:5px;display:flex;border:1px solid gray;background:#fff;font-weight:500;color:#000}.login-with-apple-btn[_ngcontent-%COMP%]:hover{box-shadow:0 -1px #0000000a,0 2px 4px #00000040}.login-with-apple-btn[_ngcontent-%COMP%]:active{color:#000;outline:none;background-color:#fff}.login-with-apple-btn[_ngcontent-%COMP%]:focus{outline:none;box-shadow:0 -1px #0000000a,0 2px 4px #00000040,0 0 0 3px #c8dafc}"]})),A})();var l=e(2608),i=e(4006),a=e(9079),s=e(2928),r=e(1342),c=e(33),m=e(2716),h=e(7987);let I=(()=>{var C;class A extends m.H{open(_){var v=this;return(0,p.A)(function*(){const T=yield Promise.all([e.e(2076),e.e(1172)]).then(e.bind(e,1172));return v.matDialog.open(T.FactorCodeComponent.getComponent(),{data:_,scrollStrategy:new h.t0,disableClose:!0})})()}}return C=A,(0,t.A)(A,"\u0275fac",(()=>{let E;return function(v){return(E||(E=n.xGo(C)))(v||C)}})()),(0,t.A)(A,"\u0275prov",n.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})),A})(),y=(()=>{var C;class A{constructor(_){(0,t.A)(this,"factorCodeDialog",void 0),(0,t.A)(this,"user",null),(0,t.A)(this,"router",(0,n.WQX)(c.Ix)),(0,t.A)(this,"dialog",(0,n.WQX)(i.bZ)),(0,t.A)(this,"destroyRef",(0,n.WQX)(n.abz)),(0,t.A)(this,"authService",(0,n.WQX)(l.Jn)),(0,t.A)(this,"auth_services",(0,n.WQX)(s.k)),(0,t.A)(this,"spiner_services",(0,n.WQX)(r.D)),this.factorCodeDialog=_,this.authService.authState.pipe((0,a.pQ)(this.destroyRef)).subscribe(v=>{v&&(this.user=v,this.spiner_services.show(),this.auth_services.SignUp({firstName:v.firstName,lastName:v.lastName,email:v.email,is2FAEnabled:!0}).pipe((0,a.pQ)(this.destroyRef)).subscribe(P=>{P&&(this.spiner_services.hide(),this.OpenTwoFactorCode(this.user))}))})}OpenTwoFactorCode(_){this.factorCodeDialog.open(_).then(T=>{T.afterClosed().subscribe(P=>{P&&(this.auth_services.saveToken_cookie(P.accessToken,P.refreshToken),this.router.navigate(["/"]),this.spiner_services.hide())})})}loginWithGoogle(){this.authService.signIn(l.Gz.PROVIDER_ID)}googleSignin(_){_.click()}}return C=A,(0,t.A)(A,"\u0275fac",function(_){return new(_||C)(n.rXU(I))}),(0,t.A)(A,"\u0275cmp",n.VBU({type:C,selectors:[["app-login-social"]],standalone:!0,features:[n.aNF],decls:3,vars:0,consts:[[1,"w-full","grid","grid-cols-2","gap-[10px]","items-center"],[3,"loginWithGoogle"]],template:function(_,v){1&_&&(n.j41(0,"div",0)(1,"app-google-signin",1),n.bIt("loginWithGoogle",function(P){return v.googleSignin(P)}),n.k0s(),n.nrm(2,"app-apple-signin"),n.k0s())},dependencies:[l.BD,l.AV,o,d]})),A})()},1225:(g,u,e)=>{function t(n,o){return p=>{let l=p.controls[o];l.errors&&!l.errors||l.setErrors(p.controls[n].value!==l.value?{confirmPasswordValidator:!0}:null)}}e.d(u,{S:()=>t})},480:(g,u,e)=>{e.d(u,{Z:()=>d});var t=e(9842),n=e(177),o=e(4438);function p(l,i){1&l&&o.eu8(0)}let d=(()=>{var l;class i{constructor(){(0,t.A)(this,"actionTemplate",void 0),(0,t.A)(this,"title","")}}return l=i,(0,t.A)(i,"\u0275fac",function(s){return new(s||l)}),(0,t.A)(i,"\u0275cmp",o.VBU({type:l,selectors:[["app-breadcrum"]],inputs:{actionTemplate:"actionTemplate",title:"title"},standalone:!0,features:[o.aNF],decls:5,vars:2,consts:[[1,"flex","flex-row","justify-between","p-3"],[1,"m-0","fw-bold"],[4,"ngTemplateOutlet"]],template:function(s,r){1&s&&(o.j41(0,"div",0)(1,"div")(2,"h5",1),o.EFF(3),o.k0s()(),o.DNE(4,p,1,0,"ng-container",2),o.k0s()),2&s&&(o.R7$(3),o.JRh(r.title),o.R7$(),o.Y8G("ngTemplateOutlet",r.actionTemplate))},dependencies:[n.MD,n.T3]})),i})()},5900:(g,u,e)=>{e.d(u,{M:()=>l});var t=e(9842),n=e(177),o=e(4438),p=e(5236);const d=i=>({height:i});let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"height",void 0),(0,t.A)(this,"value",void 0),(0,t.A)(this,"onChange",new o.bkB)}handleOnChange(r){this.onChange.emit(r?.target?.value??"")}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275cmp",o.VBU({type:i,selectors:[["app-inno-input-search"]],inputs:{height:"height",value:"value"},outputs:{onChange:"onChange"},standalone:!0,features:[o.aNF],decls:4,vars:7,consts:[[1,"w-full","flex","items-center","rounded-[8px]","border-[2px]","px-[12px]","bg-bg-primary",3,"ngStyle"],["src","../../../assets/img/icon/ic_search_gray.svg","alt","Icon search",1,"w-[16px]","shrink-0"],["type","text",1,"h-full","w-full","pl-[8px]","text-text-sm-regular",3,"keyup","placeholder","value"]],template:function(r,c){if(1&r&&(o.j41(0,"div",0),o.nrm(1,"img",1),o.j41(2,"input",2),o.nI1(3,"translate"),o.bIt("keyup",function(h){return c.handleOnChange(h)}),o.k0s()()),2&r){let m;o.Y8G("ngStyle",o.eq3(5,d,null!==(m=c.height)&&void 0!==m?m:"40px")),o.R7$(2),o.Y8G("placeholder",o.bMT(3,3,"COMMON.Search"))("value",c.value)}},dependencies:[n.MD,n.B3,p.h,p.D9]})),a})()},8600:(g,u,e)=>{e.d(u,{k:()=>d});var t=e(9842),n=e(4438);function o(l,i){if(1&l){const a=n.RV6();n.j41(0,"button",3),n.bIt("click",function(){const r=n.eBV(a).$implicit,c=n.XpG(2);return n.Njj(c.handleSelectTab(r.value))}),n.EFF(1),n.k0s()}if(2&l){const a=i.$implicit,s=n.XpG(2);n.AVh("active",s.value==a.value),n.R7$(),n.SpI(" ",a.label," ")}}function p(l,i){if(1&l&&(n.j41(0,"div",0)(1,"div",1),n.Z7z(2,o,2,3,"button",2,n.fX1),n.k0s()()),2&l){const a=n.XpG();n.R7$(2),n.Dyx(a.tabs)}}let d=(()=>{var l;class i{constructor(){(0,t.A)(this,"tabs",[]),(0,t.A)(this,"value",void 0),(0,t.A)(this,"onChange",new n.bkB)}handleSelectTab(s){this.onChange.emit(s??null)}}return l=i,(0,t.A)(i,"\u0275fac",function(s){return new(s||l)}),(0,t.A)(i,"\u0275cmp",n.VBU({type:l,selectors:[["app-inno-tabs"]],inputs:{tabs:"tabs",value:"value"},outputs:{onChange:"onChange"},standalone:!0,features:[n.aNF],decls:1,vars:1,consts:[[1,"flex"],[1,"h-[32px]","overflow-hidden","shrink-0","flex"],[1,"buttonTabItem",3,"active"],[1,"buttonTabItem",3,"click"]],template:function(s,r){1&s&&n.DNE(0,p,4,0,"div",0),2&s&&n.vxM(r.tabs.length>0?0:-1)},styles:[".buttonTabItem[_ngcontent-%COMP%]{padding-left:12px;padding-right:12px;height:100%;flex-shrink:0;position:relative;border:1px solid;font-size:14px;line-height:20px;font-weight:600;color:var(--text-tertiary);background-color:var(--bg-primary);border-color:var(--border-primary)}.buttonTabItem.active[_ngcontent-%COMP%]{background-color:var(--bg-brand-primary);color:var(--text-brand-primary);border-color:var(--text-brand-primary)}.buttonTabItem[_ngcontent-%COMP%]:hover{background-color:var(--bg-brand-primary);transition:all .3s}.buttonTabItem[_ngcontent-%COMP%]:first-child{border-top-left-radius:8px;border-bottom-left-radius:8px}.buttonTabItem[_ngcontent-%COMP%]:last-child{border-top-right-radius:8px;border-bottom-right-radius:8px}"]})),i})()},1470:(g,u,e)=>{e.d(u,{j:()=>p});var t=e(9842),n=e(4438);const o=["*"];let p=(()=>{var d;class l{constructor(){(0,t.A)(this,"onClose",new n.bkB)}handleClose(){this.onClose.emit()}}return d=l,(0,t.A)(l,"\u0275fac",function(a){return new(a||d)}),(0,t.A)(l,"\u0275cmp",n.VBU({type:d,selectors:[["app-innobook-modal-wrapper"]],outputs:{onClose:"onClose"},standalone:!0,features:[n.aNF],ngContentSelectors:o,decls:6,vars:0,consts:[[1,"text-end","p-3"],["type","button","id","buttonClose",1,"buttonIcon","small","btnClose",3,"click"],[1,"material-icons"],[1,"bodyModal"]],template:function(a,s){1&a&&(n.NAR(),n.j41(0,"div",0)(1,"button",1),n.bIt("click",function(){return s.handleClose()}),n.j41(2,"i",2),n.EFF(3,"close"),n.k0s()(),n.j41(4,"div",3),n.SdG(5),n.k0s()())}})),l})()},1149:(g,u,e)=>{e.d(u,{K:()=>d});var t=e(9842),n=e(5236),o=e(4438);const p=["*"];let d=(()=>{var l;class i{constructor(){}}return l=i,(0,t.A)(i,"\u0275fac",function(s){return new(s||l)}),(0,t.A)(i,"\u0275cmp",o.VBU({type:l,selectors:[["app-auth-layout"]],standalone:!0,features:[o.aNF],ngContentSelectors:p,decls:23,vars:6,consts:[[1,"flex","min-h-dvh","bg-white"],[1,"hidden","md:block","md:w-1/2","p-10","bg-bg-brand-strong-hover"],[1,"w-full","h-full","flex","flex-col","justify-between","gap-[10px]"],[1,"grow","flex","flex-col"],[1,"w-full"],["src","../../../assets/img/logo_white.png","alt","logo",1,"h-[30px]","object-contain"],[1,"grow","flex","flex-col","justify-center","items-center","pb-20"],[1,"flex","items-center","gap-4","mb-6","justify-center"],[1,"material-icons","text-white","!text-[80px]"],[1,"text-4xl","font-display","font-semibold","tracking-tight","text-white","text-center"],[1,"mt-4","text-white/80","leading-relaxed","max-w-md","text-center","mx-auto"],[1,"text-text-brand-tertiary","text-text-sm-regular","font-semibold"],[1,"w-full","md:w-1/2","flex","items-center","justify-center","py-8","container"]],template:function(s,r){1&s&&(o.NAR(),o.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"div",4),o.nrm(5,"img",5),o.k0s(),o.j41(6,"div",6)(7,"div",7)(8,"i",8),o.EFF(9,"browse_gallery"),o.k0s(),o.j41(10,"i",8),o.EFF(11,"stacked_bar_chart"),o.k0s()(),o.j41(12,"h2",9),o.EFF(13),o.nI1(14,"translate"),o.k0s(),o.j41(15,"p",10),o.EFF(16),o.nI1(17,"translate"),o.k0s()()(),o.j41(18,"div",4)(19,"p",11),o.EFF(20," \xa9 2025 InnoLogiciel Inc. "),o.k0s()()()(),o.j41(21,"div",12),o.SdG(22),o.k0s()()),2&s&&(o.R7$(13),o.SpI(" ",o.bMT(14,2,"LOGIN.TimeTrackingMade")," "),o.R7$(3),o.SpI(" ",o.bMT(17,4,"LOGIN.Describe")," "))},dependencies:[n.h,n.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),i})()},3705:(g,u,e)=>{e.d(u,{M:()=>o});var t=e(9842),n=e(4438);let o=(()=>{var p;class d{transform(i){if(0===i)return"0 Bytes";const r=Math.floor(Math.log(i)/Math.log(1024));return`${parseFloat((i/Math.pow(1024,r)).toFixed(2))} ${["Bytes","KB","MB","GB","TB"][r]}`}}return p=d,(0,t.A)(d,"\u0275fac",function(i){return new(i||p)}),(0,t.A)(d,"\u0275pipe",n.EJ8({name:"size",type:p,pure:!0,standalone:!0})),d})()},4982:(g,u,e)=>{e.d(u,{X:()=>i});var t=e(9842),n=e(1626),o=e(4438),p=e(5312),d=e(6473);const l=p.c.HOST_API+"/api/Client/";let i=(()=>{var a;class s{constructor(){(0,t.A)(this,"http",(0,o.WQX)(n.Qq))}CreateClient(c){return this.http.post(l+"Create",c)}UpdateClient(c){return this.http.post(l+"UpdateClient",c)}GetAllClient(c){const m=(0,d.yU)(c);return this.http.get(l+"AllClient",{params:m})}ClientTimeTracking(){return this.http.get(l+"ClientTimeTracking")}CalculationClient(){return this.http.get(l+"CalculationClient")}GetClientById(c){return this.http.get(l+`GetClientById?id=${c}`)}DeleteClient(c){return this.http.delete(l+"DeleteClient",{body:c})}}return a=s,(0,t.A)(s,"\u0275fac",function(c){return new(c||a)}),(0,t.A)(s,"\u0275prov",o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),s})()},7136:(g,u,e)=>{e.d(u,{Y:()=>i});var t=e(9842),n=e(5312),o=e(4438),p=e(1626),d=e(6473);const l=n.c.HOST_API+"/api";let i=(()=>{var a;class s{constructor(){(0,t.A)(this,"http",(0,o.WQX)(p.Qq))}Create(c){return this.http.post(l+"/CompanyTax/Create",c)}GetAllCompanyTax(c){const m=(0,d.yU)(c);return this.http.get(l+"/CompanyTax/GetAllCompanyTax",{params:m})}}return a=s,(0,t.A)(s,"\u0275fac",function(c){return new(c||a)}),(0,t.A)(s,"\u0275prov",o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),s})()},1719:(g,u,e)=>{e.d(u,{E:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(1328),e.e(2076),e.e(9363)]).then(e.bind(e,9363));return c.matDialog.open(m.AddClientsFormComponent.getComponent(),{panelClass:"custom_dialog",data:r,maxWidth:"600px",width:"100%",scrollStrategy:new p.t0,disableClose:!0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},7977:(g,u,e)=>{e.d(u,{w:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(1328),e.e(4823),e.e(3924),e.e(6217),e.e(9717),e.e(1875),e.e(5354),e.e(9392)]).then(e.bind(e,9392));return c.matDialog.open(m.NewEstimateComponent.getComponent(),{data:r,width:"80vw",maxWidth:"100%",maxHeight:"100%",panelClass:"custom_dialog",disableClose:!0,scrollStrategy:new p.t0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},4193:(g,u,e)=>{e.d(u,{y:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(6473),e.e(3719),e.e(2076),e.e(8222)]).then(e.bind(e,8222));return c.matDialog.open(m.AddPaymentComponent.getComponent(),{panelClass:"custom_dialog",data:r,scrollStrategy:new p.t0,disableClose:!0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},1537:(g,u,e)=>{e.d(u,{u:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(6437),e.e(9775),e.e(5599),e.e(6473),e.e(1328),e.e(9589),e.e(4823),e.e(3924),e.e(6217),e.e(9717),e.e(1875),e.e(5354),e.e(9114),e.e(8193)]).then(e.bind(e,8193));return c.matDialog.open(m.NewInvoiceComponent.getComponent(),{data:r,width:"80vw",maxWidth:"100%",maxHeight:"100%",panelClass:"custom_dialog",scrollStrategy:new p.t0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},584:(g,u,e)=>{e.d(u,{y:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(2076),e.e(3086)]).then(e.bind(e,3086));return c.matDialog.open(m.ShareLinkComponent.getComponent(),{panelClass:"custom_dialog",data:r,disableClose:!0,scrollStrategy:new p.t0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},9589:(g,u,e)=>{e.d(u,{y:()=>l});var t=e(467),n=e(9842),o=e(2716),p=e(7987),d=e(4438);let l=(()=>{var i;class a extends o.H{open(r){var c=this;return(0,t.A)(function*(){const m=yield Promise.all([e.e(2076),e.e(6194)]).then(e.bind(e,6194));return c.matDialog.open(m.SwitchWorkspaceComponent.getComponent(),{panelClass:"custom_dialog",data:r,width:"400px",disableClose:!0,scrollStrategy:new p.t0})})()}}return i=a,(0,n.A)(a,"\u0275fac",(()=>{let s;return function(c){return(s||(s=d.xGo(i)))(c||i)}})()),(0,n.A)(a,"\u0275prov",d.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},822:(g,u,e)=>{e.d(u,{b:()=>l});var t=e(9842),n=e(5312),o=e(1626),p=e(4438);const d=n.c.HOST_API+"/api";let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"http",(0,p.WQX)(o.Qq))}GetAllItem(r){return this.http.post(d+"/Item/GetAllItem",r)}GetItemById(r){return this.http.get(d+`/Item/GetItemById?id=${r}`)}CreateItem(r){return this.http.post(d+"/Item/CreateItem",r)}DeleteItem(r){return this.http.post(d+"/Item/DeleteItem",r)}Update(r){return this.http.post(d+"/Item/Update",r)}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275prov",p.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},4591:(g,u,e)=>{e.d(u,{G:()=>l});var t=e(9842),n=e(1626),o=e(4438);const d=e(5312).c.HOST_API+"/api";let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"http",(0,o.WQX)(n.Qq))}InviteMember(r){return this.http.post(d+"/Member/InviteMember",r)}CountMemberBusiness(){return this.http.get(d+"/Member/CountMemberBusiness")}GetAllMembersByProjectId(r){return this.http.get(d+`/Member/GetAllMembersByProjectIdPage=${r.Page}&PageSize=${r.PageSize}&Search=${r.Search}&ProjectId=${r.ProjectId}`)}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275prov",o.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},8897:(g,u,e)=>{e.d(u,{W:()=>l});var t=e(9842),n=e(5312),o=e(4438),p=e(1626);const d=n.c.HOST_API+"/api";let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"http",(0,o.WQX)(p.Qq))}CreatedPayment(r){return this.http.post(d+"/Payment/CreatedPayment",r)}GetAllPayment(r){return this.http.get(d+`/Payment/GetAllPayment?Page=${r.Page}&PageSize=${r.PageSize}&Search=${r.Search}&InvoiceId=${r.InvoiceId}`)}GetAllPaymentCompany(r){return this.http.get(d+`/Payment/GetAllPaymentCompany?Page=${r.Page}&PageSize=${r.PageSize}&Search=${r.Search}`)}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275prov",o.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},3114:(g,u,e)=>{e.d(u,{J:()=>l});var t=e(9842),n=e(5312),o=e(4438),p=e(1626);const d=n.c.HOST_API+"/api";let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"http",(0,o.WQX)(p.Qq))}getAllPlans(){return this.http.get(d+"/Plan/GetAllPlans")}getCurrentPlan(){return this.http.get(d+"/Plan/GetCurrentPlan")}calculatePlanPrice(r,c="month",m=0){return this.http.get(d+`/Plan/CalculatePlanPrice?planId=${r}&billingInterval=${c}&additionalUsers=${m}`)}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275prov",o.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},1433:(g,u,e)=>{e.d(u,{T:()=>i});var t=e(9842),n=e(1626),o=e(4438),p=e(5312),d=e(6473);const l=p.c.HOST_API+"/api";let i=(()=>{var a;class s{constructor(){(0,t.A)(this,"http",(0,o.WQX)(n.Qq))}CreateProject(c){return this.http.post(l+"/Project/CreateProject",c)}UpdateArchive(c,m){return this.http.post(l+`/Project/UpdateArchive?projectId=${c}&isArchive=${m}`,{})}GetAllProjectAsync(c){const m=(0,d.yU)(c);return this.http.get(l+"/Project/GetAllProjectAsync",{params:m})}DeleteProject(c,m){return this.http.post(l+`/Project/DeleteProject?isActive=${m}`,c)}UpdateProject(c){return this.http.post(l+"/Project/UpdateProject",c)}CalculationProject(){return this.http.get(l+"/Project/CalculationProject")}GetProjectById(c){return this.http.get(l+`/Project/GetProjectById?projectId=${c}`)}}return a=s,(0,t.A)(s,"\u0275fac",function(c){return new(c||a)}),(0,t.A)(s,"\u0275prov",o.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),s})()},7152:(g,u,e)=>{e.d(u,{d:()=>l});var t=e(9842),n=e(5312),o=e(4438),p=e(1626);const d=n.c.HOST_API+"/api";let l=(()=>{var i;class a{constructor(){(0,t.A)(this,"http",(0,o.WQX)(p.Qq))}createPaymentSession(r,c="USD",m="Payment",h="one_time",I="month",y,C=0){return this.http.post(d+"/Stripe/CreateSession",{amount:r,currency:c,description:m,paymentType:h,billingInterval:I,planId:y,additionalUsers:C})}verifyPayment(r){return this.http.get(d+`/Stripe/VerifyPayment?sessionId=${r}`)}}return i=a,(0,t.A)(a,"\u0275fac",function(r){return new(r||i)}),(0,t.A)(a,"\u0275prov",o.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),a})()},3570:(g,u,e)=>{e.d(u,{l:()=>n});var t=e(2953);const n=[{label:"MENUACTION.MarkAsPaid",icon:"../../../../assets/img/icon/ic_cash_circle.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],action:"paid"},{label:"MENUACTION.MarkAsSent",icon:"../../../../assets/img/icon/ic_arrow_right.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],action:"sent"},{label:"MENUACTION.Duplicate",icon:"../../../../assets/img/icon/ic_duplicate.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],action:"duplicate"},{label:"MENUACTION.DownloadPDF",icon:"../../../../assets/img/icon/ic_download.svg",permissions:[t.X.Admin,t.X.Accountant,t.X.Manager,t.X.Employee,t.X.Contractor],action:"download"},{label:"MENUACTION.Print",icon:"../../../../assets/img/icon/ic_print.svg",permissions:[t.X.Admin,t.X.Accountant,t.X.Manager,t.X.Employee,t.X.Contractor],action:"print"},{label:"MENUACTION.Archive",icon:"../../../../assets/img/icon/ic_archive.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],action:"archive"},{label:"MENUACTION.Delete",icon:"../../../../assets/img/icon/ic_trash.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],action:"delete"}]},6327:(g,u,e)=>{e.d(u,{F:()=>t});const t=[{name:"United States",code:"US"},{name:"Canada",code:"CA"},{name:"United Kingdom",code:"UK"},{name:"Australia",code:"AU"},{name:"Germany",code:"DE"}]},5508:(g,u,e)=>{e.d(u,{r:()=>n});var t=e(2953);const n=[{url:"/estimates",text:"MENU.Estimates",id:"estimates",icon:"ic_estimate.svg",permissions:[t.X.Admin,t.X.Manager],children:[],group:"Tracking"},{url:"/time-tracking",text:"MENU.TimeTracking",id:"time_tracking",icon:"ic_time_tracking.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],children:[],group:"Tracking"},{url:"/dashboard",text:"MENU.Dashboard",id:"Dashboard",icon:"ic_dashboard.svg",permissions:[t.X.All],children:[],group:"Analyze"},{url:"/reports",text:"MENU.Reports",id:"reports",icon:"ic_report.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Accountant],children:[],group:"Analyze"},{url:"/clients",text:"MENU.Clients",id:"client",icon:"ic_client.svg",permissions:[t.X.Admin,t.X.Manager],children:[],group:"Manager"},{url:"/projects",text:"MENU.Projects",id:"projects",icon:"ic_project.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Contractor],children:[],group:"Manager"},{url:"/pos",text:"MENU.Pos",id:"pos",icon:"ic_post.svg",permissions:[t.X.Admin,t.X.Manager],children:[],group:"Manager"},{url:"/invoices",text:"MENU.Invoices",id:"invoices",permissions:[t.X.Admin,t.X.Manager,t.X.Accountant,t.X.Contractor],icon:"ic_invoice.svg",children:[],group:"Manager"},{url:"/payments",text:"Payments",id:"payments",icon:"ic_payment.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Accountant]},{url:"/expenses",text:"MENU.Expenses",id:"expenses",icon:"ic_expense.svg",permissions:[t.X.Admin,t.X.Manager,t.X.Employee,t.X.Accountant],children:[{url:"/expenses/upload",text:"MENU.Upload",id:"upload",icon:""}],group:"Manager"},{url:"/items-and-services",text:"MENU.ItemServices",id:"reports",icon:"ic_project.svg",permissions:[t.X.Admin,t.X.Manager],children:[],group:"Manager"},{url:"/members",text:"MENU.TeamMembers",id:"members",icon:"ic_team.svg",permissions:[t.X.Admin],children:[],group:"Manager"},{url:"/settings",text:"MENU.Settings",id:"settings",icon:"ic_setting.svg",permissions:[t.X.Admin],children:[{url:"/billing",text:"SETTINGS.Billing.Title",id:"billing",icon:""}],group:"Manager"}]},7302:(g,u,e)=>{e.d(u,{c:()=>t});const t=[{name:"Admin",value:"Admin",depression:"Full access to your InnoBook account",role:[{text:"Clients",check:!0},{text:"Invoices",check:!0},{text:"Reports",check:!0},{text:"Expenses",check:!0},{text:"Time Tracking",check:!0},{text:"Projects",check:!0},{text:"Payments",check:!0}]},{name:"Manager",value:"Manager",depression:"Full access to your InnoBook account with exceptions",role:[{text:"Clients",check:!0},{text:"Invoices",check:!0},{text:"Reports",check:!0},{text:"Expenses",check:!0},{text:"Time Tracking",check:!0},{text:"Projects",check:!0},{text:"Payments",check:!0}]},{name:"Employee",value:"Employee",depression:"Track their own time and expenses",role:[{text:"Clients",check:!0},{text:"Invoices",check:!0},{text:"Reports",check:!1},{text:"Expenses",check:!1},{text:"Time Tracking",check:!0},{text:"Projects",check:!1},{text:"Payments",check:!0}]},{name:"Contractor",value:"Contractor",depression:"Track their own time and expenses and send you invoices",role:[{text:"Clients",check:!1},{text:"Invoices",check:!0},{text:"Reports",check:!1},{text:"Expenses",check:!1},{text:"Time Tracking",check:!0},{text:"Projects",check:!1},{text:"Payments",check:!1}]},{name:"Accountant(Free)",value:"Accountant",depression:"Access reports, expenses and create journal entries",role:[{text:"Clients",check:!1},{text:"Invoices",check:!0},{text:"Reports",check:!1},{text:"Expenses",check:!0},{text:"Time Tracking",check:!1},{text:"Projects",check:!1},{text:"Payments",check:!1}]}]},7497:(g,u,e)=>{e.d(u,{J:()=>t});const t=[{name:"Admin",value:"Admin",depression:"Full access to your InnoBook account"},{name:"Manager",value:"Manager",depression:"Full access to your InnoBook account with exceptions"},{name:"Employee",value:"Employee",depression:"Track their own time and expenses"},{name:"Contractor",value:"Contractor",depression:"Track their own time and expenses and send you invoices"},{name:"Accountant(Free)",value:"Accountant",depression:"Access reports, expenses and create journal entries"}]},5861:(g,u,e)=>{e.d(u,{A:()=>t,S:()=>n});const t=[{Name:"(UTC-12:00) International Date Line West",Code:"Dateline Standard Time",Moment:["Etc/GMT+12"]},{Name:"(UTC-11:00) Coordinated Universal Time-11",Code:"UTC-11",Moment:["Pacific/Midway"]},{Name:"(UTC-10:00) Hawaii",Code:"Hawaiian Standard Time",Moment:["Pacific/Honolulu"]},{Name:"(UTC-09:00) Alaska",Code:"Alaskan Standard Time",Moment:["America/Anchorage"]},{Name:"(UTC-08:00) Pacific Time (US & Canada)",Code:"Pacific Standard Time",Moment:["America/Los_Angeles"]},{Name:"(UTC-07:00) Mountain Time (US & Canada)",Code:"Mountain Standard Time",Moment:["America/Denver"]},{Name:"(UTC-06:00) Central Time (US & Canada)",Code:"Central Standard Time",Moment:["America/Chicago"]},{Name:"(UTC-05:00) Eastern Time (US & Canada)",Code:"Eastern Standard Time",Moment:["America/New_York"]},{Name:"(UTC-04:00) Atlantic Time (Canada)",Code:"Atlantic Standard Time",Moment:["America/Halifax"]},{Name:"(UTC-03:00) Buenos Aires",Code:"Argentina Standard Time",Moment:["America/Argentina/Buenos_Aires"]},{Name:"(UTC-02:00) Mid-Atlantic",Code:"Mid-Atlantic Standard Time",Moment:["Atlantic/South_Georgia"]},{Name:"(UTC-01:00) Azores",Code:"Azores Standard Time",Moment:["Atlantic/Azores"]},{Name:"(UTC\xb100:00) London, Lisbon",Code:"GMT Standard Time",Moment:["Europe/London"]},{Name:"(UTC+01:00) Brussels, Paris",Code:"Central European Standard Time",Moment:["Europe/Brussels"]},{Name:"(UTC+02:00) Cairo",Code:"Egypt Standard Time",Moment:["Africa/Cairo"]},{Name:"(UTC+03:00) Moscow, Riyadh",Code:"Arab Standard Time",Moment:["Europe/Moscow"]},{Name:"(UTC+04:00) Dubai",Code:"Azerbaijan Standard Time",Moment:["Asia/Dubai"]},{Name:"(UTC+05:00) Pakistan",Code:"Pakistan Standard Time",Moment:["Asia/Karachi"]},{Name:"(UTC+06:00) Almaty",Code:"Central Asia Standard Time",Moment:["Asia/Almaty"]},{Name:"(UTC+07:00) Bangkok",Code:"SE Asia Standard Time",Moment:["Asia/Bangkok"]},{Name:"(UTC+08:00) Beijing",Code:"China Standard Time",Moment:["Asia/Shanghai"]},{Name:"(UTC+09:00) Tokyo",Code:"Tokyo Standard Time",Moment:["Asia/Tokyo"]},{Name:"(UTC+10:00) Sydney",Code:"AUS Eastern Standard Time",Moment:["Australia/Sydney"]},{Name:"(UTC+11:00) Solomon Islands",Code:"Central Pacific Standard Time",Moment:["Pacific/Guadalcanal"]},{Name:"(UTC+12:00) Fiji",Code:"Fiji Standard Time",Moment:["Pacific/Fiji"]}],n={"America/Toronto":"America/New_York","America/Vancouver":"America/Los_Angeles","America/Los_Angeles":"America/Los_Angeles","America/Chicago":"America/Chicago","America/New_York":"America/New_York","America/Anchorage":"America/Anchorage","America/Houston":"America/Chicago","America/Denver":"America/Denver","America/Calgary":"America/Denver","America/Phoenix":"America/Denver","America/Mexico_City":"America/Mexico_City","America/Regina":"America/Chicago","America/Bogota":"America/New_York","Europe/London":"Europe/London","Europe/Paris":"Europe/Paris","Europe/Brussels":"Europe/Brussels","Europe/Berlin":"Europe/Brussels","Europe/Madrid":"Europe/Brussels","Europe/Amsterdam":"Europe/Brussels","Europe/Rome":"Europe/Brussels","Europe/Copenhagen":"Europe/Brussels","Europe/Stockholm":"Europe/Brussels","Europe/Helsinki":"Europe/Brussels","Europe/Istanbul":"Europe/Istanbul","Asia/Tokyo":"Asia/Tokyo","Asia/Seoul":"Asia/Tokyo","Asia/Shanghai":"Asia/Shanghai","Asia/Hong_Kong":"Asia/Shanghai","Asia/Karachi":"Asia/Karachi","Asia/Kolkata":"Asia/Kolkata","Asia/Kathmandu":"Asia/Kolkata","Asia/Jakarta":"Asia/Jakarta","Asia/Ho_Chi_Minh":"Asia/Jakarta","Australia/Sydney":"Australia/Sydney","Australia/Brisbane":"Australia/Sydney","Australia/Melbourne":"Australia/Sydney","Pacific/Guam":"Pacific/Guam","Pacific/Fiji":"Pacific/Fiji","Pacific/Noumea":"Pacific/Fiji","Africa/Cairo":"Africa/Cairo","Africa/Nairobi":"Africa/Nairobi","Africa/Johannesburg":"Africa/Johannesburg","Africa/Lagos":"Africa/Johannesburg","Africa/Khartoum":"Africa/Cairo","Africa/Douala":"Africa/Johannesburg","Asia/Dubai":"Asia/Dubai","Asia/Baku":"Asia/Dubai","Asia/Singapore":"Asia/Singapore","Asia/Bangkok":"Asia/Bangkok","Asia/Kuala_Lumpur":"Asia/Singapore","Atlantic/South_Georgia":"Atlantic/South_Georgia","Atlantic/Azores":"Atlantic/Azores","Asia/Almaty":"Asia/Almaty","Asia/Yekaterinburg":"Asia/Yekaterinburg","Asia/Samara":"Asia/Samara","Africa/Casablanca":"Africa/Casablanca","Africa/Abidjan":"Africa/Casablanca","Africa/Dakar":"Africa/Casablanca"}},4477:(g,u,e)=>{e.d(u,{b:()=>a});var t=e(9842),n=e(4438),o=e(7656),p=e(1328),d=e(6146),l=e(5236);function i(s,r){if(1&s){const c=n.RV6();n.j41(0,"div",8)(1,"button",9),n.bIt("click",function(){n.eBV(c);const h=n.XpG();return n.Njj(h.handleDelete())}),n.nrm(2,"img",10),n.k0s()()}}let a=(()=>{var s;class r{constructor(){(0,t.A)(this,"tax",void 0),(0,t.A)(this,"isHideDeleteButton",!1),(0,t.A)(this,"onSelectedTax",new n.bkB),(0,t.A)(this,"onChange",new n.bkB),(0,t.A)(this,"onDelete",new n.bkB)}ngOnInit(){}handleChangeTax(m,h){Object.keys(this.tax)?.length&&(this.tax[m]="amount"==m&&/^0\d+/.test(h)&&!/^0\.\d+$/.test(h)?h.replace(/^0+/,""):h,"selected"===m&&this.onSelectedTax.emit(h))}handleDelete(){this.onDelete?.emit(this.tax)}}return s=r,(0,t.A)(r,"\u0275fac",function(m){return new(m||s)}),(0,t.A)(r,"\u0275cmp",n.VBU({type:s,selectors:[["app-input-tax"]],inputs:{tax:"tax",isHideDeleteButton:"isHideDeleteButton"},outputs:{onSelectedTax:"onSelectedTax",onChange:"onChange",onDelete:"onDelete"},standalone:!0,features:[n.aNF],decls:16,vars:21,consts:[[1,"overflow-auto"],[1,"layoutGrid"],[1,"w-full","h-full","flex","items-end","pb-[15px]"],[3,"onChange","checked"],[1,"w-full"],[3,"onChange","label","placeholder","value"],["placeholder","0.00","type","number",3,"onChange","label","removeZeros","value"],["type","number",3,"onChange","label","placeholder","value"],[1,"w-full","flex","h-full","items-end","pb-[5px]"],[1,"button-icon",3,"click"],["src","../../../../../../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(m,h){1&m&&(n.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"app-inno-form-checkbox",3),n.bIt("onChange",function(y){return h.handleChangeTax("selected",y)}),n.k0s()(),n.j41(4,"div",4)(5,"app-inno-form-input",5),n.nI1(6,"translate"),n.nI1(7,"translate"),n.bIt("onChange",function(y){return h.handleChangeTax("name",y)}),n.k0s()(),n.j41(8,"div",4)(9,"app-inno-form-input",6),n.nI1(10,"translate"),n.bIt("onChange",function(y){return h.handleChangeTax("amount",y)}),n.k0s()(),n.j41(11,"div",4)(12,"app-inno-form-input",7),n.nI1(13,"translate"),n.nI1(14,"translate"),n.bIt("onChange",function(y){return h.handleChangeTax("taxeNumber",y)}),n.k0s()(),n.DNE(15,i,3,0,"div",8),n.k0s()()),2&m&&(n.R7$(3),n.Y8G("checked",h.tax.selected),n.R7$(2),n.Y8G("label",n.bMT(6,11,"TAX.TaxName"))("placeholder",n.bMT(7,13,"TAX.PlaceholderTaxName"))("value",h.tax.name),n.R7$(4),n.Y8G("label",n.bMT(10,15,"TAX.Rate"))("removeZeros",!0)("value",h.tax.amount),n.R7$(3),n.Y8G("label",n.bMT(13,17,"TAX.TaxNumber"))("placeholder",n.bMT(14,19,"TAX.PlaceholderTaxNumber"))("value",h.tax.taxeNumber),n.R7$(3),n.vxM(h.isHideDeleteButton?-1:15))},dependencies:[d.G,l.D9,p.a,o.V],styles:[".layoutGrid[_ngcontent-%COMP%]{display:grid;grid-template-columns:20px 120px 120px 120px 40px;grid-column-gap:10px}"]})),r})()},6674:(g,u,e)=>{e.d(u,{Ph:()=>C,wB:()=>A});var t=e(4438),o=(e(6402),e(177));let C=(()=>{class D{}return D.\u0275fac=function(M){return new(M||D)},D.\u0275mod=t.$C({type:D}),D.\u0275inj=t.G2t({imports:[[o.MD]]}),D})(),A=(()=>{class D{}return D.\u0275fac=function(M){return new(M||D)},D.\u0275mod=t.$C({type:D}),D.\u0275inj=t.G2t({providers:[],imports:[[o.MD,C],C]}),D})()}}]);