"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6900],{6900:(xe,Q,u)=>{u.r(Q),u.d(Q,{HomeComponent:()=>De});var V=u(9842),bt=u(33),_t=u(6146),ft=u(9115),gt=u(3924),I=u(687),B=u(7987),E=u(6939),W=u(177),a=u(4438),D=u(6860),m=u(6600);const vt=["mat-button",""],yt=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],Dt=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"],xt=["mat-icon-button",""],kt=["*"],At=new a.nKC("MAT_BUTTON_CONFIG"),Ct=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mat-mdc-fab-base","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}];let q=(()=>{var i;class s{get ripple(){return this._rippleLoader?.getRipple(this._elementRef.nativeElement)}set ripple(t){this._rippleLoader?.attachRipple(this._elementRef.nativeElement,t)}get disableRipple(){return this._disableRipple}set disableRipple(t){this._disableRipple=t,this._updateRippleDisabled()}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._updateRippleDisabled()}constructor(t,e,n,o){this._elementRef=t,this._platform=e,this._ngZone=n,this._animationMode=o,this._focusMonitor=(0,a.WQX)(I.FN),this._rippleLoader=(0,a.WQX)(m.Ej),this._isFab=!1,this._disableRipple=!1,this._disabled=!1;const d=(0,a.WQX)(At,{optional:!0}),c=t.nativeElement,_=c.classList;this.disabledInteractive=d?.disabledInteractive??!1,this.color=d?.color??null,this._rippleLoader?.configureRipple(c,{className:"mat-mdc-button-ripple"});for(const{attribute:f,mdcClasses:v}of Ct)c.hasAttribute(f)&&_.add(...v)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(t="program",e){t?this._focusMonitor.focusVia(this._elementRef.nativeElement,t,e):this._elementRef.nativeElement.focus(e)}_getAriaDisabled(){return null!=this.ariaDisabled?this.ariaDisabled:!(!this.disabled||!this.disabledInteractive)||null}_getDisabledAttribute(){return!(this.disabledInteractive||!this.disabled)||null}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}}return(i=s).\u0275fac=function(t){a.QTQ()},i.\u0275dir=a.FsC({type:i,inputs:{color:"color",disableRipple:[2,"disableRipple","disableRipple",a.L39],disabled:[2,"disabled","disabled",a.L39],ariaDisabled:[2,"aria-disabled","ariaDisabled",a.L39],disabledInteractive:[2,"disabledInteractive","disabledInteractive",a.L39]},features:[a.GFd]}),s})(),J=(()=>{var i;class s extends q{constructor(t,e,n,o){super(t,e,n,o)}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.aKT),a.rXU(D.OD),a.rXU(a.SKi),a.rXU(a.bc$,8))},i.\u0275cmp=a.VBU({type:i,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(t,e){2&t&&(a.BMQ("disabled",e._getDisabledAttribute())("aria-disabled",e._getAriaDisabled()),a.HbH(e.color?"mat-"+e.color:""),a.AVh("mat-mdc-button-disabled",e.disabled)("mat-mdc-button-disabled-interactive",e.disabledInteractive)("_mat-animation-noopable","NoopAnimations"===e._animationMode)("mat-unthemed",!e.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[a.Vt3,a.aNF],attrs:vt,ngContentSelectors:Dt,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(t,e){1&t&&(a.NAR(yt),a.nrm(0,"span",0),a.SdG(1),a.j41(2,"span",1),a.SdG(3,1),a.k0s(),a.SdG(4,2),a.nrm(5,"span",2)(6,"span",3)),2&t&&a.AVh("mdc-button__ripple",!e._isFab)("mdc-fab__ripple",e._isFab)},styles:['.mat-mdc-button-base{text-decoration:none}.mdc-button{-webkit-user-select:none;user-select:none;position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0);padding:0 8px}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__label{position:relative}.mat-mdc-button{padding:0 var(--mat-text-button-horizontal-padding, 8px);height:var(--mdc-text-button-container-height);font-family:var(--mdc-text-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-text-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-text-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-text-button-label-text-transform);font-weight:var(--mdc-text-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 8px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color)}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display)}.mat-mdc-button,.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color, var(--mat-app-primary))}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-unelevated-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-filled-button-horizontal-padding, 16px);height:var(--mdc-filled-button-container-height);font-family:var(--mdc-filled-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-filled-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-filled-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-filled-button-label-text-transform);font-weight:var(--mdc-filled-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color)}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color, var(--mat-app-on-primary))}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color, var(--mat-app-on-primary));background-color:var(--mdc-filled-button-container-color, var(--mat-app-primary))}.mat-mdc-unelevated-button,.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color);background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-protected-button-horizontal-padding, 16px);box-shadow:var(--mdc-protected-button-container-elevation-shadow, var(--mat-app-level1));height:var(--mdc-protected-button-container-height);font-family:var(--mdc-protected-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-protected-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-protected-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-protected-button-label-text-transform);font-weight:var(--mdc-protected-button-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color)}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color, var(--mat-app-primary));background-color:var(--mdc-protected-button-container-color, var(--mat-app-surface))}.mat-mdc-raised-button,.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape, var(--mat-app-corner-full))}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow, var(--mat-app-level2))}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow, var(--mat-app-level1))}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow, var(--mat-app-level1))}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color);background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow, var(--mat-app-level0))}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 var(--mat-outlined-button-horizontal-padding, 15px);height:var(--mdc-outlined-button-container-height);font-family:var(--mdc-outlined-button-label-text-font, var(--mat-app-label-large-font));font-size:var(--mdc-outlined-button-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mdc-outlined-button-label-text-tracking, var(--mat-app-label-large-tracking));text-transform:var(--mdc-outlined-button-label-text-transform);font-weight:var(--mdc-outlined-button-label-text-weight, var(--mat-app-label-large-weight));border-radius:var(--mdc-outlined-button-container-shape, var(--mat-app-corner-full));border-width:var(--mdc-outlined-button-outline-width)}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color)}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color, var(--mat-app-primary))}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color, var(--mat-app-primary));border-color:var(--mdc-outlined-button-outline-color, var(--mat-app-outline))}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color);border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button .mdc-button__ripple{border-width:var(--mdc-outlined-button-outline-width);border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-button .mat-icon,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-unelevated-button .mat-icon,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-raised-button .mat-icon,.mat-mdc-outlined-button .mdc-button__label,.mat-mdc-outlined-button .mat-icon{z-index:1;position:relative}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px)*-1)}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0}),s})(),tt=(()=>{var i;class s extends q{constructor(t,e,n,o){super(t,e,n,o),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.aKT),a.rXU(D.OD),a.rXU(a.SKi),a.rXU(a.bc$,8))},i.\u0275cmp=a.VBU({type:i,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(t,e){2&t&&(a.BMQ("disabled",e._getDisabledAttribute())("aria-disabled",e._getAriaDisabled()),a.HbH(e.color?"mat-"+e.color:""),a.AVh("mat-mdc-button-disabled",e.disabled)("mat-mdc-button-disabled-interactive",e.disabledInteractive)("_mat-animation-noopable","NoopAnimations"===e._animationMode)("mat-unthemed",!e.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[a.Vt3,a.aNF],attrs:xt,ngContentSelectors:kt,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(t,e){1&t&&(a.NAR(),a.nrm(0,"span",0),a.SdG(1),a.nrm(2,"span",1)(3,"span",2))},styles:['.mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 48px);height:var(--mdc-icon-button-state-layer-size, 48px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 48px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size);color:var(--mdc-icon-button-icon-color, var(--mat-app-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mdc-icon-button-icon-size);height:var(--mdc-icon-button-icon-size);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color)}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-app-on-surface-variant))}.mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-app-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-app-focus-state-layer-opacity))}.mat-mdc-icon-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-app-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0}),s})(),It=(()=>{var i;class s{}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275mod=a.$C({type:i}),i.\u0275inj=a.G2t({imports:[m.yE,m.pZ,m.yE]}),s})();var Et=u(7333),A=u(1413),g=u(8359),P=u(7673),et=u(7786),l=u(7336),O=u(8203),N=u(9172),p=u(9969),F=(u(9417),u(3719));let Ft=(()=>{var i;class s{}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275mod=a.$C({type:i}),i.\u0275inj=a.G2t({}),s})(),St=(()=>{var i;class s{}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275mod=a.$C({type:i}),i.\u0275inj=a.G2t({imports:[m.yE,F.RG,F.RG,Ft,m.yE]}),s})();const Tt=["mat-calendar-body",""];function Vt(i,s){return this._trackRow(s)}const at=(i,s)=>s.id;function Bt(i,s){if(1&i&&(a.j41(0,"tr",0)(1,"td",3),a.EFF(2),a.k0s()()),2&i){const r=a.XpG();a.R7$(),a.xc7("padding-top",r._cellPadding)("padding-bottom",r._cellPadding),a.BMQ("colspan",r.numCols),a.R7$(),a.SpI(" ",r.label," ")}}function Pt(i,s){if(1&i&&(a.j41(0,"td",3),a.EFF(1),a.k0s()),2&i){const r=a.XpG(2);a.xc7("padding-top",r._cellPadding)("padding-bottom",r._cellPadding),a.BMQ("colspan",r._firstRowOffset),a.R7$(),a.SpI(" ",r._firstRowOffset>=r.labelMinRequiredCells?r.label:""," ")}}function Ot(i,s){if(1&i){const r=a.RV6();a.j41(0,"td",6)(1,"button",7),a.bIt("click",function(e){const n=a.eBV(r).$implicit,o=a.XpG(2);return a.Njj(o._cellClicked(n,e))})("focus",function(e){const n=a.eBV(r).$implicit,o=a.XpG(2);return a.Njj(o._emitActiveDateChange(n,e))}),a.j41(2,"span",8),a.EFF(3),a.k0s(),a.nrm(4,"span",9),a.k0s()()}if(2&i){const r=s.$implicit,t=s.$index,e=a.XpG().$index,n=a.XpG();a.xc7("width",n._cellWidth)("padding-top",n._cellPadding)("padding-bottom",n._cellPadding),a.BMQ("data-mat-row",e)("data-mat-col",t),a.R7$(),a.AVh("mat-calendar-body-disabled",!r.enabled)("mat-calendar-body-active",n._isActiveCell(e,t))("mat-calendar-body-range-start",n._isRangeStart(r.compareValue))("mat-calendar-body-range-end",n._isRangeEnd(r.compareValue))("mat-calendar-body-in-range",n._isInRange(r.compareValue))("mat-calendar-body-comparison-bridge-start",n._isComparisonBridgeStart(r.compareValue,e,t))("mat-calendar-body-comparison-bridge-end",n._isComparisonBridgeEnd(r.compareValue,e,t))("mat-calendar-body-comparison-start",n._isComparisonStart(r.compareValue))("mat-calendar-body-comparison-end",n._isComparisonEnd(r.compareValue))("mat-calendar-body-in-comparison-range",n._isInComparisonRange(r.compareValue))("mat-calendar-body-preview-start",n._isPreviewStart(r.compareValue))("mat-calendar-body-preview-end",n._isPreviewEnd(r.compareValue))("mat-calendar-body-in-preview",n._isInPreview(r.compareValue)),a.Y8G("ngClass",r.cssClasses)("tabindex",n._isActiveCell(e,t)?0:-1),a.BMQ("aria-label",r.ariaLabel)("aria-disabled",!r.enabled||null)("aria-pressed",n._isSelected(r.compareValue))("aria-current",n.todayValue===r.compareValue?"date":null)("aria-describedby",n._getDescribedby(r.compareValue)),a.R7$(),a.AVh("mat-calendar-body-selected",n._isSelected(r.compareValue))("mat-calendar-body-comparison-identical",n._isComparisonIdentical(r.compareValue))("mat-calendar-body-today",n.todayValue===r.compareValue),a.R7$(),a.SpI(" ",r.displayValue," ")}}function Nt(i,s){if(1&i&&(a.j41(0,"tr",1),a.DNE(1,Pt,2,6,"td",4),a.Z7z(2,Ot,5,48,"td",5,at),a.k0s()),2&i){const r=s.$implicit,t=s.$index,e=a.XpG();a.R7$(),a.vxM(0===t&&e._firstRowOffset?1:-1),a.R7$(),a.Dyx(r)}}function Yt(i,s){if(1&i&&(a.j41(0,"th",2)(1,"span",6),a.EFF(2),a.k0s(),a.j41(3,"span",3),a.EFF(4),a.k0s()()),2&i){const r=s.$implicit;a.R7$(2),a.JRh(r.long),a.R7$(2),a.JRh(r.narrow)}}const Lt=["*"];function jt(i,s){}function zt(i,s){if(1&i){const r=a.RV6();a.j41(0,"mat-month-view",4),a.mxI("activeDateChange",function(e){a.eBV(r);const n=a.XpG();return a.DH7(n.activeDate,e)||(n.activeDate=e),a.Njj(e)}),a.bIt("_userSelection",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._dateSelected(e))})("dragStarted",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._dragStarted(e))})("dragEnded",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._dragEnded(e))}),a.k0s()}if(2&i){const r=a.XpG();a.R50("activeDate",r.activeDate),a.Y8G("selected",r.selected)("dateFilter",r.dateFilter)("maxDate",r.maxDate)("minDate",r.minDate)("dateClass",r.dateClass)("comparisonStart",r.comparisonStart)("comparisonEnd",r.comparisonEnd)("startDateAccessibleName",r.startDateAccessibleName)("endDateAccessibleName",r.endDateAccessibleName)("activeDrag",r._activeDrag)}}function Ht(i,s){if(1&i){const r=a.RV6();a.j41(0,"mat-year-view",5),a.mxI("activeDateChange",function(e){a.eBV(r);const n=a.XpG();return a.DH7(n.activeDate,e)||(n.activeDate=e),a.Njj(e)}),a.bIt("monthSelected",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._monthSelectedInYearView(e))})("selectedChange",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._goToDateInView(e,"month"))}),a.k0s()}if(2&i){const r=a.XpG();a.R50("activeDate",r.activeDate),a.Y8G("selected",r.selected)("dateFilter",r.dateFilter)("maxDate",r.maxDate)("minDate",r.minDate)("dateClass",r.dateClass)}}function Ut(i,s){if(1&i){const r=a.RV6();a.j41(0,"mat-multi-year-view",6),a.mxI("activeDateChange",function(e){a.eBV(r);const n=a.XpG();return a.DH7(n.activeDate,e)||(n.activeDate=e),a.Njj(e)}),a.bIt("yearSelected",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._yearSelectedInMultiYearView(e))})("selectedChange",function(e){a.eBV(r);const n=a.XpG();return a.Njj(n._goToDateInView(e,"year"))}),a.k0s()}if(2&i){const r=a.XpG();a.R50("activeDate",r.activeDate),a.Y8G("selected",r.selected)("dateFilter",r.dateFilter)("maxDate",r.maxDate)("minDate",r.minDate)("dateClass",r.dateClass)}}function Gt(i,s){}const Xt=["button"],$t=[[["","matDatepickerToggleIcon",""]]],Kt=["[matDatepickerToggleIcon]"];function Zt(i,s){1&i&&(a.qSk(),a.j41(0,"svg",2),a.nrm(1,"path",3),a.k0s())}let C=(()=>{var i;class s{constructor(){this.changes=new A.B,this.calendarLabel="Calendar",this.openCalendarLabel="Open calendar",this.closeCalendarLabel="Close calendar",this.prevMonthLabel="Previous month",this.nextMonthLabel="Next month",this.prevYearLabel="Previous year",this.nextYearLabel="Next year",this.prevMultiYearLabel="Previous 24 years",this.nextMultiYearLabel="Next 24 years",this.switchToMonthViewLabel="Choose date",this.switchToMultiYearViewLabel="Choose month and year",this.startDateLabel="Start date",this.endDateLabel="End date"}formatYearRange(t,e){return`${t} \u2013 ${e}`}formatYearRangeLabel(t,e){return`${t} to ${e}`}}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275prov=a.jDH({token:i,factory:i.\u0275fac,providedIn:"root"}),s})(),Qt=0;class L{constructor(s,r,t,e,n={},o=s,d){this.value=s,this.displayValue=r,this.ariaLabel=t,this.enabled=e,this.cssClasses=n,this.compareValue=o,this.rawValue=d,this.id=Qt++}}let Wt=1;const nt=(0,D.BQ)({passive:!1,capture:!0}),y=(0,D.BQ)({passive:!0,capture:!0}),R=(0,D.BQ)({passive:!0});let k=(()=>{var i;class s{ngAfterViewChecked(){this._focusActiveCellAfterViewChecked&&(this._focusActiveCell(),this._focusActiveCellAfterViewChecked=!1)}constructor(t,e){this._elementRef=t,this._ngZone=e,this._platform=(0,a.WQX)(D.OD),this._focusActiveCellAfterViewChecked=!1,this.numCols=7,this.activeCell=0,this.isRange=!1,this.cellAspectRatio=1,this.previewStart=null,this.previewEnd=null,this.selectedValueChange=new a.bkB,this.previewChange=new a.bkB,this.activeDateChange=new a.bkB,this.dragStarted=new a.bkB,this.dragEnded=new a.bkB,this._didDragSinceMouseDown=!1,this._injector=(0,a.WQX)(a.zZn),this._trackRow=n=>n,this._enterHandler=n=>{if(this._skipNextFocus&&"focus"===n.type)this._skipNextFocus=!1;else if(n.target&&this.isRange){const o=this._getCellFromElement(n.target);o&&this._ngZone.run(()=>this.previewChange.emit({value:o.enabled?o:null,event:n}))}},this._touchmoveHandler=n=>{if(!this.isRange)return;const o=it(n),d=o?this._getCellFromElement(o):null;o!==n.target&&(this._didDragSinceMouseDown=!0),z(n.target)&&n.preventDefault(),this._ngZone.run(()=>this.previewChange.emit({value:d?.enabled?d:null,event:n}))},this._leaveHandler=n=>{null!==this.previewEnd&&this.isRange&&("blur"!==n.type&&(this._didDragSinceMouseDown=!0),n.target&&this._getCellFromElement(n.target)&&(!n.relatedTarget||!this._getCellFromElement(n.relatedTarget))&&this._ngZone.run(()=>this.previewChange.emit({value:null,event:n})))},this._mousedownHandler=n=>{if(!this.isRange)return;this._didDragSinceMouseDown=!1;const o=n.target&&this._getCellFromElement(n.target);!o||!this._isInRange(o.compareValue)||this._ngZone.run(()=>{this.dragStarted.emit({value:o.rawValue,event:n})})},this._mouseupHandler=n=>{if(!this.isRange)return;const o=z(n.target);o?o.closest(".mat-calendar-body")===this._elementRef.nativeElement&&this._ngZone.run(()=>{const d=this._getCellFromElement(o);this.dragEnded.emit({value:d?.rawValue??null,event:n})}):this._ngZone.run(()=>{this.dragEnded.emit({value:null,event:n})})},this._touchendHandler=n=>{const o=it(n);o&&this._mouseupHandler({target:o})},this._id="mat-calendar-body-"+Wt++,this._startDateLabelId=`${this._id}-start-date`,this._endDateLabelId=`${this._id}-end-date`,e.runOutsideAngular(()=>{const n=t.nativeElement;n.addEventListener("touchmove",this._touchmoveHandler,nt),n.addEventListener("mouseenter",this._enterHandler,y),n.addEventListener("focus",this._enterHandler,y),n.addEventListener("mouseleave",this._leaveHandler,y),n.addEventListener("blur",this._leaveHandler,y),n.addEventListener("mousedown",this._mousedownHandler,R),n.addEventListener("touchstart",this._mousedownHandler,R),this._platform.isBrowser&&(window.addEventListener("mouseup",this._mouseupHandler),window.addEventListener("touchend",this._touchendHandler))})}_cellClicked(t,e){this._didDragSinceMouseDown||t.enabled&&this.selectedValueChange.emit({value:t.value,event:e})}_emitActiveDateChange(t,e){t.enabled&&this.activeDateChange.emit({value:t.value,event:e})}_isSelected(t){return this.startValue===t||this.endValue===t}ngOnChanges(t){const e=t.numCols,{rows:n,numCols:o}=this;(t.rows||e)&&(this._firstRowOffset=n&&n.length&&n[0].length?o-n[0].length:0),(t.cellAspectRatio||e||!this._cellPadding)&&(this._cellPadding=50*this.cellAspectRatio/o+"%"),(e||!this._cellWidth)&&(this._cellWidth=100/o+"%")}ngOnDestroy(){const t=this._elementRef.nativeElement;t.removeEventListener("touchmove",this._touchmoveHandler,nt),t.removeEventListener("mouseenter",this._enterHandler,y),t.removeEventListener("focus",this._enterHandler,y),t.removeEventListener("mouseleave",this._leaveHandler,y),t.removeEventListener("blur",this._leaveHandler,y),t.removeEventListener("mousedown",this._mousedownHandler,R),t.removeEventListener("touchstart",this._mousedownHandler,R),this._platform.isBrowser&&(window.removeEventListener("mouseup",this._mouseupHandler),window.removeEventListener("touchend",this._touchendHandler))}_isActiveCell(t,e){let n=t*this.numCols+e;return t&&(n-=this._firstRowOffset),n==this.activeCell}_focusActiveCell(t=!0){(0,a.mal)(()=>{setTimeout(()=>{const e=this._elementRef.nativeElement.querySelector(".mat-calendar-body-active");e&&(t||(this._skipNextFocus=!0),e.focus())})},{injector:this._injector})}_scheduleFocusActiveCellAfterViewChecked(){this._focusActiveCellAfterViewChecked=!0}_isRangeStart(t){return H(t,this.startValue,this.endValue)}_isRangeEnd(t){return U(t,this.startValue,this.endValue)}_isInRange(t){return G(t,this.startValue,this.endValue,this.isRange)}_isComparisonStart(t){return H(t,this.comparisonStart,this.comparisonEnd)}_isComparisonBridgeStart(t,e,n){if(!this._isComparisonStart(t)||this._isRangeStart(t)||!this._isInRange(t))return!1;let o=this.rows[e][n-1];if(!o){const d=this.rows[e-1];o=d&&d[d.length-1]}return o&&!this._isRangeEnd(o.compareValue)}_isComparisonBridgeEnd(t,e,n){if(!this._isComparisonEnd(t)||this._isRangeEnd(t)||!this._isInRange(t))return!1;let o=this.rows[e][n+1];if(!o){const d=this.rows[e+1];o=d&&d[0]}return o&&!this._isRangeStart(o.compareValue)}_isComparisonEnd(t){return U(t,this.comparisonStart,this.comparisonEnd)}_isInComparisonRange(t){return G(t,this.comparisonStart,this.comparisonEnd,this.isRange)}_isComparisonIdentical(t){return this.comparisonStart===this.comparisonEnd&&t===this.comparisonStart}_isPreviewStart(t){return H(t,this.previewStart,this.previewEnd)}_isPreviewEnd(t){return U(t,this.previewStart,this.previewEnd)}_isInPreview(t){return G(t,this.previewStart,this.previewEnd,this.isRange)}_getDescribedby(t){return this.isRange?this.startValue===t&&this.endValue===t?`${this._startDateLabelId} ${this._endDateLabelId}`:this.startValue===t?this._startDateLabelId:this.endValue===t?this._endDateLabelId:null:null}_getCellFromElement(t){const e=z(t);if(e){const n=e.getAttribute("data-mat-row"),o=e.getAttribute("data-mat-col");if(n&&o)return this.rows[parseInt(n)][parseInt(o)]}return null}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.aKT),a.rXU(a.SKi))},i.\u0275cmp=a.VBU({type:i,selectors:[["","mat-calendar-body",""]],hostAttrs:[1,"mat-calendar-body"],inputs:{label:"label",rows:"rows",todayValue:"todayValue",startValue:"startValue",endValue:"endValue",labelMinRequiredCells:"labelMinRequiredCells",numCols:"numCols",activeCell:"activeCell",isRange:"isRange",cellAspectRatio:"cellAspectRatio",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",previewStart:"previewStart",previewEnd:"previewEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedValueChange:"selectedValueChange",previewChange:"previewChange",activeDateChange:"activeDateChange",dragStarted:"dragStarted",dragEnded:"dragEnded"},exportAs:["matCalendarBody"],standalone:!0,features:[a.OA$,a.aNF],attrs:Tt,decls:7,vars:5,consts:[["aria-hidden","true"],["role","row"],[1,"mat-calendar-body-hidden-label",3,"id"],[1,"mat-calendar-body-label"],[1,"mat-calendar-body-label",3,"paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container",3,"width","paddingTop","paddingBottom"],["role","gridcell",1,"mat-calendar-body-cell-container"],["type","button",1,"mat-calendar-body-cell",3,"click","focus","ngClass","tabindex"],[1,"mat-calendar-body-cell-content","mat-focus-indicator"],["aria-hidden","true",1,"mat-calendar-body-cell-preview"]],template:function(t,e){1&t&&(a.DNE(0,Bt,3,6,"tr",0),a.Z7z(1,Nt,4,1,"tr",1,Vt,!0),a.j41(3,"span",2),a.EFF(4),a.k0s(),a.j41(5,"span",2),a.EFF(6),a.k0s()),2&t&&(a.vxM(e._firstRowOffset<e.labelMinRequiredCells?0:-1),a.R7$(),a.Dyx(e.rows),a.R7$(2),a.Y8G("id",e._startDateLabelId),a.R7$(),a.SpI(" ",e.startDateAccessibleName,"\n"),a.R7$(),a.Y8G("id",e._endDateLabelId),a.R7$(),a.SpI(" ",e.endDateAccessibleName,"\n"))},dependencies:[W.YU],styles:['.mat-calendar-body{min-width:224px}.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-outline-color, var(--mat-app-primary))}.mat-calendar-body-label{height:0;line-height:0;text-align:start;padding-left:4.7142857143%;padding-right:4.7142857143%;font-size:var(--mat-datepicker-calendar-body-label-text-size, var(--mat-app-title-small-size));font-weight:var(--mat-datepicker-calendar-body-label-text-weight, var(--mat-app-title-small-weight));color:var(--mat-datepicker-calendar-body-label-text-color, var(--mat-app-on-surface))}.mat-calendar-body-hidden-label{display:none}.mat-calendar-body-cell-container{position:relative;height:0;line-height:0}.mat-calendar-body-cell{-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);position:absolute;top:0;left:0;width:100%;height:100%;background:none;text-align:center;outline:none;font-family:inherit;margin:0;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-app-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-app-body-medium-size))}.mat-calendar-body-cell::-moz-focus-inner{border:0}.mat-calendar-body-cell::before,.mat-calendar-body-cell::after,.mat-calendar-body-cell-preview{content:"";position:absolute;top:5%;left:0;z-index:0;box-sizing:border-box;display:block;height:90%;width:100%}.mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-start::after,.mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,.mat-calendar-body-comparison-start::after,.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:5%;width:95%;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-range-start:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-start:not(.mat-calendar-body-comparison-bridge-start)::before,[dir=rtl] .mat-calendar-body-comparison-start::after,[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{left:0;border-radius:0;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,.mat-calendar-body-comparison-end::after,.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}[dir=rtl] .mat-calendar-body-range-end:not(.mat-calendar-body-in-comparison-range)::before,[dir=rtl] .mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-end:not(.mat-calendar-body-comparison-bridge-end)::before,[dir=rtl] .mat-calendar-body-comparison-end::after,[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{left:5%;border-radius:0;border-top-left-radius:999px;border-bottom-left-radius:999px}[dir=rtl] .mat-calendar-body-comparison-bridge-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-bridge-end.mat-calendar-body-range-start::after{width:95%;border-top-right-radius:999px;border-bottom-right-radius:999px}.mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,[dir=rtl] .mat-calendar-body-comparison-start.mat-calendar-body-range-end::after,.mat-calendar-body-comparison-end.mat-calendar-body-range-start::after,[dir=rtl] .mat-calendar-body-comparison-end.mat-calendar-body-range-start::after{width:90%}.mat-calendar-body-in-preview{color:var(--mat-datepicker-calendar-date-preview-state-outline-color, var(--mat-app-primary))}.mat-calendar-body-in-preview .mat-calendar-body-cell-preview{border-top:dashed 1px;border-bottom:dashed 1px}.mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:dashed 1px}[dir=rtl] .mat-calendar-body-preview-start .mat-calendar-body-cell-preview{border-left:0;border-right:dashed 1px}.mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:dashed 1px}[dir=rtl] .mat-calendar-body-preview-end .mat-calendar-body-cell-preview{border-right:0;border-left:dashed 1px}.mat-calendar-body-disabled{cursor:default}.mat-calendar-body-disabled>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){color:var(--mat-datepicker-calendar-date-disabled-state-text-color)}.mat-calendar-body-disabled>.mat-calendar-body-today:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:var(--mat-datepicker-calendar-date-today-disabled-state-outline-color)}.cdk-high-contrast-active .mat-calendar-body-disabled{opacity:.5}.mat-calendar-body-cell-content{top:5%;left:5%;z-index:1;display:flex;align-items:center;justify-content:center;box-sizing:border-box;width:90%;height:90%;line-height:1;border-width:1px;border-style:solid;border-radius:999px;color:var(--mat-datepicker-calendar-date-text-color, var(--mat-app-on-surface));border-color:var(--mat-datepicker-calendar-date-outline-color)}.mat-calendar-body-cell-content.mat-focus-indicator{position:absolute}.cdk-high-contrast-active .mat-calendar-body-cell-content{border:none}.cdk-keyboard-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical),.cdk-program-focused .mat-calendar-body-active>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-focus-state-background-color)}@media(hover: hover){.mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:var(--mat-datepicker-calendar-date-hover-state-background-color)}}.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-state-background-color, var(--mat-app-primary));color:var(--mat-datepicker-calendar-date-selected-state-text-color, var(--mat-app-on-primary))}.mat-calendar-body-disabled>.mat-calendar-body-selected{background-color:var(--mat-datepicker-calendar-date-selected-disabled-state-background-color)}.mat-calendar-body-selected.mat-calendar-body-today{box-shadow:inset 0 0 0 1px var(--mat-datepicker-calendar-date-today-selected-state-outline-color, var(--mat-app-primary))}.mat-calendar-body-in-range::before{background:var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-app-primary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-app-tertiary-container))}.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range::before{background:var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-app-tertiary-container))}.mat-calendar-body-comparison-bridge-start::before,[dir=rtl] .mat-calendar-body-comparison-bridge-end::before{background:linear-gradient(to right, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-app-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-app-tertiary-container)) 50%)}.mat-calendar-body-comparison-bridge-end::before,[dir=rtl] .mat-calendar-body-comparison-bridge-start::before{background:linear-gradient(to left, var(--mat-datepicker-calendar-date-in-range-state-background-color, var(--mat-app-primary-container)) 50%, var(--mat-datepicker-calendar-date-in-comparison-range-state-background-color, var(--mat-app-tertiary-container)) 50%)}.mat-calendar-body-in-range>.mat-calendar-body-comparison-identical,.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range::after{background:var(--mat-datepicker-calendar-date-in-overlap-range-state-background-color, var(--mat-app-secondary-container))}.mat-calendar-body-comparison-identical.mat-calendar-body-selected,.mat-calendar-body-in-comparison-range>.mat-calendar-body-selected{background:var(--mat-datepicker-calendar-date-in-overlap-range-selected-state-background-color, var(--mat-app-secondary))}.cdk-high-contrast-active .mat-datepicker-popup:not(:empty),.cdk-high-contrast-active .mat-calendar-body-cell:not(.mat-calendar-body-in-range) .mat-calendar-body-selected{outline:solid 1px}.cdk-high-contrast-active .mat-calendar-body-today{outline:dotted 1px}.cdk-high-contrast-active .mat-calendar-body-cell::before,.cdk-high-contrast-active .mat-calendar-body-cell::after,.cdk-high-contrast-active .mat-calendar-body-selected{background:none}.cdk-high-contrast-active .mat-calendar-body-in-range::before,.cdk-high-contrast-active .mat-calendar-body-comparison-bridge-start::before,.cdk-high-contrast-active .mat-calendar-body-comparison-bridge-end::before{border-top:solid 1px;border-bottom:solid 1px}.cdk-high-contrast-active .mat-calendar-body-range-start::before{border-left:solid 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-range-start::before{border-left:0;border-right:solid 1px}.cdk-high-contrast-active .mat-calendar-body-range-end::before{border-right:solid 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-range-end::before{border-right:0;border-left:solid 1px}.cdk-high-contrast-active .mat-calendar-body-in-comparison-range::before{border-top:dashed 1px;border-bottom:dashed 1px}.cdk-high-contrast-active .mat-calendar-body-comparison-start::before{border-left:dashed 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-comparison-start::before{border-left:0;border-right:dashed 1px}.cdk-high-contrast-active .mat-calendar-body-comparison-end::before{border-right:dashed 1px}[dir=rtl] .cdk-high-contrast-active .mat-calendar-body-comparison-end::before{border-right:0;border-left:dashed 1px}'],encapsulation:2,changeDetection:0}),s})();function j(i){return"TD"===i?.nodeName}function z(i){let s;return j(i)?s=i:j(i.parentNode)?s=i.parentNode:j(i.parentNode?.parentNode)&&(s=i.parentNode.parentNode),null!=s?.getAttribute("data-mat-row")?s:null}function H(i,s,r){return null!==r&&s!==r&&i<r&&i===s}function U(i,s,r){return null!==s&&s!==r&&i>=s&&i===r}function G(i,s,r,t){return t&&null!==s&&null!==r&&s!==r&&i>=s&&i<=r}function it(i){const s=i.changedTouches[0];return document.elementFromPoint(s.clientX,s.clientY)}class h{constructor(s,r){this.start=s,this.end=r}}let M=(()=>{var i;class s{constructor(t,e){this.selection=t,this._adapter=e,this._selectionChanged=new A.B,this.selectionChanged=this._selectionChanged,this.selection=t}updateSelection(t,e){const n=this.selection;this.selection=t,this._selectionChanged.next({selection:t,source:e,oldValue:n})}ngOnDestroy(){this._selectionChanged.complete()}_isValidDateInstance(t){return this._adapter.isDateInstance(t)&&this._adapter.isValid(t)}}return(i=s).\u0275fac=function(t){a.QTQ()},i.\u0275prov=a.jDH({token:i,factory:i.\u0275fac}),s})(),qt=(()=>{var i;class s extends M{constructor(t){super(null,t)}add(t){super.updateSelection(t,this)}isValid(){return null!=this.selection&&this._isValidDateInstance(this.selection)}isComplete(){return null!=this.selection}clone(){const t=new s(this._adapter);return t.updateSelection(this.selection,this),t}}return(i=s).\u0275fac=function(t){return new(t||i)(a.KVO(m.MJ))},i.\u0275prov=a.jDH({token:i,factory:i.\u0275fac}),s})();const ee={provide:M,deps:[[new a.Xx1,new a.kdw,M],m.MJ],useFactory:function te(i,s){return i||new qt(s)}},S=new a.nKC("MAT_DATE_RANGE_SELECTION_STRATEGY");let re=0,rt=(()=>{var i;class s{get activeDate(){return this._activeDate}set activeDate(t){const e=this._activeDate,n=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(n,this.minDate,this.maxDate),this._hasSameMonthAndYear(e,this._activeDate)||this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof h?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setRanges(this._selected)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,n,o,d){this._changeDetectorRef=t,this._dateFormats=e,this._dateAdapter=n,this._dir=o,this._rangeStrategy=d,this._rerenderSubscription=g.yU.EMPTY,this.activeDrag=null,this.selectedChange=new a.bkB,this._userSelection=new a.bkB,this.dragStarted=new a.bkB,this.dragEnded=new a.bkB,this.activeDateChange=new a.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,N.Z)(null)).subscribe(()=>this._init())}ngOnChanges(t){const e=t.comparisonStart||t.comparisonEnd;e&&!e.firstChange&&this._setRanges(this.selected),t.activeDrag&&!this.activeDrag&&this._clearPreview()}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_dateSelected(t){const e=t.value,n=this._getDateFromDayOfMonth(e);let o,d;this._selected instanceof h?(o=this._getDateInCurrentMonth(this._selected.start),d=this._getDateInCurrentMonth(this._selected.end)):o=d=this._getDateInCurrentMonth(this._selected),(o!==e||d!==e)&&this.selectedChange.emit(n),this._userSelection.emit({value:n,event:t.event}),this._clearPreview(),this._changeDetectorRef.markForCheck()}_updateActiveDate(t){const n=this._activeDate;this.activeDate=this._getDateFromDayOfMonth(t.value),this._dateAdapter.compareDate(n,this.activeDate)&&this.activeDateChange.emit(this._activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,n=this._isRtl();switch(t.keyCode){case l.UQ:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,n?1:-1);break;case l.LE:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,n?-1:1);break;case l.i7:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,-7);break;case l.n6:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,7);break;case l.yZ:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,1-this._dateAdapter.getDate(this._activeDate));break;case l.Kp:this.activeDate=this._dateAdapter.addCalendarDays(this._activeDate,this._dateAdapter.getNumDaysInMonth(this._activeDate)-this._dateAdapter.getDate(this._activeDate));break;case l.w_:this.activeDate=t.altKey?this._dateAdapter.addCalendarYears(this._activeDate,-1):this._dateAdapter.addCalendarMonths(this._activeDate,-1);break;case l.dB:this.activeDate=t.altKey?this._dateAdapter.addCalendarYears(this._activeDate,1):this._dateAdapter.addCalendarMonths(this._activeDate,1);break;case l.Fm:case l.t6:return this._selectionKeyPressed=!0,void(this._canSelect(this._activeDate)&&t.preventDefault());case l._f:return void(null!=this._previewEnd&&!(0,l.rp)(t)&&(this._clearPreview(),this.activeDrag?this.dragEnded.emit({value:null,event:t}):(this.selectedChange.emit(null),this._userSelection.emit({value:null,event:t})),t.preventDefault(),t.stopPropagation()));default:return}this._dateAdapter.compareDate(e,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===l.t6||t.keyCode===l.Fm)&&(this._selectionKeyPressed&&this._canSelect(this._activeDate)&&this._dateSelected({value:this._dateAdapter.getDate(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_init(){this._setRanges(this.selected),this._todayDate=this._getCellCompareValue(this._dateAdapter.today()),this._monthLabel=this._dateFormats.display.monthLabel?this._dateAdapter.format(this.activeDate,this._dateFormats.display.monthLabel):this._dateAdapter.getMonthNames("short")[this._dateAdapter.getMonth(this.activeDate)].toLocaleUpperCase();let t=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),1);this._firstWeekOffset=(7+this._dateAdapter.getDayOfWeek(t)-this._dateAdapter.getFirstDayOfWeek())%7,this._initWeekdays(),this._createWeekCells(),this._changeDetectorRef.markForCheck()}_focusActiveCell(t){this._matCalendarBody._focusActiveCell(t)}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_previewChanged({event:t,value:e}){if(this._rangeStrategy){const n=e?e.rawValue:null,o=this._rangeStrategy.createPreview(n,this.selected,t);if(this._previewStart=this._getCellCompareValue(o.start),this._previewEnd=this._getCellCompareValue(o.end),this.activeDrag&&n){const d=this._rangeStrategy.createDrag?.(this.activeDrag.value,this.selected,n,t);d&&(this._previewStart=this._getCellCompareValue(d.start),this._previewEnd=this._getCellCompareValue(d.end))}this._changeDetectorRef.detectChanges()}}_dragEnded(t){if(this.activeDrag)if(t.value){const e=this._rangeStrategy?.createDrag?.(this.activeDrag.value,this.selected,t.value,t.event);this.dragEnded.emit({value:e??null,event:t.event})}else this.dragEnded.emit({value:null,event:t.event})}_getDateFromDayOfMonth(t){return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),t)}_initWeekdays(){const t=this._dateAdapter.getFirstDayOfWeek(),e=this._dateAdapter.getDayOfWeekNames("narrow");let o=this._dateAdapter.getDayOfWeekNames("long").map((d,c)=>({long:d,narrow:e[c],id:re++}));this._weekdays=o.slice(t).concat(o.slice(0,t))}_createWeekCells(){const t=this._dateAdapter.getNumDaysInMonth(this.activeDate),e=this._dateAdapter.getDateNames();this._weeks=[[]];for(let n=0,o=this._firstWeekOffset;n<t;n++,o++){7==o&&(this._weeks.push([]),o=0);const d=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),this._dateAdapter.getMonth(this.activeDate),n+1),c=this._shouldEnableDate(d),_=this._dateAdapter.format(d,this._dateFormats.display.dateA11yLabel),f=this.dateClass?this.dateClass(d,"month"):void 0;this._weeks[this._weeks.length-1].push(new L(n+1,e[n],_,c,f,this._getCellCompareValue(d),d))}}_shouldEnableDate(t){return!!t&&(!this.minDate||this._dateAdapter.compareDate(t,this.minDate)>=0)&&(!this.maxDate||this._dateAdapter.compareDate(t,this.maxDate)<=0)&&(!this.dateFilter||this.dateFilter(t))}_getDateInCurrentMonth(t){return t&&this._hasSameMonthAndYear(t,this.activeDate)?this._dateAdapter.getDate(t):null}_hasSameMonthAndYear(t,e){return!(!t||!e||this._dateAdapter.getMonth(t)!=this._dateAdapter.getMonth(e)||this._dateAdapter.getYear(t)!=this._dateAdapter.getYear(e))}_getCellCompareValue(t){if(t){const e=this._dateAdapter.getYear(t),n=this._dateAdapter.getMonth(t),o=this._dateAdapter.getDate(t);return new Date(e,n,o).getTime()}return null}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setRanges(t){t instanceof h?(this._rangeStart=this._getCellCompareValue(t.start),this._rangeEnd=this._getCellCompareValue(t.end),this._isRange=!0):(this._rangeStart=this._rangeEnd=this._getCellCompareValue(t),this._isRange=!1),this._comparisonRangeStart=this._getCellCompareValue(this.comparisonStart),this._comparisonRangeEnd=this._getCellCompareValue(this.comparisonEnd)}_canSelect(t){return!this.dateFilter||this.dateFilter(t)}_clearPreview(){this._previewStart=this._previewEnd=null}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.gRc),a.rXU(m.de,8),a.rXU(m.MJ,8),a.rXU(O.dS,8),a.rXU(S,8))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-month-view"]],viewQuery:function(t,e){if(1&t&&a.GBs(k,5),2&t){let n;a.mGM(n=a.lsd())&&(e._matCalendarBody=n.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName",activeDrag:"activeDrag"},outputs:{selectedChange:"selectedChange",_userSelection:"_userSelection",dragStarted:"dragStarted",dragEnded:"dragEnded",activeDateChange:"activeDateChange"},exportAs:["matMonthView"],standalone:!0,features:[a.OA$,a.aNF],decls:8,vars:14,consts:[["role","grid",1,"mat-calendar-table"],[1,"mat-calendar-table-header"],["scope","col"],["aria-hidden","true"],["colspan","7",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","previewChange","dragStarted","dragEnded","keyup","keydown","label","rows","todayValue","startValue","endValue","comparisonStart","comparisonEnd","previewStart","previewEnd","isRange","labelMinRequiredCells","activeCell","startDateAccessibleName","endDateAccessibleName"],[1,"cdk-visually-hidden"]],template:function(t,e){1&t&&(a.j41(0,"table",0)(1,"thead",1)(2,"tr"),a.Z7z(3,Yt,5,2,"th",2,at),a.k0s(),a.j41(5,"tr",3),a.nrm(6,"th",4),a.k0s()(),a.j41(7,"tbody",5),a.bIt("selectedValueChange",function(o){return e._dateSelected(o)})("activeDateChange",function(o){return e._updateActiveDate(o)})("previewChange",function(o){return e._previewChanged(o)})("dragStarted",function(o){return e.dragStarted.emit(o)})("dragEnded",function(o){return e._dragEnded(o)})("keyup",function(o){return e._handleCalendarBodyKeyup(o)})("keydown",function(o){return e._handleCalendarBodyKeydown(o)}),a.k0s()()),2&t&&(a.R7$(3),a.Dyx(e._weekdays),a.R7$(4),a.Y8G("label",e._monthLabel)("rows",e._weeks)("todayValue",e._todayDate)("startValue",e._rangeStart)("endValue",e._rangeEnd)("comparisonStart",e._comparisonRangeStart)("comparisonEnd",e._comparisonRangeEnd)("previewStart",e._previewStart)("previewEnd",e._previewEnd)("isRange",e._isRange)("labelMinRequiredCells",3)("activeCell",e._dateAdapter.getDate(e.activeDate)-1)("startDateAccessibleName",e.startDateAccessibleName)("endDateAccessibleName",e.endDateAccessibleName))},dependencies:[k],encapsulation:2,changeDetection:0}),s})();const b=24;let ot=(()=>{var i;class s{get activeDate(){return this._activeDate}set activeDate(t){let e=this._activeDate;const n=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(n,this.minDate,this.maxDate),st(this._dateAdapter,e,this._activeDate,this.minDate,this.maxDate)||this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof h?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setSelectedYear(t)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,n){this._changeDetectorRef=t,this._dateAdapter=e,this._dir=n,this._rerenderSubscription=g.yU.EMPTY,this.selectedChange=new a.bkB,this.yearSelected=new a.bkB,this.activeDateChange=new a.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,N.Z)(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_init(){this._todayYear=this._dateAdapter.getYear(this._dateAdapter.today());const e=this._dateAdapter.getYear(this._activeDate)-w(this._dateAdapter,this.activeDate,this.minDate,this.maxDate);this._years=[];for(let n=0,o=[];n<b;n++)o.push(e+n),4==o.length&&(this._years.push(o.map(d=>this._createCellForYear(d))),o=[]);this._changeDetectorRef.markForCheck()}_yearSelected(t){const e=t.value,n=this._dateAdapter.createDate(e,0,1),o=this._getDateFromYear(e);this.yearSelected.emit(n),this.selectedChange.emit(o)}_updateActiveDate(t){const n=this._activeDate;this.activeDate=this._getDateFromYear(t.value),this._dateAdapter.compareDate(n,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,n=this._isRtl();switch(t.keyCode){case l.UQ:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,n?1:-1);break;case l.LE:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,n?-1:1);break;case l.i7:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-4);break;case l.n6:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,4);break;case l.yZ:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,-w(this._dateAdapter,this.activeDate,this.minDate,this.maxDate));break;case l.Kp:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,b-w(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)-1);break;case l.w_:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10*-b:-b);break;case l.dB:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10*b:b);break;case l.Fm:case l.t6:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(e,this.activeDate)&&this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked(),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===l.t6||t.keyCode===l.Fm)&&(this._selectionKeyPressed&&this._yearSelected({value:this._dateAdapter.getYear(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_getActiveCell(){return w(this._dateAdapter,this.activeDate,this.minDate,this.maxDate)}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getDateFromYear(t){const e=this._dateAdapter.getMonth(this.activeDate),n=this._dateAdapter.getNumDaysInMonth(this._dateAdapter.createDate(t,e,1));return this._dateAdapter.createDate(t,e,Math.min(this._dateAdapter.getDate(this.activeDate),n))}_createCellForYear(t){const e=this._dateAdapter.createDate(t,0,1),n=this._dateAdapter.getYearName(e),o=this.dateClass?this.dateClass(e,"multi-year"):void 0;return new L(t,n,n,this._shouldEnableYear(t),o)}_shouldEnableYear(t){if(null==t||this.maxDate&&t>this._dateAdapter.getYear(this.maxDate)||this.minDate&&t<this._dateAdapter.getYear(this.minDate))return!1;if(!this.dateFilter)return!0;for(let n=this._dateAdapter.createDate(t,0,1);this._dateAdapter.getYear(n)==t;n=this._dateAdapter.addCalendarDays(n,1))if(this.dateFilter(n))return!0;return!1}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setSelectedYear(t){if(this._selectedYear=null,t instanceof h){const e=t.start||t.end;e&&(this._selectedYear=this._dateAdapter.getYear(e))}else t&&(this._selectedYear=this._dateAdapter.getYear(t))}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.gRc),a.rXU(m.MJ,8),a.rXU(O.dS,8))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-multi-year-view"]],viewQuery:function(t,e){if(1&t&&a.GBs(k,5),2&t){let n;a.mGM(n=a.lsd())&&(e._matCalendarBody=n.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",activeDateChange:"activeDateChange"},exportAs:["matMultiYearView"],standalone:!0,features:[a.aNF],decls:5,vars:7,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","rows","todayValue","startValue","endValue","numCols","cellAspectRatio","activeCell"]],template:function(t,e){1&t&&(a.j41(0,"table",0)(1,"thead",1)(2,"tr"),a.nrm(3,"th",2),a.k0s()(),a.j41(4,"tbody",3),a.bIt("selectedValueChange",function(o){return e._yearSelected(o)})("activeDateChange",function(o){return e._updateActiveDate(o)})("keyup",function(o){return e._handleCalendarBodyKeyup(o)})("keydown",function(o){return e._handleCalendarBodyKeydown(o)}),a.k0s()()),2&t&&(a.R7$(4),a.Y8G("rows",e._years)("todayValue",e._todayYear)("startValue",e._selectedYear)("endValue",e._selectedYear)("numCols",4)("cellAspectRatio",4/7)("activeCell",e._getActiveCell()))},dependencies:[k],encapsulation:2,changeDetection:0}),s})();function st(i,s,r,t,e){const n=i.getYear(s),o=i.getYear(r),d=dt(i,t,e);return Math.floor((n-d)/b)===Math.floor((o-d)/b)}function w(i,s,r,t){return function oe(i,s){return(i%s+s)%s}(i.getYear(s)-dt(i,r,t),b)}function dt(i,s,r){let t=0;return r?t=i.getYear(r)-b+1:s&&(t=i.getYear(s)),t}let ct=(()=>{var i;class s{get activeDate(){return this._activeDate}set activeDate(t){let e=this._activeDate;const n=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))||this._dateAdapter.today();this._activeDate=this._dateAdapter.clampDate(n,this.minDate,this.maxDate),this._dateAdapter.getYear(e)!==this._dateAdapter.getYear(this._activeDate)&&this._init()}get selected(){return this._selected}set selected(t){this._selected=t instanceof h?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t)),this._setSelectedMonth(t)}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}constructor(t,e,n,o){this._changeDetectorRef=t,this._dateFormats=e,this._dateAdapter=n,this._dir=o,this._rerenderSubscription=g.yU.EMPTY,this.selectedChange=new a.bkB,this.monthSelected=new a.bkB,this.activeDateChange=new a.bkB,this._activeDate=this._dateAdapter.today()}ngAfterContentInit(){this._rerenderSubscription=this._dateAdapter.localeChanges.pipe((0,N.Z)(null)).subscribe(()=>this._init())}ngOnDestroy(){this._rerenderSubscription.unsubscribe()}_monthSelected(t){const e=t.value,n=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),e,1);this.monthSelected.emit(n);const o=this._getDateFromMonth(e);this.selectedChange.emit(o)}_updateActiveDate(t){const n=this._activeDate;this.activeDate=this._getDateFromMonth(t.value),this._dateAdapter.compareDate(n,this.activeDate)&&this.activeDateChange.emit(this.activeDate)}_handleCalendarBodyKeydown(t){const e=this._activeDate,n=this._isRtl();switch(t.keyCode){case l.UQ:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,n?1:-1);break;case l.LE:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,n?-1:1);break;case l.i7:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-4);break;case l.n6:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,4);break;case l.yZ:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,-this._dateAdapter.getMonth(this._activeDate));break;case l.Kp:this.activeDate=this._dateAdapter.addCalendarMonths(this._activeDate,11-this._dateAdapter.getMonth(this._activeDate));break;case l.w_:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?-10:-1);break;case l.dB:this.activeDate=this._dateAdapter.addCalendarYears(this._activeDate,t.altKey?10:1);break;case l.Fm:case l.t6:this._selectionKeyPressed=!0;break;default:return}this._dateAdapter.compareDate(e,this.activeDate)&&(this.activeDateChange.emit(this.activeDate),this._focusActiveCellAfterViewChecked()),t.preventDefault()}_handleCalendarBodyKeyup(t){(t.keyCode===l.t6||t.keyCode===l.Fm)&&(this._selectionKeyPressed&&this._monthSelected({value:this._dateAdapter.getMonth(this._activeDate),event:t}),this._selectionKeyPressed=!1)}_init(){this._setSelectedMonth(this.selected),this._todayMonth=this._getMonthInCurrentYear(this._dateAdapter.today()),this._yearLabel=this._dateAdapter.getYearName(this.activeDate);let t=this._dateAdapter.getMonthNames("short");this._months=[[0,1,2,3],[4,5,6,7],[8,9,10,11]].map(e=>e.map(n=>this._createCellForMonth(n,t[n]))),this._changeDetectorRef.markForCheck()}_focusActiveCell(){this._matCalendarBody._focusActiveCell()}_focusActiveCellAfterViewChecked(){this._matCalendarBody._scheduleFocusActiveCellAfterViewChecked()}_getMonthInCurrentYear(t){return t&&this._dateAdapter.getYear(t)==this._dateAdapter.getYear(this.activeDate)?this._dateAdapter.getMonth(t):null}_getDateFromMonth(t){const e=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,1),n=this._dateAdapter.getNumDaysInMonth(e);return this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,Math.min(this._dateAdapter.getDate(this.activeDate),n))}_createCellForMonth(t,e){const n=this._dateAdapter.createDate(this._dateAdapter.getYear(this.activeDate),t,1),o=this._dateAdapter.format(n,this._dateFormats.display.monthYearA11yLabel),d=this.dateClass?this.dateClass(n,"year"):void 0;return new L(t,e.toLocaleUpperCase(),o,this._shouldEnableMonth(t),d)}_shouldEnableMonth(t){const e=this._dateAdapter.getYear(this.activeDate);if(null==t||this._isYearAndMonthAfterMaxDate(e,t)||this._isYearAndMonthBeforeMinDate(e,t))return!1;if(!this.dateFilter)return!0;for(let o=this._dateAdapter.createDate(e,t,1);this._dateAdapter.getMonth(o)==t;o=this._dateAdapter.addCalendarDays(o,1))if(this.dateFilter(o))return!0;return!1}_isYearAndMonthAfterMaxDate(t,e){if(this.maxDate){const n=this._dateAdapter.getYear(this.maxDate),o=this._dateAdapter.getMonth(this.maxDate);return t>n||t===n&&e>o}return!1}_isYearAndMonthBeforeMinDate(t,e){if(this.minDate){const n=this._dateAdapter.getYear(this.minDate),o=this._dateAdapter.getMonth(this.minDate);return t<n||t===n&&e<o}return!1}_isRtl(){return this._dir&&"rtl"===this._dir.value}_setSelectedMonth(t){this._selectedMonth=t instanceof h?this._getMonthInCurrentYear(t.start)||this._getMonthInCurrentYear(t.end):this._getMonthInCurrentYear(t)}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.gRc),a.rXU(m.de,8),a.rXU(m.MJ,8),a.rXU(O.dS,8))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-year-view"]],viewQuery:function(t,e){if(1&t&&a.GBs(k,5),2&t){let n;a.mGM(n=a.lsd())&&(e._matCalendarBody=n.first)}},inputs:{activeDate:"activeDate",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass"},outputs:{selectedChange:"selectedChange",monthSelected:"monthSelected",activeDateChange:"activeDateChange"},exportAs:["matYearView"],standalone:!0,features:[a.aNF],decls:5,vars:9,consts:[["role","grid",1,"mat-calendar-table"],["aria-hidden","true",1,"mat-calendar-table-header"],["colspan","4",1,"mat-calendar-table-header-divider"],["mat-calendar-body","",3,"selectedValueChange","activeDateChange","keyup","keydown","label","rows","todayValue","startValue","endValue","labelMinRequiredCells","numCols","cellAspectRatio","activeCell"]],template:function(t,e){1&t&&(a.j41(0,"table",0)(1,"thead",1)(2,"tr"),a.nrm(3,"th",2),a.k0s()(),a.j41(4,"tbody",3),a.bIt("selectedValueChange",function(o){return e._monthSelected(o)})("activeDateChange",function(o){return e._updateActiveDate(o)})("keyup",function(o){return e._handleCalendarBodyKeyup(o)})("keydown",function(o){return e._handleCalendarBodyKeydown(o)}),a.k0s()()),2&t&&(a.R7$(4),a.Y8G("label",e._yearLabel)("rows",e._months)("todayValue",e._todayMonth)("startValue",e._selectedMonth)("endValue",e._selectedMonth)("labelMinRequiredCells",2)("numCols",4)("cellAspectRatio",4/7)("activeCell",e._dateAdapter.getMonth(e.activeDate)))},dependencies:[k],encapsulation:2,changeDetection:0}),s})(),se=1,lt=(()=>{var i;class s{constructor(t,e,n,o,d){this._intl=t,this.calendar=e,this._dateAdapter=n,this._dateFormats=o,this._id="mat-calendar-header-"+se++,this._periodButtonLabelId=`${this._id}-period-label`,this.calendar.stateChanges.subscribe(()=>d.markForCheck())}get periodButtonText(){return"month"==this.calendar.currentView?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():"year"==this.calendar.currentView?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRange(...this._formatMinAndMaxYearLabels())}get periodButtonDescription(){return"month"==this.calendar.currentView?this._dateAdapter.format(this.calendar.activeDate,this._dateFormats.display.monthYearLabel).toLocaleUpperCase():"year"==this.calendar.currentView?this._dateAdapter.getYearName(this.calendar.activeDate):this._intl.formatYearRangeLabel(...this._formatMinAndMaxYearLabels())}get periodButtonLabel(){return"month"==this.calendar.currentView?this._intl.switchToMultiYearViewLabel:this._intl.switchToMonthViewLabel}get prevButtonLabel(){return{month:this._intl.prevMonthLabel,year:this._intl.prevYearLabel,"multi-year":this._intl.prevMultiYearLabel}[this.calendar.currentView]}get nextButtonLabel(){return{month:this._intl.nextMonthLabel,year:this._intl.nextYearLabel,"multi-year":this._intl.nextMultiYearLabel}[this.calendar.currentView]}currentPeriodClicked(){this.calendar.currentView="month"==this.calendar.currentView?"multi-year":"month"}previousClicked(){this.calendar.activeDate="month"==this.calendar.currentView?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,-1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,"year"==this.calendar.currentView?-1:-b)}nextClicked(){this.calendar.activeDate="month"==this.calendar.currentView?this._dateAdapter.addCalendarMonths(this.calendar.activeDate,1):this._dateAdapter.addCalendarYears(this.calendar.activeDate,"year"==this.calendar.currentView?1:b)}previousEnabled(){return!this.calendar.minDate||!this.calendar.minDate||!this._isSameView(this.calendar.activeDate,this.calendar.minDate)}nextEnabled(){return!this.calendar.maxDate||!this._isSameView(this.calendar.activeDate,this.calendar.maxDate)}_isSameView(t,e){return"month"==this.calendar.currentView?this._dateAdapter.getYear(t)==this._dateAdapter.getYear(e)&&this._dateAdapter.getMonth(t)==this._dateAdapter.getMonth(e):"year"==this.calendar.currentView?this._dateAdapter.getYear(t)==this._dateAdapter.getYear(e):st(this._dateAdapter,t,e,this.calendar.minDate,this.calendar.maxDate)}_formatMinAndMaxYearLabels(){const e=this._dateAdapter.getYear(this.calendar.activeDate)-w(this._dateAdapter,this.calendar.activeDate,this.calendar.minDate,this.calendar.maxDate),n=e+b-1;return[this._dateAdapter.getYearName(this._dateAdapter.createDate(e,0,1)),this._dateAdapter.getYearName(this._dateAdapter.createDate(n,0,1))]}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(C),a.rXU((0,a.Rfq)(()=>K)),a.rXU(m.MJ,8),a.rXU(m.de,8),a.rXU(a.gRc))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-calendar-header"]],exportAs:["matCalendarHeader"],standalone:!0,features:[a.aNF],ngContentSelectors:Lt,decls:13,vars:11,consts:[[1,"mat-calendar-header"],[1,"mat-calendar-controls"],["aria-live","polite",1,"cdk-visually-hidden",3,"id"],["mat-button","","type","button",1,"mat-calendar-period-button",3,"click"],["aria-hidden","true"],["viewBox","0 0 10 5","focusable","false","aria-hidden","true",1,"mat-calendar-arrow"],["points","0,0 5,5 10,0"],[1,"mat-calendar-spacer"],["mat-icon-button","","type","button",1,"mat-calendar-previous-button",3,"click","disabled"],["mat-icon-button","","type","button",1,"mat-calendar-next-button",3,"click","disabled"]],template:function(t,e){1&t&&(a.NAR(),a.j41(0,"div",0)(1,"div",1)(2,"span",2),a.EFF(3),a.k0s(),a.j41(4,"button",3),a.bIt("click",function(){return e.currentPeriodClicked()}),a.j41(5,"span",4),a.EFF(6),a.k0s(),a.qSk(),a.j41(7,"svg",5),a.nrm(8,"polygon",6),a.k0s()(),a.joV(),a.nrm(9,"div",7),a.SdG(10),a.j41(11,"button",8),a.bIt("click",function(){return e.previousClicked()}),a.k0s(),a.j41(12,"button",9),a.bIt("click",function(){return e.nextClicked()}),a.k0s()()()),2&t&&(a.R7$(2),a.Y8G("id",e._periodButtonLabelId),a.R7$(),a.JRh(e.periodButtonDescription),a.R7$(),a.BMQ("aria-label",e.periodButtonLabel)("aria-describedby",e._periodButtonLabelId),a.R7$(2),a.JRh(e.periodButtonText),a.R7$(),a.AVh("mat-calendar-invert","month"!==e.calendar.currentView),a.R7$(4),a.Y8G("disabled",!e.previousEnabled()),a.BMQ("aria-label",e.prevButtonLabel),a.R7$(),a.Y8G("disabled",!e.nextEnabled()),a.BMQ("aria-label",e.nextButtonLabel))},dependencies:[J,tt],encapsulation:2,changeDetection:0}),s})(),K=(()=>{var i;class s{get startAt(){return this._startAt}set startAt(t){this._startAt=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get selected(){return this._selected}set selected(t){this._selected=t instanceof h?t:this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get minDate(){return this._minDate}set minDate(t){this._minDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get maxDate(){return this._maxDate}set maxDate(t){this._maxDate=this._dateAdapter.getValidDateOrNull(this._dateAdapter.deserialize(t))}get activeDate(){return this._clampedActiveDate}set activeDate(t){this._clampedActiveDate=this._dateAdapter.clampDate(t,this.minDate,this.maxDate),this.stateChanges.next(),this._changeDetectorRef.markForCheck()}get currentView(){return this._currentView}set currentView(t){const e=this._currentView!==t?t:null;this._currentView=t,this._moveFocusOnNextTick=!0,this._changeDetectorRef.markForCheck(),e&&this.viewChanged.emit(e)}constructor(t,e,n,o){this._dateAdapter=e,this._dateFormats=n,this._changeDetectorRef=o,this._moveFocusOnNextTick=!1,this.startView="month",this.selectedChange=new a.bkB,this.yearSelected=new a.bkB,this.monthSelected=new a.bkB,this.viewChanged=new a.bkB(!0),this._userSelection=new a.bkB,this._userDragDrop=new a.bkB,this._activeDrag=null,this.stateChanges=new A.B,this._intlChanges=t.changes.subscribe(()=>{o.markForCheck(),this.stateChanges.next()})}ngAfterContentInit(){this._calendarHeaderPortal=new E.A8(this.headerComponent||lt),this.activeDate=this.startAt||this._dateAdapter.today(),this._currentView=this.startView}ngAfterViewChecked(){this._moveFocusOnNextTick&&(this._moveFocusOnNextTick=!1,this.focusActiveCell())}ngOnDestroy(){this._intlChanges.unsubscribe(),this.stateChanges.complete()}ngOnChanges(t){const e=t.minDate&&!this._dateAdapter.sameDate(t.minDate.previousValue,t.minDate.currentValue)?t.minDate:void 0,n=t.maxDate&&!this._dateAdapter.sameDate(t.maxDate.previousValue,t.maxDate.currentValue)?t.maxDate:void 0,o=e||n||t.dateFilter;if(o&&!o.firstChange){const d=this._getCurrentViewComponent();d&&(this._moveFocusOnNextTick=!0,this._changeDetectorRef.detectChanges(),d._init())}this.stateChanges.next()}focusActiveCell(){this._getCurrentViewComponent()._focusActiveCell(!1)}updateTodaysDate(){this._getCurrentViewComponent()._init()}_dateSelected(t){const e=t.value;(this.selected instanceof h||e&&!this._dateAdapter.sameDate(e,this.selected))&&this.selectedChange.emit(e),this._userSelection.emit(t)}_yearSelectedInMultiYearView(t){this.yearSelected.emit(t)}_monthSelectedInYearView(t){this.monthSelected.emit(t)}_goToDateInView(t,e){this.activeDate=t,this.currentView=e}_dragStarted(t){this._activeDrag=t}_dragEnded(t){this._activeDrag&&(t.value&&this._userDragDrop.emit(t),this._activeDrag=null)}_getCurrentViewComponent(){return this.monthView||this.yearView||this.multiYearView}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(C),a.rXU(m.MJ,8),a.rXU(m.de,8),a.rXU(a.gRc))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-calendar"]],viewQuery:function(t,e){if(1&t&&(a.GBs(rt,5),a.GBs(ct,5),a.GBs(ot,5)),2&t){let n;a.mGM(n=a.lsd())&&(e.monthView=n.first),a.mGM(n=a.lsd())&&(e.yearView=n.first),a.mGM(n=a.lsd())&&(e.multiYearView=n.first)}},hostAttrs:[1,"mat-calendar"],inputs:{headerComponent:"headerComponent",startAt:"startAt",startView:"startView",selected:"selected",minDate:"minDate",maxDate:"maxDate",dateFilter:"dateFilter",dateClass:"dateClass",comparisonStart:"comparisonStart",comparisonEnd:"comparisonEnd",startDateAccessibleName:"startDateAccessibleName",endDateAccessibleName:"endDateAccessibleName"},outputs:{selectedChange:"selectedChange",yearSelected:"yearSelected",monthSelected:"monthSelected",viewChanged:"viewChanged",_userSelection:"_userSelection",_userDragDrop:"_userDragDrop"},exportAs:["matCalendar"],standalone:!0,features:[a.Jv_([ee]),a.OA$,a.aNF],decls:5,vars:2,consts:[[3,"cdkPortalOutlet"],["cdkMonitorSubtreeFocus","","tabindex","-1",1,"mat-calendar-content"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","_userSelection","dragStarted","dragEnded","activeDate","selected","dateFilter","maxDate","minDate","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName","activeDrag"],[3,"activeDateChange","monthSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"],[3,"activeDateChange","yearSelected","selectedChange","activeDate","selected","dateFilter","maxDate","minDate","dateClass"]],template:function(t,e){if(1&t&&(a.DNE(0,jt,0,0,"ng-template",0),a.j41(1,"div",1),a.DNE(2,zt,1,11,"mat-month-view",2)(3,Ht,1,6,"mat-year-view",3)(4,Ut,1,6,"mat-multi-year-view",3),a.k0s()),2&t){let n;a.Y8G("cdkPortalOutlet",e._calendarHeaderPortal),a.R7$(2),a.vxM("month"===(n=e.currentView)?2:"year"===n?3:"multi-year"===n?4:-1)}},dependencies:[E.I3,I.vR,rt,ct,ot],styles:['.mat-calendar{display:block;line-height:normal;font-family:var(--mat-datepicker-calendar-text-font, var(--mat-app-body-medium-font));font-size:var(--mat-datepicker-calendar-text-size, var(--mat-app-body-medium-size))}.mat-calendar-header{padding:8px 8px 0 8px}.mat-calendar-content{padding:0 8px 8px 8px;outline:none}.mat-calendar-controls{display:flex;align-items:center;margin:5% calc(4.7142857143% - 16px)}.mat-calendar-spacer{flex:1 1 auto}.mat-calendar-period-button{min-width:0;margin:0 8px;font-size:var(--mat-datepicker-calendar-period-button-text-size, var(--mat-app-title-small-size));font-weight:var(--mat-datepicker-calendar-period-button-text-weight, var(--mat-app-title-small-weight));--mdc-text-button-label-text-color:var(--mat-datepicker-calendar-period-button-text-color, var(--mat-app-on-surface-variant))}.mat-calendar-arrow{display:inline-block;width:10px;height:5px;margin:0 0 0 5px;vertical-align:middle;fill:var(--mat-datepicker-calendar-period-button-icon-color, var(--mat-app-on-surface-variant))}.mat-calendar-arrow.mat-calendar-invert{transform:rotate(180deg)}[dir=rtl] .mat-calendar-arrow{margin:0 5px 0 0}.cdk-high-contrast-active .mat-calendar-arrow{fill:CanvasText}.mat-calendar-previous-button,.mat-calendar-next-button{position:relative}.mat-datepicker-content .mat-calendar-previous-button:not(.mat-mdc-button-disabled),.mat-datepicker-content .mat-calendar-next-button:not(.mat-mdc-button-disabled){color:var(--mat-datepicker-calendar-navigation-button-icon-color, var(--mat-app-on-surface-variant))}.mat-calendar-previous-button::after,.mat-calendar-next-button::after{top:0;left:0;right:0;bottom:0;position:absolute;content:"";margin:15.5px;border:0 solid currentColor;border-top-width:2px}[dir=rtl] .mat-calendar-previous-button,[dir=rtl] .mat-calendar-next-button{transform:rotate(180deg)}.mat-calendar-previous-button::after{border-left-width:2px;transform:translateX(2px) rotate(-45deg)}.mat-calendar-next-button::after{border-right-width:2px;transform:translateX(-2px) rotate(45deg)}.mat-calendar-table{border-spacing:0;border-collapse:collapse;width:100%}.mat-calendar-table-header th{text-align:center;padding:0 0 8px 0;color:var(--mat-datepicker-calendar-header-text-color, var(--mat-app-on-surface-variant));font-size:var(--mat-datepicker-calendar-header-text-size, var(--mat-app-title-small-size));font-weight:var(--mat-datepicker-calendar-header-text-weight, var(--mat-app-title-small-weight))}.mat-calendar-table-header-divider{position:relative;height:1px}.mat-calendar-table-header-divider::after{content:"";position:absolute;top:0;left:-8px;right:-8px;height:1px;background:var(--mat-datepicker-calendar-header-divider-color)}.mat-calendar-body-cell-content::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 3px)*-1)}.mat-calendar-body-cell:focus .mat-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0}),s})();const mt={transformPanel:(0,p.hZ)("transformPanel",[(0,p.kY)("void => enter-dropdown",(0,p.i0)("120ms cubic-bezier(0, 0, 0.2, 1)",(0,p.i7)([(0,p.iF)({opacity:0,transform:"scale(1, 0.8)"}),(0,p.iF)({opacity:1,transform:"scale(1, 1)"})]))),(0,p.kY)("void => enter-dialog",(0,p.i0)("150ms cubic-bezier(0, 0, 0.2, 1)",(0,p.i7)([(0,p.iF)({opacity:0,transform:"scale(0.7)"}),(0,p.iF)({transform:"none",opacity:1})]))),(0,p.kY)("* => void",(0,p.i0)("100ms linear",(0,p.iF)({opacity:0})))]),fadeInCalendar:(0,p.hZ)("fadeInCalendar",[(0,p.wk)("void",(0,p.iF)({opacity:0})),(0,p.wk)("enter",(0,p.iF)({opacity:1})),(0,p.kY)("void => *",(0,p.i0)("120ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)"))])},le={provide:new a.nKC("mat-datepicker-scroll-strategy",{providedIn:"root",factory:()=>{const i=(0,a.WQX)(B.hJ);return()=>i.scrollStrategies.reposition()}}),deps:[B.hJ],useFactory:function ce(i){return()=>i.scrollStrategies.reposition()}};let me=(()=>{var i;class s{constructor(t,e,n,o,d,c){this._elementRef=t,this._changeDetectorRef=e,this._globalModel=n,this._dateAdapter=o,this._rangeSelectionStrategy=d,this._subscriptions=new g.yU,this._animationDone=new A.B,this._isAnimating=!1,this._actionsPortal=null,this._closeButtonText=c.closeCalendarLabel}ngOnInit(){this._animationState=this.datepicker.touchUi?"enter-dialog":"enter-dropdown"}ngAfterViewInit(){this._subscriptions.add(this.datepicker.stateChanges.subscribe(()=>{this._changeDetectorRef.markForCheck()})),this._calendar.focusActiveCell()}ngOnDestroy(){this._subscriptions.unsubscribe(),this._animationDone.complete()}_handleUserSelection(t){const e=this._model.selection,n=t.value,o=e instanceof h;if(o&&this._rangeSelectionStrategy){const d=this._rangeSelectionStrategy.selectionFinished(n,e,t.event);this._model.updateSelection(d,this)}else n&&(o||!this._dateAdapter.sameDate(n,e))&&this._model.add(n);(!this._model||this._model.isComplete())&&!this._actionsPortal&&this.datepicker.close()}_handleUserDragDrop(t){this._model.updateSelection(t.value,this)}_startExitAnimation(){this._animationState="void",this._changeDetectorRef.markForCheck()}_handleAnimationEvent(t){this._isAnimating="start"===t.phaseName,this._isAnimating||this._animationDone.next()}_getSelected(){return this._model.selection}_applyPendingSelection(){this._model!==this._globalModel&&this._globalModel.updateSelection(this._model.selection,this)}_assignActions(t,e){this._model=t?this._globalModel.clone():this._globalModel,this._actionsPortal=t,e&&this._changeDetectorRef.detectChanges()}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(a.aKT),a.rXU(a.gRc),a.rXU(M),a.rXU(m.MJ),a.rXU(S,8),a.rXU(C))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-datepicker-content"]],viewQuery:function(t,e){if(1&t&&a.GBs(K,5),2&t){let n;a.mGM(n=a.lsd())&&(e._calendar=n.first)}},hostAttrs:[1,"mat-datepicker-content"],hostVars:5,hostBindings:function(t,e){1&t&&a.Kam("@transformPanel.start",function(o){return e._handleAnimationEvent(o)})("@transformPanel.done",function(o){return e._handleAnimationEvent(o)}),2&t&&(a.zvX("@transformPanel",e._animationState),a.HbH(e.color?"mat-"+e.color:""),a.AVh("mat-datepicker-content-touch",e.datepicker.touchUi))},inputs:{color:"color"},exportAs:["matDatepickerContent"],standalone:!0,features:[a.aNF],decls:5,vars:27,consts:[["cdkTrapFocus","","role","dialog",1,"mat-datepicker-content-container"],[3,"yearSelected","monthSelected","viewChanged","_userSelection","_userDragDrop","id","startAt","startView","minDate","maxDate","dateFilter","headerComponent","selected","dateClass","comparisonStart","comparisonEnd","startDateAccessibleName","endDateAccessibleName"],[3,"cdkPortalOutlet"],["type","button","mat-raised-button","",1,"mat-datepicker-close-button",3,"focus","blur","click","color"]],template:function(t,e){if(1&t&&(a.j41(0,"div",0)(1,"mat-calendar",1),a.bIt("yearSelected",function(o){return e.datepicker._selectYear(o)})("monthSelected",function(o){return e.datepicker._selectMonth(o)})("viewChanged",function(o){return e.datepicker._viewChanged(o)})("_userSelection",function(o){return e._handleUserSelection(o)})("_userDragDrop",function(o){return e._handleUserDragDrop(o)}),a.k0s(),a.DNE(2,Gt,0,0,"ng-template",2),a.j41(3,"button",3),a.bIt("focus",function(){return e._closeButtonFocused=!0})("blur",function(){return e._closeButtonFocused=!1})("click",function(){return e.datepicker.close()}),a.EFF(4),a.k0s()()),2&t){let n;a.AVh("mat-datepicker-content-container-with-custom-header",e.datepicker.calendarHeaderComponent)("mat-datepicker-content-container-with-actions",e._actionsPortal),a.BMQ("aria-modal",!0)("aria-labelledby",null!==(n=e._dialogLabelId)&&void 0!==n?n:void 0),a.R7$(),a.HbH(e.datepicker.panelClass),a.Y8G("id",e.datepicker.id)("startAt",e.datepicker.startAt)("startView",e.datepicker.startView)("minDate",e.datepicker._getMinDate())("maxDate",e.datepicker._getMaxDate())("dateFilter",e.datepicker._getDateFilter())("headerComponent",e.datepicker.calendarHeaderComponent)("selected",e._getSelected())("dateClass",e.datepicker.dateClass)("comparisonStart",e.comparisonStart)("comparisonEnd",e.comparisonEnd)("@fadeInCalendar","enter")("startDateAccessibleName",e.startDateAccessibleName)("endDateAccessibleName",e.endDateAccessibleName),a.R7$(),a.Y8G("cdkPortalOutlet",e._actionsPortal),a.R7$(),a.AVh("cdk-visually-hidden",!e._closeButtonFocused),a.Y8G("color",e.color||"primary"),a.R7$(),a.JRh(e._closeButtonText)}},dependencies:[I.kB,K,E.I3,J],styles:[".mat-datepicker-content{display:block;border-radius:4px;background-color:var(--mat-datepicker-calendar-container-background-color, var(--mat-app-surface-container-high));color:var(--mat-datepicker-calendar-container-text-color, var(--mat-app-on-surface));box-shadow:var(--mat-datepicker-calendar-container-elevation-shadow);border-radius:var(--mat-datepicker-calendar-container-shape, var(--mat-app-corner-large))}.mat-datepicker-content .mat-calendar{width:296px;height:354px}.mat-datepicker-content .mat-datepicker-content-container-with-custom-header .mat-calendar{height:auto}.mat-datepicker-content .mat-datepicker-close-button{position:absolute;top:100%;left:0;margin-top:8px}.ng-animating .mat-datepicker-content .mat-datepicker-close-button{display:none}.mat-datepicker-content-container{display:flex;flex-direction:column;justify-content:space-between}.mat-datepicker-content-touch{display:block;max-height:80vh;box-shadow:var(--mat-datepicker-calendar-container-touch-elevation-shadow);border-radius:var(--mat-datepicker-calendar-container-touch-shape, var(--mat-app-corner-extra-large));position:relative;overflow:visible}.mat-datepicker-content-touch .mat-datepicker-content-container{min-height:312px;max-height:788px;min-width:250px;max-width:750px}.mat-datepicker-content-touch .mat-calendar{width:100%;height:auto}@media all and (orientation: landscape){.mat-datepicker-content-touch .mat-datepicker-content-container{width:64vh;height:80vh}}@media all and (orientation: portrait){.mat-datepicker-content-touch .mat-datepicker-content-container{width:80vw;height:100vw}.mat-datepicker-content-touch .mat-datepicker-content-container-with-actions{height:115vw}}"],encapsulation:2,data:{animation:[mt.transformPanel,mt.fadeInCalendar]},changeDetection:0}),s})(),_e=(()=>{var i;class s{}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275dir=a.FsC({type:i,selectors:[["","matDatepickerToggleIcon",""]],standalone:!0}),s})(),fe=(()=>{var i;class s{get disabled(){return void 0===this._disabled&&this.datepicker?this.datepicker.disabled:!!this._disabled}set disabled(t){this._disabled=t}constructor(t,e,n){this._intl=t,this._changeDetectorRef=e,this._stateChanges=g.yU.EMPTY;const o=Number(n);this.tabIndex=o||0===o?o:null}ngOnChanges(t){t.datepicker&&this._watchStateChanges()}ngOnDestroy(){this._stateChanges.unsubscribe()}ngAfterContentInit(){this._watchStateChanges()}_open(t){this.datepicker&&!this.disabled&&(this.datepicker.open(),t.stopPropagation())}_watchStateChanges(){const t=this.datepicker?this.datepicker.stateChanges:(0,P.of)(),e=this.datepicker&&this.datepicker.datepickerInput?this.datepicker.datepickerInput.stateChanges:(0,P.of)(),n=this.datepicker?(0,et.h)(this.datepicker.openedStream,this.datepicker.closedStream):(0,P.of)();this._stateChanges.unsubscribe(),this._stateChanges=(0,et.h)(this._intl.changes,t,e,n).subscribe(()=>this._changeDetectorRef.markForCheck())}}return(i=s).\u0275fac=function(t){return new(t||i)(a.rXU(C),a.rXU(a.gRc),a.kS0("tabindex"))},i.\u0275cmp=a.VBU({type:i,selectors:[["mat-datepicker-toggle"]],contentQueries:function(t,e,n){if(1&t&&a.wni(n,_e,5),2&t){let o;a.mGM(o=a.lsd())&&(e._customIcon=o.first)}},viewQuery:function(t,e){if(1&t&&a.GBs(Xt,5),2&t){let n;a.mGM(n=a.lsd())&&(e._button=n.first)}},hostAttrs:[1,"mat-datepicker-toggle"],hostVars:8,hostBindings:function(t,e){1&t&&a.bIt("click",function(o){return e._open(o)}),2&t&&(a.BMQ("tabindex",null)("data-mat-calendar",e.datepicker?e.datepicker.id:null),a.AVh("mat-datepicker-toggle-active",e.datepicker&&e.datepicker.opened)("mat-accent",e.datepicker&&"accent"===e.datepicker.color)("mat-warn",e.datepicker&&"warn"===e.datepicker.color))},inputs:{datepicker:[0,"for","datepicker"],tabIndex:"tabIndex",ariaLabel:[0,"aria-label","ariaLabel"],disabled:[2,"disabled","disabled",a.L39],disableRipple:"disableRipple"},exportAs:["matDatepickerToggle"],standalone:!0,features:[a.GFd,a.OA$,a.aNF],ngContentSelectors:Kt,decls:4,vars:6,consts:[["button",""],["mat-icon-button","","type","button",3,"disabled","disableRipple"],["viewBox","0 0 24 24","width","24px","height","24px","fill","currentColor","focusable","false","aria-hidden","true",1,"mat-datepicker-toggle-default-icon"],["d","M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"]],template:function(t,e){1&t&&(a.NAR($t),a.j41(0,"button",1,0),a.DNE(2,Zt,2,0,":svg:svg",2),a.SdG(3),a.k0s()),2&t&&(a.Y8G("disabled",e.disabled)("disableRipple",e.disableRipple),a.BMQ("aria-haspopup",e.datepicker?"dialog":null)("aria-label",e.ariaLabel||e._intl.openCalendarLabel)("tabindex",e.disabled?-1:e.tabIndex),a.R7$(2),a.vxM(e._customIcon?-1:2))},dependencies:[tt],styles:[".mat-datepicker-toggle{pointer-events:auto;color:var(--mat-datepicker-toggle-icon-color, var(--mat-app-on-surface-variant))}.mat-datepicker-toggle-active{color:var(--mat-datepicker-toggle-active-state-icon-color, var(--mat-app-on-surface-variant))}.cdk-high-contrast-active .mat-datepicker-toggle-default-icon{color:CanvasText}"],encapsulation:2,changeDetection:0}),s})(),ye=(()=>{var i;class s{}return(i=s).\u0275fac=function(t){return new(t||i)},i.\u0275mod=a.$C({type:i}),i.\u0275inj=a.G2t({providers:[C,le],imports:[W.MD,It,B.z_,I.Pd,E.jc,m.yE,me,fe,lt,Et.Gj]}),s})(),De=(()=>{var i;class s{constructor(){(0,V.A)(this,"unsubscribe",[])}ngOnInit(){}ngOnDestroy(){this.unsubscribe.forEach(t=>t.unsubscribe())}}return i=s,(0,V.A)(s,"\u0275fac",function(t){return new(t||i)}),(0,V.A)(s,"\u0275cmp",a.VBU({type:i,selectors:[["app-home"]],standalone:!0,features:[a.Jv_([(0,m.aw)()]),a.aNF],decls:5,vars:0,consts:[[1,"w-full","pb-3"],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"]],template:function(t,e){1&t&&(a.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"p",3),a.EFF(4," Feature is developed "),a.k0s()()()())},dependencies:[bt.iI,_t.G,ft.Cn,gt.tZ,F.RG,St,ye],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),s})()}}]);