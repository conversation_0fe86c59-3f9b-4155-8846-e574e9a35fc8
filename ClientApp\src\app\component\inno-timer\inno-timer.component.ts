import { InnoEnterEditHoursComponent } from 'app/component/inno-enter-edit-hours/inno-enter-edit-hours.component';
import { ChangeDetectorRef, Component, HostListener, inject, OnDestroy, OnInit } from '@angular/core';
import { InnoFormTextareaComponent } from '../inno-form-textarea/inno-form-textarea.component';
import { InnoTagsComponent } from '../inno-tags/inno-tags.component';
import { InnoSelectSearchTagsComponent } from '../inno-select-search-tags/inno-select-search-tags.component';
import { InnoDatepickerComponent } from '../inno-datepicker/inno-datepicker.component';
import { InnoFormCheckboxComponent } from '../inno-form-checkbox/inno-form-checkbox.component';
import { Subscription } from 'rxjs';
import { DataService } from 'app/service/data.service';
import { InnoSelectSearchProjectComponent } from '../inno-select-search-project/inno-select-search-project.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { SharedModule } from 'app/module/shared.module';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { formatTimeHoursFromSeconds } from 'app/helpers/common.helper';
import { InnoTimerProvider } from './inno-timer.provider';
import { InnoSelectSearchServiceComponent } from '../inno-select-search-service/inno-select-search-service.component';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-inno-timer',
  templateUrl: './inno-timer.component.html',
  styleUrls: ['./inno-timer.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoFormTextareaComponent,
    InnoTagsComponent,
    InnoTagsComponent,
    InnoSelectSearchTagsComponent,
    InnoDatepickerComponent,
    InnoFormCheckboxComponent,
    InnoSelectSearchProjectComponent,
    InnoSelectSearchServiceComponent,
    InnoEnterEditHoursComponent
  ],
  providers: [LayoutUtilsService]
})
export class InnoTimerComponent implements OnInit, OnDestroy {
  public isEditTimerHours: boolean = false
  public isExpand: boolean = false
  public isShowingTimer: boolean = false
  public isResume: boolean = false
  public title!: string;
  public isPause = true;
  public projectId: string = ''
  public previewWorkingInfo?: IFilterDropdownOption
  public previewServiceInfo?: IFilterDropdownOption
  public previewDescription?: string = ''
  public previewDate?: Date = new Date()
  public previewBillable?: boolean = false
  public timerHours: string = '00:00:00';
  public timerHoursEdit: string = '00:00:00';
  public timerHoursChange: string = '00:00:00';
  public isInternal: boolean = false

  private dataService = inject(DataService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private innoTimerProvider = inject(InnoTimerProvider)
  private translate = inject(TranslateService)
  private interval: any = null;
  private unsubscribe: Subscription[] = [];
  private unsubscribeTrackingTimer?: Subscription;

  constructor() { }
  @HostListener('document:visibilitychange', ['$event'])
  visibilitychange() {
    this.checkActiveBrowser();
  }

  checkActiveBrowser() {
    if (!document.hidden) {
      this.innoTimerProvider.getTimerInfoFromServer().then(({ totalSecondsTimerOnServer }) => {
        const [hoursnew, minutesnew] = this.timerHoursChange.split(':').map(Number);
        const totalSecondsTimerChange = hoursnew * 3600 + minutesnew * 60 + totalSecondsTimerOnServer;
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ totalSeconds: totalSecondsTimerChange })
      })
    }
  }
  ngOnInit(): void {
    this.unsubscribe.push(
      this.dataService.GetTimeTrackingShowingTimer().subscribe(isShowing => {
        if (this.dataService.getResume()) {
          this.isResume = true
          this.title = this.dataService.getResume().title
          this.previewWorkingInfo = this.dataService.getResume()?.workingInfo
          this.previewDescription = this.dataService.getResume()?.description
          this.previewServiceInfo = this.dataService.getResume()?.serviceInfo
          this.projectId = this.previewWorkingInfo?.value
          this.previewDate = this.dataService.getResume()?.date
          this.innoTimerProvider.updateTimeTrackingTimerInfo({ workingInfo: this.previewWorkingInfo, description: this.previewDescription, date: this.dataService.getResume()?.date, serviceInfo: this.previewServiceInfo })
        }
        else {
          this.isResume = false
        }
        this.isShowingTimer = isShowing
        if (this.isShowingTimer) {
          if (this.unsubscribeTrackingTimer) return

          this.unsubscribeTrackingTimer = this.dataService
            .GetTimeTrackingCreateTimerInfo()
            .subscribe(data => {
              this.timerHours = formatTimeHoursFromSeconds(data?.totalSeconds)
              this.previewDate = data?.date ?? new Date()
              this.previewDescription = data?.description ?? ''
              this.previewBillable = data?.billable ?? false
              this.previewWorkingInfo = data?.workingInfo
              this.title = this.previewWorkingInfo?.label
              this.previewServiceInfo = data?.serviceInfo
              switch (data?.timerStatus) {
                case 'running': {
                  this.startClock()
                  break
                }

                case 'paused': {
                  this.stopClock()
                  break;
                }

                // Discard timer
                default: {
                  this.stopClock()
                  break;
                }
              }
            })
        }
      })
    )

    this.innoTimerProvider.getTimerInfoFromServer().then(({ totalSecondsTimerOnServer }) => {
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ totalSeconds: totalSecondsTimerOnServer })
      if (totalSecondsTimerOnServer > 0 && localStorage.getItem("isRunning") === 'true') {
        this.dataService.SetNewTimeTrackingShowingTimer(true)
      }
    })

    if (this.innoTimerProvider.getCacheClientProject()) {
      this.innoTimerProvider.getCacheClientProject().metadata?.objectClient?.isInternal ? this.isInternal = true : this.isInternal = false;
      this.dataService.SetisInternalClient(this.isInternal)
      this.title = this.innoTimerProvider.getCacheClientProject().label;
      this.projectId = this.innoTimerProvider.getCacheClientProject().value;
      const timerDate = this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date ?? new Date()
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ workingInfo: this.innoTimerProvider.getCacheClientProject(), date: timerDate })
      if (this.isInternal) {
        this.previewBillable = false
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: false })
      }
      else {
        const billable = this.innoTimerProvider.getCacheClientProject().metadata?.project?.billable
        this.previewBillable = billable
        this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: billable })
      }
    }
    if (this.innoTimerProvider.getCacheService()) {
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ serviceInfo: this.innoTimerProvider.getCacheService() })
    }
  }

  EditTimer() {
    this.timerHoursEdit = this.timerHours
    this.isEditTimerHours = !this.isEditTimerHours
  }

  handleEditEndTime(args: { newHours: string, isAlreadyEdit: boolean }) {
    this.isEditTimerHours = false
    if (args.isAlreadyEdit) {
      this.innoTimerProvider.handleUpdateActualTimer(args.newHours)
    }
  }

  handleToggleExpand() {
    this.isExpand = !this.isExpand
  }
  handleSelectServices(item: IFilterDropdownOption) {
    this.previewServiceInfo = item
    this.innoTimerProvider.setCacheService(item)
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ serviceInfo: item })
  }
  handleSelectProject(item: IFilterDropdownOption) {
    item.metadata?.objectClient?.isInternal ? this.isInternal = true : this.isInternal = false;
    this.dataService.SetisInternalClient(this.isInternal)
    this.innoTimerProvider.setCacheClientProject(item)
    this.title = item.label
    this.projectId = item.value
    const timerDate = this.dataService.GetTimeTrackingCreateTimerInfoValue()?.date ?? new Date()
    const __dataUpdate = { workingInfo: item, date: timerDate }
    this.innoTimerProvider.updateTimeTrackingTimerInfo(__dataUpdate)

    // Clear service selection when project changes
    this.previewServiceInfo = undefined;
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ serviceInfo: undefined })

    if (this.isInternal) {
      this.previewBillable = false
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: false })
    }
    else {
      const billable = item.metadata?.project?.billable
      this.previewBillable = billable
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: billable })

    }
  }

  handleChangeDate(value: any) {
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ date: value })
  }

  handleChangeNote(description: any) {
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ description })
  }

  handleChangeBillable(value: any) {
    this.innoTimerProvider.updateTimeTrackingTimerInfo({ billable: value })
  }

  handleCloseTimer() {
    this.dataService.SetNewTimeTrackingShowingTimer(false)
  }

  private stopClock = () => {
    this.isPause = true
    clearInterval(this.interval);
    this.interval = null
  }

  private startClock = () => {
    this.isPause = false
    if (this.interval) return
    this.interval = setInterval(() => {
      let totalSeconds = this.dataService.GetTimeTrackingCreateTimerInfoValue()?.totalSeconds ?? 0
      totalSeconds += 1;
      this.timerHours = formatTimeHoursFromSeconds(totalSeconds)
      this.innoTimerProvider.updateTimeTrackingTimerInfo({ totalSeconds })
    }, 1000);
  }

  handleDiscard() {
    const _title = this.translate.instant('TIMETRACKING.DIALOG.TitleConfirm');
    const _description = this.translate.instant('TIMETRACKING.DIALOG.Discard');
    this.layoutUtilsService.alertConfirm({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return;

      this.stopClock()
      this.innoTimerProvider.clearCache()
      this.innoTimerProvider.handleResetTimer()
    })
  }

  handlePauseOrResumeTime = this.innoTimerProvider.handlePauseOrResumeTime
  handleAddTimeTrackingRecord = this.innoTimerProvider.createTimeTrackingFromTimer

  ngOnDestroy(): void {
    this.unsubscribe.forEach((s) => s.unsubscribe());
    this.stopClock();
    this.title = "";
    this.dataService.SetNewTimeTrackingShowingTimer(false)
    this.unsubscribeTrackingTimer?.unsubscribe()
    this.unsubscribeTrackingTimer = undefined
  }
}
