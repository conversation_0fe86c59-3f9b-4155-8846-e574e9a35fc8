"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[8193],{1556:(B,j,a)=>{a.d(j,{Z:()=>v});var C=a(9842),s=a(4438),F=a(467),S=a(2716),b=a(7987);let E=(()=>{var r;class d extends S.H{open(o){var u=this;return(0,F.A)(function*(){const _=yield a.e(3190).then(a.bind(a,3190));return u.matDialog.open(_.AlertConfirmComponent.getComponent(),{data:o,width:"440px",panelClass:"custom_dialog",scrollStrategy:new b.t0,disableClose:!0})})()}}return r=d,(0,C.A)(d,"\u0275fac",(()=>{let l;return function(u){return(l||(l=s.xGo(r)))(u||r)}})()),(0,C.A)(d,"\u0275prov",s.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})),d})(),v=(()=>{var r;class d{constructor(o){(0,C.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=o}alertDelete(o){const{title:u,description:_,textSubmit:T="COMMON.Delete",textCancel:N}=o;return new Promise(R=>{this.alertConfirmDialog.open({title:u,description:_,textSubmit:T,textCancel:N,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(P=>{P.afterClosed().subscribe(G=>{R(G??!1)})})})}alertConfirm(o){const{title:u,description:_,textSubmit:T,textCancel:N}=o;return new Promise(R=>{this.alertConfirmDialog.open({title:u,description:_,textSubmit:T,textCancel:N}).then(P=>{P.afterClosed().subscribe(G=>{R(G??!1)})})})}}return r=d,(0,C.A)(d,"\u0275fac",function(o){return new(o||r)(s.KVO(E))}),(0,C.A)(d,"\u0275prov",s.jDH({token:r,factory:r.\u0275fac})),d})()},4433:(B,j,a)=>{a.d(j,{Q:()=>F});var C=a(9842),s=a(4438);let F=(()=>{var S;class b{transform(e,v=2){const r=Math.pow(10,v);return(Math.trunc(Number((e*r).toFixed(v+5)))/r).toFixed(v)}}return S=b,(0,C.A)(b,"\u0275fac",function(e){return new(e||S)}),(0,C.A)(b,"\u0275pipe",s.EJ8({name:"decimal",type:S,pure:!0,standalone:!0})),b})()},6617:(B,j,a)=>{a.d(j,{p:()=>S});var C=a(9842),s=a(6473),F=a(4438);let S=(()=>{var b;class E{transform(v){return(0,s.ZV)(v)}}return b=E,(0,C.A)(E,"\u0275fac",function(v){return new(v||b)}),(0,C.A)(E,"\u0275pipe",F.EJ8({name:"formatNumber",type:b,pure:!0,standalone:!0})),E})()},5936:(B,j,a)=>{a.d(j,{H:()=>E});var C=a(9842),s=a(1626),F=a(4438);const b=a(5312).c.HOST_API+"/api";let E=(()=>{var e;class v{constructor(){(0,C.A)(this,"http",(0,F.WQX)(s.Qq))}GetFile(d){return this.http.get(b+`/Images/GetFile?nameFile=${d}`,{responseType:"blob"})}GetFileURL(d){return this.http.get(b+`/Images/GetFileURL?nameFile=${d}`,{responseType:"blob"})}}return e=v,(0,C.A)(v,"\u0275fac",function(d){return new(d||e)}),(0,C.A)(v,"\u0275prov",F.jDH({token:e,factory:e.\u0275fac,providedIn:"root"})),v})()},1456:(B,j,a)=>{a.d(j,{D:()=>e});var C=a(9842),s=a(5312),F=a(1626),S=a(4438),b=a(6473);const E=s.c.HOST_API+"/api";let e=(()=>{var v;class r{constructor(){(0,C.A)(this,"http",(0,S.WQX)(F.Qq))}CreateExpenses(l){return this.http.post(E+"/Expenses/CreateExpenses",l)}UpdateExpenses(l){return this.http.post(E+"/Expenses/UpdateExpenses",l)}GetExpensesById(l){return this.http.get(E+`/Expenses/GetExpensesById?Id=${l}`)}GetAllExpenses(l){const o=(0,b.yU)(l);return this.http.get(E+"/Expenses/GetAllExpenses",{params:o})}GetAllUploadExpenses(l){const o=(0,b.yU)(l);return this.http.get(E+"/Expenses/GetAllUploadExpenses",{params:o})}DeleteFileExpenses(l){return this.http.post(E+"/Expenses/DeleteExpenses",l)}DeleteExpenses(l){return this.http.post(E+"/Expenses/DeleteExpenses",l)}MarkAsPaid(l){return this.http.put(E+`/Expenses/MarkAsPaid?Id=${l}`,null)}GetExpenseItemsByExpenseIds(l){return this.http.post(E+"/Expenses/GetExpenseItemsByExpenseIds",l)}}return v=r,(0,C.A)(r,"\u0275fac",function(l){return new(l||v)}),(0,C.A)(r,"\u0275prov",S.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})),r})()},5644:(B,j,a)=>{a.d(j,{p:()=>v});var C=a(9842),s=a(1342),F=a(5312),S=a(1626),b=a(4438),E=a(6473);const e=F.c.HOST_API+"/api";let v=(()=>{var r;class d{constructor(){(0,C.A)(this,"http",(0,b.WQX)(S.Qq)),(0,C.A)(this,"spinnerService",(0,b.WQX)(s.D))}CreatedInvoice(o){return this.http.post(e+"/Invoices/CreatedInvoice",o)}CreatedInvoiceSend(o){return this.http.post(e+"/Invoices/CreatedInvoiceSend",o)}CountInvoiceByCompany(){return this.http.get(e+"/Invoices/CountInvoiceByCompany")}CountInvoiceByContractor(){return this.http.get(e+"/Invoices/CountInvoiceByContractor")}CountEstimate(){return this.http.get(e+"/Invoices/CountEstimate")}SendMailInvoice(o){return this.http.post(e+"/Invoices/SendMailInvoice",o)}GetAllEstimate(o){const u=(0,E.yU)(o);return this.http.get(e+"/Invoices/GetAllEstimate",{params:u})}GetAllInvoice(o){const u=(0,E.yU)(o);return this.http.get(e+"/Invoices/GetAllInvoice",{params:u})}GetAllInvoiceSendToMe(o){const u=(0,E.yU)(o);return this.http.get(e+"/Invoices/GetAllInvoiceSendToMe",{params:u})}GetAllEstimateSendToMe(o){return this.http.post(e+"/Invoices/GetAllEstimateSendToMe",o)}DeleteInvoice(o,u){return this.http.post(e+`/Invoices/DeleteInvoice?isActive=${u}`,o)}ChangePosition(o){return this.http.post(e+"/Invoices/ChangePosition?",o)}GetRevenueChart(o){return this.http.get(e+`/Invoices/GetRevenueChart?year=${o}`)}GraphicsChart(o){return this.http.get(e+`/Invoices/GraphicsChart?year=${o}`)}MarkAsPaid(o){return this.http.post(e+`/Invoices/MarkAsPaid?Id=${o}`,null)}MarkAsSent(o){return this.http.post(e+`/Invoices/MarkAsSent?Id=${o}`,null)}UpdateInvoice(o){return o?.itemInvoices.forEach(u=>{u.user=null}),this.http.post(e+"/Invoices/UpdateInvoice",o)}UpdateArchive(o,u){return this.http.post(e+`/Invoices/UpdateArchive?invoiceId=${o}&isArchive=${u}`,{})}ConvertToInvoice(o){return this.http.post(e+`/Invoices/ConvertToInvoice?invoiceId=${o}`,{})}GetInvoiceById(o){return this.http.get(e+`/Invoices/GetInvoiceById?InvoiceId=${o}`)}PrintInvoiceById(o,u){this.http.get(`${e}/Invoices/PrintInvoice?InvoiceId=${o}`,{responseType:"blob"}).subscribe({next:_=>{const T=new Blob([_],{type:"application/pdf"}),N=window.URL.createObjectURL(T),R=document.createElement("a");R.href=N,R.download=`Invoice_${u}.pdf`,document.body.appendChild(R),R.click(),this.spinnerService.hide(),window.URL.revokeObjectURL(N)},error:_=>{console.error("Error downloading the invoice:",_)}})}GetInvoiceByIdLink(o){return this.http.get(e+`/Invoices/GetInvoiceByIdLink?InvoiceId=${o}`)}CalculationInvoice(o){return this.http.get(e+`/Invoices/CalculationInvoice?status=${o}`)}CalculationInvoiceSendToMe(o){return this.http.get(e+`/Invoices/CalculationInvoiceSendToMe?status=${o}`)}CalculationEstimateSendToMe(o){return this.http.get(e+`/Invoices/CalculationEstimateSendToMe?status=${o}`)}CalculationEstimate(o){return this.http.get(e+`/Invoices/CalculationEstimate?status=${o}`)}uploadFile(o){const u=new FormData;return u.append("formFile",o),this.http.post(e+"/Invoices/UploadFile",u)}}return r=d,(0,C.A)(d,"\u0275fac",function(o){return new(o||r)}),(0,C.A)(d,"\u0275prov",b.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})),d})()},5909:(B,j,a)=>{a.d(j,{R2:()=>F,Xj:()=>s,az:()=>e,jQ:()=>C,yo:()=>E});const C=(v,r)=>{if(!v||!r)return 0;const d=v.split(":").map(Number);let l=0,o=0,u=0;return 3===d.length?[l,o,u]=d:2===d.length?[l,o]=d:1===d.length&&([l]=d),Number(((l+o/60+u/3600)*r).toFixed(2))},s=(v=[],r=!1)=>(v.some(l=>l.companyTax||l.selected||void 0===l.selected)?v:v.filter(l=>l.selected)).map(l=>{let o,u;return l.companyTax?(o=l.companyTax.name,u=l.companyTax.amount):(o=l.name,u=l.amount),r?`${o} (${u}%)`:o}).filter(Boolean).sort((l,o)=>l.localeCompare(o)).join(", "),F=(v,r)=>v&&r?Number((v*r).toFixed(2)):0,E=v=>{let r=0,d={};v.forEach(({rate:o,qty:u,taxes:_})=>{if(!o||!u)return;const T=Number((o*u).toFixed(2));r+=T,_&&0!==_.length&&(_.some(D=>D.companyTaxId)?_.filter(D=>D.companyTaxId):_.filter(D=>D.selected)).forEach(D=>{const P=D.name||"Unknown Tax",G=D.taxeNumber||"",W=Number(D?.companyTax?.amount??D.amount??0);d[P]||(d[P]={name:P,numberTax:G,amount:W,taxableAmount:0,total:0}),d[P].taxableAmount+=T})});let l=0;return Object.values(d).forEach(o=>{o.total=Number((o.taxableAmount*(o.amount/100)).toFixed(2)),l+=o.total}),{subtotal:Number(r.toFixed(2)),totalTaxes:d,grandTotalTax:Number(l.toFixed(2))}},e=v=>{let r=0,d={};v.forEach(({total:o,taxes:u})=>{o&&(r+=o,u&&0!==u.length)&&(u.some(N=>N.companyTaxId)?u.filter(N=>N.companyTaxId):u.filter(N=>N.selected)).forEach(N=>{const R=N.name||"Unknown Tax",D=N.taxeNumber||"",P=Number(N?.companyTax?.amount??N.amount??0);d[R]||(d[R]={name:R,numberTax:D,amount:P,taxableAmount:0,total:0}),d[R].taxableAmount+=o})});let l=0;return Object.values(d).forEach(o=>{o.total=Number((o.taxableAmount*(o.amount/100)).toFixed(2)),l+=o.total}),{subtotal:Number(r.toFixed(2)),totalTaxes:d,grandTotalTax:Number(l.toFixed(2))}}},8193:(B,j,a)=>{a.r(j),a.d(j,{NewInvoiceComponent:()=>Re});var C=a(467),s=a(9842),F=a(9088),S=a(4433),b=a(5644),E=a(5312),e=a(4438),v=a(1626);const r=E.c.HOST_API+"/api";let d=(()=>{var c;class g{constructor(){(0,s.A)(this,"http",(0,e.WQX)(v.Qq))}CreateTax(t){return this.http.post(r+"/Tax/CreateTax",t)}UpdateTax(t){return this.http.post(r+"/Tax/UpdateTax",t)}GetAllTax(t){return this.http.get(r+`/Tax/GetAllTax?Page=${t.Page}&PageSize=${t.PageSize}&Search=${t.Search}&InvoiceId=${t.InvoiceId}`)}}return c=g,(0,s.A)(g,"\u0275fac",function(t){return new(t||c)}),(0,s.A)(g,"\u0275prov",e.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),g})();var l=a(1456),o=a(1110),u=a(4006),_=a(6146),T=a(9079),N=a(3492),R=a(6508),D=a.n(R),P=a(4978),G=a(9248),W=a(1328),q=a(3202),ee=a(7086),te=a(8556),ne=a(344),U=a(9417),ie=a(6463),K=a(1970),oe=a(7572),z=a(6473),Y=a(33),ae=a(5277),se=a(6617),Z=a(1556),re=a(1342),w=a(1875),le=a(2953),Q=a(177),X=a(5909),ce=a(5936),pe=a(97),me=a(2387),de=a(3989),ue=a(2716),ve=a(7987);let he=(()=>{var c;class g extends ue.H{open(t){var n=this;return(0,C.A)(function*(){const i=yield Promise.all([a.e(2591),a.e(1448),a.e(9805)]).then(a.bind(a,9805));return n.matDialog.open(i.SelectExpensesComponent.getComponent(),{width:"100%",maxWidth:"1300px",data:t,panelClass:"custom_dialog",disableClose:!0,scrollStrategy:new ve.t0})})()}}return c=g,(0,s.A)(g,"\u0275fac",(()=>{let p;return function(n){return(p||(p=e.xGo(c)))(n||c)}})()),(0,s.A)(g,"\u0275prov",e.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),g})();var Ie=a(6757),fe=a(9211),J=a(5236);const xe=["selectSearchClientElement"],H=c=>({required:c}),Ce=c=>({"pl-8":c});function ge(c,g){1&c&&(e.j41(0,"p",10),e.EFF(1," Loading... "),e.k0s())}function ye(c,g){if(1&c&&(e.j41(0,"div",12)(1,"p",40),e.EFF(2),e.k0s()()),2&c){const p=e.XpG();e.R7$(2),e.SpI(" ",null==p.InforCompany?null:p.InforCompany.phone," ")}}function Ee(c,g){if(1&c&&(e.j41(0,"p",13),e.EFF(1),e.k0s()),2&c){const p=e.XpG();e.R7$(),e.SpI(" ",p.combinedAddress," ")}}function be(c,g){if(1&c&&e.nrm(0,"ngx-avatars",42),2&c){const p=e.XpG().$implicit;e.Y8G("size",32)("name",p.label)}}function _e(c,g){1&c&&(e.j41(0,"div",43),e.nrm(1,"img",46),e.k0s())}function Te(c,g){if(1&c&&(e.j41(0,"p",45),e.EFF(1),e.k0s()),2&c){const p=e.XpG().$implicit;e.R7$(),e.SpI(" ",p.metadata.description," ")}}function Ne(c,g){if(1&c){const p=e.RV6();e.j41(0,"div",41),e.bIt("click",function(){const n=e.eBV(p).$implicit,i=e.XpG();return e.Njj(i.handleSelectProject(n))}),e.DNE(1,be,1,2,"ngx-avatars",42)(2,_e,2,0,"div",43),e.j41(3,"div",9)(4,"p",44),e.EFF(5),e.k0s(),e.DNE(6,Te,2,1,"p",45),e.k0s()()}if(2&c){const p=g.$implicit,t=e.XpG();e.AVh("selected",p.value===t.f.projectId.value),e.Y8G("ngClass",e.eq3(6,Ce,"project"==(null==p||null==p.metadata?null:p.metadata.type))),e.R7$(),e.vxM("client"==(null==p||null==p.metadata?null:p.metadata.type)?1:2),e.R7$(4),e.SpI(" ",p.label," "),e.R7$(),e.vxM(null!=p.metadata&&p.metadata.description?6:-1)}}function Ae(c,g){if(1&c){const p=e.RV6();e.j41(0,"p",56),e.bIt("click",function(){e.eBV(p);const n=e.XpG(),i=n.$implicit,m=n.$index,y=e.XpG();return e.Njj(y.handleModifyTaxes(null==i?null:i.taxes,m))}),e.EFF(1),e.k0s()}if(2&c){const p=e.XpG().$implicit,t=e.XpG();e.R7$(),e.SpI(" ",t.getNameSelectedTaxes(null==p?null:p.taxes)," ")}}function Se(c,g){if(1&c){const p=e.RV6();e.j41(0,"p",57),e.bIt("click",function(){e.eBV(p);const n=e.XpG(),i=n.$implicit,m=n.$index,y=e.XpG();return e.Njj(y.handleModifyTaxes(null==i?null:i.taxes,m))}),e.EFF(1),e.nI1(2,"translate"),e.k0s()}2&c&&(e.R7$(),e.SpI(" ",e.bMT(2,1,"INVOICES.INVOICE_FORM.Buttons.AddTaxes")," "))}function De(c,g){if(1&c){const p=e.RV6();e.j41(0,"div",27)(1,"div",47)(2,"div",48)(3,"span",49),e.EFF(4," drag_indicator "),e.k0s(),e.j41(5,"p",50),e.EFF(6),e.k0s()()(),e.j41(7,"p",51),e.EFF(8),e.nI1(9,"formatNumber"),e.k0s(),e.j41(10,"p",51),e.EFF(11),e.nI1(12,"decimal"),e.nI1(13,"formatNumber"),e.k0s(),e.DNE(14,Ae,2,1,"p",52)(15,Se,3,3,"p",53),e.j41(16,"p",54),e.EFF(17),e.nI1(18,"decimal"),e.nI1(19,"formatNumber"),e.k0s(),e.j41(20,"app-inno-table-action",55),e.bIt("onEdit",function(){const n=e.eBV(p),i=n.$implicit,m=n.$index,y=e.XpG();return e.Njj(y.handleModifyInvoiceItem(m,i))})("onDelete",function(){const n=e.eBV(p).$index,i=e.XpG();return e.Njj(i.handleDeleteInvoiceItem(n))}),e.k0s()()}if(2&c){let p,t,n;const i=g.$implicit,m=e.XpG();e.R7$(6),e.SpI(" ",null!==(p=m.createDescription(i))&&void 0!==p?p:""," "),e.R7$(2),e.SpI(" $",e.bMT(9,5,null!==(t=null==i?null:i.rate)&&void 0!==t?t:0)," "),e.R7$(3),e.SpI(" ",e.bMT(13,10,e.i5U(12,7,null!==(n=null==i?null:i.qty)&&void 0!==n?n:0,2))," "),e.R7$(3),e.vxM(null!=i&&i.taxes&&(null==i?null:i.taxes.length)>0&&""!=m.getNameSelectedTaxes(null==i?null:i.taxes)?14:15),e.R7$(3),e.SpI(" $",e.bMT(19,15,e.i5U(18,12,m.calculateTotalInvoiceItem(null==i?null:i.rate,null==i?null:i.qty),2))," ")}}function je(c,g){if(1&c&&(e.j41(0,"div",34)(1,"div",58)(2,"p",59),e.EFF(3),e.k0s(),e.j41(4,"p",59),e.EFF(5),e.k0s()(),e.j41(6,"p",33),e.EFF(7),e.nI1(8,"decimal"),e.nI1(9,"formatNumber"),e.k0s()()),2&c){const p=g.$implicit;e.R7$(3),e.Lme(" ",p.name," (",p.amount,"%) "),e.R7$(2),e.SpI(" #",p.numberTax," "),e.R7$(2),e.SpI(" $",e.bMT(9,7,e.i5U(8,4,p.total,2))," ")}}function Fe(c,g){if(1&c){const p=e.RV6();e.j41(0,"div",60)(1,"button",61),e.bIt("click",function(){e.eBV(p);const n=e.XpG();return e.Njj(n.handleSave())}),e.EFF(2),e.nI1(3,"translate"),e.k0s(),e.j41(4,"button",62),e.bIt("click",function(){e.eBV(p);const n=e.XpG();return e.Njj(n.handleSendInvoice())}),e.EFF(5),e.nI1(6,"translate"),e.k0s()()}2&c&&(e.R7$(2),e.SpI(" ",e.bMT(3,2,"BUTTON.Save")," "),e.R7$(3),e.SpI(" ",e.bMT(6,4,"BUTTON.SendInvoice")," "))}let Re=(()=>{var c;class g{static getComponent(){return g}constructor(t,n,i,m,y,h,f,M,I,x){(0,s.A)(this,"dialogRef",void 0),(0,s.A)(this,"cdnService",void 0),(0,s.A)(this,"modifyInvoiceItemDialog",void 0),(0,s.A)(this,"modifyTaxesDialog",void 0),(0,s.A)(this,"selectTimeTrackingDialog",void 0),(0,s.A)(this,"selectExpensesDialog",void 0),(0,s.A)(this,"sendInvoiceDialog",void 0),(0,s.A)(this,"addNewItemDialog",void 0),(0,s.A)(this,"translate",void 0),(0,s.A)(this,"data",void 0),(0,s.A)(this,"taxArray",[]),(0,s.A)(this,"today",new Date),(0,s.A)(this,"projectName",void 0),(0,s.A)(this,"projectId",void 0),(0,s.A)(this,"base64",void 0),(0,s.A)(this,"type",void 0),(0,s.A)(this,"filename",void 0),(0,s.A)(this,"isEdit",void 0),(0,s.A)(this,"invoiceForm",void 0),(0,s.A)(this,"itemInvoice2",[]),(0,s.A)(this,"calculateTotalInvoiceItem",X.R2),(0,s.A)(this,"getNameSelectedTaxes",X.Xj),(0,s.A)(this,"calculateGroupedTaxes",X.yo),(0,s.A)(this,"projectAndClientOptions",[]),(0,s.A)(this,"subtotal",0),(0,s.A)(this,"totalAmount",0),(0,s.A)(this,"selectedDateStart",this.formatDate(new Date)),(0,s.A)(this,"selectedDateEnd",this.formatDate(new Date)),(0,s.A)(this,"imageUrl",void 0),(0,s.A)(this,"companyAvatarUrl",void 0),(0,s.A)(this,"companyBase64",void 0),(0,s.A)(this,"companyType",void 0),(0,s.A)(this,"companyFilename",void 0),(0,s.A)(this,"combinedAddress",void 0),(0,s.A)(this,"formBuilder",(0,e.WQX)(U.ze)),(0,s.A)(this,"inforUser",void 0),(0,s.A)(this,"InforCompany",void 0),(0,s.A)(this,"payment",[]),(0,s.A)(this,"nameTax",""),(0,s.A)(this,"sumtax",0),(0,s.A)(this,"invoiceNumber","0000001"),(0,s.A)(this,"listTax",[]),(0,s.A)(this,"_taxService",(0,e.WQX)(d)),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"router",(0,e.WQX)(Y.Ix)),(0,s.A)(this,"_toastService",(0,e.WQX)(N.f)),(0,s.A)(this,"_storeService",(0,e.WQX)(o.n)),(0,s.A)(this,"_spinnerService",(0,e.WQX)(re.D)),(0,s.A)(this,"_invoiceService",(0,e.WQX)(b.p)),(0,s.A)(this,"dataService",(0,e.WQX)(ae.u)),(0,s.A)(this,"dropdownOptionService",(0,e.WQX)(ie.R)),(0,s.A)(this,"layoutUtilsService",(0,e.WQX)(Z.Z)),(0,s.A)(this,"_companyServices",(0,e.WQX)(F.B)),(0,s.A)(this,"_expensesService",(0,e.WQX)(l.D)),(0,s.A)(this,"selectSearchClientElement",void 0),this.dialogRef=t,this.cdnService=n,this.modifyInvoiceItemDialog=i,this.modifyTaxesDialog=m,this.selectTimeTrackingDialog=y,this.selectExpensesDialog=h,this.sendInvoiceDialog=f,this.addNewItemDialog=M,this.translate=I,this.data=x,this.inforUser=this._storeService.get_InforUser(),this.invoiceForm=this.formBuilder.group({clientId:[x?.clientId??"",U.k0.compose([U.k0.required])],invoiceDate:[x?.invoiceDate??null,U.k0.compose([U.k0.required])],dueDate:[x?.dueDate??null,U.k0.compose([U.k0.required])],invoiceNumber:[x?.invoiceNumber],projectId:[""],notes:[x?.notes??""],itemInvoice:[[]]}),this.invoiceForm.get("itemInvoice")?.valueChanges.subscribe(O=>{this.subtotal=0,O?.forEach(A=>{this.subtotal+=(0,X.R2)(A?.rate,A?.qty)}),this.calculateAllTax()}),this.invoiceForm.get("invoiceNumber")?.disable(),this.data&&(this.projectId=this.data?.projectId,this.projectName=this.data?.project?.projectName,this.data.itemInvoices.forEach(O=>{O.taxes.forEach(A=>{A.selected=!0})}),this.f.itemInvoice.setValue(this.data?.itemInvoices??[]),this.data?.img&&this.GetImg(this.data?.img),this.isEdit=!!this.data.id)}GetImg(t){this.cdnService.GetFile(t).pipe((0,T.pQ)(this.destroyRef)).subscribe(n=>{if(n){const i=new FileReader;i.onload=()=>{this.imageUrl=i.result},i.readAsDataURL(n)}})}handleClose(){this.dialogRef.close()}calculateAllTax(){this.taxArray=[],this.sumtax=0;const t=(0,X.yo)(this.f.itemInvoice.value);this.taxArray=Object.values(t.totalTaxes),this.sumtax=t.grandTotalTax,this.totalAmount=this.subtotal+(isNaN(this.sumtax)?0:this.sumtax)}handleSelectProject(t){var n=this;return(0,C.A)(function*(){let i="",m="";"client"==t.metadata?.type?(i=t.value,m=""):(n.projectName=t.label,n.projectId=t.value,i=t.metadata?.objectClient?.id,m=t.value);const h=n.f.clientId.value===i,f=[...n.f.itemInvoice?.value??[]];if(!h&&f.length){if(!(yield n.layoutUtilsService.alertConfirm({title:"Warning",description:"You are changing the client, and the invoices will be reset. Are you sure you want to continue?"})))return;n.f.itemInvoice.setValue([])}n.f.clientId.setValue(i),n.f.projectId.setValue(m),n.selectSearchClientElement.handleCloseSearchResult()})()}ngOnInit(){this.dropdownOptionService.getDropdownOptionsProjectAndClientTimeTracking().then(t=>this.projectAndClientOptions=t),this._storeService.getRoleBusiness()!=le.X.Contractor?(this.data||this.CountInvoiceByCompany(),this.GetInforCompany()):this.CountInvoiceByContractor()}GetInforCompany(){this._companyServices.GetInforCompany().pipe((0,T.pQ)(this.destroyRef)).subscribe(t=>{t&&(this.InforCompany=t,this.combinedAddress=this.getAddress(),t.companyImage&&this.GetCompanyAvatar(t.companyImage))})}GetCompanyAvatar(t){this.cdnService.GetFile(t).pipe((0,T.pQ)(this.destroyRef)).subscribe(n=>{if(n){const i=new FileReader;i.onload=()=>{this.imageUrl=i.result,this.companyAvatarUrl=i.result},i.readAsDataURL(n)}})}saveCompanyAvatarIfChanged(){var t=this;return(0,C.A)(function*(){return new Promise(n=>{if(t.companyBase64&&t.companyFilename&&t.companyType){const i={...t.InforCompany,businessName:t.InforCompany?.businessName,base64:t.companyBase64,filename:t.companyFilename,type:t.companyType};t._companyServices.UpdateCompany(i).pipe((0,T.pQ)(t.destroyRef)).subscribe({next:m=>{m&&t._toastService.showSuccess("Company avatar updated","Success"),n()},error:()=>{t._toastService.showError("Failed to update company avatar","Error"),n()}})}else n()})})()}getAddress(){return(0,z.Aw)({addressLine1:this.InforCompany?.adress??"",addressLine2:this.InforCompany?.adress2??"",stateProvince:this.InforCompany?.province??"",postalCode:this.InforCompany?.postalCode??"",country:this.InforCompany?.country??""})}get f(){return this.invoiceForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(t=>{t.markAsTouched()})}formatDate(t){return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`}convertToHours(t){const[n,i]=t.split(":").map(Number);return n+i/60}handleChangePicture(t){var n=this;return(0,C.A)(function*(){if(!t||0===t.length)return n.imageUrl="",n.companyAvatarUrl="",n.companyBase64="",n.companyType="",void(n.companyFilename="");const i=t?.[0],{base64:m,fileName:y,type:h}=yield(0,z.EX)(i);n.companyBase64=m,n.companyType=h,n.companyFilename=y,n.base64=m,n.type=h,n.filename=y})()}CountInvoiceByContractor(){this._invoiceService.CountInvoiceByContractor().pipe((0,T.pQ)(this.destroyRef)).subscribe(t=>t?0==t?(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber=(t+1).toString().padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)))}CountInvoiceByCompany(){this._invoiceService.CountInvoiceByCompany().pipe((0,T.pQ)(this.destroyRef)).subscribe(t=>t?0==t?(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber=(t+1).toString().padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)):(this.invoiceNumber="1".padStart(7,"0"),void this.f.invoiceNumber.setValue(this.invoiceNumber)))}GetAllTax(){this._taxService.GetAllTax({Page:0,PageSize:100,Search:""}).pipe((0,T.pQ)(this.destroyRef)).subscribe({next:n=>{n&&(this.listTax=n.data,this.listTax.forEach(i=>{i.selected&&(this.nameTax=this.nameTax+","+i.name,this.sumtax+=i.amount)}),this.nameTax=this.nameTax.trimStart().replace(/^,/,"+"))}})}handleDeleteInvoiceItem(t){const n=[...this.f.itemInvoice?.value??[]];n?.length&&(n.splice(t,1),this.f.itemInvoice.setValue(n))}handleCancel(){this.dialogRef.close()}get getInvoicePayload(){if(this.invoiceForm.invalid)return null;let t={clientId:this.f.clientId.value,invoiceNumber:this.f.invoiceNumber.value,invoiceDate:D().utc(this.f.invoiceDate.value).toDate(),dueDate:D().utc(this.f.dueDate.value).toDate(),reference:"",notes:this.f.notes.value,projectId:this.f.projectId.value,payments:this.payment,taxes:this.listTax,base64:this.base64,type:this.type,filename:this.filename,itemInvoices:this.f.itemInvoice.value.map(n=>({...n,taxes:n.taxes.some(i=>i.companyTax)?n.taxes.map(({companyTax:i,...m})=>m):n.taxes.filter(i=>i.selected),description:this.createDescription(n)})),paidAmount:this.subtotal,taxAmount:this.sumtax,totalAmount:this.totalAmount,rate:0,status:0,timeAmount:0};return this.data&&this.data&&(t.id=this.data?.id,t.createdAt=this.data?.createdAt),t}handleAddUnBillTimeExpenses(){let t;const n=this.f.clientId.value,i=this.f.projectId.value;if(!n)return void this._toastService.showWarning("No selected client","Please select a client to add the time.");const m=this.projectAndClientOptions.find(I=>I.value===n)?.metadata?.client;if(!m)return this._toastService.showWarning("Not fount client");i&&(t=this.projectAndClientOptions.find(I=>I.value===i&&"project"==I.metadata.type)?.metadata?.project);const y=[...this.f.itemInvoice?.value??[]],h=y.map(I=>I.ExpensesId).filter(I=>I);this.selectExpensesDialog.open({client:m,project:t,listIdTimeTrackingSelected:h}).then(I=>{I.afterClosed().subscribe(x=>{if(!x?.length)return;const O=x.map(A=>A.expensesId);this._expensesService.GetExpenseItemsByExpenseIds(O).pipe((0,T.pQ)(this.destroyRef)).subscribe(A=>{if(!A?.length)return;const k=A.map($=>{const V=x.find(L=>L.expensesId===$.expensesId);return{ExpensesId:$.expensesId,description:`${V.expensesName} - ${$.description}`||"",rate:$.rate||0,qty:$.qty||1,total:$.total||0,taxes:$.taxes?.map(L=>({...L,selected:!0}))||[],date:V?.date,dateSelectItem:V?.dateSelectItem,projectName:V?.projectName,expensesName:V?.expensesName,inforUser:V?.inforUser,position:0,isnew:!0}});y.push(...k),this.f.itemInvoice.setValue(y)})})})}handleAddUnBillTime(){let t;const n=this.f.clientId.value,i=this.f.projectId.value;if(!n)return void this._toastService.showWarning("No selected client","Please select a client to add the time.");const m=this.projectAndClientOptions.find(I=>I.value===n)?.metadata?.client;if(!m)return this._toastService.showWarning("Not fount client");i&&(t=this.projectAndClientOptions.find(I=>I.value===i&&"project"==I.metadata.type)?.metadata?.project);const y=[...this.f.itemInvoice?.value??[]],h=y.map(I=>I.trackingId).filter(I=>I);this.selectTimeTrackingDialog.open({client:m,project:t,listIdTimeTrackingSelected:h}).then(I=>{I.afterClosed().subscribe(x=>{x?.length&&(y.push(...x),this.f.itemInvoice.setValue(y))})})}createDescription(t){const n=new Q.vh("en-US");if(0==t?.position||this.data&&!t?.isnew)return t?.description;{const i=t?.projectName,m=t?.itemName,y=t?.description,h=t?.serviceName,f=t?.expensesName,M=n.transform(t.dateSelectItem,"MMM, d yyyy");return[i,m,h,f,`${this.getFullName(t?.inforUser)}- ${M}`,y].filter(A=>null!=A&&""!==A).join("\n")}}handleSave(){if(this.invoiceForm.invalid)return this.markAllControlsAsTouched(),void this._toastService.showWarning(this.translate.instant("INVOICES.Warning")," ");this._spinnerService.show(),this.saveCompanyAvatarIfChanged().then(()=>{this.data&&!this.data?.isGenrate?this._invoiceService.UpdateInvoice(this.getInvoicePayload).pipe((0,T.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._spinnerService.hide(),this.dataService.triggerRefreshInvoice(),this.dialogRef.close(t),this._toastService.showSuccess("Save","Success"))}):this._invoiceService.CreatedInvoice(this.getInvoicePayload).pipe((0,T.pQ)(this.destroyRef)).subscribe(t=>{t&&(this._spinnerService.hide(),this.dataService.triggerRefreshInvoice(),this.dialogRef.close(t),this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")))})})}handleAddNewItem(){const t=this.addNewItemDialog.open(this.getInvoicePayload),n=[...this.f.itemInvoice?.value??[]];t.then(i=>{i.afterClosed().subscribe(m=>{if(!m?.length)return;const y=m.map(({id:h,isServices:f,taxes:M,itemName:I,projectId:x,serviceId:O,serviceName:A,projectName:k,description:$,createdAt:V,...L})=>({...L,description:$,itemName:I,itemId:0==f?h:null,projectId:1==f?x:null,projectName:k??null,serviceId:1==f?h:null,serviceName:A,date:V??this.today,isnew:!0,dateSelectItem:V??new Date,taxes:M?.map(({id:Me,itemId:Pe,serviceId:Ue,...Oe})=>Oe)}));n.push(...y),this.f.itemInvoice.setValue(n)})})}handleSendInvoice(){if(this.invoiceForm.invalid)return this.markAllControlsAsTouched(),void this._toastService.showWarning("Please fill in all the invoice information completely."," ");this.saveCompanyAvatarIfChanged().then(()=>{this.sendInvoiceDialog.open(this.getInvoicePayload).then(n=>{n.afterClosed().subscribe(i=>{i&&(this.dataService.triggerRefreshInvoice(),this.dialogRef.close())})})})}handleModifyInvoiceItem(t,n){const i=n&&{...n};0==i?.position&&(i.description=this.createDescription(i)),this.modifyInvoiceItemDialog.open(i).then(y=>{y.afterClosed().subscribe(h=>{if(!h)return;const f=this.f.itemInvoice.value??[];void 0===t?(h.projectName=this.projectName??h.projectName??f[t]?.projectName??"",h.projectId=this.projectId??h.projectId??f[t]?.projectId??null,f.push(h),this.f.itemInvoice.setValue(f)):(h.projectName=this.projectName??h.projectName??f[t]?.projectName??"",h.projectId=this.projectId??h.projectId??f[t]?.projectId??null,f[t]=f[t].dateSelectItem?{...h,itemName:f[t]?.itemName,date:h?.dateSelectItem??this.today,dateSelectItem:f[t].dateSelectItem,serviceId:f[t]?.serviceId,serviceName:f[t]?.service?.serviceName??f[t]?.serviceName??""}:h,this.f.itemInvoice.setValue(f)),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemInvoice.value.forEach(I=>{I.taxes.length>0?h.taxes.filter(x=>1==x.selected).forEach(x=>{I.taxes.some(A=>A.companyTax?A?.companyTax.name===x?.name:A.name===x.name)||I.taxes.push(x)}):h.taxes.forEach(x=>{I.taxes.push(x)})}),this.calculateAllTax())})})}handleSelectClient(t){this.f.clientId.setValue(t.value),this.selectSearchClientElement.handleCloseSearchResult()}RouterSetting(){this.dialogRef.close(),this.router.navigate(["/settings/business"])}handleModifyTaxes(t,n){const i=structuredClone(this.f.itemInvoice.value),m=t.map(h=>h.companyTax?(h.companyTax.selected=!0,h.companyTax):h);this.modifyTaxesDialog.open(m.filter(h=>h.selected)).then(h=>{h.afterClosed().subscribe(f=>{if(!f)return void this.f.itemInvoice.setValue(i);const M=this.f.itemInvoice.value??[];i.forEach((I,x)=>{n!=x&&(M[x].taxes=I.taxes)}),M[n].taxes=f.taxes,this.f.itemInvoice.setValue(M),this._storeService.get_ApplyTaxAll()&&(this._storeService.set_ApplyTaxAll(!1),this.f.itemInvoice.value.forEach(x=>{x.taxes.length>0?f.taxes.filter(O=>1==O.selected).forEach(O=>{x.taxes.some(k=>k.companyTax?k?.companyTax.name===O?.name:k.name===O.name)||x.taxes.push(O)}):f.taxes.forEach(O=>{x.taxes.push(O)})}),this.calculateAllTax())})})}getFullName(t){return t?t?.firstName&&t?.lastName?t.firstName+" "+t.lastName:t?.email??"":this.inforUser?.firstName&&this.inforUser?.lastName?this.inforUser.firstName+" "+this.inforUser.lastName:this.inforUser?.email??""}drop(t){t.previousContainer===t.container?(0,w.HD)(t.container.data,t.previousIndex,t.currentIndex):(0,w.eg)(t.previousContainer.data,t.container.data,t.previousIndex,t.currentIndex)}}return c=g,(0,s.A)(g,"\u0275fac",function(t){return new(t||c)(e.rXU(u.CP),e.rXU(ce.H),e.rXU(pe.r),e.rXU(me.I),e.rXU(de.d),e.rXU(he),e.rXU(Ie.o),e.rXU(fe.z),e.rXU(J.c$),e.rXU(u.Vh))}),(0,s.A)(g,"\u0275cmp",e.VBU({type:c,selectors:[["app-new-invoice"]],viewQuery:function(t,n){if(1&t&&e.GBs(xe,5),2&t){let i;e.mGM(i=e.lsd())&&(n.selectSearchClientElement=i.first)}},standalone:!0,features:[e.Jv_([Z.Z]),e.aNF],decls:106,vars:124,consts:[["selectSearchClientElement",""],["projectOptionTemplate",""],["customSubmitNewInvoice",""],[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"flex","w-full","gap-[18px]","xl:flex-row","flex-col"],[1,"w-[160px]","shrink-0","mx-auto","md:mx-[unset]"],["onerror","this.src='../../../../assets/img/image_default.svg'",3,"onChange","canEdit","isBussiness","placeholder","imageUrl"],[1,"w-full","flex","flex-col","gap-[16px]"],[1,"w-full"],[1,"text-text-md-semibold","text-text-primary","mb-[1px]"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]","font-bold"],[1,"flex","items-center","gap-[8px]"],[1,"w-full","text-text-xs-regular","text-text-secondary"],[1,"button-link-primary","mt-[3px]",3,"click"],[1,"w-full","grid","md:grid-cols-2","lg:grid-cols-4","gap-[16px]"],[3,"label","options","formControl","projectId","value","placeholder","errorMessages","customOptionTemplate"],["type","number",3,"label","formControl","value"],[3,"label","placeholder","formControl","value","errorMessages"],[3,"label","placeholder","formControl"],[1,"w-full","mt-[16px]","border-t","border-dashed","border-border-primary"],[1,"flex","gap-1.5","flex-wrap","justify-center","lg:justify-start","lg:flex-nowrap"],[1,"button-outline-primary","button-size-md","mt-[16px]","min-w-fit",3,"click"],["type","button",1,"button-outline","button-size-md","mt-[16px]","min-w-fit",3,"click"],["cdkDropList","",1,"overflow-auto","w-full",3,"cdkDropListDropped","cdkDropListData"],[1,"invoiceTableLayout"],[1,"text-text-tertiary","text-text-sm-semibold"],["cdkDrag","",1,"invoiceTableLayout"],[1,"mt-[8px]","button-size-md","button-outline-primary","w-full","border-dashed","justify-center",3,"click"],["src","../../../../../assets/img/icon/ic_add_green.svg","alt","Icon"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"],[1,"flex","justify-end","items-start","gap-[8px]","mb-2"],[1,"block"],[1,"button-link-primary"],[1,"text-text-primary","text-headline-md-bold","text-right","w-[160px]","shrink-0"],["footer",""],[3,"onCancel","customSubmitButton"],[1,"text-text-xs-regular","text-text-secondary"],[1,"w-full","flex","p-[8px]","items-center","gap-[10px]","rounded-md","cursor-pointer","hover:bg-bg-brand-primary",3,"click","ngClass"],[3,"size","name"],[1,"w-[32px]","h-[32px]","rounded-full","overflow-hidden","flex","justify-center","items-center","bg-bg-brand-primary","shrink-0"],[1,"line-clamp-1","text-text-primary","text-text-sm-regular","txtTitle"],[1,"line-clamp-1","text-text-tertiary","text-text-xs-regular","txtDescription"],["src","../../../assets/img/icon/ic_file_green.svg","alt","Icon",1,"w-[16px]"],[1,"flex","flex-col"],[1,"flex","items-center"],["cdkDragHandle","",1,"material-icons","cursor-pointer","mr-3","!text-[20px]"],[1,"text-text-primary","text-text-md-regular","whitespace-pre-line"],[1,"text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-regular","cursor-pointer"],[1,"text-blue-500","text-sm","cursor-pointer"],[1,"text-text-primary","text-text-md-bold"],[3,"onEdit","onDelete"],[1,"text-text-primary","text-text-md-regular","cursor-pointer",3,"click"],[1,"text-blue-500","text-sm","cursor-pointer",3,"click"],[1,"flex","flex-col","pl-2"],[1,"text-right","text-text-primary","text-text-sm-regular"],[1,"flex","items-center","gap-[12px]"],[1,"button-outline-primary","button-size-md",3,"click"],[1,"button-primary","button-size-md",3,"click"]],template:function(t,n){if(1&t){const i=e.RV6();e.j41(0,"app-inno-modal-wrapper",3),e.nI1(1,"translate"),e.bIt("onClose",function(){return e.eBV(i),e.Njj(n.handleClose())}),e.j41(2,"div",4)(3,"div",5)(4,"div",6)(5,"app-inno-upload",7),e.bIt("onChange",function(y){return e.eBV(i),e.Njj(n.handleChangePicture(y))}),e.k0s()(),e.j41(6,"div",8)(7,"div",9),e.DNE(8,ge,2,0,"p",10),e.j41(9,"p",11),e.EFF(10),e.k0s(),e.DNE(11,ye,3,1,"div",12)(12,Ee,2,1,"p",13),e.j41(13,"button",14),e.bIt("click",function(){return e.eBV(i),e.Njj(n.RouterSetting())}),e.EFF(14),e.nI1(15,"translate"),e.k0s()(),e.j41(16,"div",15)(17,"app-inno-form-select-search",16,0),e.nI1(19,"translate"),e.nI1(20,"translate"),e.nI1(21,"translate"),e.DNE(22,Ne,7,8,"ng-template",null,1,e.C5r),e.k0s(),e.nrm(24,"app-inno-form-input",17),e.nI1(25,"translate"),e.nrm(26,"app-inno-form-datepicker",18),e.nI1(27,"translate"),e.nI1(28,"translate"),e.nI1(29,"translate"),e.nrm(30,"app-inno-form-datepicker",18),e.nI1(31,"translate"),e.nI1(32,"translate"),e.nI1(33,"translate"),e.k0s(),e.j41(34,"div",9),e.nrm(35,"app-inno-form-textarea",19),e.nI1(36,"translate"),e.nI1(37,"translate"),e.k0s()()(),e.j41(38,"div",20)(39,"div",21)(40,"button",22),e.bIt("click",function(){return e.eBV(i),e.Njj(n.handleAddUnBillTime())}),e.EFF(41),e.nI1(42,"translate"),e.k0s(),e.j41(43,"button",23),e.bIt("click",function(){return e.eBV(i),e.Njj(n.handleAddNewItem())}),e.EFF(44),e.nI1(45,"translate"),e.k0s(),e.j41(46,"button",22),e.bIt("click",function(){return e.eBV(i),e.Njj(n.handleAddUnBillTimeExpenses())}),e.EFF(47),e.nI1(48,"translate"),e.k0s()(),e.j41(49,"div",24),e.bIt("cdkDropListDropped",function(y){return e.eBV(i),e.Njj(n.drop(y))}),e.j41(50,"div",25)(51,"p",26),e.EFF(52),e.nI1(53,"translate"),e.k0s(),e.j41(54,"p",26),e.EFF(55),e.nI1(56,"translate"),e.k0s(),e.j41(57,"p",26),e.EFF(58),e.nI1(59,"translate"),e.k0s(),e.j41(60,"p",26),e.EFF(61),e.nI1(62,"translate"),e.k0s(),e.j41(63,"p",26),e.EFF(64),e.nI1(65,"translate"),e.k0s()(),e.Z7z(66,De,21,17,"div",27,e.fX1),e.k0s(),e.j41(68,"button",28),e.bIt("click",function(){return e.eBV(i),e.Njj(n.handleModifyInvoiceItem())}),e.nrm(69,"img",29),e.EFF(70),e.nI1(71,"translate"),e.k0s()(),e.j41(72,"div",30)(73,"div",31)(74,"p",32),e.EFF(75),e.nI1(76,"translate"),e.k0s(),e.j41(77,"p",33),e.EFF(78),e.nI1(79,"decimal"),e.nI1(80,"formatNumber"),e.k0s()(),e.Z7z(81,je,10,9,"div",34,e.fX1),e.j41(83,"div",31)(84,"div",35)(85,"p",32),e.EFF(86),e.nI1(87,"translate"),e.k0s(),e.j41(88,"button",36),e.EFF(89),e.nI1(90,"translate"),e.k0s()(),e.j41(91,"p",33),e.EFF(92," $0 "),e.k0s()(),e.j41(93,"div",31)(94,"p",32),e.EFF(95),e.nI1(96,"translate"),e.nI1(97,"async"),e.k0s(),e.j41(98,"p",37),e.EFF(99),e.nI1(100,"decimal"),e.nI1(101,"formatNumber"),e.k0s()()()(),e.j41(102,"div",38)(103,"app-inno-modal-footer",39),e.bIt("onCancel",function(){return e.eBV(i),e.Njj(n.handleCancel())}),e.k0s(),e.DNE(104,Fe,7,6,"ng-template",null,2,e.C5r),e.k0s()()}if(2&t){const i=e.sdS(23),m=e.sdS(105);e.Y8G("title",e.bMT(1,52,n.isEdit?"INVOICES.Buttons.EditInvoice":"INVOICES.Buttons.NewInvoice")),e.R7$(5),e.Y8G("canEdit",!0)("isBussiness",!0)("placeholder","Avatar")("imageUrl",n.imageUrl),e.R7$(3),e.vxM(null!=n.InforCompany&&n.InforCompany.businessName?-1:8),e.R7$(2),e.SpI("",null==n.InforCompany?null:n.InforCompany.businessName," "),e.R7$(),e.vxM(null!=n.InforCompany&&n.InforCompany.phone?11:-1),e.R7$(),e.vxM(n.combinedAddress?12:-1),e.R7$(2),e.SpI(" ",e.bMT(15,54,"INVOICES.INVOICE_FORM.EditBusinessInfo")," "),e.R7$(3),e.Y8G("label",e.bMT(19,56,"INVOICES.INVOICE_FORM.ClientProject"))("options",n.projectAndClientOptions)("formControl",n.f.clientId)("projectId",n.f.projectId.value)("value",n.f.clientId.value)("placeholder",e.bMT(20,58,"INVOICES.INVOICE_FORM.ClientProjectPlaceholder"))("errorMessages",e.eq3(118,H,e.bMT(21,60,"INVOICES.INVOICE_FORM.Validation.ClientRequired")))("customOptionTemplate",i),e.R7$(7),e.Y8G("label",e.bMT(25,62,"INVOICES.INVOICE_FORM.InvoiceNumber"))("formControl",n.f.invoiceNumber)("value",n.f.invoiceNumber.value),e.R7$(2),e.Y8G("label",e.bMT(27,64,"INVOICES.INVOICE_FORM.IssueDate"))("placeholder",e.bMT(28,66,"INVOICES.INVOICE_FORM.IssueDatePlaceholder"))("formControl",n.f.invoiceDate)("value",n.f.invoiceDate.value)("errorMessages",e.eq3(120,H,e.bMT(29,68,"INVOICES.INVOICE_FORM.Validation.IssueDateRequired"))),e.R7$(4),e.Y8G("label",e.bMT(31,70,"INVOICES.INVOICE_FORM.DueDate"))("placeholder",e.bMT(32,72,"INVOICES.INVOICE_FORM.DueDatePlaceholder"))("formControl",n.f.dueDate)("value",n.f.dueDate.value)("errorMessages",e.eq3(122,H,e.bMT(33,74,"INVOICES.INVOICE_FORM.Validation.DueDateRequired"))),e.R7$(5),e.Y8G("label",e.bMT(36,76,"INVOICES.INVOICE_FORM.Description"))("placeholder",e.bMT(37,78,"INVOICES.INVOICE_FORM.DescriptionPlaceholder"))("formControl",n.f.notes),e.R7$(6),e.SpI(" ",e.bMT(42,80,"INVOICES.INVOICE_FORM.Buttons.AddUnbillTime")," "),e.R7$(3),e.SpI(" ",e.bMT(45,82,"INVOICES.INVOICE_FORM.Buttons.AddNewItem")," "),e.R7$(3),e.SpI(" ",e.bMT(48,84,"INVOICES.INVOICE_FORM.Buttons.AddUnbillExpense")," "),e.R7$(2),e.Y8G("cdkDropListData",n.f.itemInvoice.value),e.R7$(3),e.SpI(" ",e.bMT(53,86,"INVOICES.INVOICE_FORM.TableHeaders.InvoiceItem")," "),e.R7$(3),e.SpI(" ",e.bMT(56,88,"INVOICES.INVOICE_FORM.TableHeaders.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(59,90,"INVOICES.INVOICE_FORM.TableHeaders.Quantity")," "),e.R7$(3),e.SpI(" ",e.bMT(62,92,"INVOICES.INVOICE_FORM.TableHeaders.Tax")," "),e.R7$(3),e.SpI(" ",e.bMT(65,94,"INVOICES.INVOICE_FORM.TableHeaders.LineTotal")," "),e.R7$(2),e.Dyx(n.f.itemInvoice.value),e.R7$(4),e.SpI(" ",e.bMT(71,96,"INVOICES.INVOICE_FORM.Buttons.AddNewLine")," "),e.R7$(5),e.SpI(" ",e.bMT(76,98,"INVOICES.INVOICE_FORM.Subtotal")," "),e.R7$(3),e.SpI(" $",e.bMT(80,103,e.i5U(79,100,n.subtotal,2))," "),e.R7$(3),e.Dyx(n.taxArray),e.R7$(5),e.SpI(" ",e.bMT(87,105,"INVOICES.INVOICE_FORM.Discount")," "),e.R7$(3),e.SpI(" ",e.bMT(90,107,"INVOICES.INVOICE_FORM.AddDiscount")," "),e.R7$(6),e.Lme(" ",e.bMT(96,109,"INVOICES.INVOICE_FORM.AmountDue")," (",e.bMT(97,111,n._storeService.curencyCompany),") "),e.R7$(4),e.SpI(" $",e.bMT(101,116,e.i5U(100,113,n.totalAmount,2))," "),e.R7$(4),e.Y8G("customSubmitButton",m)}},dependencies:[_.G,Q.YU,U.BC,U.l_,Q.Jj,J.D9,K.mC,K.fw,Y.iI,w.ad,w.O7,w.T1,w.Fb,P.I,G.M,W.a,q.k,ee.C,te.K,ne.k,oe.P,se.p,S.Q],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.invoiceTableLayout[_ngcontent-%COMP%]{align-items:center;width:100%;min-width:70dvw;display:grid;grid-template-columns:minmax(200px,1fr) 100px 100px 100px 200px 100px;grid-column-gap:8px;padding-top:8px;padding-bottom:8px}"]})),g})()}}]);