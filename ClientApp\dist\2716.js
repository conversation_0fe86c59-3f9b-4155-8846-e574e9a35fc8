"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2716],{2716:(Z,I,c)=>{c.d(I,{H:()=>y});var v=c(9842),C=c(4438),a=c(4006);let y=(()=>{var b;class l{constructor(A){(0,v.A)(this,"matDialog",void 0),this.matDialog=A}}return b=l,(0,v.A)(l,"\u0275fac",function(A){return new(A||b)(C.rXU(a.bZ))}),(0,v.A)(l,"\u0275dir",C.FsC({type:b})),l})()},2529:(Z,I,c)=>{c.d(I,{Pe:()=>L,Ph:()=>j,lG:()=>W,sU:()=>B});var v=c(687),C=c(7987),a=c(6860),y=c(6939),b=c(177),l=c(4438),T=c(7336),A=c(1413),N=c(9030),z=c(7673),x=c(8203),k=c(9172);function G(_,g){}class L{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.closeOnNavigation=!0,this.closeOnDestroy=!0,this.closeOnOverlayDetachments=!0}}let B=(()=>{var _;class g extends y.lb{constructor(t,i,r,h,p,u,O,P){super(),this._elementRef=t,this._focusTrapFactory=i,this._config=h,this._interactivityChecker=p,this._ngZone=u,this._overlayRef=O,this._focusMonitor=P,this._platform=(0,l.WQX)(a.OD),this._focusTrap=null,this._elementFocusedBeforeDialogWasOpened=null,this._closeInteractionType=null,this._ariaLabelledByQueue=[],this._changeDetectorRef=(0,l.WQX)(l.gRc),this._injector=(0,l.WQX)(l.zZn),this._isDestroyed=!1,this.attachDomPortal=Q=>{this._portalOutlet.hasAttached();const Y=this._portalOutlet.attachDomPortal(Q);return this._contentAttached(),Y},this._document=r,this._config.ariaLabelledBy&&this._ariaLabelledByQueue.push(this._config.ariaLabelledBy)}_addAriaLabelledBy(t){this._ariaLabelledByQueue.push(t),this._changeDetectorRef.markForCheck()}_removeAriaLabelledBy(t){const i=this._ariaLabelledByQueue.indexOf(t);i>-1&&(this._ariaLabelledByQueue.splice(i,1),this._changeDetectorRef.markForCheck())}_contentAttached(){this._initializeFocusTrap(),this._handleBackdropClicks(),this._captureInitialFocus()}_captureInitialFocus(){this._trapFocus()}ngOnDestroy(){this._isDestroyed=!0,this._restoreFocus()}attachComponentPortal(t){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachComponentPortal(t);return this._contentAttached(),i}attachTemplatePortal(t){this._portalOutlet.hasAttached();const i=this._portalOutlet.attachTemplatePortal(t);return this._contentAttached(),i}_recaptureFocus(){this._containsFocus()||this._trapFocus()}_forceFocus(t,i){this._interactivityChecker.isFocusable(t)||(t.tabIndex=-1,this._ngZone.runOutsideAngular(()=>{const r=()=>{t.removeEventListener("blur",r),t.removeEventListener("mousedown",r),t.removeAttribute("tabindex")};t.addEventListener("blur",r),t.addEventListener("mousedown",r)})),t.focus(i)}_focusByCssSelector(t,i){let r=this._elementRef.nativeElement.querySelector(t);r&&this._forceFocus(r,i)}_trapFocus(){this._isDestroyed||(0,l.mal)(()=>{const t=this._elementRef.nativeElement;switch(this._config.autoFocus){case!1:case"dialog":this._containsFocus()||t.focus();break;case!0:case"first-tabbable":this._focusTrap?.focusInitialElement()||this._focusDialogContainer();break;case"first-heading":this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role="heading"]');break;default:this._focusByCssSelector(this._config.autoFocus)}},{injector:this._injector})}_restoreFocus(){const t=this._config.restoreFocus;let i=null;if("string"==typeof t?i=this._document.querySelector(t):"boolean"==typeof t?i=t?this._elementFocusedBeforeDialogWasOpened:null:t&&(i=t),this._config.restoreFocus&&i&&"function"==typeof i.focus){const r=(0,a.vc)(),h=this._elementRef.nativeElement;(!r||r===this._document.body||r===h||h.contains(r))&&(this._focusMonitor?(this._focusMonitor.focusVia(i,this._closeInteractionType),this._closeInteractionType=null):i.focus())}this._focusTrap&&this._focusTrap.destroy()}_focusDialogContainer(){this._elementRef.nativeElement.focus&&this._elementRef.nativeElement.focus()}_containsFocus(){const t=this._elementRef.nativeElement,i=(0,a.vc)();return t===i||t.contains(i)}_initializeFocusTrap(){this._platform.isBrowser&&(this._focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement),this._document&&(this._elementFocusedBeforeDialogWasOpened=(0,a.vc)()))}_handleBackdropClicks(){this._overlayRef.backdropClick().subscribe(()=>{this._config.disableClose&&this._recaptureFocus()})}}return(_=g).\u0275fac=function(t){return new(t||_)(l.rXU(l.aKT),l.rXU(v.GX),l.rXU(b.qQ,8),l.rXU(L),l.rXU(v.Z7),l.rXU(l.SKi),l.rXU(C.yY),l.rXU(v.FN))},_.\u0275cmp=l.VBU({type:_,selectors:[["cdk-dialog-container"]],viewQuery:function(t,i){if(1&t&&l.GBs(y.I3,7),2&t){let r;l.mGM(r=l.lsd())&&(i._portalOutlet=r.first)}},hostAttrs:["tabindex","-1",1,"cdk-dialog-container"],hostVars:6,hostBindings:function(t,i){2&t&&l.BMQ("id",i._config.id||null)("role",i._config.role)("aria-modal",i._config.ariaModal)("aria-labelledby",i._config.ariaLabel?null:i._ariaLabelledByQueue[0])("aria-label",i._config.ariaLabel)("aria-describedby",i._config.ariaDescribedBy||null)},standalone:!0,features:[l.Vt3,l.aNF],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(t,i){1&t&&l.DNE(0,G,0,0,"ng-template",0)},dependencies:[y.I3],styles:[".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}"],encapsulation:2}),g})();class f{constructor(g,m){this.overlayRef=g,this.config=m,this.closed=new A.B,this.disableClose=m.disableClose,this.backdropClick=g.backdropClick(),this.keydownEvents=g.keydownEvents(),this.outsidePointerEvents=g.outsidePointerEvents(),this.id=m.id,this.keydownEvents.subscribe(t=>{t.keyCode===T._f&&!this.disableClose&&!(0,T.rp)(t)&&(t.preventDefault(),this.close(void 0,{focusOrigin:"keyboard"}))}),this.backdropClick.subscribe(()=>{this.disableClose||this.close(void 0,{focusOrigin:"mouse"})}),this._detachSubscription=g.detachments().subscribe(()=>{!1!==m.closeOnOverlayDetachments&&this.close()})}close(g,m){if(this.containerInstance){const t=this.closed;this.containerInstance._closeInteractionType=m?.focusOrigin||"program",this._detachSubscription.unsubscribe(),this.overlayRef.dispose(),t.next(g),t.complete(),this.componentInstance=this.containerInstance=null}}updatePosition(){return this.overlayRef.updatePosition(),this}updateSize(g="",m=""){return this.overlayRef.updateSize({width:g,height:m}),this}addPanelClass(g){return this.overlayRef.addPanelClass(g),this}removePanelClass(g){return this.overlayRef.removePanelClass(g),this}}const K=new l.nKC("DialogScrollStrategy",{providedIn:"root",factory:()=>{const _=(0,l.WQX)(C.hJ);return()=>_.scrollStrategies.block()}}),F=new l.nKC("DialogData"),S=new l.nKC("DefaultDialogConfig");let H=0,W=(()=>{var _;class g{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}constructor(t,i,r,h,p,u){this._overlay=t,this._injector=i,this._defaultOptions=r,this._parentDialog=h,this._overlayContainer=p,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new A.B,this._afterOpenedAtThisLevel=new A.B,this._ariaHiddenElements=new Map,this.afterAllClosed=(0,N.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,k.Z)(void 0))),this._scrollStrategy=u}open(t,i){(i={...this._defaultOptions||new L,...i}).id=i.id||"cdk-dialog-"+H++,i.id&&this.getDialogById(i.id);const h=this._getOverlayConfig(i),p=this._overlay.create(h),u=new f(p,i),O=this._attachContainer(p,u,i);return u.containerInstance=O,this._attachDialogContent(t,u,O,i),this.openDialogs.length||this._hideNonDialogContentFromAssistiveTechnology(),this.openDialogs.push(u),u.closed.subscribe(()=>this._removeOpenDialog(u,!0)),this.afterOpened.next(u),u}closeAll(){R(this.openDialogs,t=>t.close())}getDialogById(t){return this.openDialogs.find(i=>i.id===t)}ngOnDestroy(){R(this._openDialogsAtThisLevel,t=>{!1===t.config.closeOnDestroy&&this._removeOpenDialog(t,!1)}),R(this._openDialogsAtThisLevel,t=>t.close()),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete(),this._openDialogsAtThisLevel=[]}_getOverlayConfig(t){const i=new C.rR({positionStrategy:t.positionStrategy||this._overlay.position().global().centerHorizontally().centerVertically(),scrollStrategy:t.scrollStrategy||this._scrollStrategy(),panelClass:t.panelClass,hasBackdrop:t.hasBackdrop,direction:t.direction,minWidth:t.minWidth,minHeight:t.minHeight,maxWidth:t.maxWidth,maxHeight:t.maxHeight,width:t.width,height:t.height,disposeOnNavigation:t.closeOnNavigation});return t.backdropClass&&(i.backdropClass=t.backdropClass),i}_attachContainer(t,i,r){const h=r.injector||r.viewContainerRef?.injector,p=[{provide:L,useValue:r},{provide:f,useValue:i},{provide:C.yY,useValue:t}];let u;r.container?"function"==typeof r.container?u=r.container:(u=r.container.type,p.push(...r.container.providers(r))):u=B;const O=new y.A8(u,r.viewContainerRef,l.zZn.create({parent:h||this._injector,providers:p}),r.componentFactoryResolver);return t.attach(O).instance}_attachDialogContent(t,i,r,h){if(t instanceof l.C4Q){const p=this._createInjector(h,i,r,void 0);let u={$implicit:h.data,dialogRef:i};h.templateContext&&(u={...u,..."function"==typeof h.templateContext?h.templateContext():h.templateContext}),r.attachTemplatePortal(new y.VA(t,null,u,p))}else{const p=this._createInjector(h,i,r,this._injector),u=r.attachComponentPortal(new y.A8(t,h.viewContainerRef,p,h.componentFactoryResolver));i.componentRef=u,i.componentInstance=u.instance}}_createInjector(t,i,r,h){const p=t.injector||t.viewContainerRef?.injector,u=[{provide:F,useValue:t.data},{provide:f,useValue:i}];return t.providers&&("function"==typeof t.providers?u.push(...t.providers(i,t,r)):u.push(...t.providers)),t.direction&&(!p||!p.get(x.dS,null,{optional:!0}))&&u.push({provide:x.dS,useValue:{value:t.direction,change:(0,z.of)()}}),l.zZn.create({parent:p||h,providers:u})}_removeOpenDialog(t,i){const r=this.openDialogs.indexOf(t);r>-1&&(this.openDialogs.splice(r,1),this.openDialogs.length||(this._ariaHiddenElements.forEach((h,p)=>{h?p.setAttribute("aria-hidden",h):p.removeAttribute("aria-hidden")}),this._ariaHiddenElements.clear(),i&&this._getAfterAllClosed().next()))}_hideNonDialogContentFromAssistiveTechnology(){const t=this._overlayContainer.getContainerElement();if(t.parentElement){const i=t.parentElement.children;for(let r=i.length-1;r>-1;r--){const h=i[r];h!==t&&"SCRIPT"!==h.nodeName&&"STYLE"!==h.nodeName&&!h.hasAttribute("aria-live")&&(this._ariaHiddenElements.set(h,h.getAttribute("aria-hidden")),h.setAttribute("aria-hidden","true"))}}}_getAfterAllClosed(){const t=this._parentDialog;return t?t._getAfterAllClosed():this._afterAllClosedAtThisLevel}}return(_=g).\u0275fac=function(t){return new(t||_)(l.KVO(C.hJ),l.KVO(l.zZn),l.KVO(S,8),l.KVO(_,12),l.KVO(C.Sf),l.KVO(K))},_.\u0275prov=l.jDH({token:_,factory:_.\u0275fac,providedIn:"root"}),g})();function R(_,g){let m=_.length;for(;m--;)g(_[m])}let j=(()=>{var _;class g{}return(_=g).\u0275fac=function(t){return new(t||_)},_.\u0275mod=l.$C({type:_}),_.\u0275inj=l.G2t({providers:[W],imports:[C.z_,y.jc,v.Pd,y.jc]}),g})()},4006:(Z,I,c)=>{c.d(I,{CP:()=>m,Vh:()=>i,bZ:()=>P,hM:()=>nt});var v=c(7987),C=c(177),a=c(4438),y=c(687),b=c(2529),l=c(4085),T=c(6939),A=c(1413),N=c(7786),z=c(9030),x=c(5964),k=c(6697),G=c(9172),L=c(7336),B=(c(7333),c(6600));function K(e,s){}c(9969);class F{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.delayFocusTrap=!0,this.closeOnNavigation=!0}}const S="mdc-dialog--open",V="mdc-dialog--opening",X="mdc-dialog--closing";let R=(()=>{var e;class s extends b.sU{constructor(n,o,D,E,M,w,U,et,st){super(n,o,D,E,M,w,U,st),this._animationMode=et,this._animationStateChanged=new a.bkB,this._animationsEnabled="NoopAnimations"!==this._animationMode,this._actionSectionCount=0,this._hostElement=this._elementRef.nativeElement,this._enterAnimationDuration=this._animationsEnabled?_(this._config.enterAnimationDuration)??150:0,this._exitAnimationDuration=this._animationsEnabled?_(this._config.exitAnimationDuration)??75:0,this._animationTimer=null,this._finishDialogOpen=()=>{this._clearAnimationClasses(),this._openAnimationDone(this._enterAnimationDuration)},this._finishDialogClose=()=>{this._clearAnimationClasses(),this._animationStateChanged.emit({state:"closed",totalTime:this._exitAnimationDuration})}}_contentAttached(){super._contentAttached(),this._startOpenAnimation()}_startOpenAnimation(){this._animationStateChanged.emit({state:"opening",totalTime:this._enterAnimationDuration}),this._animationsEnabled?(this._hostElement.style.setProperty(j,`${this._enterAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(V,S)),this._waitForAnimationToComplete(this._enterAnimationDuration,this._finishDialogOpen)):(this._hostElement.classList.add(S),Promise.resolve().then(()=>this._finishDialogOpen()))}_startExitAnimation(){this._animationStateChanged.emit({state:"closing",totalTime:this._exitAnimationDuration}),this._hostElement.classList.remove(S),this._animationsEnabled?(this._hostElement.style.setProperty(j,`${this._exitAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(X)),this._waitForAnimationToComplete(this._exitAnimationDuration,this._finishDialogClose)):Promise.resolve().then(()=>this._finishDialogClose())}_updateActionSectionCount(n){this._actionSectionCount+=n,this._changeDetectorRef.markForCheck()}_clearAnimationClasses(){this._hostElement.classList.remove(V,X)}_waitForAnimationToComplete(n,o){null!==this._animationTimer&&clearTimeout(this._animationTimer),this._animationTimer=setTimeout(o,n)}_requestAnimationFrame(n){this._ngZone.runOutsideAngular(()=>{"function"==typeof requestAnimationFrame?requestAnimationFrame(n):n()})}_captureInitialFocus(){this._config.delayFocusTrap||this._trapFocus()}_openAnimationDone(n){this._config.delayFocusTrap&&this._trapFocus(),this._animationStateChanged.next({state:"opened",totalTime:n})}ngOnDestroy(){super.ngOnDestroy(),null!==this._animationTimer&&clearTimeout(this._animationTimer)}attachComponentPortal(n){const o=super.attachComponentPortal(n);return o.location.nativeElement.classList.add("mat-mdc-dialog-component-host"),o}}return(e=s).\u0275fac=function(n){return new(n||e)(a.rXU(a.aKT),a.rXU(y.GX),a.rXU(C.qQ,8),a.rXU(F),a.rXU(y.Z7),a.rXU(a.SKi),a.rXU(v.yY),a.rXU(a.bc$,8),a.rXU(y.FN))},e.\u0275cmp=a.VBU({type:e,selectors:[["mat-dialog-container"]],hostAttrs:["tabindex","-1",1,"mat-mdc-dialog-container","mdc-dialog"],hostVars:10,hostBindings:function(n,o){2&n&&(a.Mr5("id",o._config.id),a.BMQ("aria-modal",o._config.ariaModal)("role",o._config.role)("aria-labelledby",o._config.ariaLabel?null:o._ariaLabelledByQueue[0])("aria-label",o._config.ariaLabel)("aria-describedby",o._config.ariaDescribedBy||null),a.AVh("_mat-animation-noopable",!o._animationsEnabled)("mat-mdc-dialog-container-with-actions",o._actionSectionCount>0))},standalone:!0,features:[a.Vt3,a.aNF],decls:3,vars:0,consts:[[1,"mat-mdc-dialog-inner-container","mdc-dialog__container"],[1,"mat-mdc-dialog-surface","mdc-dialog__surface"],["cdkPortalOutlet",""]],template:function(n,o){1&n&&(a.j41(0,"div",0)(1,"div",1),a.DNE(2,K,0,0,"ng-template",2),a.k0s()())},dependencies:[T.I3],styles:['.mat-mdc-dialog-container{width:100%;height:100%;display:block;box-sizing:border-box;max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;outline:0}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-inner-container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;transition:opacity linear var(--mat-dialog-transition-duration, 0ms);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mdc-dialog--closing .mat-mdc-dialog-inner-container{transition:opacity 75ms linear;transform:none}.mdc-dialog--open .mat-mdc-dialog-inner-container{opacity:1}._mat-animation-noopable .mat-mdc-dialog-inner-container{transition:none}.mat-mdc-dialog-surface{display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;width:100%;height:100%;position:relative;overflow-y:auto;outline:0;transform:scale(0.8);transition:transform var(--mat-dialog-transition-duration, 0ms) cubic-bezier(0, 0, 0.2, 1);max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit;box-shadow:var(--mat-dialog-container-elevation-shadow, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12));border-radius:var(--mdc-dialog-container-shape, var(--mat-app-corner-extra-large, 4px));background-color:var(--mdc-dialog-container-color, var(--mat-app-surface, white))}[dir=rtl] .mat-mdc-dialog-surface{text-align:right}.mdc-dialog--open .mat-mdc-dialog-surface,.mdc-dialog--closing .mat-mdc-dialog-surface{transform:none}._mat-animation-noopable .mat-mdc-dialog-surface{transition:none}.mat-mdc-dialog-surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}.mat-mdc-dialog-title{display:block;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-title::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}[dir=rtl] .mat-mdc-dialog-title{text-align:right}.mat-mdc-dialog-container .mat-mdc-dialog-title{color:var(--mdc-dialog-subhead-color, var(--mat-app-on-surface, rgba(0, 0, 0, 0.87)));font-family:var(--mdc-dialog-subhead-font, var(--mat-app-headline-small-font, inherit));line-height:var(--mdc-dialog-subhead-line-height, var(--mat-app-headline-small-line-height, 1.5rem));font-size:var(--mdc-dialog-subhead-size, var(--mat-app-headline-small-size, 1rem));font-weight:var(--mdc-dialog-subhead-weight, var(--mat-app-headline-small-weight, 400));letter-spacing:var(--mdc-dialog-subhead-tracking, var(--mat-app-headline-small-tracking, 0.03125em))}.mat-mdc-dialog-content{display:block;flex-grow:1;box-sizing:border-box;margin:0;overflow:auto;max-height:65vh}.mat-mdc-dialog-content>:first-child{margin-top:0}.mat-mdc-dialog-content>:last-child{margin-bottom:0}.mat-mdc-dialog-container .mat-mdc-dialog-content{color:var(--mdc-dialog-supporting-text-color, var(--mat-app-on-surface-variant, rgba(0, 0, 0, 0.6)));font-family:var(--mdc-dialog-supporting-text-font, var(--mat-app-body-medium-font, inherit));line-height:var(--mdc-dialog-supporting-text-line-height, var(--mat-app-body-medium-line-height, 1.5rem));font-size:var(--mdc-dialog-supporting-text-size, var(--mat-app-body-medium-size, 1rem));font-weight:var(--mdc-dialog-supporting-text-weight, var(--mat-app-body-medium-weight, 400));letter-spacing:var(--mdc-dialog-supporting-text-tracking, var(--mat-app-body-medium-tracking, 0.03125em))}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0);padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.cdk-high-contrast-active .mat-mdc-dialog-actions{border-top-color:CanvasText}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}.mat-mdc-dialog-component-host{display:contents}'],encapsulation:2}),s})();const j="--mat-dialog-transition-duration";function _(e){return null==e?null:"number"==typeof e?e:e.endsWith("ms")?(0,l.OE)(e.substring(0,e.length-2)):e.endsWith("s")?1e3*(0,l.OE)(e.substring(0,e.length-1)):"0"===e?0:null}var g=function(e){return e[e.OPEN=0]="OPEN",e[e.CLOSING=1]="CLOSING",e[e.CLOSED=2]="CLOSED",e}(g||{});class m{constructor(s,d,n){this._ref=s,this._containerInstance=n,this._afterOpened=new A.B,this._beforeClosed=new A.B,this._state=g.OPEN,this.disableClose=d.disableClose,this.id=s.id,s.addPanelClass("mat-mdc-dialog-panel"),n._animationStateChanged.pipe((0,x.p)(o=>"opened"===o.state),(0,k.s)(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),n._animationStateChanged.pipe((0,x.p)(o=>"closed"===o.state),(0,k.s)(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),this._finishDialogClose()}),s.overlayRef.detachments().subscribe(()=>{this._beforeClosed.next(this._result),this._beforeClosed.complete(),this._finishDialogClose()}),(0,N.h)(this.backdropClick(),this.keydownEvents().pipe((0,x.p)(o=>o.keyCode===L._f&&!this.disableClose&&!(0,L.rp)(o)))).subscribe(o=>{this.disableClose||(o.preventDefault(),function t(e,s,d){return e._closeInteractionType=s,e.close(d)}(this,"keydown"===o.type?"keyboard":"mouse"))})}close(s){this._result=s,this._containerInstance._animationStateChanged.pipe((0,x.p)(d=>"closing"===d.state),(0,k.s)(1)).subscribe(d=>{this._beforeClosed.next(s),this._beforeClosed.complete(),this._ref.overlayRef.detachBackdrop(),this._closeFallbackTimeout=setTimeout(()=>this._finishDialogClose(),d.totalTime+100)}),this._state=g.CLOSING,this._containerInstance._startExitAnimation()}afterOpened(){return this._afterOpened}afterClosed(){return this._ref.closed}beforeClosed(){return this._beforeClosed}backdropClick(){return this._ref.backdropClick}keydownEvents(){return this._ref.keydownEvents}updatePosition(s){let d=this._ref.config.positionStrategy;return s&&(s.left||s.right)?s.left?d.left(s.left):d.right(s.right):d.centerHorizontally(),s&&(s.top||s.bottom)?s.top?d.top(s.top):d.bottom(s.bottom):d.centerVertically(),this._ref.updatePosition(),this}updateSize(s="",d=""){return this._ref.updateSize(s,d),this}addPanelClass(s){return this._ref.addPanelClass(s),this}removePanelClass(s){return this._ref.removePanelClass(s),this}getState(){return this._state}_finishDialogClose(){this._state=g.CLOSED,this._ref.close(this._result,{focusOrigin:this._closeInteractionType}),this.componentInstance=null}}const i=new a.nKC("MatMdcDialogData"),r=new a.nKC("mat-mdc-dialog-default-options"),h=new a.nKC("mat-mdc-dialog-scroll-strategy",{providedIn:"root",factory:()=>{const e=(0,a.WQX)(v.hJ);return()=>e.scrollStrategies.block()}});let O=0,P=(()=>{var e;class s{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}_getAfterAllClosed(){const n=this._parentDialog;return n?n._getAfterAllClosed():this._afterAllClosedAtThisLevel}constructor(n,o,D,E,M,w,U,et){this._overlay=n,this._defaultOptions=E,this._scrollStrategy=M,this._parentDialog=w,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new A.B,this._afterOpenedAtThisLevel=new A.B,this.dialogConfigClass=F,this.afterAllClosed=(0,z.v)(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe((0,G.Z)(void 0))),this._dialog=o.get(b.lG),this._dialogRefConstructor=m,this._dialogContainerType=R,this._dialogDataToken=i}open(n,o){let D;(o={...this._defaultOptions||new F,...o}).id=o.id||"mat-mdc-dialog-"+O++,o.scrollStrategy=o.scrollStrategy||this._scrollStrategy();const E=this._dialog.open(n,{...o,positionStrategy:this._overlay.position().global().centerHorizontally().centerVertically(),disableClose:!0,closeOnDestroy:!1,closeOnOverlayDetachments:!1,container:{type:this._dialogContainerType,providers:()=>[{provide:this.dialogConfigClass,useValue:o},{provide:b.Pe,useValue:o}]},templateContext:()=>({dialogRef:D}),providers:(M,w,U)=>(D=new this._dialogRefConstructor(M,o,U),D.updatePosition(o?.position),[{provide:this._dialogContainerType,useValue:U},{provide:this._dialogDataToken,useValue:w.data},{provide:this._dialogRefConstructor,useValue:D}])});return D.componentRef=E.componentRef,D.componentInstance=E.componentInstance,this.openDialogs.push(D),this.afterOpened.next(D),D.afterClosed().subscribe(()=>{const M=this.openDialogs.indexOf(D);M>-1&&(this.openDialogs.splice(M,1),this.openDialogs.length||this._getAfterAllClosed().next())}),D}closeAll(){this._closeDialogs(this.openDialogs)}getDialogById(n){return this.openDialogs.find(o=>o.id===n)}ngOnDestroy(){this._closeDialogs(this._openDialogsAtThisLevel),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete()}_closeDialogs(n){let o=n.length;for(;o--;)n[o].close()}}return(e=s).\u0275fac=function(n){return new(n||e)(a.KVO(v.hJ),a.KVO(a.zZn),a.KVO(C.aZ,8),a.KVO(r,8),a.KVO(h),a.KVO(e,12),a.KVO(v.Sf),a.KVO(a.bc$,8))},e.\u0275prov=a.jDH({token:e,factory:e.\u0275fac,providedIn:"root"}),s})(),nt=(()=>{var e;class s{}return(e=s).\u0275fac=function(n){return new(n||e)},e.\u0275mod=a.$C({type:e}),e.\u0275inj=a.G2t({providers:[P],imports:[b.Ph,v.z_,T.jc,B.yE,B.yE]}),s})()}}]);