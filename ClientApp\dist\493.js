"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[493],{493:(d,i,t)=>{t.r(i),t.d(i,{RecurringTemplatesComponent:()=>g});var l=t(9842),p=t(480),e=t(4438),s=t(4006),m=t(1537);function u(o,a){if(1&o){const c=e.RV6();e.j41(0,"div",2)(1,"button",3),e.bIt("click",function(){e.eBV(c);const r=e.XpG();return e.Njj(r.NewInvoice())}),e.EFF(2,"New Recurring Templates"),e.k0s()()}}let g=(()=>{var o;class a{constructor(n){(0,l.A)(this,"newInvoiceDialog",void 0),(0,l.A)(this,"dialog",(0,e.WQX)(s.bZ)),this.newInvoiceDialog=n}NewInvoice(){this.newInvoiceDialog.open({}).then(r=>{r.afterClosed().subscribe(_=>{})})}}return o=a,(0,l.A)(a,"\u0275fac",function(n){return new(n||o)(e.rXU(m.u))}),(0,l.A)(a,"\u0275cmp",e.VBU({type:o,selectors:[["app-recurring-templates"]],standalone:!0,features:[e.aNF],decls:4,vars:1,consts:[["actionTemplate",""],["title","Recurring Templates",3,"actionTemplate"],[1,"flex","gap-2","align-content-end"],[1,"btn","btn-success",3,"click"]],template:function(n,r){if(1&n&&(e.j41(0,"app-breadcrum",1),e.DNE(1,u,3,0,"ng-template",null,0,e.C5r),e.k0s(),e.nrm(3,"hr")),2&n){const _=e.sdS(2);e.Y8G("actionTemplate",_)}},dependencies:[p.Z,s.hM]})),a})()}}]);