"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[469],{7086:(b,u,o)=>{o.d(u,{C:()=>r});var n=o(9842),e=o(4438),a=o(9417),t=o(6146),l=o(8192),T=o(177);const f=i=>({"resize-none":i});function h(i,p){if(1&i&&(e.j41(0,"label",3),e.EFF(1),e.k0s()),2&i){const s=e.XpG();e.AVh("required",s.isRequired),e.R7$(),e.JRh(s.label)}}function v(i,p){if(1&i){const s=e.RV6();e.j41(0,"textarea",4),e.bIt("keyup",function(I){e.eBV(s);const A=e.XpG();return e.Njj(<PERSON><PERSON><PERSON><PERSON>(I))}),e.k0s(),e.nrm(1,"app-inno-error-message",5)}if(2&i){const s=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",s.class,""),e.Y8G("placeholder",s.placeholder)("formControl",s.formControl)("ngClass",e.eq3(7,f,!s.isAbleResize)),e.R7$(),e.Y8G("message",s.getErrorMessage())}}function d(i,p){if(1&i){const s=e.RV6();e.j41(0,"textarea",6),e.bIt("keyup",function(I){e.eBV(s);const A=e.XpG();return e.Njj(A.handleChange(I))}),e.k0s()}if(2&i){const s=e.XpG();e.ZvI("w-full text-left text-text-md-regular text-text-secondary p-[8px] rounded-md border-2 border-border-primary min-h-[40px] h-[70px] placeholder-text-placeholder ",s.class,""),e.Y8G("value",s.value)("placeholder",s.placeholder)("ngClass",e.eq3(6,f,!s.isAbleResize))}}let r=(()=>{var i;class p{constructor(){(0,n.A)(this,"class",""),(0,n.A)(this,"isAbleResize",!1),(0,n.A)(this,"isRequired",void 0),(0,n.A)(this,"label",""),(0,n.A)(this,"placeholder",""),(0,n.A)(this,"value",""),(0,n.A)(this,"onChange",new e.bkB),(0,n.A)(this,"formControl",void 0),(0,n.A)(this,"errorMessages",void 0)}registerOnChange(c){}registerOnTouched(c){}setDisabledState(c){}writeValue(c){}hasError(){return this.formControl?.invalid&&(this.formControl.dirty||this.formControl.touched)}getErrorMessage(){if(!this.hasError())return"";if(this.formControl?.errors&&this.errorMessages)for(const c in this.formControl.errors)if(this.errorMessages[c])return this.errorMessages[c];return""}handleChange(c){this.onChange.emit(c?.target?.value??"")}}return i=p,(0,n.A)(p,"\u0275fac",function(c){return new(c||i)}),(0,n.A)(p,"\u0275cmp",e.VBU({type:i,selectors:[["app-inno-form-textarea"]],inputs:{class:"class",isAbleResize:"isAbleResize",isRequired:"isRequired",label:"label",placeholder:"placeholder",value:"value",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[e.Jv_([{provide:a.kq,useExisting:(0,e.Rfq)(()=>i),multi:!0}]),e.aNF],decls:4,vars:4,consts:[[1,"w-full","flex","flex-col","relative"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]",3,"required"],[3,"value","placeholder","class","ngClass"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[3,"keyup","placeholder","formControl","ngClass"],[3,"message"],[3,"keyup","value","placeholder","ngClass"]],template:function(c,I){1&c&&(e.j41(0,"div",0),e.DNE(1,h,2,3,"label",1)(2,v,2,9)(3,d,1,8,"textarea",2),e.k0s()),2&c&&(e.AVh("error",I.hasError()),e.R7$(),e.vxM(I.label?1:-1),e.R7$(),e.vxM(I.formControl?2:3))},dependencies:[t.G,T.YU,a.me,a.BC,a.l_,l.Y],styles:['p[_ngcontent-%COMP%]{margin-bottom:0}.btnShowHide[_ngcontent-%COMP%]{top:30px;background-color:transparent}.showTogglePassword[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding-right:40px}.isRequired[_ngcontent-%COMP%]:after{content:" *";color:var(--text-danger)}']})),p})()},4978:(b,u,o)=>{o.d(u,{I:()=>v});var n=o(9842),e=o(4438),a=o(6146),t=o(5236);const l=["*",[["","footer",""]]],T=["*","[footer]"];function f(d,r){if(1&d){const i=e.RV6();e.j41(0,"button",7),e.bIt("click",function(){e.eBV(i);const s=e.XpG(2);return e.Njj(s.handleClose())}),e.nrm(1,"img",8),e.k0s()}}function h(d,r){if(1&d&&(e.j41(0,"div",4)(1,"p",5),e.EFF(2),e.nI1(3,"translate"),e.k0s()(),e.DNE(4,f,2,0,"button",6)),2&d){const i=e.XpG();e.R7$(2),e.JRh(e.bMT(3,2,i.title)),e.R7$(2),e.vxM(i.onClose.observers.length?4:-1)}}let v=(()=>{var d;class r{constructor(){(0,n.A)(this,"title",void 0),(0,n.A)(this,"onClose",new e.bkB)}handleClose(){this.onClose.emit()}}return d=r,(0,n.A)(r,"\u0275fac",function(p){return new(p||d)}),(0,n.A)(r,"\u0275cmp",e.VBU({type:d,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[e.aNF],ngContentSelectors:T,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(p,s){1&p&&(e.NAR(l),e.j41(0,"div",0)(1,"div",1),e.DNE(2,h,5,4),e.k0s(),e.j41(3,"div",2),e.SdG(4),e.k0s(),e.j41(5,"div",3),e.SdG(6,1),e.k0s()()),2&p&&(e.R7$(2),e.vxM(s.title?2:-1))},dependencies:[a.G,t.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),r})()},6586:(b,u,o)=>{o.d(u,{A:()=>n,a:()=>e});var n=function(a){return a.Created_Tab="Created_Tab",a.Sent_To_Me_Tab="Sent_To_Me_Tab",a}(n||{}),e=function(a){return a[a.all=1]="all",a[a.this_month=2]="this_month",a[a.last_month=3]="last_month",a[a.custom=4]="custom",a}(e||{})},1588:(b,u,o)=>{o.d(u,{j:()=>n});var n=function(e){return e.Day="Day",e.Week="Week",e.Month="Month",e.All="All",e}(n||{})},5277:(b,u,o)=>{o.d(u,{u:()=>f});var n=o(9842),e=o(4438),a=o(6586),t=o(1588),l=o(4412),T=o(1413);let f=(()=>{var h;class v{constructor(){(0,n.A)(this,"behaviorTimeTrackingTypeView",new l.t(t.j.Day)),(0,n.A)(this,"behaviorTimeTrackingDate",new l.t(void 0)),(0,n.A)(this,"behaviorTimeTrackingFilter",new l.t({typeView:t.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,n.A)(this,"behaviorTimeTrackingCreateTimer",new l.t(void 0)),(0,n.A)(this,"behaviorisInternalClient",new l.t(!1)),(0,n.A)(this,"behaviorTimeTrackingShowingTimer",new l.t(!1)),(0,n.A)(this,"reloadItem",new T.B),(0,n.A)(this,"reloadService",new T.B),(0,n.A)(this,"isEstimate",(0,e.vPA)(!1)),(0,n.A)(this,"behaviorInvoiceFilter",new l.t({typeView:a.A.Created_Tab,textSearch:""}))}SetisInternalClient(r){this.behaviorisInternalClient.next(r)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(r){this.behaviorTimeTrackingTypeView.next(r)}SetNewTimeTrackingDate(r){this.behaviorTimeTrackingDate.next(r)}SetNewTimeTrackingShowingTimer(r){this.behaviorTimeTrackingShowingTimer.next(r)}SetNewTimeTrackingCreateTimerInfo(r){this.behaviorTimeTrackingCreateTimer.next(r)}SetNewTimeTrackingFilter(r){this.behaviorTimeTrackingFilter.next(r)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(r){localStorage.setItem("ResumeData",JSON.stringify(r))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(r){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...r})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return h=v,(0,n.A)(v,"\u0275fac",function(r){return new(r||h)}),(0,n.A)(v,"\u0275prov",e.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})),v})()},2387:(b,u,o)=>{o.d(u,{I:()=>T});var n=o(467),e=o(9842),a=o(2716),t=o(7987),l=o(4438);let T=(()=>{var f;class h extends a.H{open(d){var r=this;return(0,n.A)(function*(){const i=yield Promise.all([o.e(1328),o.e(2076),o.e(4592)]).then(o.bind(o,4592));return r.matDialog.open(i.ModifyTaxesComponent.getComponent(),{panelClass:"custom_dialog",data:d,width:"500px",scrollStrategy:new t.t0})})()}}return f=h,(0,e.A)(h,"\u0275fac",(()=>{let v;return function(r){return(v||(v=l.xGo(f)))(r||f)}})()),(0,e.A)(h,"\u0275prov",l.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})),h})()},469:(b,u,o)=>{o.r(u),o.d(u,{ModifyInvoiceItemComponent:()=>S});var n=o(9842),e=o(5277),a=o(7086),t=o(4438),l=o(9417),T=o(4006),f=o(1328),h=o(344),v=o(4978),d=o(6146),r=o(177),i=o(1110),p=o(5909),s=o(2387),c=o(5236);const I=M=>({required:M});function A(M,x){if(1&M&&(t.j41(0,"div",6)(1,"label",9),t.EFF(2,"Taxes"),t.k0s(),t.j41(3,"p",10),t.EFF(4),t.k0s()()),2&M){const P=t.XpG();t.R7$(4),t.SpI(" ",P.listTaxName," ")}}let S=(()=>{var M;class x{static getComponent(){return x}constructor(_,m,g){(0,n.A)(this,"dialogRef",void 0),(0,n.A)(this,"modifyTaxesDialog",void 0),(0,n.A)(this,"data",void 0),(0,n.A)(this,"invoiceItemForm",void 0),(0,n.A)(this,"today",new Date),(0,n.A)(this,"listTaxName",""),(0,n.A)(this,"inforUser",void 0),(0,n.A)(this,"title",void 0),(0,n.A)(this,"cleanupEffect",void 0),(0,n.A)(this,"formBuilder",(0,t.WQX)(l.ze)),(0,n.A)(this,"dataService",(0,t.WQX)(e.u)),(0,n.A)(this,"storeService",(0,t.WQX)(i.n)),this.dialogRef=_,this.modifyTaxesDialog=m,this.data=g,this.cleanupEffect=(0,t.QZP)(()=>{const E=this.data,y=this.dataService.isEstimate();this.title=E&&y?"ITEM_INVOICE.EditEstimateItem":E&&!y?"ITEM_INVOICE.EditInvoiceItem":!E&&y?"ITEM_INVOICE.NewEstimateItem":"ITEM_INVOICE.NewInvoiceItem"}),this.inforUser=this.data?.inforUser??this.storeService.get_InforUser();const C=this.data?this.createDescription(this.data):"",{rate:D,qty:R,taxes:F}=g||{},O=F?.map(E=>({...E,selected:!0}))??[];this.invoiceItemForm=this.formBuilder.group({description:[C,[]],rate:[D,l.k0.compose([l.k0.required])],qty:[R,l.k0.compose([l.k0.required])],taxes:[O]}),O&&(this.listTaxName=(0,p.Xj)(O)),this.invoiceItemForm.valueChanges.subscribe(E=>{this.listTaxName=(0,p.Xj)(E.taxes)})}ngOnDestroy(){this.cleanupEffect&&this.cleanupEffect.destroy()}getFullName(){return this.inforUser?.firstName&&this.inforUser?.lastName?this.inforUser.firstName+" "+this.inforUser.lastName:this.inforUser?.email??""}createDescription(_){if(0==_?.position||_?.id)return _?.description;{const m=new r.vh("en-US"),g=_?.projectName??"",C=_?.itemName??"",D=_?.description,R=_?.serviceName??"",F=_?.expensesName,O=m.transform(_?.dateSelectItem,"MMM, d yyyy");return[g,C,R,F,`${this.getFullName()}- ${O}`,D].filter(B=>null!=B&&""!==B).join("\n")}}handleClose(){this.dialogRef.close()}get f(){return this.invoiceItemForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(_=>{_.markAsTouched()})}handleCancel(){this.dialogRef.close()}handleSubmit(){if(this.invoiceItemForm.invalid)return void this.markAllControlsAsTouched();const _={description:this.f.description.value,rate:this.f.rate.value,qty:this.f.qty.value,taxes:this.f.taxes.value,date:this.today,position:this.data?.position??0,dateSelectItem:this.data?.date??this.today,trackingId:this.data?.trackingId,serviceId:this.data?.serviceId,projectId:this.data?.projectId,projectName:this.data?.projectName,expensesName:this.data?.expensesName,ExpensesId:this.data?.ExpensesId};this.data?.id&&(_.id=this.data.id),this.dialogRef.close(_)}handleModifyTaxes(){const m=[...this.f.taxes?.value?.filter(C=>C.companyTax).map(C=>({...C.companyTax,selected:!0})),...this.f.taxes?.value];this.modifyTaxesDialog.open(m.filter(C=>C.selected)??[]).then(C=>{C.afterClosed().subscribe(D=>{D?.taxes&&this.f.taxes.setValue(D.taxes)})})}}return M=x,(0,n.A)(x,"\u0275fac",function(_){return new(_||M)(t.rXU(T.CP),t.rXU(s.I),t.rXU(T.Vh))}),(0,n.A)(x,"\u0275cmp",t.VBU({type:M,selectors:[["app-modify-invoice-item"]],standalone:!0,features:[t.aNF],decls:15,vars:29,consts:[[3,"onClose","title"],[3,"formGroup"],[1,"w-full","flex","flex-col","gap-[16px]","px-[16px]","pb-[16px]"],["label","Description",3,"isAbleResize","formControl","value","placeholder"],["type","number","label","Rate","placeholder","0.00",3,"formControl","value","errorMessages"],["type","number","label","Qty",3,"placeholder","formControl","value","errorMessages"],[1,"w-full","flex","flex-col","relative"],[1,"button-link-primary",3,"click"],[3,"onCancel","onSubmit"],[1,"text-text-secondary","text-text-sm-semibold","mb-[2px]"],[1,"text-text-primary","text-text-sm-bold"]],template:function(_,m){1&_&&(t.j41(0,"app-inno-modal-wrapper",0),t.bIt("onClose",function(){return m.handleClose()}),t.j41(1,"form",1)(2,"div",2),t.nrm(3,"app-inno-form-textarea",3),t.nI1(4,"translate"),t.nrm(5,"app-inno-form-input",4),t.nI1(6,"translate"),t.nrm(7,"app-inno-form-input",5),t.nI1(8,"translate"),t.nI1(9,"translate"),t.DNE(10,A,5,1,"div",6),t.j41(11,"button",7),t.bIt("click",function(){return m.handleModifyTaxes()}),t.EFF(12),t.nI1(13,"translate"),t.k0s()(),t.j41(14,"app-inno-modal-footer",8),t.bIt("onCancel",function(){return m.handleCancel()})("onSubmit",function(){return m.handleSubmit()}),t.k0s()()()),2&_&&(t.Y8G("title",m.title),t.R7$(),t.Y8G("formGroup",m.invoiceItemForm),t.R7$(2),t.Y8G("isAbleResize",!0)("formControl",m.f.description)("value",m.f.description.value)("placeholder",t.bMT(4,15,"ITEM_INVOICE.EnterInvoiceDescription")),t.R7$(2),t.Y8G("formControl",m.f.rate)("value",m.f.rate.value)("errorMessages",t.eq3(25,I,t.bMT(6,17,"ITEM_INVOICE.RateRequired"))),t.R7$(2),t.Y8G("placeholder",t.bMT(8,19,"ITEM_INVOICE.EnterQty"))("formControl",m.f.qty)("value",m.f.qty.value)("errorMessages",t.eq3(27,I,t.bMT(9,21,"ITEM_INVOICE.QuantityRequired"))),t.R7$(3),t.vxM(null!=m.listTaxName&&m.listTaxName.length?10:-1),t.R7$(2),t.SpI(" ",t.bMT(13,23,"ITEM_INVOICE.AddUpdateTaxes")," "))},dependencies:[d.G,l.qT,l.BC,l.cb,l.l_,l.j4,c.D9,v.I,h.k,f.a,a.C],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),x})()}}]);