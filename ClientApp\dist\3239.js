"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[3239],{4978:(v,m,n)=>{n.d(m,{I:()=>u});var r=n(9842),e=n(4438),a=n(6146),d=n(5236);const t=["*",[["","footer",""]]],h=["*","[footer]"];function T(s,i){if(1&s){const _=e.RV6();e.j41(0,"button",7),e.bIt("click",function(){e.eBV(_);const l=e.XpG(2);return e.Njj(l.handleClose())}),e.nrm(1,"img",8),e.k0s()}}function c(s,i){if(1&s&&(e.j41(0,"div",4)(1,"p",5),e.<PERSON>(2),e.nI1(3,"translate"),e.k0s()(),e.<PERSON><PERSON>(4,T,2,0,"button",6)),2&s){const _=e.XpG();e.R7$(2),e.<PERSON>h(e.bMT(3,2,_.title)),e.R7$(2),e.vxM(_.onClose.observers.length?4:-1)}}let u=(()=>{var s;class i{constructor(){(0,r.A)(this,"title",void 0),(0,r.A)(this,"onClose",new e.bkB)}handleClose(){this.onClose.emit()}}return s=i,(0,r.A)(i,"\u0275fac",function(o){return new(o||s)}),(0,r.A)(i,"\u0275cmp",e.VBU({type:s,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[e.aNF],ngContentSelectors:h,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(o,l){1&o&&(e.NAR(t),e.j41(0,"div",0)(1,"div",1),e.DNE(2,c,5,4),e.k0s(),e.j41(3,"div",2),e.SdG(4),e.k0s(),e.j41(5,"div",3),e.SdG(6,1),e.k0s()()),2&o&&(e.R7$(2),e.vxM(l.title?2:-1))},dependencies:[a.G,d.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),i})()},3239:(v,m,n)=>{n.r(m),n.d(m,{ConfirmTimerComponent:()=>u});var r=n(467),e=n(9842),a=n(4978),d=n(5277),t=n(4438),h=n(4006),T=n(6473),c=n(2928);let u=(()=>{var s;class i{static getComponent(){return i}constructor(o,l){(0,e.A)(this,"dialogRef",void 0),(0,e.A)(this,"data",void 0),(0,e.A)(this,"dataService",(0,t.WQX)(d.u)),(0,e.A)(this,"authenticationService",(0,t.WQX)(c.k)),(0,e.A)(this,"unsubscribe",[]),(0,e.A)(this,"timerHours","00:00:00"),this.dialogRef=o,this.data=l}handleClose(){this.dialogRef.close()}ngOnInit(){const o=this.dataService.GetTimeTrackingCreateTimerInfo().subscribe(l=>{this.timerHours=(0,T.Lc)(l?.totalSeconds??0)});this.unsubscribe.push(o)}onNoClick(){this.dialogRef.close(),this.handleDestroy()}onYesClick(){var o=this;return(0,r.A)(function*(){o.authenticationService.UpdateTimer({isRunning:!1,timer:null,timerStartTime:null}).subscribe(),o.dialogRef.close(!0),o.dataService.SetNewTimeTrackingShowingTimer(!1),o.handleDestroy()})()}handleDestroy(){this.unsubscribe&&this.unsubscribe.forEach(o=>{o.unsubscribe()})}ngOnDestroy(){this.handleDestroy()}}return s=i,(0,e.A)(i,"\u0275fac",function(o){return new(o||s)(t.rXU(h.CP),t.rXU(h.Vh))}),(0,e.A)(i,"\u0275cmp",t.VBU({type:s,selectors:[["app-confirm-timer"]],standalone:!0,features:[t.aNF],decls:14,vars:1,consts:[["title","Warning Time",3,"onClose"],[1,"p-3"],[1,"modal-title","h4","text-text-md-regular","pb-2"],[1,"text-text-primary","text-headline-md-bold"],[1,"mt-7"],[1,"pb-2","flex","justify-end"],["type","button",1,"py-2.5","px-5","me-2","mb-2","text-sm","font-medium","text-gray-900","focus:outline-none","bg-white","rounded-lg","border","border-gray-200","hover:bg-gray-100","hover:text-blue-700","focus:z-10","focus:ring-4","focus:ring-gray-100",3,"click"],["type","button",1,"text-white","bg-blue-700","hover:bg-blue-800","focus:ring-4","focus:ring-blue-300","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"]],template:function(o,l){1&o&&(t.j41(0,"app-inno-modal-wrapper",0),t.bIt("onClose",function(){return l.handleClose()}),t.j41(1,"div",1)(2,"div",2),t.EFF(3,"You are tracking time ! Do you want to move your business ?"),t.k0s(),t.j41(4,"div")(5,"p",3),t.EFF(6),t.k0s()(),t.nrm(7,"hr"),t.j41(8,"div",4)(9,"div",5)(10,"button",6),t.bIt("click",function(){return l.onNoClick()}),t.EFF(11," No"),t.k0s(),t.j41(12,"button",7),t.bIt("click",function(){return l.onYesClick()}),t.EFF(13," Yes"),t.k0s()()()()()),2&o&&(t.R7$(6),t.SpI(" Time: ",l.timerHours||"00:00:00"," "))},dependencies:[a.I]})),i})()},6586:(v,m,n)=>{n.d(m,{A:()=>r,a:()=>e});var r=function(a){return a.Created_Tab="Created_Tab",a.Sent_To_Me_Tab="Sent_To_Me_Tab",a}(r||{}),e=function(a){return a[a.all=1]="all",a[a.this_month=2]="this_month",a[a.last_month=3]="last_month",a[a.custom=4]="custom",a}(e||{})},1588:(v,m,n)=>{n.d(m,{j:()=>r});var r=function(e){return e.Day="Day",e.Week="Week",e.Month="Month",e.All="All",e}(r||{})},5277:(v,m,n)=>{n.d(m,{u:()=>T});var r=n(9842),e=n(4438),a=n(6586),d=n(1588),t=n(4412),h=n(1413);let T=(()=>{var c;class u{constructor(){(0,r.A)(this,"behaviorTimeTrackingTypeView",new t.t(d.j.Day)),(0,r.A)(this,"behaviorTimeTrackingDate",new t.t(void 0)),(0,r.A)(this,"behaviorTimeTrackingFilter",new t.t({typeView:d.j.Day,userSelected:void 0,clientSelected:void 0,projectSelected:void 0,startDate:void 0,endDate:void 0,dateSelected:new Date,textSearch:""})),(0,r.A)(this,"behaviorTimeTrackingCreateTimer",new t.t(void 0)),(0,r.A)(this,"behaviorisInternalClient",new t.t(!1)),(0,r.A)(this,"behaviorTimeTrackingShowingTimer",new t.t(!1)),(0,r.A)(this,"reloadItem",new h.B),(0,r.A)(this,"reloadService",new h.B),(0,r.A)(this,"isEstimate",(0,e.vPA)(!1)),(0,r.A)(this,"behaviorInvoiceFilter",new t.t({typeView:a.A.Created_Tab,textSearch:""}))}SetisInternalClient(i){this.behaviorisInternalClient.next(i)}getisInternalClient(){return this.behaviorisInternalClient.value}SetNewTimeTrackingTypeView(i){this.behaviorTimeTrackingTypeView.next(i)}SetNewTimeTrackingDate(i){this.behaviorTimeTrackingDate.next(i)}SetNewTimeTrackingShowingTimer(i){this.behaviorTimeTrackingShowingTimer.next(i)}SetNewTimeTrackingCreateTimerInfo(i){this.behaviorTimeTrackingCreateTimer.next(i)}SetNewTimeTrackingFilter(i){this.behaviorTimeTrackingFilter.next(i)}triggerRefreshListTimeTracking(){this.behaviorTimeTrackingFilter.next({...this.behaviorTimeTrackingFilter.value})}GetTimeTrackingTypeView(){return this.behaviorTimeTrackingTypeView.asObservable()}GetTimeTrackingDate(){return this.behaviorTimeTrackingDate.asObservable()}GetTimeTrackingShowingTimer(){return this.behaviorTimeTrackingShowingTimer.asObservable()}GetTimeTrackingShowingTimerValue(){return this.behaviorTimeTrackingShowingTimer.value}GetTimeTrackingCreateTimerInfo(){return this.behaviorTimeTrackingCreateTimer.asObservable()}GetTimeTrackingCreateTimerInfoValue(){return this.behaviorTimeTrackingCreateTimer.value}GetTimeTrackingFilter(){return this.behaviorTimeTrackingFilter.asObservable()}GetTimeTrackingFilterValue(){return this.behaviorTimeTrackingFilter.value}SetResume(i){localStorage.setItem("ResumeData",JSON.stringify(i))}getResume(){if(localStorage.getItem("ResumeData"))return JSON.parse(localStorage.getItem("ResumeData")?.toString())}SetNewInvoiceFilter(i){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value,...i})}ResetInvoiceFilter(){this.behaviorInvoiceFilter.next({})}triggerRefreshInvoice(){this.behaviorInvoiceFilter.next({...this.behaviorInvoiceFilter.value})}GetInvoiceFilter(){return this.behaviorInvoiceFilter.asObservable()}GetInvoiceFilterValue(){return this.behaviorInvoiceFilter.value}}return c=u,(0,r.A)(u,"\u0275fac",function(i){return new(i||c)}),(0,r.A)(u,"\u0275prov",e.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),u})()}}]);