"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7875],{4433:(ot,me,G)=>{G.d(me,{Q:()=>s});var E=G(9842),Q=G(4438);let s=(()=>{var Y;class H{transform(I,A=2){const P=Math.pow(10,A);return(Math.trunc(Number((I*P).toFixed(A+5)))/P).toFixed(A)}}return Y=H,(0,E.A)(H,"\u0275fac",function(I){return new(I||Y)}),(0,E.A)(H,"\u0275pipe",Q.EJ8({name:"decimal",type:Y,pure:!0,standalone:!0})),H})()},6617:(ot,me,G)=>{G.d(me,{p:()=>Y});var E=G(9842),Q=G(6473),s=G(4438);let Y=(()=>{var H;class y{transform(A){return(0,Q.ZV)(A)}}return H=y,(0,E.A)(y,"\u0275fac",function(A){return new(A||H)}),(0,E.A)(y,"\u0275pipe",s.EJ8({name:"formatNumber",type:H,pure:!0,standalone:!0})),y})()},5644:(ot,me,G)=>{G.d(me,{p:()=>A});var E=G(9842),Q=G(1342),s=G(5312),Y=G(1626),H=G(4438),y=G(6473);const I=s.c.HOST_API+"/api";let A=(()=>{var P;class S{constructor(){(0,E.A)(this,"http",(0,H.WQX)(Y.Qq)),(0,E.A)(this,"spinnerService",(0,H.WQX)(Q.D))}CreatedInvoice(v){return this.http.post(I+"/Invoices/CreatedInvoice",v)}CreatedInvoiceSend(v){return this.http.post(I+"/Invoices/CreatedInvoiceSend",v)}CountInvoiceByCompany(){return this.http.get(I+"/Invoices/CountInvoiceByCompany")}CountInvoiceByContractor(){return this.http.get(I+"/Invoices/CountInvoiceByContractor")}CountEstimate(){return this.http.get(I+"/Invoices/CountEstimate")}SendMailInvoice(v){return this.http.post(I+"/Invoices/SendMailInvoice",v)}GetAllEstimate(v){const F=(0,y.yU)(v);return this.http.get(I+"/Invoices/GetAllEstimate",{params:F})}GetAllInvoice(v){const F=(0,y.yU)(v);return this.http.get(I+"/Invoices/GetAllInvoice",{params:F})}GetAllInvoiceSendToMe(v){const F=(0,y.yU)(v);return this.http.get(I+"/Invoices/GetAllInvoiceSendToMe",{params:F})}GetAllEstimateSendToMe(v){return this.http.post(I+"/Invoices/GetAllEstimateSendToMe",v)}DeleteInvoice(v,F){return this.http.post(I+`/Invoices/DeleteInvoice?isActive=${F}`,v)}ChangePosition(v){return this.http.post(I+"/Invoices/ChangePosition?",v)}GetRevenueChart(v){return this.http.get(I+`/Invoices/GetRevenueChart?year=${v}`)}GraphicsChart(v){return this.http.get(I+`/Invoices/GraphicsChart?year=${v}`)}MarkAsPaid(v){return this.http.post(I+`/Invoices/MarkAsPaid?Id=${v}`,null)}MarkAsSent(v){return this.http.post(I+`/Invoices/MarkAsSent?Id=${v}`,null)}UpdateInvoice(v){return v?.itemInvoices.forEach(F=>{F.user=null}),this.http.post(I+"/Invoices/UpdateInvoice",v)}UpdateArchive(v,F){return this.http.post(I+`/Invoices/UpdateArchive?invoiceId=${v}&isArchive=${F}`,{})}ConvertToInvoice(v){return this.http.post(I+`/Invoices/ConvertToInvoice?invoiceId=${v}`,{})}GetInvoiceById(v){return this.http.get(I+`/Invoices/GetInvoiceById?InvoiceId=${v}`)}PrintInvoiceById(v,F){this.http.get(`${I}/Invoices/PrintInvoice?InvoiceId=${v}`,{responseType:"blob"}).subscribe({next:N=>{const be=new Blob([N],{type:"application/pdf"}),xe=window.URL.createObjectURL(be),Be=document.createElement("a");Be.href=xe,Be.download=`Invoice_${F}.pdf`,document.body.appendChild(Be),Be.click(),this.spinnerService.hide(),window.URL.revokeObjectURL(xe)},error:N=>{console.error("Error downloading the invoice:",N)}})}GetInvoiceByIdLink(v){return this.http.get(I+`/Invoices/GetInvoiceByIdLink?InvoiceId=${v}`)}CalculationInvoice(v){return this.http.get(I+`/Invoices/CalculationInvoice?status=${v}`)}CalculationInvoiceSendToMe(v){return this.http.get(I+`/Invoices/CalculationInvoiceSendToMe?status=${v}`)}CalculationEstimateSendToMe(v){return this.http.get(I+`/Invoices/CalculationEstimateSendToMe?status=${v}`)}CalculationEstimate(v){return this.http.get(I+`/Invoices/CalculationEstimate?status=${v}`)}uploadFile(v){const F=new FormData;return F.append("formFile",v),this.http.post(I+"/Invoices/UploadFile",F)}}return P=S,(0,E.A)(S,"\u0275fac",function(v){return new(v||P)}),(0,E.A)(S,"\u0275prov",H.jDH({token:P,factory:P.\u0275fac,providedIn:"root"})),S})()},7875:(ot,me,G)=>{G.r(me),G.d(me,{DashboardComponent:()=>wp});var E=G(9842),Q=G(4591),s=G(4438),Y=G(9079),H=G(5236),y=G(177),I=G(1635),A=G(6939),P=G(3726),S=G(152),_=G(9969);function v(){}function F(t){return null==t?v:function(){return this.querySelector(t)}}function xe(){return[]}function Be(t){return null==t?xe:function(){return this.querySelectorAll(t)}}function Bn(t){return function(){return this.matches(t)}}function Vn(t){return function(n){return n.matches(t)}}var Ar=Array.prototype.find;function Dr(){return this.firstElementChild}var Er=Array.prototype.filter;function Mr(){return Array.from(this.children)}function On(t){return new Array(t.length)}function Qt(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function Gr(t,n,e,i,a,r){for(var l,o=0,h=n.length,c=r.length;o<c;++o)(l=n[o])?(l.__data__=r[o],i[o]=l):e[o]=new Qt(t,r[o]);for(;o<h;++o)(l=n[o])&&(a[o]=l)}function $r(t,n,e,i,a,r,o){var l,h,g,c=new Map,m=n.length,d=r.length,u=new Array(m);for(l=0;l<m;++l)(h=n[l])&&(u[l]=g=o.call(h,h.__data__,l,n)+"",c.has(g)?a[l]=h:c.set(g,h));for(l=0;l<d;++l)g=o.call(t,r[l],l,r)+"",(h=c.get(g))?(i[l]=h,h.__data__=r[l],c.delete(g)):e[l]=new Qt(t,r[l]);for(l=0;l<m;++l)(h=n[l])&&c.get(u[l])===h&&(a[l]=h)}function Rr(t){return t.__data__}function Xr(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Ur(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}Qt.prototype={constructor:Qt,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var ki="http://www.w3.org/1999/xhtml";const In={svg:"http://www.w3.org/2000/svg",xhtml:ki,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function qt(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),In.hasOwnProperty(n)?{space:In[n],local:t}:t}function to(t){return function(){this.removeAttribute(t)}}function io(t){return function(){this.removeAttributeNS(t.space,t.local)}}function no(t,n){return function(){this.setAttribute(t,n)}}function ao(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function so(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function ro(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}function Pn(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function lo(t){return function(){this.style.removeProperty(t)}}function co(t,n,e){return function(){this.style.setProperty(t,n,e)}}function ho(t,n,e){return function(){var i=n.apply(this,arguments);null==i?this.style.removeProperty(t):this.style.setProperty(t,i,e)}}function lt(t,n){return t.style.getPropertyValue(n)||Pn(t).getComputedStyle(t,null).getPropertyValue(n)}function uo(t){return function(){delete this[t]}}function go(t,n){return function(){this[t]=n}}function po(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function Gn(t){return t.trim().split(/^|\s+/)}function Ai(t){return t.classList||new $n(t)}function $n(t){this._node=t,this._names=Gn(t.getAttribute("class")||"")}function Rn(t,n){for(var e=Ai(t),i=-1,a=n.length;++i<a;)e.add(n[i])}function Hn(t,n){for(var e=Ai(t),i=-1,a=n.length;++i<a;)e.remove(n[i])}function xo(t){return function(){Rn(this,t)}}function _o(t){return function(){Hn(this,t)}}function vo(t,n){return function(){(n.apply(this,arguments)?Rn:Hn)(this,t)}}function To(){this.textContent=""}function Co(t){return function(){this.textContent=t}}function bo(t){return function(){var n=t.apply(this,arguments);this.textContent=n??""}}function So(){this.innerHTML=""}function ko(t){return function(){this.innerHTML=t}}function Ao(t){return function(){var n=t.apply(this,arguments);this.innerHTML=n??""}}function Do(){this.nextSibling&&this.parentNode.appendChild(this)}function Eo(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Bo(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===ki&&n.documentElement.namespaceURI===ki?n.createElement(t):n.createElementNS(e,t)}}function Vo(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Xn(t){var n=qt(t);return(n.local?Vo:Bo)(n)}function Io(){return null}function Go(){var t=this.parentNode;t&&t.removeChild(this)}function Ro(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function Ho(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function Wo(t){return function(){var n=this.__on;if(n){for(var r,e=0,i=-1,a=n.length;e<a;++e)r=n[e],t.type&&r.type!==t.type||r.name!==t.name?n[++i]=r:this.removeEventListener(r.type,r.listener,r.options);++i?n.length=i:delete this.__on}}}function jo(t,n,e){return function(){var a,i=this.__on,r=function No(t){return function(n){t.call(this,n,this.__data__)}}(n);if(i)for(var o=0,l=i.length;o<l;++o)if((a=i[o]).type===t.type&&a.name===t.name)return this.removeEventListener(a.type,a.listener,a.options),this.addEventListener(a.type,a.listener=r,a.options=e),void(a.value=n);this.addEventListener(t.type,r,e),a={type:t.type,name:t.name,value:n,listener:r,options:e},i?i.push(a):this.__on=[a]}}function Yn(t,n,e){var i=Pn(t),a=i.CustomEvent;"function"==typeof a?a=new a(n,e):(a=i.document.createEvent("Event"),e?(a.initEvent(n,e.bubbles,e.cancelable),a.detail=e.detail):a.initEvent(n,!1,!1)),t.dispatchEvent(a)}function Qo(t,n){return function(){return Yn(this,t,n)}}function qo(t,n){return function(){return Yn(this,t,n.apply(this,arguments))}}$n.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Nn=[null];function we(t,n){this._groups=t,this._parents=n}function zn(){return new we([[document.documentElement]],Nn)}we.prototype=zn.prototype={constructor:we,select:function N(t){"function"!=typeof t&&(t=F(t));for(var n=this._groups,e=n.length,i=new Array(e),a=0;a<e;++a)for(var h,c,r=n[a],o=r.length,l=i[a]=new Array(o),m=0;m<o;++m)(h=r[m])&&(c=t.call(h,h.__data__,m,r))&&("__data__"in h&&(c.__data__=h.__data__),l[m]=c);return new we(i,this._parents)},selectAll:function kr(t){t="function"==typeof t?function Sr(t){return function(){return function be(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}(t.apply(this,arguments))}}(t):Be(t);for(var n=this._groups,e=n.length,i=[],a=[],r=0;r<e;++r)for(var h,o=n[r],l=o.length,c=0;c<l;++c)(h=o[c])&&(i.push(t.call(h,h.__data__,c,o)),a.push(h));return new we(i,a)},selectChild:function Fr(t){return this.select(null==t?Dr:function Lr(t){return function(){return Ar.call(this.children,t)}}("function"==typeof t?t:Vn(t)))},selectChildren:function Vr(t){return this.selectAll(null==t?Mr:function Br(t){return function(){return Er.call(this.children,t)}}("function"==typeof t?t:Vn(t)))},filter:function Or(t){"function"!=typeof t&&(t=Bn(t));for(var n=this._groups,e=n.length,i=new Array(e),a=0;a<e;++a)for(var h,r=n[a],o=r.length,l=i[a]=[],c=0;c<o;++c)(h=r[c])&&t.call(h,h.__data__,c,r)&&l.push(h);return new we(i,this._parents)},data:function Hr(t,n){if(!arguments.length)return Array.from(this,Rr);var e=n?$r:Gr,i=this._parents,a=this._groups;"function"!=typeof t&&(t=function Pr(t){return function(){return t}}(t));for(var r=a.length,o=new Array(r),l=new Array(r),h=new Array(r),c=0;c<r;++c){var m=i[c],d=a[c],u=d.length,g=Xr(t.call(m,m&&m.__data__,c,i)),f=g.length,b=l[c]=new Array(f),p=o[c]=new Array(f);e(m,d,b,p,h[c]=new Array(u),g,n);for(var M,V,L=0,T=0;L<f;++L)if(M=b[L]){for(L>=T&&(T=L+1);!(V=p[T])&&++T<f;);M._next=V||null}}return(o=new we(o,i))._enter=l,o._exit=h,o},enter:function Ir(){return new we(this._enter||this._groups.map(On),this._parents)},exit:function Yr(){return new we(this._exit||this._groups.map(On),this._parents)},join:function Nr(t,n,e){var i=this.enter(),a=this,r=this.exit();return"function"==typeof t?(i=t(i))&&(i=i.selection()):i=i.append(t+""),null!=n&&(a=n(a))&&(a=a.selection()),null==e?r.remove():e(r),i&&a?i.merge(a).order():a},merge:function zr(t){for(var n=t.selection?t.selection():t,e=this._groups,i=n._groups,a=e.length,o=Math.min(a,i.length),l=new Array(a),h=0;h<o;++h)for(var g,c=e[h],m=i[h],d=c.length,u=l[h]=new Array(d),f=0;f<d;++f)(g=c[f]||m[f])&&(u[f]=g);for(;h<a;++h)l[h]=e[h];return new we(l,this._parents)},selection:function Jo(){return this},order:function Wr(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var o,i=t[n],a=i.length-1,r=i[a];--a>=0;)(o=i[a])&&(r&&4^o.compareDocumentPosition(r)&&r.parentNode.insertBefore(o,r),r=o);return this},sort:function jr(t){function n(d,u){return d&&u?t(d.__data__,u.__data__):!d-!u}t||(t=Ur);for(var e=this._groups,i=e.length,a=new Array(i),r=0;r<i;++r){for(var c,o=e[r],l=o.length,h=a[r]=new Array(l),m=0;m<l;++m)(c=o[m])&&(h[m]=c);h.sort(n)}return new we(a,this._parents).order()},call:function Qr(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function qr(){return Array.from(this)},node:function Zr(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var i=t[n],a=0,r=i.length;a<r;++a){var o=i[a];if(o)return o}return null},size:function Kr(){let t=0;for(const n of this)++t;return t},empty:function Jr(){return!this.node()},each:function eo(t){for(var n=this._groups,e=0,i=n.length;e<i;++e)for(var l,a=n[e],r=0,o=a.length;r<o;++r)(l=a[r])&&t.call(l,l.__data__,r,a);return this},attr:function oo(t,n){var e=qt(t);if(arguments.length<2){var i=this.node();return e.local?i.getAttributeNS(e.space,e.local):i.getAttribute(e)}return this.each((null==n?e.local?io:to:"function"==typeof n?e.local?ro:so:e.local?ao:no)(e,n))},style:function mo(t,n,e){return arguments.length>1?this.each((null==n?lo:"function"==typeof n?ho:co)(t,n,e??"")):lt(this.node(),t)},property:function fo(t,n){return arguments.length>1?this.each((null==n?uo:"function"==typeof n?po:go)(t,n)):this.node()[t]},classed:function yo(t,n){var e=Gn(t+"");if(arguments.length<2){for(var i=Ai(this.node()),a=-1,r=e.length;++a<r;)if(!i.contains(e[a]))return!1;return!0}return this.each(("function"==typeof n?vo:n?xo:_o)(e,n))},text:function wo(t){return arguments.length?this.each(null==t?To:("function"==typeof t?bo:Co)(t)):this.node().textContent},html:function Lo(t){return arguments.length?this.each(null==t?So:("function"==typeof t?Ao:ko)(t)):this.node().innerHTML},raise:function Fo(){return this.each(Do)},lower:function Mo(){return this.each(Eo)},append:function Oo(t){var n="function"==typeof t?t:Xn(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function Po(t,n){var e="function"==typeof t?t:Xn(t),i=null==n?Io:"function"==typeof n?n:F(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),i.apply(this,arguments)||null)})},remove:function $o(){return this.each(Go)},clone:function Xo(t){return this.select(t?Ho:Ro)},datum:function Yo(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function Uo(t,n,e){var a,o,i=function zo(t){return t.trim().split(/^|\s+/).map(function(n){var e="",i=n.indexOf(".");return i>=0&&(e=n.slice(i+1),n=n.slice(0,i)),{type:n,name:e}})}(t+""),r=i.length;if(!(arguments.length<2)){for(l=n?jo:Wo,a=0;a<r;++a)this.each(l(i[a],n,e));return this}var l=this.node().__on;if(l)for(var m,h=0,c=l.length;h<c;++h)for(a=0,m=l[h];a<r;++a)if((o=i[a]).type===m.type&&o.name===m.name)return m.value},dispatch:function Zo(t,n){return this.each(("function"==typeof n?qo:Qo)(t,n))},[Symbol.iterator]:function*Ko(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var o,i=t[n],a=0,r=i.length;a<r;++a)(o=i[a])&&(yield o)}};const St=zn;function Se(t){return"string"==typeof t?new we([[document.querySelector(t)]],[document.documentElement]):new we([[t]],Nn)}var el={value:()=>{}};function Wn(){for(var i,t=0,n=arguments.length,e={};t<n;++t){if(!(i=arguments[t]+"")||i in e||/[\s.]/.test(i))throw new Error("illegal type: "+i);e[i]=[]}return new Zt(e)}function Zt(t){this._=t}function il(t,n){for(var a,e=0,i=t.length;e<i;++e)if((a=t[e]).name===n)return a.value}function jn(t,n,e){for(var i=0,a=t.length;i<a;++i)if(t[i].name===n){t[i]=el,t=t.slice(0,i).concat(t.slice(i+1));break}return null!=e&&t.push({name:n,value:e}),t}Zt.prototype=Wn.prototype={constructor:Zt,on:function(t,n){var a,e=this._,i=function tl(t,n){return t.trim().split(/^|\s+/).map(function(e){var i="",a=e.indexOf(".");if(a>=0&&(i=e.slice(a+1),e=e.slice(0,a)),e&&!n.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:i}})}(t+"",e),r=-1,o=i.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++r<o;)if(a=(t=i[r]).type)e[a]=jn(e[a],t.name,n);else if(null==n)for(a in e)e[a]=jn(e[a],t.name,null);return this}for(;++r<o;)if((a=(t=i[r]).type)&&(a=il(e[a],t.name)))return a},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new Zt(t)},call:function(t,n){if((a=arguments.length-2)>0)for(var a,r,e=new Array(a),i=0;i<a;++i)e[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(i=0,a=(r=this._[t]).length;i<a;++i)r[i].value.apply(n,e)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var i=this._[t],a=0,r=i.length;a<r;++a)i[a].value.apply(n,e)}};const Un=Wn,Li={capture:!0,passive:!1};function Di(t){t.preventDefault(),t.stopImmediatePropagation()}function Fi(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function Qn(t,n){var e=Object.create(t.prototype);for(var i in n)e[i]=n[i];return e}function kt(){}var Kt=1/.7,ct="\\s*([+-]?\\d+)\\s*",Lt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Ve="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",sl=/^#([0-9a-f]{3,8})$/,rl=new RegExp(`^rgb\\(${ct},${ct},${ct}\\)$`),ol=new RegExp(`^rgb\\(${Ve},${Ve},${Ve}\\)$`),ll=new RegExp(`^rgba\\(${ct},${ct},${ct},${Lt}\\)$`),cl=new RegExp(`^rgba\\(${Ve},${Ve},${Ve},${Lt}\\)$`),hl=new RegExp(`^hsl\\(${Lt},${Ve},${Ve}\\)$`),dl=new RegExp(`^hsla\\(${Lt},${Ve},${Ve},${Lt}\\)$`),qn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Zn(){return this.rgb().formatHex()}function Kn(){return this.rgb().formatRgb()}function qe(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=sl.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?Jn(n):3===e?new Ce(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?Jt(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?Jt(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=rl.exec(t))?new Ce(n[1],n[2],n[3],1):(n=ol.exec(t))?new Ce(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=ll.exec(t))?Jt(n[1],n[2],n[3],n[4]):(n=cl.exec(t))?Jt(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=hl.exec(t))?ia(n[1],n[2]/100,n[3]/100,1):(n=dl.exec(t))?ia(n[1],n[2]/100,n[3]/100,n[4]):qn.hasOwnProperty(t)?Jn(qn[t]):"transparent"===t?new Ce(NaN,NaN,NaN,0):null}function Jn(t){return new Ce(t>>16&255,t>>8&255,255&t,1)}function Jt(t,n,e,i){return i<=0&&(t=n=e=NaN),new Ce(t,n,e,i)}function ei(t,n,e,i){return 1===arguments.length?function gl(t){return t instanceof kt||(t=qe(t)),t?new Ce((t=t.rgb()).r,t.g,t.b,t.opacity):new Ce}(t):new Ce(t,n,e,i??1)}function Ce(t,n,e,i){this.r=+t,this.g=+n,this.b=+e,this.opacity=+i}function ea(){return`#${Ke(this.r)}${Ke(this.g)}${Ke(this.b)}`}function ta(){const t=ti(this.opacity);return`${1===t?"rgb(":"rgba("}${Ze(this.r)}, ${Ze(this.g)}, ${Ze(this.b)}${1===t?")":`, ${t})`}`}function ti(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Ze(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Ke(t){return((t=Ze(t))<16?"0":"")+t.toString(16)}function ia(t,n,e,i){return i<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Fe(t,n,e,i)}function na(t){if(t instanceof Fe)return new Fe(t.h,t.s,t.l,t.opacity);if(t instanceof kt||(t=qe(t)),!t)return new Fe;if(t instanceof Fe)return t;var n=(t=t.rgb()).r/255,e=t.g/255,i=t.b/255,a=Math.min(n,e,i),r=Math.max(n,e,i),o=NaN,l=r-a,h=(r+a)/2;return l?(o=n===r?(e-i)/l+6*(e<i):e===r?(i-n)/l+2:(n-e)/l+4,l/=h<.5?r+a:2-r-a,o*=60):l=h>0&&h<1?0:o,new Fe(o,l,h,t.opacity)}function Fe(t,n,e,i){this.h=+t,this.s=+n,this.l=+e,this.opacity=+i}function aa(t){return(t=(t||0)%360)<0?t+360:t}function ii(t){return Math.max(0,Math.min(1,t||0))}function Ei(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function sa(t,n,e,i,a){var r=t*t,o=r*t;return((1-3*t+3*r-o)*n+(4-6*r+3*o)*e+(1+3*t+3*r-3*o)*i+o*a)/6}Fi(kt,qe,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:Zn,formatHex:Zn,formatHex8:function ml(){return this.rgb().formatHex8()},formatHsl:function ul(){return na(this).formatHsl()},formatRgb:Kn,toString:Kn}),Fi(Ce,ei,Qn(kt,{brighter(t){return t=null==t?Kt:Math.pow(Kt,t),new Ce(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Ce(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Ce(Ze(this.r),Ze(this.g),Ze(this.b),ti(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ea,formatHex:ea,formatHex8:function pl(){return`#${Ke(this.r)}${Ke(this.g)}${Ke(this.b)}${Ke(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:ta,toString:ta})),Fi(Fe,function fl(t,n,e,i){return 1===arguments.length?na(t):new Fe(t,n,e,i??1)},Qn(kt,{brighter(t){return t=null==t?Kt:Math.pow(Kt,t),new Fe(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Fe(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,i=e+(e<.5?e:1-e)*n,a=2*e-i;return new Ce(Ei(t>=240?t-240:t+120,a,i),Ei(t,a,i),Ei(t<120?t+240:t-120,a,i),this.opacity)},clamp(){return new Fe(aa(this.h),ii(this.s),ii(this.l),ti(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=ti(this.opacity);return`${1===t?"hsl(":"hsla("}${aa(this.h)}, ${100*ii(this.s)}%, ${100*ii(this.l)}%${1===t?")":`, ${t})`}`}}));const Mi=t=>()=>t;function oa(t,n){var e=n-t;return e?function ra(t,n){return function(e){return t+e*n}}(t,e):Mi(isNaN(t)?n:t)}const ni=function t(n){var e=function yl(t){return 1==(t=+t)?oa:function(n,e){return e-n?function vl(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(i){return Math.pow(t+i*n,e)}}(n,e,t):Mi(isNaN(n)?e:n)}}(n);function i(a,r){var o=e((a=ei(a)).r,(r=ei(r)).r),l=e(a.g,r.g),h=e(a.b,r.b),c=oa(a.opacity,r.opacity);return function(m){return a.r=o(m),a.g=l(m),a.b=h(m),a.opacity=c(m),a+""}}return i.gamma=t,i}(1);function la(t){return function(n){var o,l,e=n.length,i=new Array(e),a=new Array(e),r=new Array(e);for(o=0;o<e;++o)l=ei(n[o]),i[o]=l.r||0,a[o]=l.g||0,r[o]=l.b||0;return i=t(i),a=t(a),r=t(r),l.opacity=1,function(h){return l.r=i(h),l.g=a(h),l.b=r(h),l+""}}}function ca(t,n){var o,e=n?n.length:0,i=t?Math.min(e,t.length):0,a=new Array(i),r=new Array(e);for(o=0;o<i;++o)a[o]=ai(t[o],n[o]);for(;o<e;++o)r[o]=n[o];return function(l){for(o=0;o<i;++o)r[o]=a[o](l);return r}}function Tl(t,n){var e=new Date;return t=+t,n=+n,function(i){return e.setTime(t*(1-i)+n*i),e}}function Ee(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}function Cl(t,n){var a,e={},i={};for(a in(null===t||"object"!=typeof t)&&(t={}),(null===n||"object"!=typeof n)&&(n={}),n)a in t?e[a]=ai(t[a],n[a]):i[a]=n[a];return function(r){for(a in e)i[a]=e[a](r);return i}}la(function xl(t){var n=t.length-1;return function(e){var i=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),a=t[i],r=t[i+1];return sa((e-i/n)*n,i>0?t[i-1]:2*a-r,a,r,i<n-1?t[i+2]:2*r-a)}}),la(function _l(t){var n=t.length;return function(e){var i=Math.floor(((e%=1)<0?++e:e)*n);return sa((e-i/n)*n,t[(i+n-1)%n],t[i%n],t[(i+1)%n],t[(i+2)%n])}});var Bi=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Vi=new RegExp(Bi.source,"g");function ha(t,n){var i,a,r,e=Bi.lastIndex=Vi.lastIndex=0,o=-1,l=[],h=[];for(t+="",n+="";(i=Bi.exec(t))&&(a=Vi.exec(n));)(r=a.index)>e&&(r=n.slice(e,r),l[o]?l[o]+=r:l[++o]=r),(i=i[0])===(a=a[0])?l[o]?l[o]+=a:l[++o]=a:(l[++o]=null,h.push({i:o,x:Ee(i,a)})),e=Vi.lastIndex;return e<n.length&&(r=n.slice(e),l[o]?l[o]+=r:l[++o]=r),l.length<2?h[0]?function wl(t){return function(n){return t(n)+""}}(h[0].x):function bl(t){return function(){return t}}(n):(n=h.length,function(c){for(var d,m=0;m<n;++m)l[(d=h[m]).i]=d.x(c);return l.join("")})}function Sl(t,n){n||(n=[]);var a,e=t?Math.min(n.length,t.length):0,i=n.slice();return function(r){for(a=0;a<e;++a)i[a]=t[a]*(1-r)+n[a]*r;return i}}function ai(t,n){var i,e=typeof n;return null==n||"boolean"===e?Mi(n):("number"===e?Ee:"string"===e?(i=qe(n))?(n=i,ni):ha:n instanceof qe?ni:n instanceof Date?Tl:function kl(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}(n)?Sl:Array.isArray(n)?ca:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Cl:Ee)(t,n)}function da(t,n){if(t=function Al(t){let n;for(;n=t.sourceEvent;)t=n;return t}(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var i=e.createSVGPoint();return i.x=t.clientX,i.y=t.clientY,[(i=i.matrixTransform(n.getScreenCTM().inverse())).x,i.y]}if(n.getBoundingClientRect){var a=n.getBoundingClientRect();return[t.clientX-a.left-n.clientLeft,t.clientY-a.top-n.clientTop]}}return[t.pageX,t.pageY]}var si,Et,ht=0,Dt=0,Ft=0,ma=1e3,ri=0,Je=0,oi=0,Mt="object"==typeof performance&&performance.now?performance:Date,ua="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Oi(){return Je||(ua(Ll),Je=Mt.now()+oi)}function Ll(){Je=0}function li(){this._call=this._time=this._next=null}function ga(t,n,e){var i=new li;return i.restart(t,n,e),i}function pa(){Je=(ri=Mt.now())+oi,ht=Dt=0;try{!function Dl(){Oi(),++ht;for(var n,t=si;t;)(n=Je-t._time)>=0&&t._call.call(void 0,n),t=t._next;--ht}()}finally{ht=0,function El(){for(var t,e,n=si,i=1/0;n;)n._call?(i>n._time&&(i=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:si=e);Et=t,Ii(i)}(),Je=0}}function Fl(){var t=Mt.now(),n=t-ri;n>ma&&(oi-=n,ri=t)}function Ii(t){ht||(Dt&&(Dt=clearTimeout(Dt)),t-Je>24?(t<1/0&&(Dt=setTimeout(pa,t-Mt.now()-oi)),Ft&&(Ft=clearInterval(Ft))):(Ft||(ri=Mt.now(),Ft=setInterval(Fl,ma)),ht=1,ua(pa)))}function fa(t,n,e){var i=new li;return i.restart(a=>{i.stop(),t(a+n)},n=null==n?0:+n,e),i}li.prototype=ga.prototype={constructor:li,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?Oi():+e)+(null==n?0:+n),!this._next&&Et!==this&&(Et?Et._next=this:si=this,Et=this),this._call=t,this._time=e,Ii()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ii())}};var Ml=Un("start","end","cancel","interrupt"),Bl=[],xa=0,Pi=2,ci=3,Gi=5,hi=6;function di(t,n,e,i,a,r){var o=t.__transition;if(o){if(e in o)return}else t.__transition={};!function Vl(t,n,e){var a,i=t.__transition;function o(c){var m,d,u,g;if(1!==e.state)return h();for(m in i)if((g=i[m]).name===e.name){if(g.state===ci)return fa(o);4===g.state?(g.state=hi,g.timer.stop(),g.on.call("interrupt",t,t.__data__,g.index,g.group),delete i[m]):+m<n&&(g.state=hi,g.timer.stop(),g.on.call("cancel",t,t.__data__,g.index,g.group),delete i[m])}if(fa(function(){e.state===ci&&(e.state=4,e.timer.restart(l,e.delay,e.time),l(c))}),e.state=Pi,e.on.call("start",t,t.__data__,e.index,e.group),e.state===Pi){for(e.state=ci,a=new Array(u=e.tween.length),m=0,d=-1;m<u;++m)(g=e.tween[m].value.call(t,t.__data__,e.index,e.group))&&(a[++d]=g);a.length=d+1}}function l(c){for(var m=c<e.duration?e.ease.call(null,c/e.duration):(e.timer.restart(h),e.state=Gi,1),d=-1,u=a.length;++d<u;)a[d].call(t,m);e.state===Gi&&(e.on.call("end",t,t.__data__,e.index,e.group),h())}function h(){for(var c in e.state=hi,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=ga(function r(c){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=c&&o(c-e.delay)},0,e.time)}(t,e,{name:n,index:i,group:a,on:Ml,tween:Bl,time:r.time,delay:r.delay,duration:r.duration,ease:r.ease,timer:null,state:xa})}function $i(t,n){var e=Me(t,n);if(e.state>xa)throw new Error("too late; already scheduled");return e}function Oe(t,n){var e=Me(t,n);if(e.state>ci)throw new Error("too late; already running");return e}function Me(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function Ri(t,n){var i,a,o,e=t.__transition,r=!0;if(e){for(o in n=null==n?null:n+"",e)(i=e[o]).name===n?(a=i.state>Pi&&i.state<Gi,i.state=hi,i.timer.stop(),i.on.call(a?"interrupt":"cancel",t,t.__data__,i.index,i.group),delete e[o]):r=!1;r&&delete t.__transition}}var mi,ya=180/Math.PI,Hi={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Ta(t,n,e,i,a,r){var o,l,h;return(o=Math.sqrt(t*t+n*n))&&(t/=o,n/=o),(h=t*e+n*i)&&(e-=t*h,i-=n*h),(l=Math.sqrt(e*e+i*i))&&(e/=l,i/=l,h/=l),t*i<n*e&&(t=-t,n=-n,h=-h,o=-o),{translateX:a,translateY:r,rotate:Math.atan2(n,t)*ya,skewX:Math.atan(h)*ya,scaleX:o,scaleY:l}}function Ca(t,n,e,i){function a(c){return c.length?c.pop()+" ":""}return function(c,m){var d=[],u=[];return c=t(c),m=t(m),function r(c,m,d,u,g,f){if(c!==d||m!==u){var b=g.push("translate(",null,n,null,e);f.push({i:b-4,x:Ee(c,d)},{i:b-2,x:Ee(m,u)})}else(d||u)&&g.push("translate("+d+n+u+e)}(c.translateX,c.translateY,m.translateX,m.translateY,d,u),function o(c,m,d,u){c!==m?(c-m>180?m+=360:m-c>180&&(c+=360),u.push({i:d.push(a(d)+"rotate(",null,i)-2,x:Ee(c,m)})):m&&d.push(a(d)+"rotate("+m+i)}(c.rotate,m.rotate,d,u),function l(c,m,d,u){c!==m?u.push({i:d.push(a(d)+"skewX(",null,i)-2,x:Ee(c,m)}):m&&d.push(a(d)+"skewX("+m+i)}(c.skewX,m.skewX,d,u),function h(c,m,d,u,g,f){if(c!==d||m!==u){var b=g.push(a(g)+"scale(",null,",",null,")");f.push({i:b-4,x:Ee(c,d)},{i:b-2,x:Ee(m,u)})}else(1!==d||1!==u)&&g.push(a(g)+"scale("+d+","+u+")")}(c.scaleX,c.scaleY,m.scaleX,m.scaleY,d,u),c=m=null,function(g){for(var p,f=-1,b=u.length;++f<b;)d[(p=u[f]).i]=p.x(g);return d.join("")}}}var Gl=Ca(function Il(t){const n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?Hi:Ta(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),$l=Ca(function Pl(t){return null!=t&&(mi||(mi=document.createElementNS("http://www.w3.org/2000/svg","g")),mi.setAttribute("transform",t),t=mi.transform.baseVal.consolidate())?Ta((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Hi},", ",")",")");function Rl(t,n){var e,i;return function(){var a=Oe(this,t),r=a.tween;if(r!==e)for(var o=0,l=(i=e=r).length;o<l;++o)if(i[o].name===n){(i=i.slice()).splice(o,1);break}a.tween=i}}function Hl(t,n,e){var i,a;if("function"!=typeof e)throw new Error;return function(){var r=Oe(this,t),o=r.tween;if(o!==i){a=(i=o).slice();for(var l={name:n,value:e},h=0,c=a.length;h<c;++h)if(a[h].name===n){a[h]=l;break}h===c&&a.push(l)}r.tween=a}}function Xi(t,n,e){var i=t._id;return t.each(function(){var a=Oe(this,i);(a.value||(a.value={}))[n]=e.apply(this,arguments)}),function(a){return Me(a,i).value[n]}}function ba(t,n){var e;return("number"==typeof n?Ee:n instanceof qe?ni:(e=qe(n))?(n=e,ni):ha)(t,n)}function Yl(t){return function(){this.removeAttribute(t)}}function Nl(t){return function(){this.removeAttributeNS(t.space,t.local)}}function zl(t,n,e){var i,r,a=e+"";return function(){var o=this.getAttribute(t);return o===a?null:o===i?r:r=n(i=o,e)}}function Wl(t,n,e){var i,r,a=e+"";return function(){var o=this.getAttributeNS(t.space,t.local);return o===a?null:o===i?r:r=n(i=o,e)}}function jl(t,n,e){var i,a,r;return function(){var o,h,l=e(this);return null==l?void this.removeAttribute(t):(o=this.getAttribute(t))===(h=l+"")?null:o===i&&h===a?r:(a=h,r=n(i=o,l))}}function Ul(t,n,e){var i,a,r;return function(){var o,h,l=e(this);return null==l?void this.removeAttributeNS(t.space,t.local):(o=this.getAttributeNS(t.space,t.local))===(h=l+"")?null:o===i&&h===a?r:(a=h,r=n(i=o,l))}}function Kl(t,n){var e,i;function a(){var r=n.apply(this,arguments);return r!==i&&(e=(i=r)&&function Zl(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}(t,r)),e}return a._value=n,a}function Jl(t,n){var e,i;function a(){var r=n.apply(this,arguments);return r!==i&&(e=(i=r)&&function ql(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}(t,r)),e}return a._value=n,a}function tc(t,n){return function(){$i(this,t).delay=+n.apply(this,arguments)}}function ic(t,n){return n=+n,function(){$i(this,t).delay=n}}function ac(t,n){return function(){Oe(this,t).duration=+n.apply(this,arguments)}}function sc(t,n){return n=+n,function(){Oe(this,t).duration=n}}var yc=St.prototype.constructor;function wa(t){return function(){this.style.removeProperty(t)}}var Gc=0;function Ie(t,n,e,i){this._groups=t,this._parents=n,this._name=e,this._id=i}function Sa(){return++Gc}var Pe=St.prototype;Ie.prototype=function $c(t){return St().transition(t)}.prototype={constructor:Ie,select:function _c(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=F(t));for(var i=this._groups,a=i.length,r=new Array(a),o=0;o<a;++o)for(var m,d,l=i[o],h=l.length,c=r[o]=new Array(h),u=0;u<h;++u)(m=l[u])&&(d=t.call(m,m.__data__,u,l))&&("__data__"in m&&(d.__data__=m.__data__),c[u]=d,di(c[u],n,e,u,c,Me(m,e)));return new Ie(r,this._parents,n,e)},selectAll:function vc(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=Be(t));for(var i=this._groups,a=i.length,r=[],o=[],l=0;l<a;++l)for(var m,h=i[l],c=h.length,d=0;d<c;++d)if(m=h[d]){for(var g,u=t.call(m,m.__data__,d,h),f=Me(m,e),b=0,p=u.length;b<p;++b)(g=u[b])&&di(g,n,e,b,u,f);r.push(u),o.push(m)}return new Ie(r,o,n,e)},selectChild:Pe.selectChild,selectChildren:Pe.selectChildren,filter:function dc(t){"function"!=typeof t&&(t=Bn(t));for(var n=this._groups,e=n.length,i=new Array(e),a=0;a<e;++a)for(var h,r=n[a],o=r.length,l=i[a]=[],c=0;c<o;++c)(h=r[c])&&t.call(h,h.__data__,c,r)&&l.push(h);return new Ie(i,this._parents,this._name,this._id)},merge:function mc(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,i=n.length,r=Math.min(i,e.length),o=new Array(i),l=0;l<r;++l)for(var u,h=n[l],c=e[l],m=h.length,d=o[l]=new Array(m),g=0;g<m;++g)(u=h[g]||c[g])&&(d[g]=u);for(;l<i;++l)o[l]=n[l];return new Ie(o,this._parents,this._name,this._id)},selection:function Tc(){return new yc(this._groups,this._parents)},transition:function Ic(){for(var t=this._name,n=this._id,e=Sa(),i=this._groups,a=i.length,r=0;r<a;++r)for(var h,o=i[r],l=o.length,c=0;c<l;++c)if(h=o[c]){var m=Me(h,n);di(h,t,e,c,o,{time:m.time+m.delay+m.duration,delay:0,duration:m.duration,ease:m.ease})}return new Ie(i,this._parents,t,e)},call:Pe.call,nodes:Pe.nodes,node:Pe.node,size:Pe.size,empty:Pe.empty,each:Pe.each,on:function pc(t,n){var e=this._id;return arguments.length<2?Me(this.node(),e).on.on(t):this.each(function gc(t,n,e){var i,a,r=function uc(t){return(t+"").trim().split(/^|\s+/).every(function(n){var e=n.indexOf(".");return e>=0&&(n=n.slice(0,e)),!n||"start"===n})}(n)?$i:Oe;return function(){var o=r(this,t),l=o.on;l!==i&&(a=(i=l).copy()).on(n,e),o.on=a}}(e,t,n))},attr:function Ql(t,n){var e=qt(t),i="transform"===e?$l:ba;return this.attrTween(t,"function"==typeof n?(e.local?Ul:jl)(e,i,Xi(this,"attr."+t,n)):null==n?(e.local?Nl:Yl)(e):(e.local?Wl:zl)(e,i,n))},attrTween:function ec(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var i=qt(t);return this.tween(e,(i.local?Kl:Jl)(i,n))},style:function kc(t,n,e){var i="transform"==(t+="")?Gl:ba;return null==n?this.styleTween(t,function Cc(t,n){var e,i,a;return function(){var r=lt(this,t),o=(this.style.removeProperty(t),lt(this,t));return r===o?null:r===e&&o===i?a:a=n(e=r,i=o)}}(t,i)).on("end.style."+t,wa(t)):"function"==typeof n?this.styleTween(t,function wc(t,n,e){var i,a,r;return function(){var o=lt(this,t),l=e(this),h=l+"";return null==l&&(this.style.removeProperty(t),h=l=lt(this,t)),o===h?null:o===i&&h===a?r:(a=h,r=n(i=o,l))}}(t,i,Xi(this,"style."+t,n))).each(function Sc(t,n){var e,i,a,l,r="style."+n,o="end."+r;return function(){var h=Oe(this,t),c=h.on,m=null==h.value[r]?l||(l=wa(n)):void 0;(c!==e||a!==m)&&(i=(e=c).copy()).on(o,a=m),h.on=i}}(this._id,t)):this.styleTween(t,function bc(t,n,e){var i,r,a=e+"";return function(){var o=lt(this,t);return o===a?null:o===i?r:r=n(i=o,e)}}(t,i,n),e).on("end.style."+t,null)},styleTween:function Dc(t,n,e){var i="style."+(t+="");if(arguments.length<2)return(i=this.tween(i))&&i._value;if(null==n)return this.tween(i,null);if("function"!=typeof n)throw new Error;return this.tween(i,function Lc(t,n,e){var i,a;function r(){var o=n.apply(this,arguments);return o!==a&&(i=(a=o)&&function Ac(t,n,e){return function(i){this.style.setProperty(t,n.call(this,i),e)}}(t,o,e)),i}return r._value=n,r}(t,n,e??""))},text:function Mc(t){return this.tween("text","function"==typeof t?function Ec(t){return function(){var n=t(this);this.textContent=n??""}}(Xi(this,"text",t)):function Fc(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function Oc(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;return this.tween(n,function Vc(t){var n,e;function i(){var a=t.apply(this,arguments);return a!==e&&(n=(e=a)&&function Bc(t){return function(n){this.textContent=t.call(this,n)}}(a)),n}return i._value=t,i}(t))},remove:function xc(){return this.on("end.remove",function fc(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}(this._id))},tween:function Xl(t,n){var e=this._id;if(t+="",arguments.length<2){for(var o,i=Me(this.node(),e).tween,a=0,r=i.length;a<r;++a)if((o=i[a]).name===t)return o.value;return null}return this.each((null==n?Rl:Hl)(e,t,n))},delay:function nc(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?tc:ic)(n,t)):Me(this.node(),n).delay},duration:function rc(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?ac:sc)(n,t)):Me(this.node(),n).duration},ease:function lc(t){var n=this._id;return arguments.length?this.each(function oc(t,n){if("function"!=typeof n)throw new Error;return function(){Oe(this,t).ease=n}}(n,t)):Me(this.node(),n).ease},easeVarying:function hc(t){if("function"!=typeof t)throw new Error;return this.each(function cc(t,n){return function(){var e=n.apply(this,arguments);if("function"!=typeof e)throw new Error;Oe(this,t).ease=e}}(this._id,t))},end:function Pc(){var t,n,e=this,i=e._id,a=e.size();return new Promise(function(r,o){var l={value:o},h={value:function(){0==--a&&r()}};e.each(function(){var c=Oe(this,i),m=c.on;m!==t&&((n=(t=m).copy())._.cancel.push(l),n._.interrupt.push(l),n._.end.push(h)),c.on=n}),0===a&&r()})},[Symbol.iterator]:Pe[Symbol.iterator]};var Hc={time:null,delay:0,duration:250,ease:function Rc(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function Xc(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}St.prototype.interrupt=function Ol(t){return this.each(function(){Ri(this,t)})},St.prototype.transition=function Yc(t){var n,e;t instanceof Ie?(n=t._id,t=t._name):(n=Sa(),(e=Hc).time=Oi(),t=null==t?null:t+"");for(var i=this._groups,a=i.length,r=0;r<a;++r)for(var h,o=i[r],l=o.length,c=0;c<l;++c)(h=o[c])&&di(h,t,n,c,o,e||Xc(h,n));return new Ie(i,this._parents,t,n)};const Yi=t=>()=>t;function Nc(t,{sourceEvent:n,target:e,selection:i,mode:a,dispatch:r}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:i,enumerable:!0,configurable:!0},mode:{value:a,enumerable:!0,configurable:!0},_:{value:r}})}function Ni(t){t.preventDefault(),t.stopImmediatePropagation()}var ka={name:"drag"},zi={name:"space"},dt={name:"handle"},mt={name:"center"};const{abs:Aa,max:ge,min:pe}=Math;function La(t){return[+t[0],+t[1]]}function Wi(t){return[La(t[0]),La(t[1])]}var ui={name:"x",handles:["w","e"].map(Bt),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},gi={name:"y",handles:["n","s"].map(Bt),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},Ge=(["n","w","e","s","nw","ne","sw","se"].map(Bt),{overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"}),Da={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},Fa={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},jc={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},Uc={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function Bt(t){return{type:t}}function Qc(t){return!t.ctrlKey&&!t.button}function qc(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function Zc(){return navigator.maxTouchPoints||"ontouchstart"in this}function ji(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function Vt(t,n){return null==t||null==n?NaN:t<n?-1:t>n?1:t>=n?0:NaN}function eh(t,n){return null==t||null==n?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function Qi(t){let n,e,i;function a(l,h,c=0,m=l.length){if(c<m){if(0!==n(h,h))return m;do{const d=c+m>>>1;e(l[d],h)<0?c=d+1:m=d}while(c<m)}return c}return 2!==t.length?(n=Vt,e=(l,h)=>Vt(t(l),h),i=(l,h)=>t(l)-h):(n=t===Vt||t===eh?t:th,e=t,i=t),{left:a,center:function o(l,h,c=0,m=l.length){const d=a(l,h,c,m-1);return d>c&&i(l[d-1],h)>-i(l[d],h)?d-1:d},right:function r(l,h,c=0,m=l.length){if(c<m){if(0!==n(h,h))return m;do{const d=c+m>>>1;e(l[d],h)<=0?c=d+1:m=d}while(c<m)}return c}}}function th(){return 0}const ih=Math.sqrt(50),nh=Math.sqrt(10),ah=Math.sqrt(2);function pi(t,n,e){const i=(n-t)/Math.max(0,e),a=Math.floor(Math.log10(i)),r=i/Math.pow(10,a),o=r>=ih?10:r>=nh?5:r>=ah?2:1;let l,h,c;return a<0?(c=Math.pow(10,-a)/o,l=Math.round(t*c),h=Math.round(n*c),l/c<t&&++l,h/c>n&&--h,c=-c):(c=Math.pow(10,a)*o,l=Math.round(t/c),h=Math.round(n/c),l*c<t&&++l,h*c>n&&--h),h<l&&.5<=e&&e<2?pi(t,n,2*e):[l,h,c]}function qi(t,n,e){return pi(t=+t,n=+n,e=+e)[2]}function Zi(t,n,e){e=+e;const i=(n=+n)<(t=+t),a=i?qi(n,t,e):qi(t,n,e);return(i?-1:1)*(a<0?1/-a:a)}const $e=1e3,De=6e4,Re=60*De,He=24*Re,Ki=7*He,Ea=30*He,Ji=365*He,en=new Date,tn=new Date;function oe(t,n,e,i){function a(r){return t(r=0===arguments.length?new Date:new Date(+r)),r}return a.floor=r=>(t(r=new Date(+r)),r),a.ceil=r=>(t(r=new Date(r-1)),n(r,1),t(r),r),a.round=r=>{const o=a(r),l=a.ceil(r);return r-o<l-r?o:l},a.offset=(r,o)=>(n(r=new Date(+r),null==o?1:Math.floor(o)),r),a.range=(r,o,l)=>{const h=[];if(r=a.ceil(r),l=null==l?1:Math.floor(l),!(r<o&&l>0))return h;let c;do{h.push(c=new Date(+r)),n(r,l),t(r)}while(c<r&&r<o);return h},a.filter=r=>oe(o=>{if(o>=o)for(;t(o),!r(o);)o.setTime(o-1)},(o,l)=>{if(o>=o)if(l<0)for(;++l<=0;)for(;n(o,-1),!r(o););else for(;--l>=0;)for(;n(o,1),!r(o););}),e&&(a.count=(r,o)=>(en.setTime(+r),tn.setTime(+o),t(en),t(tn),Math.floor(e(en,tn))),a.every=r=>(r=Math.floor(r),isFinite(r)&&r>0?r>1?a.filter(i?o=>i(o)%r==0:o=>a.count(0,o)%r==0):a:null)),a}const fi=oe(()=>{},(t,n)=>{t.setTime(+t+n)},(t,n)=>n-t);fi.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?oe(n=>{n.setTime(Math.floor(n/t)*t)},(n,e)=>{n.setTime(+n+e*t)},(n,e)=>(e-n)/t):fi:null);const ut=oe(t=>{t.setTime(t-t.getMilliseconds())},(t,n)=>{t.setTime(+t+n*$e)},(t,n)=>(n-t)/$e,t=>t.getUTCSeconds()),nn=oe(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*$e)},(t,n)=>{t.setTime(+t+n*De)},(t,n)=>(n-t)/De,t=>t.getMinutes()),Ma=oe(t=>{t.setUTCSeconds(0,0)},(t,n)=>{t.setTime(+t+n*De)},(t,n)=>(n-t)/De,t=>t.getUTCMinutes()),an=oe(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*$e-t.getMinutes()*De)},(t,n)=>{t.setTime(+t+n*Re)},(t,n)=>(n-t)/Re,t=>t.getHours()),Ba=oe(t=>{t.setUTCMinutes(0,0,0)},(t,n)=>{t.setTime(+t+n*Re)},(t,n)=>(n-t)/Re,t=>t.getUTCHours()),sn=oe(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*De)/He,t=>t.getDate()-1),Va=(oe(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/He,t=>t.getUTCDate()-1),oe(t=>{t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n)},(t,n)=>(n-t)/He,t=>Math.floor(t/He)));function et(t){return oe(n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},(n,e)=>{n.setDate(n.getDate()+7*e)},(n,e)=>(e-n-(e.getTimezoneOffset()-n.getTimezoneOffset())*De)/Ki)}const rn=et(0);function tt(t){return oe(n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCDate(n.getUTCDate()+7*e)},(n,e)=>(e-n)/Ki)}et(1),et(2),et(3),et(4),et(5),et(6);const Oa=tt(0),on=(tt(1),tt(2),tt(3),tt(4),tt(5),tt(6),oe(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,n)=>{t.setMonth(t.getMonth()+n)},(t,n)=>n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear()),t=>t.getMonth())),Ia=oe(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)},(t,n)=>n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth()),xi=oe(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n)},(t,n)=>n.getFullYear()-t.getFullYear(),t=>t.getFullYear());xi.every=t=>isFinite(t=Math.floor(t))&&t>0?oe(n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},(n,e)=>{n.setFullYear(n.getFullYear()+e*t)}):null;const ln=oe(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)},(t,n)=>n.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function Pa(t,n,e,i,a,r){const o=[[ut,1,$e],[ut,5,5e3],[ut,15,15e3],[ut,30,3e4],[r,1,De],[r,5,5*De],[r,15,15*De],[r,30,30*De],[a,1,Re],[a,3,3*Re],[a,6,6*Re],[a,12,12*Re],[i,1,He],[i,2,2*He],[e,1,Ki],[n,1,Ea],[n,3,3*Ea],[t,1,Ji]];function h(c,m,d){const u=Math.abs(m-c)/d,g=Qi(([,,p])=>p).right(o,u);if(g===o.length)return t.every(Zi(c/Ji,m/Ji,d));if(0===g)return fi.every(Math.max(Zi(c,m,d),1));const[f,b]=o[u/o[g-1][2]<o[g][2]/u?g-1:g];return f.every(b)}return[function l(c,m,d){const u=m<c;u&&([c,m]=[m,c]);const g=d&&"function"==typeof d.range?d:h(c,m,d),f=g?g.range(c,+m+1):[];return u?f.reverse():f},h]}ln.every=t=>isFinite(t=Math.floor(t))&&t>0?oe(n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},(n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null;const[df,mf]=Pa(ln,Ia,Oa,Va,Ba,Ma),[_h,vh]=Pa(xi,on,rn,sn,an,nn);var cn=new Date,hn=new Date;function Xe(t,n,e,i){function a(r){return t(r=0===arguments.length?new Date:new Date(+r)),r}return a.floor=function(r){return t(r=new Date(+r)),r},a.ceil=function(r){return t(r=new Date(r-1)),n(r,1),t(r),r},a.round=function(r){var o=a(r),l=a.ceil(r);return r-o<l-r?o:l},a.offset=function(r,o){return n(r=new Date(+r),null==o?1:Math.floor(o)),r},a.range=function(r,o,l){var c,h=[];if(r=a.ceil(r),l=null==l?1:Math.floor(l),!(r<o&&l>0))return h;do{h.push(c=new Date(+r)),n(r,l),t(r)}while(c<r&&r<o);return h},a.filter=function(r){return Xe(function(o){if(o>=o)for(;t(o),!r(o);)o.setTime(o-1)},function(o,l){if(o>=o)if(l<0)for(;++l<=0;)for(;n(o,-1),!r(o););else for(;--l>=0;)for(;n(o,1),!r(o););})},e&&(a.count=function(r,o){return cn.setTime(+r),hn.setTime(+o),t(cn),t(hn),Math.floor(e(cn,hn))},a.every=function(r){return r=Math.floor(r),isFinite(r)&&r>0?r>1?a.filter(i?function(o){return i(o)%r==0}:function(o){return a.count(0,o)%r==0}):a:null}),a}const Ot=864e5,Ga=7*Ot;function it(t){return Xe(function(n){n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},function(n,e){n.setUTCDate(n.getUTCDate()+7*e)},function(n,e){return(e-n)/Ga})}var $a=it(0),_i=it(1),gt=(it(2),it(3),it(4));const Ha=(it(5),it(6),Xe(function(t){t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n)},function(t,n){return(n-t)/Ot},function(t){return t.getUTCDate()-1}));function nt(t){return Xe(function(n){n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},function(n,e){n.setDate(n.getDate()+7*e)},function(n,e){return(e-n-6e4*(e.getTimezoneOffset()-n.getTimezoneOffset()))/Ga})}var Xa=nt(0),vi=nt(1),pt=(nt(2),nt(3),nt(4));const Na=(nt(5),nt(6),Xe(t=>t.setHours(0,0,0,0),(t,n)=>t.setDate(t.getDate()+n),(t,n)=>(n-t-6e4*(n.getTimezoneOffset()-t.getTimezoneOffset()))/Ot,t=>t.getDate()-1));var mn=Xe(function(t){t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n)},function(t,n){return n.getFullYear()-t.getFullYear()},function(t){return t.getFullYear()});mn.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Xe(function(n){n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},function(n,e){n.setFullYear(n.getFullYear()+e*t)}):null};const It=mn;var un=Xe(function(t){t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n)},function(t,n){return n.getUTCFullYear()-t.getUTCFullYear()},function(t){return t.getUTCFullYear()});un.every=function(t){return isFinite(t=Math.floor(t))&&t>0?Xe(function(n){n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},function(n,e){n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null};const Pt=un;function gn(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function pn(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Gt(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}var ft,ss,za={"-":"",_:" ",0:"0"},ce=/^\s*\d+/,Dh=/^%/,Fh=/[\\^$*+?|[\]().{}]/g;function z(t,n,e){var i=t<0?"-":"",a=(i?-t:t)+"",r=a.length;return i+(r<e?new Array(e-r+1).join(n)+a:a)}function Eh(t){return t.replace(Fh,"\\$&")}function $t(t){return new RegExp("^(?:"+t.map(Eh).join("|")+")","i")}function Rt(t){return new Map(t.map((n,e)=>[n.toLowerCase(),e]))}function Mh(t,n,e){var i=ce.exec(n.slice(e,e+1));return i?(t.w=+i[0],e+i[0].length):-1}function Bh(t,n,e){var i=ce.exec(n.slice(e,e+1));return i?(t.u=+i[0],e+i[0].length):-1}function Vh(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.U=+i[0],e+i[0].length):-1}function Oh(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.V=+i[0],e+i[0].length):-1}function Ih(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.W=+i[0],e+i[0].length):-1}function Wa(t,n,e){var i=ce.exec(n.slice(e,e+4));return i?(t.y=+i[0],e+i[0].length):-1}function ja(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.y=+i[0]+(+i[0]>68?1900:2e3),e+i[0].length):-1}function Ph(t,n,e){var i=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return i?(t.Z=i[1]?0:-(i[2]+(i[3]||"00")),e+i[0].length):-1}function Gh(t,n,e){var i=ce.exec(n.slice(e,e+1));return i?(t.q=3*i[0]-3,e+i[0].length):-1}function $h(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.m=i[0]-1,e+i[0].length):-1}function Ua(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.d=+i[0],e+i[0].length):-1}function Rh(t,n,e){var i=ce.exec(n.slice(e,e+3));return i?(t.m=0,t.d=+i[0],e+i[0].length):-1}function Qa(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.H=+i[0],e+i[0].length):-1}function Hh(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.M=+i[0],e+i[0].length):-1}function Xh(t,n,e){var i=ce.exec(n.slice(e,e+2));return i?(t.S=+i[0],e+i[0].length):-1}function Yh(t,n,e){var i=ce.exec(n.slice(e,e+3));return i?(t.L=+i[0],e+i[0].length):-1}function Nh(t,n,e){var i=ce.exec(n.slice(e,e+6));return i?(t.L=Math.floor(i[0]/1e3),e+i[0].length):-1}function zh(t,n,e){var i=Dh.exec(n.slice(e,e+1));return i?e+i[0].length:-1}function Wh(t,n,e){var i=ce.exec(n.slice(e));return i?(t.Q=+i[0],e+i[0].length):-1}function jh(t,n,e){var i=ce.exec(n.slice(e));return i?(t.s=+i[0],e+i[0].length):-1}function qa(t,n){return z(t.getDate(),n,2)}function Uh(t,n){return z(t.getHours(),n,2)}function Qh(t,n){return z(t.getHours()%12||12,n,2)}function qh(t,n){return z(1+Na.count(It(t),t),n,3)}function Za(t,n){return z(t.getMilliseconds(),n,3)}function Zh(t,n){return Za(t,n)+"000"}function Kh(t,n){return z(t.getMonth()+1,n,2)}function Jh(t,n){return z(t.getMinutes(),n,2)}function ed(t,n){return z(t.getSeconds(),n,2)}function td(t){var n=t.getDay();return 0===n?7:n}function id(t,n){return z(Xa.count(It(t)-1,t),n,2)}function Ka(t){var n=t.getDay();return n>=4||0===n?pt(t):pt.ceil(t)}function nd(t,n){return t=Ka(t),z(pt.count(It(t),t)+(4===It(t).getDay()),n,2)}function ad(t){return t.getDay()}function sd(t,n){return z(vi.count(It(t)-1,t),n,2)}function rd(t,n){return z(t.getFullYear()%100,n,2)}function od(t,n){return z((t=Ka(t)).getFullYear()%100,n,2)}function ld(t,n){return z(t.getFullYear()%1e4,n,4)}function cd(t,n){var e=t.getDay();return z((t=e>=4||0===e?pt(t):pt.ceil(t)).getFullYear()%1e4,n,4)}function hd(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+z(n/60|0,"0",2)+z(n%60,"0",2)}function Ja(t,n){return z(t.getUTCDate(),n,2)}function dd(t,n){return z(t.getUTCHours(),n,2)}function md(t,n){return z(t.getUTCHours()%12||12,n,2)}function ud(t,n){return z(1+Ha.count(Pt(t),t),n,3)}function es(t,n){return z(t.getUTCMilliseconds(),n,3)}function gd(t,n){return es(t,n)+"000"}function pd(t,n){return z(t.getUTCMonth()+1,n,2)}function fd(t,n){return z(t.getUTCMinutes(),n,2)}function xd(t,n){return z(t.getUTCSeconds(),n,2)}function _d(t){var n=t.getUTCDay();return 0===n?7:n}function vd(t,n){return z($a.count(Pt(t)-1,t),n,2)}function ts(t){var n=t.getUTCDay();return n>=4||0===n?gt(t):gt.ceil(t)}function yd(t,n){return t=ts(t),z(gt.count(Pt(t),t)+(4===Pt(t).getUTCDay()),n,2)}function Td(t){return t.getUTCDay()}function Cd(t,n){return z(_i.count(Pt(t)-1,t),n,2)}function bd(t,n){return z(t.getUTCFullYear()%100,n,2)}function wd(t,n){return z((t=ts(t)).getUTCFullYear()%100,n,2)}function Sd(t,n){return z(t.getUTCFullYear()%1e4,n,4)}function kd(t,n){var e=t.getUTCDay();return z((t=e>=4||0===e?gt(t):gt.ceil(t)).getUTCFullYear()%1e4,n,4)}function Ad(){return"+0000"}function is(){return"%"}function ns(t){return+t}function as(t){return Math.floor(+t/1e3)}function rs(t){return null===t?NaN:+t}!function Ed(t){ft=function Lh(t){var n=t.dateTime,e=t.date,i=t.time,a=t.periods,r=t.days,o=t.shortDays,l=t.months,h=t.shortMonths,c=$t(a),m=Rt(a),d=$t(r),u=Rt(r),g=$t(o),f=Rt(o),b=$t(l),p=Rt(l),C=$t(h),L=Rt(h),T={a:function te(k){return o[k.getDay()]},A:function ve(k){return r[k.getDay()]},b:function j(k){return h[k.getMonth()]},B:function le(k){return l[k.getMonth()]},c:null,d:qa,e:qa,f:Zh,g:od,G:cd,H:Uh,I:Qh,j:qh,L:Za,m:Kh,M:Jh,p:function se(k){return a[+(k.getHours()>=12)]},q:function re(k){return 1+~~(k.getMonth()/3)},Q:ns,s:as,S:ed,u:td,U:id,V:nd,w:ad,W:sd,x:null,X:null,y:rd,Y:ld,Z:hd,"%":is},M={a:function jt(k){return o[k.getUTCDay()]},A:function Ct(k){return r[k.getUTCDay()]},b:function bt(k){return h[k.getUTCMonth()]},B:function wt(k){return l[k.getUTCMonth()]},c:null,d:Ja,e:Ja,f:gd,g:wd,G:kd,H:dd,I:md,j:ud,L:es,m:pd,M:fd,p:function fe(k){return a[+(k.getUTCHours()>=12)]},q:function rt(k){return 1+~~(k.getUTCMonth()/3)},Q:ns,s:as,S:xd,u:_d,U:vd,V:yd,w:Td,W:Cd,x:null,X:null,y:bd,Y:Sd,Z:Ad,"%":is},V={a:function J(k,$,X){var x=g.exec($.slice(X));return x?(k.w=f.get(x[0].toLowerCase()),X+x[0].length):-1},A:function B(k,$,X){var x=d.exec($.slice(X));return x?(k.w=u.get(x[0].toLowerCase()),X+x[0].length):-1},b:function de(k,$,X){var x=C.exec($.slice(X));return x?(k.m=L.get(x[0].toLowerCase()),X+x[0].length):-1},B:function W(k,$,X){var x=b.exec($.slice(X));return x?(k.m=p.get(x[0].toLowerCase()),X+x[0].length):-1},c:function K(k,$,X){return Z(k,n,$,X)},d:Ua,e:Ua,f:Nh,g:ja,G:Wa,H:Qa,I:Qa,j:Rh,L:Yh,m:$h,M:Hh,p:function _e(k,$,X){var x=c.exec($.slice(X));return x?(k.p=m.get(x[0].toLowerCase()),X+x[0].length):-1},q:Gh,Q:Wh,s:jh,S:Xh,u:Bh,U:Vh,V:Oh,w:Mh,W:Ih,x:function Le(k,$,X){return Z(k,e,$,X)},X:function ee(k,$,X){return Z(k,i,$,X)},y:ja,Y:Wa,Z:Ph,"%":zh};function D(k,$){return function(X){var Te,O,ne,x=[],ie=-1,U=0,ye=k.length;for(X instanceof Date||(X=new Date(+X));++ie<ye;)37===k.charCodeAt(ie)&&(x.push(k.slice(U,ie)),null!=(O=za[Te=k.charAt(++ie)])?Te=k.charAt(++ie):O="e"===Te?" ":"0",(ne=$[Te])&&(Te=ne(X,O)),x.push(Te),U=ie+1);return x.push(k.slice(U,ie)),x.join("")}}function q(k,$){return function(X){var U,ye,x=Gt(1900,void 0,1);if(Z(x,k,X+="",0)!=X.length)return null;if("Q"in x)return new Date(x.Q);if("s"in x)return new Date(1e3*x.s+("L"in x?x.L:0));if($&&!("Z"in x)&&(x.Z=0),"p"in x&&(x.H=x.H%12+12*x.p),void 0===x.m&&(x.m="q"in x?x.q:0),"V"in x){if(x.V<1||x.V>53)return null;"w"in x||(x.w=1),"Z"in x?(ye=(U=pn(Gt(x.y,0,1))).getUTCDay(),U=ye>4||0===ye?_i.ceil(U):_i(U),U=Ha.offset(U,7*(x.V-1)),x.y=U.getUTCFullYear(),x.m=U.getUTCMonth(),x.d=U.getUTCDate()+(x.w+6)%7):(ye=(U=gn(Gt(x.y,0,1))).getDay(),U=ye>4||0===ye?vi.ceil(U):vi(U),U=Na.offset(U,7*(x.V-1)),x.y=U.getFullYear(),x.m=U.getMonth(),x.d=U.getDate()+(x.w+6)%7)}else("W"in x||"U"in x)&&("w"in x||(x.w="u"in x?x.u%7:"W"in x?1:0),ye="Z"in x?pn(Gt(x.y,0,1)).getUTCDay():gn(Gt(x.y,0,1)).getDay(),x.m=0,x.d="W"in x?(x.w+6)%7+7*x.W-(ye+5)%7:x.w+7*x.U-(ye+6)%7);return"Z"in x?(x.H+=x.Z/100|0,x.M+=x.Z%100,pn(x)):gn(x)}}function Z(k,$,X,x){for(var Te,O,ie=0,U=$.length,ye=X.length;ie<U;){if(x>=ye)return-1;if(37===(Te=$.charCodeAt(ie++))){if(Te=$.charAt(ie++),!(O=V[Te in za?$.charAt(ie++):Te])||(x=O(k,X,x))<0)return-1}else if(Te!=X.charCodeAt(x++))return-1}return x}return T.x=D(e,T),T.X=D(i,T),T.c=D(n,T),M.x=D(e,M),M.X=D(i,M),M.c=D(n,M),{format:function(k){var $=D(k+="",T);return $.toString=function(){return k},$},parse:function(k){var $=q(k+="",!1);return $.toString=function(){return k},$},utcFormat:function(k){var $=D(k+="",M);return $.toString=function(){return k},$},utcParse:function(k){var $=q(k+="",!0);return $.toString=function(){return k},$}}}(t),ss=ft.format}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Md=Qi(Vt).right,ls=(Qi(rs),Md);function Bd(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}function Od(t){return+t}var cs=[0,1];function xt(t){return t}function fn(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:function Vd(t){return function(){return t}}(isNaN(n)?NaN:.5)}function Pd(t,n,e){var i=t[0],a=t[1],r=n[0],o=n[1];return a<i?(i=fn(a,i),r=e(o,r)):(i=fn(i,a),r=e(r,o)),function(l){return r(i(l))}}function Gd(t,n,e){var i=Math.min(t.length,n.length)-1,a=new Array(i),r=new Array(i),o=-1;for(t[i]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++o<i;)a[o]=fn(t[o],t[o+1]),r[o]=e(n[o],n[o+1]);return function(l){var h=ls(t,l,1,i)-1;return r[h](a[h](l))}}function hs(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function ds(){return function $d(){var i,a,r,l,h,c,t=cs,n=cs,e=ai,o=xt;function m(){var u=Math.min(t.length,n.length);return o!==xt&&(o=function Id(t,n){var e;return t>n&&(e=t,t=n,n=e),function(i){return Math.max(t,Math.min(n,i))}}(t[0],t[u-1])),l=u>2?Gd:Pd,h=c=null,d}function d(u){return null==u||isNaN(u=+u)?r:(h||(h=l(t.map(i),n,e)))(i(o(u)))}return d.invert=function(u){return o(a((c||(c=l(n,t.map(i),Ee)))(u)))},d.domain=function(u){return arguments.length?(t=Array.from(u,Od),m()):t.slice()},d.range=function(u){return arguments.length?(n=Array.from(u),m()):n.slice()},d.rangeRound=function(u){return n=Array.from(u),e=Bd,m()},d.clamp=function(u){return arguments.length?(o=!!u||xt,m()):o!==xt},d.interpolate=function(u){return arguments.length?(e=u,m()):e},d.unknown=function(u){return arguments.length?(r=u,d):r},function(u,g){return i=u,a=g,m()}}()(xt,xt)}function Ht(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function Hd(t){return new Date(t)}function Xd(t){return t instanceof Date?+t:+new Date(+t)}function ms(t,n,e,i,a,r,o,l,h,c){var m=ds(),d=m.invert,u=m.domain,g=c(".%L"),f=c(":%S"),b=c("%I:%M"),p=c("%I %p"),C=c("%a %d"),L=c("%b %d"),T=c("%B"),M=c("%Y");function V(D){return(h(D)<D?g:l(D)<D?f:o(D)<D?b:r(D)<D?p:i(D)<D?a(D)<D?C:L:e(D)<D?T:M)(D)}return m.invert=function(D){return new Date(d(D))},m.domain=function(D){return arguments.length?u(Array.from(D,Xd)):u().map(Hd)},m.ticks=function(D){var q=u();return t(q[0],q[q.length-1],D??10)},m.tickFormat=function(D,q){return null==q?V:c(q)},m.nice=function(D){var q=u();return(!D||"function"!=typeof D.range)&&(D=n(q[0],q[q.length-1],D??10)),D?u(function Rd(t,n){var o,e=0,i=(t=t.slice()).length-1,a=t[e],r=t[i];return r<a&&(o=e,e=i,i=o,o=a,a=r,r=o),t[e]=n.floor(a),t[i]=n.ceil(r),t}(q,D)):m},m.copy=function(){return hs(m,ms(t,n,e,i,a,r,o,l,h,c))},m}function us(){return Ht.apply(ms(_h,vh,xi,on,rn,sn,an,nn,ut,ss).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}var gs,Yd=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function yi(t){if(!(n=Yd.exec(t)))throw new Error("invalid format: "+t);var n;return new xn({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}function xn(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Ti(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,i=t.slice(0,e);return[i.length>1?i[0]+i.slice(2):i,+t.slice(e+1)]}function _t(t){return(t=Ti(Math.abs(t)))?t[1]:NaN}function ps(t,n){var e=Ti(t,n);if(!e)return t+"";var i=e[0],a=e[1];return a<0?"0."+new Array(-a).join("0")+i:i.length>a+1?i.slice(0,a+1)+"."+i.slice(a+1):i+new Array(a-i.length+2).join("0")}yi.prototype=xn.prototype,xn.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const fs={"%":(t,n)=>(100*t).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function Nd(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>ps(100*t,n),r:ps,s:function Qd(t,n){var e=Ti(t,n);if(!e)return t+"";var i=e[0],a=e[1],r=a-(gs=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,o=i.length;return r===o?i:r>o?i+new Array(r-o+1).join("0"):r>0?i.slice(0,r)+"."+i.slice(r):"0."+new Array(1-r).join("0")+Ti(t,Math.max(0,n+r-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function xs(t){return t}var Ci,ys,Ts,_s=Array.prototype.map,vs=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tm(t){var n=t.domain;return t.ticks=function(e){var i=n();return function sh(t,n,e){if(!((e=+e)>0))return[];if((t=+t)==(n=+n))return[t];const i=n<t,[a,r,o]=i?pi(n,t,e):pi(t,n,e);if(!(r>=a))return[];const l=r-a+1,h=new Array(l);if(i)if(o<0)for(let c=0;c<l;++c)h[c]=(r-c)/-o;else for(let c=0;c<l;++c)h[c]=(r-c)*o;else if(o<0)for(let c=0;c<l;++c)h[c]=(a+c)/-o;else for(let c=0;c<l;++c)h[c]=(a+c)*o;return h}(i[0],i[i.length-1],e??10)},t.tickFormat=function(e,i){var a=n();return function em(t,n,e,i){var r,a=Zi(t,n,e);switch((i=yi(i??",f")).type){case"s":var o=Math.max(Math.abs(t),Math.abs(n));return null==i.precision&&!isNaN(r=function zd(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(_t(n)/3)))-_t(Math.abs(t)))}(a,o))&&(i.precision=r),Ts(i,o);case"":case"e":case"g":case"p":case"r":null==i.precision&&!isNaN(r=function Kd(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,_t(n)-_t(t))+1}(a,Math.max(Math.abs(t),Math.abs(n))))&&(i.precision=r-("e"===i.type));break;case"f":case"%":null==i.precision&&!isNaN(r=function Jd(t){return Math.max(0,-_t(Math.abs(t)))}(a))&&(i.precision=r-2*("%"===i.type))}return ys(i)}(a[0],a[a.length-1],e??10,i)},t.nice=function(e){null==e&&(e=10);var h,c,i=n(),a=0,r=i.length-1,o=i[a],l=i[r],m=10;for(l<o&&(c=o,o=l,l=c,c=a,a=r,r=c);m-- >0;){if((c=qi(o,l,e))===h)return i[a]=o,i[r]=l,n(i);if(c>0)o=Math.floor(o/c)*c,l=Math.ceil(l/c)*c;else{if(!(c<0))break;o=Math.ceil(o*c)/c,l=Math.floor(l*c)/c}h=c}return t},t}function ze(){var t=ds();return t.copy=function(){return hs(t,ze())},Ht.apply(t,arguments),tm(t)}function Cs(t,n,e){t=+t,n=+n,e=(a=arguments.length)<2?(n=t,t=0,1):a<3?1:+e;for(var i=-1,a=0|Math.max(0,Math.ceil((n-t)/e)),r=new Array(a);++i<a;)r[i]=t+i*e;return r}!function Zd(t){Ci=function qd(t){var n=void 0===t.grouping||void 0===t.thousands?xs:function Wd(t,n){return function(e,i){for(var a=e.length,r=[],o=0,l=t[0],h=0;a>0&&l>0&&(h+l+1>i&&(l=Math.max(1,i-h)),r.push(e.substring(a-=l,a+l)),!((h+=l+1)>i));)l=t[o=(o+1)%t.length];return r.reverse().join(n)}}(_s.call(t.grouping,Number),t.thousands+""),e=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",r=void 0===t.numerals?xs:function jd(t){return function(n){return n.replace(/[0-9]/g,function(e){return t[+e]})}}(_s.call(t.numerals,String)),o=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"\u2212":t.minus+"",h=void 0===t.nan?"NaN":t.nan+"";function c(d){var u=(d=yi(d)).fill,g=d.align,f=d.sign,b=d.symbol,p=d.zero,C=d.width,L=d.comma,T=d.precision,M=d.trim,V=d.type;"n"===V?(L=!0,V="g"):fs[V]||(void 0===T&&(T=12),M=!0,V="g"),(p||"0"===u&&"="===g)&&(p=!0,u="0",g="=");var D="$"===b?e:"#"===b&&/[boxX]/.test(V)?"0"+V.toLowerCase():"",q="$"===b?i:/[%p]/.test(V)?o:"",Z=fs[V],_e=/[defgprs%]/.test(V);function J(B){var K,Le,ee,de=D,W=q;if("c"===V)W=Z(B)+W,B="";else{var te=(B=+B)<0||1/B<0;if(B=isNaN(B)?h:Z(Math.abs(B),T),M&&(B=function Ud(t){e:for(var a,n=t.length,e=1,i=-1;e<n;++e)switch(t[e]){case".":i=a=e;break;case"0":0===i&&(i=e),a=e;break;default:if(!+t[e])break e;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(a+1):t}(B)),te&&0==+B&&"+"!==f&&(te=!1),de=(te?"("===f?f:l:"-"===f||"("===f?"":f)+de,W=("s"===V?vs[8+gs/3]:"")+W+(te&&"("===f?")":""),_e)for(K=-1,Le=B.length;++K<Le;)if(48>(ee=B.charCodeAt(K))||ee>57){W=(46===ee?a+B.slice(K+1):B.slice(K))+W,B=B.slice(0,K);break}}L&&!p&&(B=n(B,1/0));var ve=de.length+B.length+W.length,j=ve<C?new Array(C-ve+1).join(u):"";switch(L&&p&&(B=n(j+B,j.length?C-W.length:1/0),j=""),g){case"<":B=de+B+W+j;break;case"=":B=de+j+B+W;break;case"^":B=j.slice(0,ve=j.length>>1)+de+B+W+j.slice(ve);break;default:B=j+de+B+W}return r(B)}return T=void 0===T?6:/[gprs]/.test(V)?Math.max(1,Math.min(21,T)):Math.max(0,Math.min(20,T)),J.toString=function(){return d+""},J}return{format:c,formatPrefix:function m(d,u){var g=c(((d=yi(d)).type="f",d)),f=3*Math.max(-8,Math.min(8,Math.floor(_t(u)/3))),b=Math.pow(10,-f),p=vs[8+f/3];return function(C){return g(b*C)+p}}}}(t),ys=Ci.format,Ts=Ci.formatPrefix}({thousands:",",grouping:[3],currency:["$",""]});class bs extends Map{constructor(n,e=ks){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=n)for(const[i,a]of n)this.set(i,a)}get(n){return super.get(_n(this,n))}has(n){return super.has(_n(this,n))}set(n,e){return super.set(function ws({_intern:t,_key:n},e){const i=n(e);return t.has(i)?t.get(i):(t.set(i,e),e)}(this,n),e)}delete(n){return super.delete(function Ss({_intern:t,_key:n},e){const i=n(e);return t.has(i)&&(e=t.get(i),t.delete(i)),e}(this,n))}}function _n({_intern:t,_key:n},e){const i=n(e);return t.has(i)?t.get(i):e}function ks(t){return null!==t&&"object"==typeof t?t.valueOf():t}Set;const As=Symbol("implicit");function vn(){var t=new bs,n=[],e=[],i=As;function a(r){let o=t.get(r);if(void 0===o){if(i!==As)return i;t.set(r,o=n.push(r)-1)}return e[o%e.length]}return a.domain=function(r){if(!arguments.length)return n.slice();n=[],t=new bs;for(const o of r)t.has(o)||t.set(o,n.push(o)-1);return a},a.range=function(r){return arguments.length?(e=Array.from(r),a):e.slice()},a.unknown=function(r){return arguments.length?(i=r,a):i},a.copy=function(){return vn(n,e).unknown(i)},Ht.apply(a,arguments),a}function Xt(){var r,o,t=vn().unknown(void 0),n=t.domain,e=t.range,i=0,a=1,l=!1,h=0,c=0,m=.5;function d(){var u=n().length,g=a<i,f=g?a:i,b=g?i:a;r=(b-f)/Math.max(1,u-h+2*c),l&&(r=Math.floor(r)),f+=(b-f-r*(u-h))*m,o=r*(1-h),l&&(f=Math.round(f),o=Math.round(o));var p=Cs(u).map(function(C){return f+r*C});return e(g?p.reverse():p)}return delete t.unknown,t.domain=function(u){return arguments.length?(n(u),d()):n()},t.range=function(u){return arguments.length?([i,a]=u,i=+i,a=+a,d()):[i,a]},t.rangeRound=function(u){return[i,a]=u,i=+i,a=+a,l=!0,d()},t.bandwidth=function(){return o},t.step=function(){return r},t.round=function(u){return arguments.length?(l=!!u,d()):l},t.padding=function(u){return arguments.length?(h=Math.min(1,c=+u),d()):h},t.paddingInner=function(u){return arguments.length?(h=Math.min(1,u),d()):h},t.paddingOuter=function(u){return arguments.length?(c=+u,d()):c},t.align=function(u){return arguments.length?(m=Math.max(0,Math.min(1,u)),d()):m},t.copy=function(){return Xt(n(),[i,a]).round(l).paddingInner(h).paddingOuter(c).align(m)},Ht.apply(d(),arguments)}function Ls(t){var n=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return Ls(n())},t}function Ds(){return Ls(Xt.apply(null,arguments).paddingInner(1))}function im(t,n,e=rs){if((i=t.length)&&!isNaN(n=+n)){if(n<=0||i<2)return+e(t[0],0,t);if(n>=1)return+e(t[i-1],i-1,t);var i,a=(i-1)*n,r=Math.floor(a),o=+e(t[r],r,t);return o+(+e(t[r+1],r+1,t)-o)*(a-r)}}function Fs(){var i,t=[],n=[],e=[];function a(){var o=0,l=Math.max(1,n.length);for(e=new Array(l-1);++o<l;)e[o-1]=im(t,o/l);return r}function r(o){return null==o||isNaN(o=+o)?i:n[ls(e,o)]}return r.invertExtent=function(o){var l=n.indexOf(o);return l<0?[NaN,NaN]:[l>0?e[l-1]:t[0],l<e.length?e[l]:t[t.length-1]]},r.domain=function(o){if(!arguments.length)return t.slice();t=[];for(let l of o)null!=l&&!isNaN(l=+l)&&t.push(l);return t.sort(Vt),a()},r.range=function(o){return arguments.length?(n=Array.from(o),a()):n.slice()},r.unknown=function(o){return arguments.length?(i=o,r):i},r.quantiles=function(){return e.slice()},r.copy=function(){return Fs().domain(t).range(n).unknown(i)},Ht.apply(r,arguments)}function Es(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function he(t){return function(){return t}}function Ms(t){this._context=t}function yn(t){return new Ms(t)}Ms.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};const Tn=Math.PI,Cn=2*Tn,at=1e-6,nm=Cn-at;function Bs(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n)this._+=arguments[n]+t[n]}class bi{constructor(n){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==n?Bs:function am(t){let n=Math.floor(t);if(!(n>=0))throw new Error(`invalid digits: ${t}`);if(n>15)return Bs;const e=10**n;return function(i){this._+=i[0];for(let a=1,r=i.length;a<r;++a)this._+=Math.round(arguments[a]*e)/e+i[a]}}(n)}moveTo(n,e){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(n,e){this._append`L${this._x1=+n},${this._y1=+e}`}quadraticCurveTo(n,e,i,a){this._append`Q${+n},${+e},${this._x1=+i},${this._y1=+a}`}bezierCurveTo(n,e,i,a,r,o){this._append`C${+n},${+e},${+i},${+a},${this._x1=+r},${this._y1=+o}`}arcTo(n,e,i,a,r){if(n=+n,e=+e,i=+i,a=+a,(r=+r)<0)throw new Error(`negative radius: ${r}`);let o=this._x1,l=this._y1,h=i-n,c=a-e,m=o-n,d=l-e,u=m*m+d*d;if(null===this._x1)this._append`M${this._x1=n},${this._y1=e}`;else if(u>at)if(Math.abs(d*h-c*m)>at&&r){let g=i-o,f=a-l,b=h*h+c*c,p=g*g+f*f,C=Math.sqrt(b),L=Math.sqrt(u),T=r*Math.tan((Tn-Math.acos((b+u-p)/(2*C*L)))/2),M=T/L,V=T/C;Math.abs(M-1)>at&&this._append`L${n+M*m},${e+M*d}`,this._append`A${r},${r},0,0,${+(d*g>m*f)},${this._x1=n+V*h},${this._y1=e+V*c}`}else this._append`L${this._x1=n},${this._y1=e}`}arc(n,e,i,a,r,o){if(n=+n,e=+e,o=!!o,(i=+i)<0)throw new Error(`negative radius: ${i}`);let l=i*Math.cos(a),h=i*Math.sin(a),c=n+l,m=e+h,d=1^o,u=o?a-r:r-a;null===this._x1?this._append`M${c},${m}`:(Math.abs(this._x1-c)>at||Math.abs(this._y1-m)>at)&&this._append`L${c},${m}`,i&&(u<0&&(u=u%Cn+Cn),u>nm?this._append`A${i},${i},0,1,${d},${n-l},${e-h}A${i},${i},0,1,${d},${this._x1=c},${this._y1=m}`:u>at&&this._append`A${i},${i},0,${+(u>=Tn)},${d},${this._x1=n+i*Math.cos(r)},${this._y1=e+i*Math.sin(r)}`)}rect(n,e,i,a){this._append`M${this._x0=this._x1=+n},${this._y0=this._y1=+e}h${i=+i}v${+a}h${-i}Z`}toString(){return this._}}function Vs(t){let n=3;return t.digits=function(e){if(!arguments.length)return n;if(null==e)n=null;else{const i=Math.floor(e);if(!(i>=0))throw new RangeError(`invalid digits: ${e}`);n=i}return t},()=>new bi(n)}function Os(t){return t[0]}function Is(t){return t[1]}function Ps(t,n){var e=he(!0),i=null,a=yn,r=null,o=Vs(l);function l(h){var c,d,g,m=(h=Es(h)).length,u=!1;for(null==i&&(r=a(g=o())),c=0;c<=m;++c)!(c<m&&e(d=h[c],c,h))===u&&((u=!u)?r.lineStart():r.lineEnd()),u&&r.point(+t(d,c,h),+n(d,c,h));if(g)return r=null,g+""||null}return t="function"==typeof t?t:void 0===t?Os:he(t),n="function"==typeof n?n:void 0===n?Is:he(n),l.x=function(h){return arguments.length?(t="function"==typeof h?h:he(+h),l):t},l.y=function(h){return arguments.length?(n="function"==typeof h?h:he(+h),l):n},l.defined=function(h){return arguments.length?(e="function"==typeof h?h:he(!!h),l):e},l.curve=function(h){return arguments.length?(a=h,null!=i&&(r=a(i)),l):a},l.context=function(h){return arguments.length?(null==h?i=r=null:r=a(i=h),l):i},l}function Gs(t,n,e){var i=null,a=he(!0),r=null,o=yn,l=null,h=Vs(c);function c(d){var u,g,f,p,L,b=(d=Es(d)).length,C=!1,T=new Array(b),M=new Array(b);for(null==r&&(l=o(L=h())),u=0;u<=b;++u){if(!(u<b&&a(p=d[u],u,d))===C)if(C=!C)g=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),f=u-1;f>=g;--f)l.point(T[f],M[f]);l.lineEnd(),l.areaEnd()}C&&(T[u]=+t(p,u,d),M[u]=+n(p,u,d),l.point(i?+i(p,u,d):T[u],e?+e(p,u,d):M[u]))}if(L)return l=null,L+""||null}function m(){return Ps().defined(a).curve(o).context(r)}return t="function"==typeof t?t:void 0===t?Os:he(+t),n="function"==typeof n?n:he(void 0===n?0:+n),e="function"==typeof e?e:void 0===e?Is:he(+e),c.x=function(d){return arguments.length?(t="function"==typeof d?d:he(+d),i=null,c):t},c.x0=function(d){return arguments.length?(t="function"==typeof d?d:he(+d),c):t},c.x1=function(d){return arguments.length?(i=null==d?null:"function"==typeof d?d:he(+d),c):i},c.y=function(d){return arguments.length?(n="function"==typeof d?d:he(+d),e=null,c):n},c.y0=function(d){return arguments.length?(n="function"==typeof d?d:he(+d),c):n},c.y1=function(d){return arguments.length?(e=null==d?null:"function"==typeof d?d:he(+d),c):e},c.lineX0=c.lineY0=function(){return m().x(t).y(n)},c.lineY1=function(){return m().x(t).y(e)},c.lineX1=function(){return m().x(i).y(n)},c.defined=function(d){return arguments.length?(a="function"==typeof d?d:he(!!d),c):a},c.curve=function(d){return arguments.length?(o=d,null!=r&&(l=o(r)),c):o},c.context=function(d){return arguments.length?(null==d?r=l=null:l=o(r=d),c):r},c}var rm=G(6021);const om=["caretElm"],lm=t=>({model:t});function cm(t,n){}function hm(t,n){if(1&t&&(s.j41(0,"span"),s.DNE(1,cm,0,0,"ng-template",5),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngTemplateOutlet",e.template)("ngTemplateOutletContext",s.eq3(2,lm,e.context))}}function dm(t,n){if(1&t&&s.nrm(0,"span",6),2&t){const e=s.XpG();s.Y8G("innerHTML",e.title,s.npT)}}function mm(t,n){if(1&t&&(s.j41(0,"header",4)(1,"span",5),s.EFF(2),s.k0s()()),2&t){const e=s.XpG();s.R7$(2),s.JRh(e.title)}}function um(t,n){if(1&t){const e=s.RV6();s.j41(0,"li",6)(1,"ngx-charts-legend-entry",7),s.bIt("select",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.labelClick.emit(a))})("activate",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.activate(a))})("deactivate",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.deactivate(a))}),s.k0s()()}if(2&t){const e=n.$implicit,i=s.XpG();s.R7$(),s.Y8G("label",e.label)("formattedLabel",e.formattedLabel)("color",e.color)("isActive",i.isActive(e))}}const $s=["*"];function gm(t,n){if(1&t&&s.nrm(0,"ngx-charts-scale-legend",4),2&t){const e=s.XpG();s.Y8G("horizontal",e.legendOptions&&e.legendOptions.position===e.LegendPosition.Below)("valueRange",e.legendOptions.domain)("colors",e.legendOptions.colors)("height",e.view[1])("width",e.legendWidth)}}function pm(t,n){if(1&t){const e=s.RV6();s.j41(0,"ngx-charts-legend",5),s.bIt("labelClick",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.legendLabelClick.emit(a))})("labelActivate",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.legendLabelActivate.emit(a))})("labelDeactivate",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.legendLabelDeactivate.emit(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("horizontal",e.legendOptions&&e.legendOptions.position===e.LegendPosition.Below)("data",e.legendOptions.domain)("title",e.legendOptions.title)("colors",e.legendOptions.colors)("height",e.view[1])("width",e.legendWidth)("activeEntries",e.activeEntries)}}const fm=["ngx-charts-axis-label",""],Rs=["ticksel"],xm=["ngx-charts-x-axis-ticks",""];function _m(t,n){1&t&&(s.qSk(),s.eu8(0))}function vm(t,n){if(1&t&&(s.qSk(),s.j41(0,"tspan",10),s.EFF(1),s.k0s()),2&t){const e=n.$implicit;s.BMQ("y",12*n.index),s.R7$(),s.SpI(" ",e," ")}}function ym(t,n){if(1&t&&(s.qSk(),s.qex(0),s.DNE(1,vm,2,2,"tspan",9),s.bVm()),2&t){const e=n.ngIf;s.R7$(),s.Y8G("ngForOf",e)}}function Tm(t,n){if(1&t&&s.DNE(0,ym,2,1,"ng-container",6),2&t){const e=s.XpG(2).$implicit,i=s.XpG();s.Y8G("ngIf",i.tickChunks(e))}}function Cm(t,n){if(1&t&&s.EFF(0),2&t){const e=s.XpG().ngIf,i=s.XpG(2);s.SpI(" ",i.tickTrim(e)," ")}}function bm(t,n){if(1&t&&(s.qSk(),s.qex(0),s.j41(1,"title"),s.EFF(2),s.k0s(),s.j41(3,"text",7),s.DNE(4,_m,1,0,"ng-container",8),s.k0s(),s.DNE(5,Tm,1,1,"ng-template",null,1,s.C5r)(7,Cm,1,1,"ng-template",null,2,s.C5r),s.bVm()),2&t){const e=n.ngIf,i=s.sdS(6),a=s.sdS(8),r=s.XpG(2);s.R7$(2),s.JRh(e),s.R7$(),s.BMQ("text-anchor",r.textAnchor)("transform",r.textTransform),s.R7$(),s.Y8G("ngIf",r.isWrapTicksSupported)("ngIfThen",i)("ngIfElse",a)}}function wm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g",5),s.DNE(1,bm,9,6,"ng-container",6),s.k0s()),2&t){const e=n.$implicit,i=s.XpG();s.BMQ("transform",i.tickTransform(e)),s.R7$(),s.Y8G("ngIf",i.tickFormat(e))}}function Sm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"line",11),s.k0s()),2&t){const e=s.XpG(2);s.BMQ("transform",e.gridLineTransform()),s.R7$(),s.BMQ("y1",-e.gridLineHeight)}}function km(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Sm,2,2,"g",6),s.k0s()),2&t){const e=n.$implicit,i=s.XpG();s.BMQ("transform",i.tickTransform(e)),s.R7$(),s.Y8G("ngIf",i.showGridLines)}}const Am=["ngx-charts-x-axis",""];function Lm(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",2),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.emitTicksHeight(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("trimTicks",e.trimTicks)("rotateTicks",e.rotateTicks)("maxTickLength",e.maxTickLength)("tickFormatting",e.tickFormatting)("tickArguments",e.tickArguments)("tickStroke",e.tickStroke)("scale",e.xScale)("orient",e.xOrient)("showGridLines",e.showGridLines)("gridLineHeight",e.dims.height)("width",e.dims.width)("tickValues",e.ticks)("wrapTicks",e.wrapTicks)}}function Dm(t,n){if(1&t&&(s.qSk(),s.nrm(0,"g",3)),2&t){const e=s.XpG();s.Y8G("label",e.labelText)("offset",e.labelOffset)("orient",e.orientation.Bottom)("height",e.dims.height)("width",e.dims.width)}}const Fm=["ngx-charts-y-axis-ticks",""];function Em(t,n){1&t&&(s.qSk(),s.eu8(0))}function Mm(t,n){if(1&t&&(s.qSk(),s.j41(0,"tspan",12),s.EFF(1),s.k0s()),2&t){const e=n.$implicit,i=n.index,a=s.XpG(6);s.BMQ("y",i*(8+a.tickSpacing)),s.R7$(),s.SpI(" ",e," ")}}function Bm(t,n){if(1&t&&(s.qSk(),s.qex(0),s.DNE(1,Mm,2,2,"tspan",11),s.bVm()),2&t){const e=s.XpG().ngIf;s.R7$(),s.Y8G("ngForOf",e)}}function Vm(t,n){if(1&t&&(s.qSk(),s.qex(0),s.DNE(1,Bm,2,1,"ng-container",10),s.bVm()),2&t){const e=n.ngIf;s.XpG(2);const i=s.sdS(8);s.R7$(),s.Y8G("ngIf",e.length>1)("ngIfElse",i)}}function Om(t,n){if(1&t&&s.DNE(0,Vm,2,2,"ng-container",7),2&t){const e=s.XpG(2).$implicit,i=s.XpG();s.Y8G("ngIf",i.tickChunks(e))}}function Im(t,n){if(1&t&&s.EFF(0),2&t){const e=s.XpG().ngIf,i=s.XpG(2);s.SpI(" ",i.tickTrim(e)," ")}}function Pm(t,n){if(1&t&&(s.qSk(),s.qex(0),s.j41(1,"title"),s.EFF(2),s.k0s(),s.j41(3,"text",8),s.DNE(4,Em,1,0,"ng-container",9),s.k0s(),s.DNE(5,Om,1,1,"ng-template",null,1,s.C5r)(7,Im,1,1,"ng-template",null,2,s.C5r),s.bVm()),2&t){const e=n.ngIf,i=s.sdS(6),a=s.sdS(8),r=s.XpG(2);s.R7$(2),s.JRh(e),s.R7$(),s.xc7("font-size","12px"),s.BMQ("dy",r.dy)("x",r.x1)("y",r.y1)("text-anchor",r.textAnchor),s.R7$(),s.Y8G("ngIf",r.wrapTicks)("ngIfThen",i)("ngIfElse",a)}}function Gm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g",6),s.DNE(1,Pm,9,10,"ng-container",7),s.k0s()),2&t){const e=n.$implicit,i=s.XpG();s.BMQ("transform",i.transform(e)),s.R7$(),s.Y8G("ngIf",i.tickFormat(e))}}function $m(t,n){if(1&t&&(s.qSk(),s.nrm(0,"path",13)),2&t){const e=s.XpG();s.BMQ("d",e.referenceAreaPath)("transform",e.gridLineTransform())}}function Rm(t,n){if(1&t&&(s.qSk(),s.nrm(0,"line",15)),2&t){const e=s.XpG(3);s.BMQ("x2",e.gridLineWidth)}}function Hm(t,n){if(1&t&&(s.qSk(),s.nrm(0,"line",15)),2&t){const e=s.XpG(3);s.BMQ("x2",-e.gridLineWidth)}}function Xm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Rm,1,1,"line",14)(2,Hm,1,1,"line",14),s.k0s()),2&t){const e=s.XpG(2);s.BMQ("transform",e.gridLineTransform()),s.R7$(),s.Y8G("ngIf",e.orient===e.Orientation.Left),s.R7$(),s.Y8G("ngIf",e.orient===e.Orientation.Right)}}function Ym(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Xm,3,3,"g",7),s.k0s()),2&t){const e=n.$implicit,i=s.XpG();s.BMQ("transform",i.transform(e)),s.R7$(),s.Y8G("ngIf",i.showGridLines)}}function Nm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g")(1,"title"),s.EFF(2),s.k0s(),s.j41(3,"text",17),s.EFF(4),s.k0s()()),2&t){const e=s.XpG(2).$implicit,i=s.XpG();s.R7$(2),s.JRh(i.tickTrim(i.tickFormat(e.value))),s.R7$(),s.BMQ("dy",i.dy)("y",-6)("x",i.gridLineWidth)("text-anchor",i.textAnchor),s.R7$(),s.SpI(" ",e.name," ")}}function zm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"line",16),s.DNE(2,Nm,5,6,"g",7),s.k0s()),2&t){const e=s.XpG().$implicit,i=s.XpG();s.BMQ("transform",i.transform(e.value)),s.R7$(),s.BMQ("x2",i.gridLineWidth)("transform",i.gridLineTransform()),s.R7$(),s.Y8G("ngIf",i.showRefLabels)}}function Wm(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,zm,3,4,"g",7),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngIf",e.showRefLines)}}const jm=["ngx-charts-y-axis",""];function Um(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",2),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.emitTicksWidth(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("trimTicks",e.trimTicks)("maxTickLength",e.maxTickLength)("tickFormatting",e.tickFormatting)("tickArguments",e.tickArguments)("tickValues",e.ticks)("tickStroke",e.tickStroke)("scale",e.yScale)("orient",e.yOrient)("showGridLines",e.showGridLines)("gridLineWidth",e.dims.width)("referenceLines",e.referenceLines)("showRefLines",e.showRefLines)("showRefLabels",e.showRefLabels)("height",e.dims.height)("wrapTicks",e.wrapTicks)}}function Qm(t,n){if(1&t&&(s.qSk(),s.nrm(0,"g",3)),2&t){const e=s.XpG();s.Y8G("label",e.labelText)("offset",e.labelOffset)("orient",e.yOrient)("height",e.dims.height)("width",e.dims.width)}}const qm=["ngx-charts-svg-linear-gradient",""];function Zm(t,n){if(1&t&&(s.qSk(),s.nrm(0,"stop")),2&t){const e=n.$implicit;s.xc7("stop-color",e.color)("stop-opacity",e.opacity),s.BMQ("offset",e.offset+"%")}}const Km=["ngx-charts-circle",""],Jm=["ngx-charts-circle-series",""],eu=t=>({name:t});function tu(t,n){if(1&t&&(s.qSk(),s.nrm(0,"rect",4)),2&t){const e=s.XpG(2);s.Y8G("@animationState","active"),s.BMQ("x",e.circle.cx-e.circle.radius)("y",e.circle.cy)("width",2*e.circle.radius)("height",e.circle.height)("fill",e.gradientFill)}}function iu(t,n){if(1&t&&(s.qSk(),s.nrm(0,"rect",4)),2&t){const e=s.XpG(2);s.BMQ("x",e.circle.cx-e.circle.radius)("y",e.circle.cy)("width",2*e.circle.radius)("height",e.circle.height)("fill",e.gradientFill)}}function nu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g")(1,"defs"),s.nrm(2,"g",1),s.k0s(),s.DNE(3,tu,1,6,"rect",2)(4,iu,1,5,"rect",2),s.j41(5,"g",3),s.bIt("select",function(){s.eBV(e);const a=s.XpG();return s.Njj(a.onClick(a.circle.data))})("activate",function(){s.eBV(e);const a=s.XpG();return s.Njj(a.activateCircle())})("deactivate",function(){s.eBV(e);const a=s.XpG();return s.Njj(a.deactivateCircle())}),s.k0s()()}if(2&t){const e=s.XpG();s.R7$(2),s.Y8G("orientation",e.barOrientation.Vertical)("name",e.gradientId)("stops",e.circle.gradientStops),s.R7$(),s.Y8G("ngIf",!e.isSSR&&e.barVisible&&"standard"===e.type),s.R7$(),s.Y8G("ngIf",e.isSSR&&e.barVisible&&"standard"===e.type),s.R7$(),s.AVh("active",e.isActive(s.eq3(20,eu,e.circle.seriesName))),s.Y8G("cx",e.circle.cx)("cy",e.circle.cy)("r",e.circle.radius)("fill",e.circle.color)("pointerEvents",0===e.circle.value?"none":"all")("data",e.circle.value)("classNames",e.circle.classNames)("tooltipDisabled",e.tooltipDisabled)("tooltipPlacement",e.placementTypes.Top)("tooltipType",e.styleTypes.tooltip)("tooltipTitle",e.tooltipTemplate?void 0:e.getTooltipText(e.circle))("tooltipTemplate",e.tooltipTemplate)("tooltipContext",e.circle.data)}}const au=["ngx-charts-grid-panel",""],su=["ngx-charts-grid-panel-series",""];function ru(t,n){if(1&t&&(s.qSk(),s.nrm(0,"g",1)),2&t){const e=n.$implicit;s.AVh("grid-panel",!0)("odd","odd"===e.class)("even","even"===e.class),s.Y8G("height",e.height)("width",e.width)("x",e.x)("y",e.y)}}const cu=["ngx-charts-area",""];function hu(t,n){if(1&t&&(s.qSk(),s.j41(0,"defs"),s.nrm(1,"g",2),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("orientation",e.barOrientation.Vertical)("name",e.gradientId)("stops",e.gradientStops)}}const mu=["tooltipAnchor"],uu=["ngx-charts-tooltip-area",""];function gu(t,n){if(1&t&&(s.j41(0,"div",6),s.nrm(1,"span",7),s.EFF(2),s.k0s()),2&t){const e=n.$implicit,i=s.XpG(2);s.R7$(),s.xc7("background-color",e.color),s.R7$(),s.SpI(" ",i.getToolTipText(e)," ")}}function pu(t,n){if(1&t&&(s.j41(0,"div",4),s.DNE(1,gu,3,3,"div",5),s.k0s()),2&t){const e=n.model;s.R7$(),s.Y8G("ngForOf",e)}}const fu=["ngx-charts-timeline",""],Hs=["tooltipTemplate"],wu=["seriesTooltipTemplate"],vt=(t,n)=>[t,n],Xs=".ngx-charts-outer{animation:chartFadeIn linear .6s}@keyframes chartFadeIn{0%{opacity:0}20%{opacity:0}to{opacity:1}}.ngx-charts{float:left;overflow:visible}.ngx-charts .circle,.ngx-charts .cell,.ngx-charts .bar,.ngx-charts .node,.ngx-charts .link,.ngx-charts .arc{cursor:pointer}.ngx-charts .bar.active,.ngx-charts .bar:hover,.ngx-charts .cell.active,.ngx-charts .cell:hover,.ngx-charts .arc.active,.ngx-charts .arc:hover,.ngx-charts .node.active,.ngx-charts .node:hover,.ngx-charts .link.active,.ngx-charts .link:hover,.ngx-charts .card.active,.ngx-charts .card:hover{opacity:.8;transition:opacity .1s ease-in-out}.ngx-charts .bar:focus,.ngx-charts .cell:focus,.ngx-charts .arc:focus,.ngx-charts .node:focus,.ngx-charts .link:focus,.ngx-charts .card:focus{outline:none}.ngx-charts .bar.hidden,.ngx-charts .cell.hidden,.ngx-charts .arc.hidden,.ngx-charts .node.hidden,.ngx-charts .link.hidden,.ngx-charts .card.hidden{display:none}.ngx-charts g:focus{outline:none}.ngx-charts .line-series.inactive,.ngx-charts .line-series-range.inactive,.ngx-charts .polar-series-path.inactive,.ngx-charts .polar-series-area.inactive,.ngx-charts .area-series.inactive{transition:opacity .1s ease-in-out;opacity:.2}.ngx-charts .line-highlight{display:none}.ngx-charts .line-highlight.active{display:block}.ngx-charts .area{opacity:.6}.ngx-charts .circle:hover{cursor:pointer}.ngx-charts .label{font-size:12px;font-weight:400}.ngx-charts .tooltip-anchor{fill:#000}.ngx-charts .gridline-path{stroke:#ddd;stroke-width:1;fill:none}.ngx-charts .refline-path{stroke:#a8b2c7;stroke-width:1;stroke-dasharray:5;stroke-dashoffset:5}.ngx-charts .refline-label{font-size:9px}.ngx-charts .reference-area{fill-opacity:.05;fill:#000}.ngx-charts .gridline-path-dotted{stroke:#ddd;stroke-width:1;fill:none;stroke-dasharray:1,20;stroke-dashoffset:3}.ngx-charts .grid-panel rect{fill:none}.ngx-charts .grid-panel.odd rect{fill:#0000000d}\n",Eu=["ngx-charts-bar",""];function Mu(t,n){if(1&t&&(s.qSk(),s.j41(0,"defs"),s.nrm(1,"g",2),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("orientation",e.orientation)("name",e.gradientId)("stops",e.gradientStops)}}const Bu=["ngx-charts-bar-label",""],Hu=["ngx-charts-series-vertical",""];function Xu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",2),s.bIt("select",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.onClick(a))})("activate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.activate.emit(a))})("deactivate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.deactivate.emit(a))}),s.k0s()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("@animationState","active")("@.disabled",!i.animations)("width",e.width)("height",e.height)("x",e.x)("y",e.y)("fill",e.color)("stops",e.gradientStops)("data",e.data)("orientation",i.barOrientation.Vertical)("roundEdges",e.roundEdges)("gradient",i.gradient)("ariaLabel",e.ariaLabel)("isActive",i.isActive(e.data))("tooltipDisabled",i.tooltipDisabled)("tooltipPlacement",i.tooltipPlacement)("tooltipType",i.tooltipType)("tooltipTitle",i.tooltipTemplate?void 0:e.tooltipText)("tooltipTemplate",i.tooltipTemplate)("tooltipContext",e.data)("noBarWhenZero",i.noBarWhenZero)("animations",i.animations)}}function Yu(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Xu,1,22,"g",1),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.bars)("ngForTrackBy",e.trackBy)}}function Nu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",2),s.bIt("select",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.onClick(a))})("activate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.activate.emit(a))})("deactivate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.deactivate.emit(a))}),s.k0s()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("width",e.width)("height",e.height)("x",e.x)("y",e.y)("fill",e.color)("stops",e.gradientStops)("data",e.data)("orientation",i.barOrientation.Vertical)("roundEdges",e.roundEdges)("gradient",i.gradient)("ariaLabel",e.ariaLabel)("isActive",i.isActive(e.data))("tooltipDisabled",i.tooltipDisabled)("tooltipPlacement",i.tooltipPlacement)("tooltipType",i.tooltipType)("tooltipTitle",i.tooltipTemplate?void 0:e.tooltipText)("tooltipTemplate",i.tooltipTemplate)("tooltipContext",e.data)("noBarWhenZero",i.noBarWhenZero)("animations",i.animations)}}function zu(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Nu,1,20,"g",1),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.bars)("ngForTrackBy",e.trackBy)}}function Wu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",4),s.bIt("dimensionsChanged",function(a){const r=s.eBV(e).index,o=s.XpG(2);return s.Njj(o.dataLabelHeightChanged.emit({size:a,index:r}))}),s.k0s()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("barX",e.x)("barY",e.y)("barWidth",e.width)("barHeight",e.height)("value",e.total)("valueFormatting",i.dataLabelFormatting)("orientation",i.barOrientation.Vertical)}}function ju(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Wu,1,7,"g",3),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.barsForDataLabels)("ngForTrackBy",e.trackDataLabelBy)}}function Uu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",6),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateXAxisHeight(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("xScale",e.groupScale)("dims",e.dims)("showLabel",e.showXAxisLabel)("labelText",e.xAxisLabel)("trimTicks",e.trimXAxisTicks)("rotateTicks",e.rotateXAxisTicks)("maxTickLength",e.maxXAxisTickLength)("tickFormatting",e.xAxisTickFormatting)("ticks",e.xAxisTicks)("xAxisOffset",e.dataLabelMaxHeight.negative)("wrapTicks",e.wrapTicks)}}function Qu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",7),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateYAxisWidth(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("yScale",e.valueScale)("dims",e.dims)("showGridLines",e.showGridLines)("showLabel",e.showYAxisLabel)("labelText",e.yAxisLabel)("trimTicks",e.trimYAxisTicks)("maxTickLength",e.maxYAxisTickLength)("tickFormatting",e.yAxisTickFormatting)("ticks",e.yAxisTicks)("wrapTicks",e.wrapTicks)}}function qu(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",9),s.bIt("select",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onClick(a,r))})("activate",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onActivate(a,r))})("deactivate",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onDeactivate(a,r))})("dataLabelHeightChanged",function(a){const r=s.eBV(e).index,o=s.XpG(2);return s.Njj(o.onDataLabelMaxHeightChanged(a,r))}),s.k0s()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("@animationState","active")("activeEntries",i.activeEntries)("xScale",i.innerScale)("yScale",i.valueScale)("colors",i.colors)("series",e.series)("dims",i.dims)("gradient",i.gradient)("tooltipDisabled",i.tooltipDisabled)("tooltipTemplate",i.tooltipTemplate)("showDataLabel",i.showDataLabel)("dataLabelFormatting",i.dataLabelFormatting)("seriesName",e.name)("roundEdges",i.roundEdges)("animations",i.animations)("noBarWhenZero",i.noBarWhenZero),s.BMQ("transform",i.groupTransform(e))}}function Zu(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,qu,1,17,"g",8),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.results)("ngForTrackBy",e.trackBy)}}function Ku(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",9),s.bIt("select",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onClick(a,r))})("activate",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onActivate(a,r))})("deactivate",function(a){const r=s.eBV(e).$implicit,o=s.XpG(2);return s.Njj(o.onDeactivate(a,r))})("dataLabelHeightChanged",function(a){const r=s.eBV(e).index,o=s.XpG(2);return s.Njj(o.onDataLabelMaxHeightChanged(a,r))}),s.k0s()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("activeEntries",i.activeEntries)("xScale",i.innerScale)("yScale",i.valueScale)("colors",i.colors)("series",e.series)("dims",i.dims)("gradient",i.gradient)("tooltipDisabled",i.tooltipDisabled)("tooltipTemplate",i.tooltipTemplate)("showDataLabel",i.showDataLabel)("dataLabelFormatting",i.dataLabelFormatting)("seriesName",e.name)("roundEdges",i.roundEdges)("animations",i.animations)("noBarWhenZero",i.noBarWhenZero),s.BMQ("transform",i.groupTransform(e))}}function Ju(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Ku,1,16,"g",8),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.results)("ngForTrackBy",e.trackBy)}}const cg=["ngx-charts-line",""];function hg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"path",1),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("@animationState","active"),s.BMQ("d",e.initialPath)("fill",e.fill)("stroke",e.stroke)}}function dg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"path",1),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.BMQ("d",e.initialPath)("fill",e.fill)("stroke",e.stroke)}}const mg=["ngx-charts-line-series",""];function ug(t,n){if(1&t&&(s.qSk(),s.nrm(0,"g",4)),2&t){const e=s.XpG();s.Y8G("orientation",e.barOrientation.Vertical)("name",e.gradientId)("stops",e.gradientStops)}}function gg(t,n){if(1&t&&(s.qSk(),s.nrm(0,"g",5)),2&t){const e=s.XpG();s.AVh("active",e.isActive(e.data))("inactive",e.isInactive(e.data)),s.Y8G("data",e.data)("path",e.outerPath)("fill",e.hasGradient?e.gradientUrl:e.colors.getColor(e.data.name))("opacity",e.rangeFillOpacity)("animations",e.animations)}}function pg(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",7),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateXAxisHeight(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("xScale",e.xScale)("dims",e.dims)("showGridLines",e.showGridLines)("showLabel",e.showXAxisLabel)("labelText",e.xAxisLabel)("trimTicks",e.trimXAxisTicks)("rotateTicks",e.rotateXAxisTicks)("maxTickLength",e.maxXAxisTickLength)("tickFormatting",e.xAxisTickFormatting)("ticks",e.xAxisTicks)("wrapTicks",e.wrapTicks)}}function fg(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",8),s.bIt("dimensionsChanged",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateYAxisWidth(a))}),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("yScale",e.yScale)("dims",e.dims)("showGridLines",e.showGridLines)("showLabel",e.showYAxisLabel)("labelText",e.yAxisLabel)("trimTicks",e.trimYAxisTicks)("maxTickLength",e.maxYAxisTickLength)("tickFormatting",e.yAxisTickFormatting)("ticks",e.yAxisTicks)("referenceLines",e.referenceLines)("showRefLines",e.showRefLines)("showRefLabels",e.showRefLabels)("wrapTicks",e.wrapTicks)}}function xg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"g",10),s.k0s()),2&t){const e=n.$implicit,i=s.XpG(2);s.Y8G("@animationState","active"),s.R7$(),s.Y8G("xScale",i.xScale)("yScale",i.yScale)("colors",i.colors)("data",e)("activeEntries",i.activeEntries)("scaleType",i.scaleType)("curve",i.curve)("rangeFillOpacity",i.rangeFillOpacity)("hasRange",i.hasRange)("animations",i.animations)}}function _g(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,xg,2,11,"g",9),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.results)("ngForTrackBy",e.trackBy)}}function vg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"g",10),s.k0s()),2&t){const e=n.$implicit,i=s.XpG(2);s.R7$(),s.Y8G("xScale",i.xScale)("yScale",i.yScale)("colors",i.colors)("data",e)("activeEntries",i.activeEntries)("scaleType",i.scaleType)("curve",i.curve)("rangeFillOpacity",i.rangeFillOpacity)("hasRange",i.hasRange)("animations",i.animations)}}function yg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.DNE(1,vg,2,10,"g",9),s.k0s()),2&t){const e=s.XpG();s.R7$(),s.Y8G("ngForOf",e.results)("ngForTrackBy",e.trackBy)}}function Tg(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g")(1,"g",14),s.bIt("select",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.onClick(a))})("activate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.onActivate(a))})("deactivate",function(a){s.eBV(e);const r=s.XpG(2);return s.Njj(r.onDeactivate(a))}),s.k0s()()}if(2&t){const e=n.$implicit,i=s.XpG(2);s.R7$(),s.Y8G("xScale",i.xScale)("yScale",i.yScale)("colors",i.colors)("data",e)("scaleType",i.scaleType)("visibleValue",i.hoveredVertical)("activeEntries",i.activeEntries)("tooltipDisabled",i.tooltipDisabled)("tooltipTemplate",i.tooltipTemplate)}}function Cg(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",11),s.bIt("mouseleave",function(){s.eBV(e);const a=s.XpG();return s.Njj(a.hideCircles())}),s.j41(1,"g",12),s.bIt("hover",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateHoveredVertical(a))}),s.k0s(),s.DNE(2,Tg,2,9,"g",13),s.k0s()}if(2&t){const e=s.XpG();s.R7$(),s.Y8G("dims",e.dims)("xSet",e.xSet)("xScale",e.xScale)("yScale",e.yScale)("results",e.results)("colors",e.colors)("tooltipDisabled",e.tooltipDisabled)("tooltipTemplate",e.seriesTooltipTemplate),s.R7$(),s.Y8G("ngForOf",e.results)}}function bg(t,n){if(1&t&&(s.qSk(),s.j41(0,"g"),s.nrm(1,"g",16),s.k0s()),2&t){const e=n.$implicit,i=s.XpG(2);s.R7$(),s.Y8G("xScale",i.timelineXScale)("yScale",i.timelineYScale)("colors",i.colors)("data",e)("scaleType",i.scaleType)("curve",i.curve)("hasRange",i.hasRange)("animations",i.animations)}}function wg(t,n){if(1&t){const e=s.RV6();s.qSk(),s.j41(0,"g",15),s.bIt("onDomainChange",function(a){s.eBV(e);const r=s.XpG();return s.Njj(r.updateDomain(a))}),s.DNE(1,bg,2,8,"g",9),s.k0s()}if(2&t){const e=s.XpG();s.Y8G("results",e.results)("view",s.l_i(10,vt,e.timelineWidth,e.height))("height",e.timelineHeight)("scheme",e.scheme)("customColors",e.customColors)("scaleType",e.scaleType)("legend",e.legend),s.BMQ("transform",e.timelineTransform),s.R7$(),s.Y8G("ngForOf",e.results)("ngForTrackBy",e.trackBy)}}function Gg(t,n,e){e=e||{};let i,a,r,o=null,l=0;function h(){l=!1===e.leading?0:+new Date,o=null,r=t.apply(i,a)}return function(){const c=+new Date;!l&&!1===e.leading&&(l=c);const m=n-(c-l);return i=this,a=arguments,m<=0?(clearTimeout(o),o=null,l=c,r=t.apply(i,a)):!o&&!1!==e.trailing&&(o=setTimeout(h,m)),r}}function $g(t,n){return function(i,a,r){return{configurable:!0,enumerable:r.enumerable,get:function(){return Object.defineProperty(this,a,{configurable:!0,enumerable:r.enumerable,value:Gg(r.value,t,n)}),this[a]}}}}var R=function(t){return t.Top="top",t.Bottom="bottom",t.Left="left",t.Right="right",t.Center="center",t}(R||{});function Ys(t,n,e){return e===R.Top?t.top-7:e===R.Bottom?t.top+t.height-n.height+7:e===R.Center?t.top+t.height/2-n.height/2:void 0}function Ns(t,n,e){return e===R.Left?t.left-7:e===R.Right?t.left+t.width-n.width+7:e===R.Center?t.left+t.width/2-n.width/2:void 0}class ke{static calculateVerticalAlignment(n,e,i){let a=Ys(n,e,i);return a+e.height>window.innerHeight&&(a=window.innerHeight-e.height),a}static calculateVerticalCaret(n,e,i,a){let r;a===R.Top&&(r=n.height/2-i.height/2+7),a===R.Bottom&&(r=e.height-n.height/2-i.height/2-7),a===R.Center&&(r=e.height/2-i.height/2);const o=Ys(n,e,a);return o+e.height>window.innerHeight&&(r+=o+e.height-window.innerHeight),r}static calculateHorizontalAlignment(n,e,i){let a=Ns(n,e,i);return a+e.width>window.innerWidth&&(a=window.innerWidth-e.width),a}static calculateHorizontalCaret(n,e,i,a){let r;a===R.Left&&(r=n.width/2-i.width/2+7),a===R.Right&&(r=e.width-n.width/2-i.width/2-7),a===R.Center&&(r=e.width/2-i.width/2);const o=Ns(n,e,a);return o+e.width>window.innerWidth&&(r+=o+e.width-window.innerWidth),r}static shouldFlip(n,e,i,a){let r=!1;return i===R.Right&&n.left+n.width+e.width+a>window.innerWidth&&(r=!0),i===R.Left&&n.left-e.width-a<0&&(r=!0),i===R.Top&&n.top-e.height-a<0&&(r=!0),i===R.Bottom&&n.top+n.height+e.height+a>window.innerHeight&&(r=!0),r}static positionCaret(n,e,i,a,r){let o=0,l=0;return n===R.Right?(l=-7,o=ke.calculateVerticalCaret(i,e,a,r)):n===R.Left?(l=e.width,o=ke.calculateVerticalCaret(i,e,a,r)):n===R.Top?(o=e.height,l=ke.calculateHorizontalCaret(i,e,a,r)):n===R.Bottom&&(o=-7,l=ke.calculateHorizontalCaret(i,e,a,r)),{top:o,left:l}}static positionContent(n,e,i,a,r){let o=0,l=0;return n===R.Right?(l=i.left+i.width+a,o=ke.calculateVerticalAlignment(i,e,r)):n===R.Left?(l=i.left-e.width-a,o=ke.calculateVerticalAlignment(i,e,r)):n===R.Top?(o=i.top-e.height-a,l=ke.calculateHorizontalAlignment(i,e,r)):n===R.Bottom&&(o=i.top+i.height+a,l=ke.calculateHorizontalAlignment(i,e,r)),{top:o,left:l}}static determinePlacement(n,e,i,a){if(ke.shouldFlip(i,e,n,a)){if(n===R.Right)return R.Left;if(n===R.Left)return R.Right;if(n===R.Top)return R.Bottom;if(n===R.Bottom)return R.Top}return n}}let Rg=(()=>{class t{constructor(e,i,a){this.element=e,this.renderer=i,this.platformId=a}get cssClasses(){let e="ngx-charts-tooltip-content";return e+=` position-${this.placement}`,e+=` type-${this.type}`,e+=` ${this.cssClass}`,e}ngAfterViewInit(){setTimeout(this.position.bind(this))}position(){if(!(0,y.UE)(this.platformId))return;const e=this.element.nativeElement,i=this.host.nativeElement.getBoundingClientRect();if(!i.height&&!i.width)return;const a=e.getBoundingClientRect();this.checkFlip(i,a),this.positionContent(e,i,a),this.showCaret&&this.positionCaret(i,a),setTimeout(()=>this.renderer.addClass(e,"animate"),1)}positionContent(e,i,a){const{top:r,left:o}=ke.positionContent(this.placement,a,i,this.spacing,this.alignment);this.renderer.setStyle(e,"top",`${r}px`),this.renderer.setStyle(e,"left",`${o}px`)}positionCaret(e,i){const a=this.caretElm.nativeElement,r=a.getBoundingClientRect(),{top:o,left:l}=ke.positionCaret(this.placement,i,e,r,this.alignment);this.renderer.setStyle(a,"top",`${o}px`),this.renderer.setStyle(a,"left",`${l}px`)}checkFlip(e,i){this.placement=ke.determinePlacement(this.placement,i,e,this.spacing)}onWindowResize(){this.position()}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT),s.rXU(s.sFG),s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-tooltip-content"]],viewQuery:function(e,i){if(1&e&&s.GBs(om,5),2&e){let a;s.mGM(a=s.lsd())&&(i.caretElm=a.first)}},hostVars:2,hostBindings:function(e,i){1&e&&s.bIt("resize",function(){return i.onWindowResize()},!1,s.tSv),2&e&&s.HbH(i.cssClasses)},inputs:{host:"host",showCaret:"showCaret",type:"type",placement:"placement",alignment:"alignment",spacing:"spacing",cssClass:"cssClass",title:"title",template:"template",context:"context"},decls:6,vars:6,consts:[["caretElm",""],[3,"hidden"],[1,"tooltip-content"],[4,"ngIf"],[3,"innerHTML",4,"ngIf"],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"innerHTML"]],template:function(e,i){1&e&&(s.j41(0,"div"),s.nrm(1,"span",1,0),s.j41(3,"div",2),s.DNE(4,hm,2,4,"span",3)(5,dm,1,1,"span",4),s.k0s()()),2&e&&(s.R7$(),s.ZvI("tooltip-caret position-",i.placement,""),s.Y8G("hidden",!i.showCaret),s.R7$(3),s.Y8G("ngIf",!i.title),s.R7$(),s.Y8G("ngIf",i.title))},dependencies:[y.bT,y.T3],styles:[".ngx-charts-tooltip-content{position:fixed;border-radius:3px;z-index:5000;display:block;font-weight:400;opacity:0;pointer-events:none!important}.ngx-charts-tooltip-content.type-popover{background:#fff;color:#060709;border:1px solid #72809b;box-shadow:0 1px 3px #0003,0 1px 1px #00000024,0 2px 1px -1px #0000001f;font-size:13px;padding:4px}.ngx-charts-tooltip-content.type-popover .tooltip-caret{position:absolute;z-index:5001;width:0;height:0}.ngx-charts-tooltip-content.type-popover .tooltip-caret.position-left{border-top:7px solid transparent;border-bottom:7px solid transparent;border-left:7px solid #fff}.ngx-charts-tooltip-content.type-popover .tooltip-caret.position-top{border-left:7px solid transparent;border-right:7px solid transparent;border-top:7px solid #fff}.ngx-charts-tooltip-content.type-popover .tooltip-caret.position-right{border-top:7px solid transparent;border-bottom:7px solid transparent;border-right:7px solid #fff}.ngx-charts-tooltip-content.type-popover .tooltip-caret.position-bottom{border-left:7px solid transparent;border-right:7px solid transparent;border-bottom:7px solid #fff}.ngx-charts-tooltip-content.type-tooltip{color:#fff;background:rgba(0,0,0,.75);font-size:12px;padding:0 10px;text-align:center;pointer-events:auto}.ngx-charts-tooltip-content.type-tooltip .tooltip-caret.position-left{border-top:7px solid transparent;border-bottom:7px solid transparent;border-left:7px solid rgba(0,0,0,.75)}.ngx-charts-tooltip-content.type-tooltip .tooltip-caret.position-top{border-left:7px solid transparent;border-right:7px solid transparent;border-top:7px solid rgba(0,0,0,.75)}.ngx-charts-tooltip-content.type-tooltip .tooltip-caret.position-right{border-top:7px solid transparent;border-bottom:7px solid transparent;border-right:7px solid rgba(0,0,0,.75)}.ngx-charts-tooltip-content.type-tooltip .tooltip-caret.position-bottom{border-left:7px solid transparent;border-right:7px solid transparent;border-bottom:7px solid rgba(0,0,0,.75)}.ngx-charts-tooltip-content .tooltip-label{display:block;line-height:1em;padding:8px 5px 5px;font-size:1em}.ngx-charts-tooltip-content .tooltip-val{display:block;font-size:1.3em;line-height:1em;padding:0 5px 8px}.ngx-charts-tooltip-content .tooltip-caret{position:absolute;z-index:5001;width:0;height:0}.ngx-charts-tooltip-content.position-right{transform:translate(10px)}.ngx-charts-tooltip-content.position-left{transform:translate(-10px)}.ngx-charts-tooltip-content.position-top{transform:translateY(-10px)}.ngx-charts-tooltip-content.position-bottom{transform:translateY(10px)}.ngx-charts-tooltip-content.animate{opacity:1;transition:opacity .3s,transform .3s;transform:translate(0);pointer-events:auto}.area-tooltip-container{padding:5px 0;pointer-events:none}.tooltip-item{text-align:left;line-height:1.2em;padding:5px 0}.tooltip-item .tooltip-item-color{display:inline-block;height:12px;width:12px;margin-right:5px;color:#5b646b;border-radius:3px}\n"],encapsulation:2}),(0,I.Cg)([$g(100)],t.prototype,"onWindowResize",null),t})();class Hg{constructor(n){this.injectionService=n,this.defaults={},this.components=new Map}getByType(n=this.type){return this.components.get(n)}create(n){return this.createByType(this.type,n)}createByType(n,e){e=this.assignDefaults(e);const i=this.injectComponent(n,e);return this.register(n,i),i}destroy(n){const e=this.components.get(n.componentType);if(e&&e.length){const i=e.indexOf(n);i>-1&&(e[i].destroy(),e.splice(i,1))}}destroyAll(){this.destroyByType(this.type)}destroyByType(n){const e=this.components.get(n);if(e&&e.length){let i=e.length-1;for(;i>=0;)this.destroy(e[i--])}}injectComponent(n,e){return this.injectionService.appendComponent(n,e)}assignDefaults(n){const e={...this.defaults.inputs},i={...this.defaults.outputs};return!n.inputs&&!n.outputs&&(n={inputs:n}),e&&(n.inputs={...e,...n.inputs}),i&&(n.outputs={...i,...n.outputs}),n}register(n,e){this.components.has(n)||this.components.set(n,[]),this.components.get(n).push(e)}}let zs=(()=>{class t{constructor(e,i,a){this.applicationRef=e,this.componentFactoryResolver=i,this.injector=a}static setGlobalRootViewContainer(e){t.globalRootViewContainer=e}getRootViewContainer(){if(this._container)return this._container;if(t.globalRootViewContainer)return t.globalRootViewContainer;if(this.applicationRef.components.length)return this.applicationRef.components[0];throw new Error("View Container not found! ngUpgrade needs to manually set this via setRootViewContainer or setGlobalRootViewContainer.")}setRootViewContainer(e){this._container=e}getComponentRootNode(e){return function Xg(t){return t.element}(e)?e.element.nativeElement:e.hostView&&e.hostView.rootNodes.length>0?e.hostView.rootNodes[0]:e.location.nativeElement}getRootViewContainerNode(e){return this.getComponentRootNode(e)}projectComponentBindings(e,i){if(i){if(void 0!==i.inputs){const a=Object.getOwnPropertyNames(i.inputs);for(const r of a)e.instance[r]=i.inputs[r]}if(void 0!==i.outputs){const a=Object.getOwnPropertyNames(i.outputs);for(const r of a)e.instance[r]=i.outputs[r]}}return e}appendComponent(e,i={},a){a||(a=this.getRootViewContainer());const r=this.getComponentRootNode(a),o=new A.aI(r,this.componentFactoryResolver,this.applicationRef,this.injector),l=new A.A8(e),h=o.attach(l);return this.projectComponentBindings(h,i),h}}return t.globalRootViewContainer=null,t.\u0275fac=function(e){return new(e||t)(s.KVO(s.o8S),s.KVO(s.OM3),s.KVO(s.zZn))},t.\u0275prov=s.jDH({token:t,factory:t.\u0275fac}),t})(),bn=(()=>{class t extends Hg{constructor(e){super(e),this.type=Rg}}return t.\u0275fac=function(e){return new(e||t)(s.KVO(zs))},t.\u0275prov=s.jDH({token:t,factory:t.\u0275fac}),t})();var Ye=function(t){return t.Right="right",t.Below="below",t}(Ye||{}),Yt=function(t){return t.ScaleLegend="scaleLegend",t.Legend="legend",t}(Yt||{}),w=function(t){return t.Time="time",t.Linear="linear",t.Ordinal="ordinal",t.Quantile="quantile",t}(w||{});let Ws=(()=>{class t{constructor(){this.horizontal=!1}ngOnChanges(e){const i=this.gradientString(this.colors.range(),this.colors.domain());this.gradient=`linear-gradient(to ${this.horizontal?"right":"bottom"}, ${i})`}gradientString(e,i){i.push(1);const a=[];return e.reverse().forEach((r,o)=>{a.push(`${r} ${Math.round(100*i[o])}%`)}),a.join(", ")}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-scale-legend"]],inputs:{valueRange:"valueRange",colors:"colors",height:"height",width:"width",horizontal:"horizontal"},features:[s.OA$],decls:8,vars:10,consts:[[1,"scale-legend"],[1,"scale-legend-label"],[1,"scale-legend-wrap"]],template:function(e,i){1&e&&(s.j41(0,"div",0)(1,"div",1)(2,"span"),s.EFF(3),s.k0s()(),s.nrm(4,"div",2),s.j41(5,"div",1)(6,"span"),s.EFF(7),s.k0s()()()),2&e&&(s.xc7("height",i.horizontal?void 0:i.height,"px")("width",i.width,"px"),s.AVh("horizontal-legend",i.horizontal),s.R7$(3),s.JRh(i.valueRange[1].toLocaleString()),s.R7$(),s.xc7("background",i.gradient),s.R7$(3),s.JRh(i.valueRange[0].toLocaleString()))},styles:[".chart-legend{display:inline-block;padding:0;width:auto!important}.chart-legend .scale-legend{text-align:center;display:flex;flex-direction:column}.chart-legend .scale-legend-wrap{display:inline-block;flex:1;width:30px;border-radius:5px;margin:0 auto}.chart-legend .scale-legend-label{font-size:12px}.chart-legend .horizontal-legend.scale-legend{flex-direction:row}.chart-legend .horizontal-legend .scale-legend-wrap{width:auto;height:30px;margin:0 16px}\n"],encapsulation:2,changeDetection:0}),t})();function Nt(t){return t instanceof Date?t.toLocaleDateString():t.toLocaleString()}function wn(t){return t.toLocaleString().replace(/[&'`"<>]/g,n=>({"&":"&amp;","'":"&#x27;","`":"&#x60;",'"':"&quot;","<":"&lt;",">":"&gt;"}[n]))}let js=(()=>{class t{constructor(){this.isActive=!1,this.select=new s.bkB,this.activate=new s.bkB,this.deactivate=new s.bkB,this.toggle=new s.bkB}get trimmedLabel(){return this.formattedLabel||"(empty)"}onMouseEnter(){this.activate.emit({name:this.label})}onMouseLeave(){this.deactivate.emit({name:this.label})}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-legend-entry"]],hostBindings:function(e,i){1&e&&s.bIt("mouseenter",function(){return i.onMouseEnter()})("mouseleave",function(){return i.onMouseLeave()})},inputs:{color:"color",label:"label",formattedLabel:"formattedLabel",isActive:"isActive"},outputs:{select:"select",activate:"activate",deactivate:"deactivate",toggle:"toggle"},decls:4,vars:6,consts:[["tabindex","-1",3,"click","title"],[1,"legend-label-color",3,"click"],[1,"legend-label-text"]],template:function(e,i){1&e&&(s.j41(0,"span",0),s.bIt("click",function(){return i.select.emit(i.formattedLabel)}),s.j41(1,"span",1),s.bIt("click",function(){return i.toggle.emit(i.formattedLabel)}),s.k0s(),s.j41(2,"span",2),s.EFF(3),s.k0s()()),2&e&&(s.AVh("active",i.isActive),s.Y8G("title",i.formattedLabel),s.R7$(),s.xc7("background-color",i.color),s.R7$(2),s.SpI(" ",i.trimmedLabel," "))},encapsulation:2,changeDetection:0}),t})(),Us=(()=>{class t{constructor(e){this.cd=e,this.horizontal=!1,this.labelClick=new s.bkB,this.labelActivate=new s.bkB,this.labelDeactivate=new s.bkB,this.legendEntries=[]}ngOnChanges(e){this.update()}update(){this.cd.markForCheck(),this.legendEntries=this.getLegendEntries()}getLegendEntries(){const e=[];for(const i of this.data){const a=Nt(i);-1===e.findIndex(o=>o.label===a)&&e.push({label:i,formattedLabel:a,color:this.colors.getColor(i)})}return e}isActive(e){return!!this.activeEntries&&void 0!==this.activeEntries.find(a=>e.label===a.name)}activate(e){this.labelActivate.emit(e)}deactivate(e){this.labelDeactivate.emit(e)}trackBy(e,i){return i.label}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.gRc))},t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-legend"]],inputs:{data:"data",title:"title",colors:"colors",height:"height",width:"width",activeEntries:"activeEntries",horizontal:"horizontal"},outputs:{labelClick:"labelClick",labelActivate:"labelActivate",labelDeactivate:"labelDeactivate"},features:[s.OA$],decls:5,vars:9,consts:[["class","legend-title",4,"ngIf"],[1,"legend-wrap"],[1,"legend-labels"],["class","legend-label",4,"ngFor","ngForOf","ngForTrackBy"],[1,"legend-title"],[1,"legend-title-text"],[1,"legend-label"],[3,"select","activate","deactivate","label","formattedLabel","color","isActive"]],template:function(e,i){1&e&&(s.j41(0,"div"),s.DNE(1,mm,3,1,"header",0),s.j41(2,"div",1)(3,"ul",2),s.DNE(4,um,2,4,"li",3),s.k0s()()()),2&e&&(s.xc7("width",i.width,"px"),s.R7$(),s.Y8G("ngIf",(null==i.title?null:i.title.length)>0),s.R7$(2),s.xc7("max-height",i.height-45,"px"),s.AVh("horizontal-legend",i.horizontal),s.R7$(),s.Y8G("ngForOf",i.legendEntries)("ngForTrackBy",i.trackBy))},dependencies:[js,y.bT,y.Sq],styles:[".chart-legend{display:inline-block;padding:0;width:auto!important}.chart-legend .legend-title{white-space:nowrap;overflow:hidden;margin-left:10px;margin-bottom:5px;font-size:14px;font-weight:700}.chart-legend ul,.chart-legend li{padding:0;margin:0;list-style:none}.chart-legend .horizontal-legend li{display:inline-block}.chart-legend .legend-wrap{width:calc(100% - 10px)}.chart-legend .legend-labels{line-height:85%;list-style:none;text-align:left;float:left;width:100%;border-radius:3px;overflow-y:auto;overflow-x:hidden;white-space:nowrap;background:rgba(0,0,0,.05)}.chart-legend .legend-label{cursor:pointer;font-size:90%;margin:8px;color:#afb7c8}.chart-legend .legend-label:hover{color:#000;transition:.2s}.chart-legend .legend-label .active .legend-label-text{color:#000}.chart-legend .legend-label-color{display:inline-block;height:15px;width:15px;margin-right:5px;color:#5b646b;border-radius:3px}.chart-legend .legend-label-text{display:inline-block;vertical-align:top;line-height:15px;font-size:12px;width:calc(100% - 20px);text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.chart-legend .legend-title-text{vertical-align:bottom;display:inline-block;line-height:16px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}\n"],encapsulation:2,changeDetection:0}),t})(),Sn=(()=>{class t{constructor(){this.showLegend=!1,this.animations=!0,this.legendLabelClick=new s.bkB,this.legendLabelActivate=new s.bkB,this.legendLabelDeactivate=new s.bkB,this.LegendPosition=Ye,this.LegendType=Yt}ngOnChanges(e){this.update()}update(){let e=0;this.showLegend&&(this.legendType=this.getLegendType(),(!this.legendOptions||this.legendOptions.position===Ye.Right)&&(e=this.legendType===Yt.ScaleLegend?1:2)),this.chartWidth=Math.floor(this.view[0]*(12-e)/12),this.legendWidth=this.legendOptions&&this.legendOptions.position!==Ye.Right?this.chartWidth:Math.floor(this.view[0]*e/12)}getLegendType(){return this.legendOptions.scaleType===w.Linear?Yt.ScaleLegend:Yt.Legend}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-chart"]],inputs:{view:"view",showLegend:"showLegend",legendOptions:"legendOptions",legendType:"legendType",activeEntries:"activeEntries",animations:"animations"},outputs:{legendLabelClick:"legendLabelClick",legendLabelActivate:"legendLabelActivate",legendLabelDeactivate:"legendLabelDeactivate"},features:[s.Jv_([bn]),s.OA$],ngContentSelectors:$s,decls:5,vars:8,consts:[[1,"ngx-charts-outer"],[1,"ngx-charts"],["class","chart-legend",3,"horizontal","valueRange","colors","height","width",4,"ngIf"],["class","chart-legend",3,"horizontal","data","title","colors","height","width","activeEntries","labelClick","labelActivate","labelDeactivate",4,"ngIf"],[1,"chart-legend",3,"horizontal","valueRange","colors","height","width"],[1,"chart-legend",3,"labelClick","labelActivate","labelDeactivate","horizontal","data","title","colors","height","width","activeEntries"]],template:function(e,i){1&e&&(s.NAR(),s.j41(0,"div",0),s.qSk(),s.j41(1,"svg",1),s.SdG(2),s.k0s(),s.DNE(3,gm,1,5,"ngx-charts-scale-legend",2)(4,pm,1,7,"ngx-charts-legend",3),s.k0s()),2&e&&(s.xc7("width",i.view[0],"px")("height",i.view[1],"px"),s.R7$(),s.BMQ("width",i.chartWidth)("height",i.view[1]),s.R7$(2),s.Y8G("ngIf",i.showLegend&&i.legendType===i.LegendType.ScaleLegend),s.R7$(),s.Y8G("ngIf",i.showLegend&&i.legendType===i.LegendType.Legend))},dependencies:[Ws,Us,y.bT],encapsulation:2,changeDetection:0}),t})(),Yg=(()=>{class t{constructor(e,i){this.element=e,this.zone=i,this.visible=new s.bkB,this.isVisible=!1,this.runCheck()}destroy(){clearTimeout(this.timeout)}onVisibilityChange(){this.zone.run(()=>{this.isVisible=!0,this.visible.emit(!0)})}runCheck(){const e=()=>{if(!this.element)return;const{offsetHeight:i,offsetWidth:a}=this.element.nativeElement;i&&a?(clearTimeout(this.timeout),this.onVisibilityChange()):(clearTimeout(this.timeout),this.zone.runOutsideAngular(()=>{this.timeout=setTimeout(()=>e(),100)}))};this.zone.runOutsideAngular(()=>{this.timeout=setTimeout(()=>e())})}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT),s.rXU(s.SKi))},t.\u0275dir=s.FsC({type:t,selectors:[["visibility-observer"]],outputs:{visible:"visible"}}),t})();function Qs(t){return"[object Date]"===toString.call(t)}let kn=(()=>{class t{constructor(e,i,a,r){this.chartElement=e,this.zone=i,this.cd=a,this.platformId=r,this.scheme="cool",this.schemeType=w.Ordinal,this.animations=!0,this.select=new s.bkB}ngOnInit(){(0,y.Vy)(this.platformId)&&(this.animations=!1)}ngAfterViewInit(){this.bindWindowResizeEvent(),this.visibilityObserver=new Yg(this.chartElement,this.zone),this.visibilityObserver.visible.subscribe(this.update.bind(this))}ngOnDestroy(){this.unbindEvents(),this.visibilityObserver&&(this.visibilityObserver.visible.unsubscribe(),this.visibilityObserver.destroy())}ngOnChanges(e){this.update()}update(){if(this.results=this.results?this.cloneData(this.results):[],this.view)this.width=this.view[0],this.height=this.view[1];else{const e=this.getContainerDims();e&&(this.width=e.width,this.height=e.height)}this.width||(this.width=600),this.height||(this.height=400),this.width=Math.floor(this.width),this.height=Math.floor(this.height),this.cd&&this.cd.markForCheck()}getContainerDims(){let e,i;const a=this.chartElement.nativeElement;if((0,y.UE)(this.platformId)&&null!==a.parentNode){const r=a.parentNode.getBoundingClientRect();e=r.width,i=r.height}return e&&i?{width:e,height:i}:null}formatDates(){for(let e=0;e<this.results.length;e++){const i=this.results[e];if(i.label=i.name,Qs(i.label)&&(i.label=i.label.toLocaleDateString()),i.series)for(let a=0;a<i.series.length;a++){const r=i.series[a];r.label=r.name,Qs(r.label)&&(r.label=r.label.toLocaleDateString())}}}unbindEvents(){this.resizeSubscription&&this.resizeSubscription.unsubscribe()}bindWindowResizeEvent(){if(!(0,y.UE)(this.platformId))return;const i=(0,P.R)(window,"resize").pipe((0,S.B)(200)).subscribe(a=>{this.update(),this.cd&&this.cd.markForCheck()});this.resizeSubscription=i}cloneData(e){const i=[];for(const a of e){const r={};if(void 0!==a.name&&(r.name=a.name),void 0!==a.value&&(r.value=a.value),void 0!==a.series){r.series=[];for(const o of a.series){const l=Object.assign({},o);r.series.push(l)}}void 0!==a.extra&&(r.extra=JSON.parse(JSON.stringify(a.extra))),void 0!==a.source&&(r.source=a.source),void 0!==a.target&&(r.target=a.target),i.push(r)}return i}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT),s.rXU(s.SKi),s.rXU(s.gRc),s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["base-chart"]],inputs:{results:"results",view:"view",scheme:"scheme",schemeType:"schemeType",customColors:"customColors",animations:"animations"},outputs:{select:"select"},features:[s.OA$],decls:1,vars:0,template:function(e,i){1&e&&s.nrm(0,"div")},encapsulation:2}),t})();var ue=function(t){return t.Top="top",t.Bottom="bottom",t.Left="left",t.Right="right",t}(ue||{});let qs=(()=>{class t{constructor(e){this.textHeight=25,this.margin=5,this.element=e.nativeElement}ngOnChanges(e){this.update()}update(){switch(this.strokeWidth="0.01",this.textAnchor="middle",this.transform="",this.orient){case ue.Top:case ue.Bottom:this.y=this.offset,this.x=this.width/2;break;case ue.Left:this.y=-(this.offset+this.textHeight+this.margin),this.x=-this.height/2,this.transform="rotate(270)";break;case ue.Right:this.y=this.offset+this.margin,this.x=-this.height/2,this.transform="rotate(270)"}}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-axis-label",""]],inputs:{orient:"orient",label:"label",offset:"offset",width:"width",height:"height"},features:[s.OA$],attrs:fm,decls:2,vars:6,template:function(e,i){1&e&&(s.qSk(),s.j41(0,"text"),s.EFF(1),s.k0s()),2&e&&(s.BMQ("stroke-width",i.strokeWidth)("x",i.x)("y",i.y)("text-anchor",i.textAnchor)("transform",i.transform),s.R7$(),s.SpI(" ",i.label," "))},encapsulation:2,changeDetection:0}),t})();function An(t,n=16){return"string"!=typeof t?"number"==typeof t?t+"":"":(t=t.trim()).length<=n?t:`${t.slice(0,n)}...`}function Zs(t,n){if(t.length>n){const e=[],i=Math.floor(t.length/n);for(let a=0;a<t.length;a++)a%i==0&&e.push(t[a]);t=e}return t}function Ks(t,n,e){const i=(t||"").toString();let a=[];if(/\s/.test(i))a=i.split(/\s+/).reduce((r,o)=>{const l=(r.pop()||"")+" ";return l.length+o.length>n?[...r,l.trim(),o.trim()]:[...r,l+o]},[]);else{let r=0;for(;r<i.length;)a.push(i.substring(r,r+n)),r+=n}return a.length>e&&(a=a.splice(0,e),a[a.length-1]+="..."),a}var Ne=function(t){return t.Start="start",t.Middle="middle",t.End="end",t}(Ne||{});let Js=(()=>{class t{constructor(e){this.platformId=e,this.tickArguments=[5],this.tickStroke="#ccc",this.trimTicks=!0,this.maxTickLength=16,this.showGridLines=!1,this.rotateTicks=!0,this.wrapTicks=!1,this.dimensionsChanged=new s.bkB,this.verticalSpacing=20,this.rotateLabels=!1,this.innerTickSize=6,this.outerTickSize=6,this.tickPadding=3,this.textAnchor=Ne.Middle,this.maxTicksLength=0,this.maxAllowedLength=16,this.height=0,this.approxHeight=10,this.maxPossibleLengthForTickIfWrapped=16}get isWrapTicksSupported(){return this.wrapTicks&&this.scale.step}ngOnChanges(e){this.update()}ngAfterViewInit(){setTimeout(()=>this.updateDims())}updateDims(){if(!(0,y.UE)(this.platformId))return void this.dimensionsChanged.emit({height:this.approxHeight});const e=parseInt(this.ticksElement.nativeElement.getBoundingClientRect().height,10);e!==this.height&&(this.height=e,this.dimensionsChanged.emit({height:this.height}),setTimeout(()=>this.updateDims()))}update(){const e=this.scale;this.ticks=this.getTicks(),this.tickFormat=this.tickFormatting?this.tickFormatting:e.tickFormat?e.tickFormat.apply(e,this.tickArguments):function(a){return"Date"===a.constructor.name?a.toLocaleDateString():a.toLocaleString()};const i=this.rotateTicks?this.getRotationAngle(this.ticks):null;this.adjustedScale=this.scale.bandwidth?function(a){return this.scale(a)+.5*this.scale.bandwidth()}:this.scale,this.textTransform="",i&&0!==i?(this.textTransform=`rotate(${i})`,this.textAnchor=Ne.End,this.verticalSpacing=10):this.textAnchor=Ne.Middle,setTimeout(()=>this.updateDims())}getRotationAngle(e){let i=0;this.maxTicksLength=0;for(let d=0;d<e.length;d++){const u=this.tickFormat(e[d]).toString();let g=u.length;this.trimTicks&&(g=this.tickTrim(u).length),g>this.maxTicksLength&&(this.maxTicksLength=g)}const o=7*Math.min(this.maxTicksLength,this.maxAllowedLength);let l=o;const h=Math.floor(this.width/e.length);for(;l>h&&i>-90;)i-=30,l=Math.cos(i*(Math.PI/180))*o;let c=14;if(this.isWrapTicksSupported){const d=this.ticks.reduce((g,f)=>f.length>g.length?f:g,"");c=14*(this.tickChunks(d).length||1),this.maxPossibleLengthForTickIfWrapped=this.getMaxPossibleLengthForTick(d)}const m=0!==i?Math.max(Math.abs(Math.sin(i*Math.PI/180))*this.maxTickLength*7,10):c;return this.approxHeight=Math.min(m,200),i}getTicks(){let e;const i=this.getMaxTicks(20),a=this.getMaxTicks(100);return this.tickValues?e=this.tickValues:this.scale.ticks?e=this.scale.ticks.apply(this.scale,[a]):(e=this.scale.domain(),e=Zs(e,i)),e}getMaxTicks(e){return Math.floor(this.width/e)}tickTransform(e){return"translate("+this.adjustedScale(e)+","+this.verticalSpacing+")"}gridLineTransform(){return`translate(0,${-this.verticalSpacing-5})`}tickTrim(e){return this.trimTicks?An(e,this.maxTickLength):e}getMaxPossibleLengthForTick(e){if(this.scale.bandwidth){const a=Math.floor(this.scale.bandwidth()/7),r=e.slice(0,a);return Math.max(r.length,this.maxTickLength)}return this.maxTickLength}tickChunks(e){if(e.toString().length>this.maxTickLength&&this.scale.bandwidth){let a=this.rotateTicks?Math.floor(this.scale.step()/14):5;if(a<=1)return[this.tickTrim(e)];let r=Math.max(this.maxPossibleLengthForTickIfWrapped,this.maxTickLength);return(0,y.UE)(this.platformId)||(r=Math.floor(Math.min(this.approxHeight/5,Math.max(this.maxPossibleLengthForTickIfWrapped,this.maxTickLength)))),a=Math.min(a,5),Ks(e,r,a<1?1:a)}return[this.tickTrim(e)]}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-x-axis-ticks",""]],viewQuery:function(e,i){if(1&e&&s.GBs(Rs,5),2&e){let a;s.mGM(a=s.lsd())&&(i.ticksElement=a.first)}},inputs:{scale:"scale",orient:"orient",tickArguments:"tickArguments",tickValues:"tickValues",tickStroke:"tickStroke",trimTicks:"trimTicks",maxTickLength:"maxTickLength",tickFormatting:"tickFormatting",showGridLines:"showGridLines",gridLineHeight:"gridLineHeight",width:"width",rotateTicks:"rotateTicks",wrapTicks:"wrapTicks"},outputs:{dimensionsChanged:"dimensionsChanged"},features:[s.OA$],attrs:xm,decls:4,vars:2,consts:[["ticksel",""],["tmplMultilineTick",""],["tmplSinglelineTick",""],["class","tick",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[1,"tick"],[4,"ngIf"],["stroke-width","0.01","font-size","12px"],[4,"ngIf","ngIfThen","ngIfElse"],["x","0",4,"ngFor","ngForOf"],["x","0"],["y2","0",1,"gridline-path","gridline-path-vertical"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"g",null,0),s.DNE(2,wm,2,2,"g",3),s.k0s(),s.DNE(3,km,2,2,"g",4)),2&e&&(s.R7$(2),s.Y8G("ngForOf",i.ticks),s.R7$(),s.Y8G("ngForOf",i.ticks))},dependencies:[y.Sq,y.bT],encapsulation:2,changeDetection:0}),t})(),er=(()=>{class t{constructor(){this.rotateTicks=!0,this.showGridLines=!1,this.xOrient=ue.Bottom,this.xAxisOffset=0,this.wrapTicks=!1,this.dimensionsChanged=new s.bkB,this.xAxisClassName="x axis",this.labelOffset=0,this.fill="none",this.stroke="stroke",this.tickStroke="#ccc",this.strokeWidth="none",this.padding=5,this.orientation=ue}ngOnChanges(e){this.update()}update(){this.transform=`translate(0,${this.xAxisOffset+this.padding+this.dims.height})`,typeof this.xAxisTickCount<"u"&&(this.tickArguments=[this.xAxisTickCount])}emitTicksHeight({height:e}){const i=e+25+5;i!==this.labelOffset&&(this.labelOffset=i,setTimeout(()=>{this.dimensionsChanged.emit({height:e})},0))}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-x-axis",""]],viewQuery:function(e,i){if(1&e&&s.GBs(Js,5),2&e){let a;s.mGM(a=s.lsd())&&(i.ticksComponent=a.first)}},inputs:{xScale:"xScale",dims:"dims",trimTicks:"trimTicks",rotateTicks:"rotateTicks",maxTickLength:"maxTickLength",tickFormatting:"tickFormatting",showGridLines:"showGridLines",showLabel:"showLabel",labelText:"labelText",ticks:"ticks",xAxisTickCount:"xAxisTickCount",xOrient:"xOrient",xAxisOffset:"xAxisOffset",wrapTicks:"wrapTicks"},outputs:{dimensionsChanged:"dimensionsChanged"},features:[s.OA$],attrs:Am,decls:3,vars:4,consts:[["ngx-charts-x-axis-ticks","",3,"trimTicks","rotateTicks","maxTickLength","tickFormatting","tickArguments","tickStroke","scale","orient","showGridLines","gridLineHeight","width","tickValues","wrapTicks","dimensionsChanged",4,"ngIf"],["ngx-charts-axis-label","",3,"label","offset","orient","height","width",4,"ngIf"],["ngx-charts-x-axis-ticks","",3,"dimensionsChanged","trimTicks","rotateTicks","maxTickLength","tickFormatting","tickArguments","tickStroke","scale","orient","showGridLines","gridLineHeight","width","tickValues","wrapTicks"],["ngx-charts-axis-label","",3,"label","offset","orient","height","width"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Lm,1,13,"g",0)(2,Dm,1,5,"g",1),s.k0s()),2&e&&(s.BMQ("class",i.xAxisClassName)("transform",i.transform),s.R7$(),s.Y8G("ngIf",i.xScale),s.R7$(),s.Y8G("ngIf",i.showLabel))},dependencies:[Js,qs,y.bT],encapsulation:2,changeDetection:0}),t})();function je(t,n,e,i,a,[r,o,l,h]){let c="";return c=`M${[t+a,n]}`,c+="h"+((e=0===(e=Math.floor(e))?1:e)-2*a),c+=o?`a${[a,a]} 0 0 1 ${[a,a]}`:`h${a}v${a}`,c+="v"+((i=0===(i=Math.floor(i))?1:i)-2*a),c+=h?`a${[a,a]} 0 0 1 ${[-a,a]}`:`v${a}h${-a}`,c+="h"+(2*a-e),c+=l?`a${[a,a]} 0 0 1 ${[-a,-a]}`:`h${-a}v${-a}`,c+="v"+(2*a-i),c+=r?`a${[a,a]} 0 0 1 ${[a,-a]}`:`v${-a}h${a}`,c+="z",c}let tr=(()=>{class t{constructor(e){this.platformId=e,this.tickArguments=[5],this.tickStroke="#ccc",this.trimTicks=!0,this.maxTickLength=16,this.showGridLines=!1,this.showRefLabels=!1,this.showRefLines=!1,this.wrapTicks=!1,this.dimensionsChanged=new s.bkB,this.innerTickSize=6,this.tickPadding=3,this.verticalSpacing=20,this.textAnchor=Ne.Middle,this.width=0,this.outerTickSize=6,this.rotateLabels=!1,this.referenceLineLength=0,this.Orientation=ue}ngOnChanges(e){this.update()}ngAfterViewInit(){setTimeout(()=>this.updateDims())}updateDims(){if(!(0,y.UE)(this.platformId))return this.width=this.getApproximateAxisWidth(),void this.dimensionsChanged.emit({width:this.width});const e=parseInt(this.ticksElement.nativeElement.getBoundingClientRect().width,10);e!==this.width&&(this.width=e,this.dimensionsChanged.emit({width:e}),setTimeout(()=>this.updateDims()))}update(){const e=this.scale,i=this.orient===ue.Top||this.orient===ue.Right?-1:1;switch(this.tickSpacing=Math.max(this.innerTickSize,0)+this.tickPadding,this.ticks=this.getTicks(),this.tickFormat=this.tickFormatting?this.tickFormatting:e.tickFormat?e.tickFormat.apply(e,this.tickArguments):function(a){return"Date"===a.constructor.name?a.toLocaleDateString():a.toLocaleString()},this.adjustedScale=e.bandwidth?a=>{const r=e(a)+.5*e.bandwidth();if(this.wrapTicks&&a.toString().length>this.maxTickLength){const o=this.tickChunks(a).length;if(1===o)return r;const c=.5*e.bandwidth()-8*o*.5;return e(a)+c}return r}:e,this.showRefLines&&this.referenceLines&&this.setReferencelines(),this.orient){case ue.Top:case ue.Bottom:this.transform=function(a){return"translate("+this.adjustedScale(a)+",0)"},this.textAnchor=Ne.Middle,this.y2=this.innerTickSize*i,this.y1=this.tickSpacing*i,this.dy=i<0?"0em":".71em";break;case ue.Left:this.transform=function(a){return"translate(0,"+this.adjustedScale(a)+")"},this.textAnchor=Ne.End,this.x2=this.innerTickSize*-i,this.x1=this.tickSpacing*-i,this.dy=".32em";break;case ue.Right:this.transform=function(a){return"translate(0,"+this.adjustedScale(a)+")"},this.textAnchor=Ne.Start,this.x2=this.innerTickSize*-i,this.x1=this.tickSpacing*-i,this.dy=".32em"}setTimeout(()=>this.updateDims())}setReferencelines(){this.refMin=this.adjustedScale(Math.min.apply(null,this.referenceLines.map(e=>e.value))),this.refMax=this.adjustedScale(Math.max.apply(null,this.referenceLines.map(e=>e.value))),this.referenceLineLength=this.referenceLines.length,this.referenceAreaPath=je(0,this.refMax,this.gridLineWidth,this.refMin-this.refMax,0,[!1,!1,!1,!1])}getTicks(){let e;const i=this.getMaxTicks(20),a=this.getMaxTicks(50);return this.tickValues?e=this.tickValues:this.scale.ticks?e=this.scale.ticks.apply(this.scale,[a]):(e=this.scale.domain(),e=Zs(e,i)),e}getMaxTicks(e){return Math.floor(this.height/e)}tickTransform(e){return`translate(${this.adjustedScale(e)},${this.verticalSpacing})`}gridLineTransform(){return"translate(5,0)"}tickTrim(e){return this.trimTicks?An(e,this.maxTickLength):e}getApproximateAxisWidth(){return 7*Math.max(...this.ticks.map(a=>this.tickTrim(this.tickFormat(a)).length))}tickChunks(e){if(e.toString().length>this.maxTickLength&&this.scale.bandwidth){const i=this.maxTickLength,a=Math.floor(this.scale.bandwidth()/15);return a<=1?[this.tickTrim(e)]:Ks(e,i,Math.min(a,5))}return[this.tickFormat(e)]}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-y-axis-ticks",""]],viewQuery:function(e,i){if(1&e&&s.GBs(Rs,5),2&e){let a;s.mGM(a=s.lsd())&&(i.ticksElement=a.first)}},inputs:{scale:"scale",orient:"orient",tickArguments:"tickArguments",tickValues:"tickValues",tickStroke:"tickStroke",trimTicks:"trimTicks",maxTickLength:"maxTickLength",tickFormatting:"tickFormatting",showGridLines:"showGridLines",gridLineWidth:"gridLineWidth",height:"height",referenceLines:"referenceLines",showRefLabels:"showRefLabels",showRefLines:"showRefLines",wrapTicks:"wrapTicks"},outputs:{dimensionsChanged:"dimensionsChanged"},features:[s.OA$],attrs:Fm,decls:6,vars:4,consts:[["ticksel",""],["tmplMultilineTick",""],["tmplSinglelineTick",""],["class","tick",4,"ngFor","ngForOf"],["class","reference-area",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"tick"],[4,"ngIf"],["stroke-width","0.01"],[4,"ngIf","ngIfThen","ngIfElse"],[4,"ngIf","ngIfElse"],["x","0",4,"ngFor","ngForOf"],["x","0"],[1,"reference-area"],["class","gridline-path gridline-path-horizontal","x1","0",4,"ngIf"],["x1","0",1,"gridline-path","gridline-path-horizontal"],["x1","0",1,"refline-path","gridline-path-horizontal"],[1,"refline-label"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"g",null,0),s.DNE(2,Gm,2,2,"g",3),s.k0s(),s.DNE(3,$m,1,2,"path",4)(4,Ym,2,2,"g",5)(5,Wm,2,1,"g",5)),2&e&&(s.R7$(2),s.Y8G("ngForOf",i.ticks),s.R7$(),s.Y8G("ngIf",i.referenceLineLength>1&&i.refMax&&i.refMin&&i.showRefLines),s.R7$(),s.Y8G("ngForOf",i.ticks),s.R7$(),s.Y8G("ngForOf",i.referenceLines))},dependencies:[y.Sq,y.bT],encapsulation:2,changeDetection:0}),t})(),ir=(()=>{class t{constructor(){this.showGridLines=!1,this.yOrient=ue.Left,this.yAxisOffset=0,this.wrapTicks=!1,this.dimensionsChanged=new s.bkB,this.yAxisClassName="y axis",this.labelOffset=15,this.fill="none",this.stroke="#CCC",this.tickStroke="#CCC",this.strokeWidth=1,this.padding=5}ngOnChanges(e){this.update()}update(){this.offset=-(this.yAxisOffset+this.padding),this.yOrient===ue.Right?(this.labelOffset=65,this.transform=`translate(${this.offset+this.dims.width} , 0)`):this.transform=`translate(${this.offset} , 0)`,void 0!==this.yAxisTickCount&&(this.tickArguments=[this.yAxisTickCount])}emitTicksWidth({width:e}){e!==this.labelOffset&&this.yOrient===ue.Right?(this.labelOffset=e+this.labelOffset,setTimeout(()=>{this.dimensionsChanged.emit({width:e})},0)):e!==this.labelOffset&&(this.labelOffset=e,setTimeout(()=>{this.dimensionsChanged.emit({width:e})},0))}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-y-axis",""]],viewQuery:function(e,i){if(1&e&&s.GBs(tr,5),2&e){let a;s.mGM(a=s.lsd())&&(i.ticksComponent=a.first)}},inputs:{yScale:"yScale",dims:"dims",trimTicks:"trimTicks",maxTickLength:"maxTickLength",tickFormatting:"tickFormatting",ticks:"ticks",showGridLines:"showGridLines",showLabel:"showLabel",labelText:"labelText",yAxisTickCount:"yAxisTickCount",yOrient:"yOrient",referenceLines:"referenceLines",showRefLines:"showRefLines",showRefLabels:"showRefLabels",yAxisOffset:"yAxisOffset",wrapTicks:"wrapTicks"},outputs:{dimensionsChanged:"dimensionsChanged"},features:[s.OA$],attrs:jm,decls:3,vars:4,consts:[["ngx-charts-y-axis-ticks","",3,"trimTicks","maxTickLength","tickFormatting","tickArguments","tickValues","tickStroke","scale","orient","showGridLines","gridLineWidth","referenceLines","showRefLines","showRefLabels","height","wrapTicks","dimensionsChanged",4,"ngIf"],["ngx-charts-axis-label","",3,"label","offset","orient","height","width",4,"ngIf"],["ngx-charts-y-axis-ticks","",3,"dimensionsChanged","trimTicks","maxTickLength","tickFormatting","tickArguments","tickValues","tickStroke","scale","orient","showGridLines","gridLineWidth","referenceLines","showRefLines","showRefLabels","height","wrapTicks"],["ngx-charts-axis-label","",3,"label","offset","orient","height","width"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"g"),s.DNE(1,Um,1,15,"g",0)(2,Qm,1,5,"g",1),s.k0s()),2&e&&(s.BMQ("class",i.yAxisClassName)("transform",i.transform),s.R7$(),s.Y8G("ngIf",i.yScale),s.R7$(),s.Y8G("ngIf",i.showLabel))},dependencies:[tr,qs,y.bT],encapsulation:2,changeDetection:0}),t})(),nr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[y.MD]]}),t})();var zt=function(t){return t.popover="popover",t.tooltip="tooltip",t}(zt||{}),yt=function(t){return t[t.all="all"]="all",t[t.focus="focus"]="focus",t[t.mouseover="mouseover"]="mouseover",t}(yt||{});let Ln=(()=>{class t{constructor(e,i,a){this.tooltipService=e,this.viewContainerRef=i,this.renderer=a,this.tooltipCssClass="",this.tooltipAppendToBody=!0,this.tooltipSpacing=10,this.tooltipDisabled=!1,this.tooltipShowCaret=!0,this.tooltipPlacement=R.Top,this.tooltipAlignment=R.Center,this.tooltipType=zt.popover,this.tooltipCloseOnClickOutside=!0,this.tooltipCloseOnMouseLeave=!0,this.tooltipHideTimeout=300,this.tooltipShowTimeout=100,this.tooltipShowEvent=yt.all,this.tooltipImmediateExit=!1,this.show=new s.bkB,this.hide=new s.bkB}get listensForFocus(){return this.tooltipShowEvent===yt.all||this.tooltipShowEvent===yt.focus}get listensForHover(){return this.tooltipShowEvent===yt.all||this.tooltipShowEvent===yt.mouseover}ngOnDestroy(){this.hideTooltip(!0)}onFocus(){this.listensForFocus&&this.showTooltip()}onBlur(){this.listensForFocus&&this.hideTooltip(!0)}onMouseEnter(){this.listensForHover&&this.showTooltip()}onMouseLeave(e){if(this.listensForHover&&this.tooltipCloseOnMouseLeave){if(clearTimeout(this.timeout),this.component&&this.component.instance.element.nativeElement.contains(e))return;this.hideTooltip(this.tooltipImmediateExit)}}onMouseClick(){this.listensForHover&&this.hideTooltip(!0)}showTooltip(e){if(this.component||this.tooltipDisabled)return;const i=e?0:this.tooltipShowTimeout+(navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)?400:0);clearTimeout(this.timeout),this.timeout=setTimeout(()=>{this.tooltipService.destroyAll();const a=this.createBoundOptions();this.component=this.tooltipService.create(a),setTimeout(()=>{this.component&&this.addHideListeners(this.component.instance.element.nativeElement)},10),this.show.emit(!0)},i)}addHideListeners(e){this.mouseEnterContentEvent=this.renderer.listen(e,"mouseenter",()=>{clearTimeout(this.timeout)}),this.tooltipCloseOnMouseLeave&&(this.mouseLeaveContentEvent=this.renderer.listen(e,"mouseleave",()=>{this.hideTooltip(this.tooltipImmediateExit)})),this.tooltipCloseOnClickOutside&&(this.documentClickEvent=this.renderer.listen("window","click",i=>{e.contains(i.target)||this.hideTooltip()}))}hideTooltip(e=!1){if(!this.component)return;const i=()=>{this.mouseLeaveContentEvent&&this.mouseLeaveContentEvent(),this.mouseEnterContentEvent&&this.mouseEnterContentEvent(),this.documentClickEvent&&this.documentClickEvent(),this.hide.emit(!0),this.tooltipService.destroy(this.component),this.component=void 0};clearTimeout(this.timeout),e?i():this.timeout=setTimeout(i,this.tooltipHideTimeout)}createBoundOptions(){return{title:this.tooltipTitle,template:this.tooltipTemplate,host:this.viewContainerRef.element,placement:this.tooltipPlacement,alignment:this.tooltipAlignment,type:this.tooltipType,showCaret:this.tooltipShowCaret,cssClass:this.tooltipCssClass,spacing:this.tooltipSpacing,context:this.tooltipContext}}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(bn),s.rXU(s.c1b),s.rXU(s.sFG))},t.\u0275dir=s.FsC({type:t,selectors:[["","ngx-tooltip",""]],hostBindings:function(e,i){1&e&&s.bIt("focusin",function(){return i.onFocus()})("blur",function(){return i.onBlur()})("mouseenter",function(){return i.onMouseEnter()})("mouseleave",function(r){return i.onMouseLeave(r.target)})("click",function(){return i.onMouseClick()})},inputs:{tooltipCssClass:"tooltipCssClass",tooltipTitle:"tooltipTitle",tooltipAppendToBody:"tooltipAppendToBody",tooltipSpacing:"tooltipSpacing",tooltipDisabled:"tooltipDisabled",tooltipShowCaret:"tooltipShowCaret",tooltipPlacement:"tooltipPlacement",tooltipAlignment:"tooltipAlignment",tooltipType:"tooltipType",tooltipCloseOnClickOutside:"tooltipCloseOnClickOutside",tooltipCloseOnMouseLeave:"tooltipCloseOnMouseLeave",tooltipHideTimeout:"tooltipHideTimeout",tooltipShowTimeout:"tooltipShowTimeout",tooltipTemplate:"tooltipTemplate",tooltipShowEvent:"tooltipShowEvent",tooltipContext:"tooltipContext",tooltipImmediateExit:"tooltipImmediateExit"},outputs:{show:"show",hide:"hide"}}),t})(),ar=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({providers:[zs,bn],imports:[[y.MD]]}),t})();const sr={};function st(){let t=("0000"+(Math.random()*Math.pow(36,4)|0).toString(36)).slice(-4);return t=`a${t}`,sr[t]?st():(sr[t]=!0,t)}var ae=function(t){return t.Vertical="vertical",t.Horizontal="horizontal",t}(ae||{});let Wt=(()=>{class t{constructor(){this.orientation=ae.Vertical}ngOnChanges(e){this.x1="0%",this.x2="0%",this.y1="0%",this.y2="0%",this.orientation===ae.Horizontal?this.x2="100%":this.orientation===ae.Vertical&&(this.y1="100%")}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-svg-linear-gradient",""]],inputs:{orientation:"orientation",name:"name",stops:"stops"},features:[s.OA$],attrs:qm,decls:2,vars:6,consts:[[3,"id"],[3,"stop-color","stop-opacity",4,"ngFor","ngForOf"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"linearGradient",0),s.DNE(1,Zm,1,5,"stop",1),s.k0s()),2&e&&(s.Y8G("id",i.name),s.BMQ("x1",i.x1)("y1",i.y1)("x2",i.x2)("y2",i.y2),s.R7$(),s.Y8G("ngForOf",i.stops))},dependencies:[y.Sq],encapsulation:2,changeDetection:0}),t})(),rr=(()=>{class t{constructor(){this.select=new s.bkB,this.activate=new s.bkB,this.deactivate=new s.bkB}onClick(){this.select.emit(this.data)}onMouseEnter(){this.activate.emit(this.data)}onMouseLeave(){this.deactivate.emit(this.data)}ngOnChanges(e){this.classNames=Array.isArray(this.classNames)?this.classNames.join(" "):"",this.classNames+="circle"}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-circle",""]],hostBindings:function(e,i){1&e&&s.bIt("click",function(){return i.onClick()})("mouseenter",function(){return i.onMouseEnter()})("mouseleave",function(){return i.onMouseLeave()})},inputs:{cx:"cx",cy:"cy",r:"r",fill:"fill",stroke:"stroke",data:"data",classNames:"classNames",circleOpacity:"circleOpacity",pointerEvents:"pointerEvents"},outputs:{select:"select",activate:"activate",deactivate:"deactivate"},features:[s.OA$],attrs:Km,decls:1,vars:8,template:function(e,i){1&e&&(s.qSk(),s.nrm(0,"circle")),2&e&&s.BMQ("cx",i.cx)("cy",i.cy)("r",i.r)("fill",i.fill)("stroke",i.stroke)("opacity",i.circleOpacity)("class",i.classNames)("pointer-events",i.pointerEvents)},encapsulation:2,changeDetection:0}),t})();var wi=function(t){return t.Standard="standard",t.Stacked="stacked",t}(wi||{});let or=(()=>{class t{constructor(e){this.platformId=e,this.type=wi.Standard,this.tooltipDisabled=!1,this.select=new s.bkB,this.activate=new s.bkB,this.deactivate=new s.bkB,this.barVisible=!1,this.barOrientation=ae,this.placementTypes=R,this.styleTypes=zt,this.isSSR=!1}ngOnInit(){this.gradientId="grad"+st().toString(),this.gradientFill=`url(#${this.gradientId})`,(0,y.Vy)(this.platformId)&&(this.isSSR=!0)}ngOnChanges(){this.update()}update(){this.circle=this.getActiveCircle()}getActiveCircle(){const e=this.data.series.findIndex(i=>{const a=i.name;return a&&this.visibleValue&&a.toString()===this.visibleValue.toString()&&void 0!==i.value});if(-1!==e)return this.mapDataPointToCircle(this.data.series[e],e)}mapDataPointToCircle(e,i){const a=this.data.name,r=e.value,o=e.name,l=Nt(o);let h;h=this.xScale(this.scaleType===w.Time?o:this.scaleType===w.Linear?Number(o):o);const c=this.yScale(this.type===wi.Standard?r:e.d1),d=this.yScale.range()[0]-c;let g;return g=this.colors.getColor(this.colors.scaleType===w.Linear?this.type===wi.Standard?r:e.d1:a),{classNames:[`circle-data-${i}`],value:r,label:o,data:Object.assign({},e,{series:a,value:r,name:o}),cx:h,cy:c,radius:5,height:d,tooltipLabel:l,color:g,opacity:1,seriesName:a,gradientStops:this.getGradientStops(g),min:e.min,max:e.max}}getTooltipText({tooltipLabel:e,value:i,seriesName:a,min:r,max:o}){return`\n      <span class="tooltip-label">${wn(a)} \u2022 ${wn(e)}</span>\n      <span class="tooltip-val">${i.toLocaleString()}${this.getTooltipMinMaxText(r,o)}</span>\n    `}getTooltipMinMaxText(e,i){if(void 0!==e||void 0!==i){let a=" (";return void 0!==e?(void 0===i&&(a+="\u2265"),a+=e.toLocaleString(),void 0!==i&&(a+=" - ")):void 0!==i&&(a+="\u2264"),void 0!==i&&(a+=i.toLocaleString()),a+=")",a}return""}getGradientStops(e){return[{offset:0,color:e,opacity:.2},{offset:100,color:e,opacity:1}]}onClick(e){this.select.emit(e)}isActive(e){return!!this.activeEntries&&void 0!==this.activeEntries.find(a=>e.name===a.name)}activateCircle(){this.barVisible=!0,this.activate.emit({name:this.data.name})}deactivateCircle(){this.barVisible=!1,this.circle.opacity=0,this.deactivate.emit({name:this.data.name})}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-circle-series",""]],inputs:{data:"data",type:"type",xScale:"xScale",yScale:"yScale",colors:"colors",scaleType:"scaleType",visibleValue:"visibleValue",activeEntries:"activeEntries",tooltipDisabled:"tooltipDisabled",tooltipTemplate:"tooltipTemplate"},outputs:{select:"select",activate:"activate",deactivate:"deactivate"},features:[s.OA$],attrs:Jm,decls:1,vars:1,consts:[[4,"ngIf"],["ngx-charts-svg-linear-gradient","",3,"orientation","name","stops"],["class","tooltip-bar",4,"ngIf"],["ngx-charts-circle","","ngx-tooltip","",1,"circle",3,"select","activate","deactivate","cx","cy","r","fill","pointerEvents","data","classNames","tooltipDisabled","tooltipPlacement","tooltipType","tooltipTitle","tooltipTemplate","tooltipContext"],[1,"tooltip-bar"]],template:function(e,i){1&e&&s.DNE(0,nu,6,22,"g",0),2&e&&s.Y8G("ngIf",i.circle)},dependencies:[Wt,rr,y.bT,Ln],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)(":enter",[(0,_.iF)({opacity:0}),(0,_.i0)(250,(0,_.iF)({opacity:1}))])])]},changeDetection:0}),t})(),lr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-grid-panel",""]],inputs:{width:"width",height:"height",x:"x",y:"y"},attrs:au,decls:1,vars:4,consts:[["stroke","none",1,"gridpanel"]],template:function(e,i){1&e&&(s.qSk(),s.nrm(0,"rect",0)),2&e&&s.BMQ("height",i.height)("width",i.width)("x",i.x)("y",i.y)},encapsulation:2,changeDetection:0}),t})();var Si=function(t){return t.Odd="odd",t.Even="even",t}(Si||{});let Dn,cr=(()=>{class t{ngOnChanges(e){this.update()}update(){this.gridPanels=this.getGridPanels()}getGridPanels(){return this.data.map(e=>{let i,a,r,o,l,h=Si.Odd;if(this.orient===ae.Vertical){const c=this.xScale(e.name);Number.parseInt((c/this.xScale.step()).toString(),10)%2==1&&(h=Si.Even),i=this.xScale.bandwidth()*this.xScale.paddingInner(),a=this.xScale.bandwidth()+i,r=this.dims.height,o=this.xScale(e.name)-i/2,l=0}else if(this.orient===ae.Horizontal){const c=this.yScale(e.name);Number.parseInt((c/this.yScale.step()).toString(),10)%2==1&&(h=Si.Even),i=this.yScale.bandwidth()*this.yScale.paddingInner(),a=this.dims.width,r=this.yScale.bandwidth()+i,o=0,l=this.yScale(e.name)-i/2}return{name:e.name,class:h,height:r,width:a,x:o,y:l}})}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-grid-panel-series",""]],inputs:{data:"data",dims:"dims",xScale:"xScale",yScale:"yScale",orient:"orient"},features:[s.OA$],attrs:su,decls:1,vars:1,consts:[["ngx-charts-grid-panel","",3,"height","width","x","y","grid-panel","odd","even",4,"ngFor","ngForOf"],["ngx-charts-grid-panel","",3,"height","width","x","y"]],template:function(e,i){1&e&&s.DNE(0,ru,1,10,"g",0),2&e&&s.Y8G("ngForOf",i.gridPanels)},dependencies:[lr,y.Sq],encapsulation:2,changeDetection:0}),t})(),hr=(()=>{class t{constructor(e){this.opacity=1,this.startOpacity=.5,this.endOpacity=1,this.gradient=!1,this.animations=!0,this.select=new s.bkB,this.animationsLoaded=!1,this.hasGradient=!1,this.barOrientation=ae,this.element=e.nativeElement}ngOnChanges(){this.update(),this.animationsLoaded||(this.loadAnimation(),this.animationsLoaded=!0)}update(){this.gradientId="grad"+st().toString(),this.gradientFill=`url(#${this.gradientId})`,this.gradient||this.stops?(this.gradientStops=this.getGradient(),this.hasGradient=!0):this.hasGradient=!1,this.updatePathEl()}loadAnimation(){this.areaPath=this.startingPath,setTimeout(this.updatePathEl.bind(this),100)}updatePathEl(){const e=Se(this.element).select(".area");this.animations?e.transition().duration(750).attr("d",this.path):e.attr("d",this.path)}getGradient(){return this.stops?this.stops:[{offset:0,color:this.fill,opacity:this.startOpacity},{offset:100,color:this.fill,opacity:this.endOpacity}]}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-area",""]],inputs:{data:"data",path:"path",startingPath:"startingPath",fill:"fill",opacity:"opacity",startOpacity:"startOpacity",endOpacity:"endOpacity",gradient:"gradient",stops:"stops",animations:"animations"},outputs:{select:"select"},features:[s.OA$],attrs:cu,decls:2,vars:5,consts:[[4,"ngIf"],[1,"area"],["ngx-charts-svg-linear-gradient","",3,"orientation","name","stops"]],template:function(e,i){1&e&&(s.DNE(0,hu,2,3,"defs",0),s.qSk(),s.nrm(1,"path",1)),2&e&&(s.Y8G("ngIf",i.gradient),s.R7$(),s.xc7("opacity",i.opacity),s.BMQ("d",i.areaPath)("fill",i.gradient?i.gradientFill:i.fill))},dependencies:[Wt,y.bT],encapsulation:2,changeDetection:0}),t})();typeof window<"u"?Dn=window:typeof global<"u"&&(Dn=global);const ur=Dn.MouseEvent;function Fn(t,n=!1,e=!0){if("function"==typeof ur)return new ur(t,{bubbles:n,cancelable:e});{const i=document.createEvent("MouseEvent");return i.initEvent(t,n,e),i}}let gr=(()=>{class t{constructor(e){this.platformId=e,this.anchorOpacity=0,this.anchorPos=-1,this.anchorValues=[],this.placementTypes=R,this.styleTypes=zt,this.showPercentage=!1,this.tooltipDisabled=!1,this.hover=new s.bkB}getValues(e){const i=[];for(const a of this.results){const r=a.series.find(l=>l.name.toString()===e.toString());let o=a.name;if(o instanceof Date&&(o=o.toLocaleDateString()),r){const l=r.name;let c,h=r.value;if(this.showPercentage&&(h=(r.d1-r.d0).toFixed(2)+"%"),this.colors.scaleType===w.Linear){let d=h;r.d1&&(d=r.d1),c=this.colors.getColor(d)}else c=this.colors.getColor(a.name);const m=Object.assign({},r,{value:h,name:l,series:o,min:r.min,max:r.max,color:c});i.push(m)}}return i}mouseMove(e){if(!(0,y.UE)(this.platformId))return;const i=e.pageX-e.target.getBoundingClientRect().left,a=this.findClosestPointIndex(i),r=this.xSet[a];if(this.anchorPos=this.xScale(r),this.anchorPos=Math.max(0,this.anchorPos),this.anchorPos=Math.min(this.dims.width,this.anchorPos),this.anchorValues=this.getValues(r),this.anchorPos!==this.lastAnchorPos){const o=Fn("mouseleave");this.tooltipAnchor.nativeElement.dispatchEvent(o),this.anchorOpacity=.7,this.hover.emit({value:r}),this.showTooltip(),this.lastAnchorPos=this.anchorPos}}findClosestPointIndex(e){let i=0,a=this.xSet.length-1,r=Number.MAX_VALUE,o=0;for(;i<=a;){const l=(i+a)/2|0,h=this.xScale(this.xSet[l]),c=Math.abs(h-e);if(c<r&&(r=c,o=l),h<e)i=l+1;else{if(!(h>e)){r=0,o=l;break}a=l-1}}return o}showTooltip(){const e=Fn("mouseenter");this.tooltipAnchor.nativeElement.dispatchEvent(e)}hideTooltip(){const e=Fn("mouseleave");this.tooltipAnchor.nativeElement.dispatchEvent(e),this.anchorOpacity=0,this.lastAnchorPos=-1}getToolTipText(e){let i="";return i+=void 0!==e.series?e.series:"???",i+=": ",void 0!==e.value&&(i+=e.value.toLocaleString()),(void 0!==e.min||void 0!==e.max)&&(i+=" (",void 0!==e.min?(void 0===e.max&&(i+="\u2265"),i+=e.min.toLocaleString(),void 0!==e.max&&(i+=" - ")):void 0!==e.max&&(i+="\u2264"),void 0!==e.max&&(i+=e.max.toLocaleString()),i+=")"),i}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-tooltip-area",""]],viewQuery:function(e,i){if(1&e&&s.GBs(mu,5),2&e){let a;s.mGM(a=s.lsd())&&(i.tooltipAnchor=a.first)}},inputs:{dims:"dims",xSet:"xSet",xScale:"xScale",yScale:"yScale",results:"results",colors:"colors",showPercentage:"showPercentage",tooltipDisabled:"tooltipDisabled",tooltipTemplate:"tooltipTemplate"},outputs:{hover:"hover"},attrs:uu,decls:6,vars:18,consts:[["defaultTooltipTemplate",""],["tooltipAnchor",""],["y","0",1,"tooltip-area",2,"opacity","0","cursor","'auto'",3,"mousemove","mouseleave"],["y","0","ngx-tooltip","",1,"tooltip-anchor",3,"tooltipDisabled","tooltipPlacement","tooltipType","tooltipSpacing","tooltipTemplate","tooltipContext","tooltipImmediateExit"],[1,"area-tooltip-container"],["class","tooltip-item",4,"ngFor","ngForOf"],[1,"tooltip-item"],[1,"tooltip-item-color"]],template:function(e,i){if(1&e){const a=s.RV6();s.qSk(),s.j41(0,"g")(1,"rect",2),s.bIt("mousemove",function(o){return s.eBV(a),s.Njj(i.mouseMove(o))})("mouseleave",function(){return s.eBV(a),s.Njj(i.hideTooltip())}),s.k0s(),s.DNE(2,pu,2,1,"ng-template",null,0,s.C5r),s.nrm(4,"rect",3,1),s.k0s()}if(2&e){const a=s.sdS(3);s.R7$(),s.BMQ("x",0)("width",i.dims.width)("height",i.dims.height),s.R7$(3),s.xc7("opacity",i.anchorOpacity)("pointer-events","none"),s.Y8G("@animationState",0!==i.anchorOpacity?"active":"inactive")("tooltipDisabled",i.tooltipDisabled)("tooltipPlacement",i.placementTypes.Right)("tooltipType",i.styleTypes.tooltip)("tooltipSpacing",15)("tooltipTemplate",i.tooltipTemplate?i.tooltipTemplate:a)("tooltipContext",i.anchorValues)("tooltipImmediateExit",!0),s.BMQ("x",i.anchorPos)("width",1)("height",i.dims.height)}},dependencies:[y.Sq,Ln],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)("inactive => active",[(0,_.iF)({opacity:0}),(0,_.i0)(250,(0,_.iF)({opacity:.7}))]),(0,_.kY)("active => inactive",[(0,_.iF)({opacity:.7}),(0,_.i0)(250,(0,_.iF)({opacity:0}))])])]},changeDetection:0}),t})(),pr=(()=>{class t{constructor(e,i){this.cd=i,this.height=50,this.select=new s.bkB,this.onDomainChange=new s.bkB,this.initialized=!1,this.element=e.nativeElement}ngOnChanges(e){this.update(),this.initialized||(this.addBrush(),this.initialized=!0)}update(){this.dims=this.getDims(),this.height=this.dims.height;const e=this.view[1]-this.height;this.xDomain=this.getXDomain(),this.xScale=this.getXScale(),this.brush&&this.updateBrush(),this.transform=`translate(0 , ${e})`,this.filterId="filter"+st().toString(),this.filter=`url(#${this.filterId})`,this.cd.markForCheck()}getXDomain(){let e=[];for(const a of this.results)for(const r of a.series)e.includes(r.name)||e.push(r.name);let i=[];return this.scaleType===w.Time?i=[Math.min(...e),Math.max(...e)]:this.scaleType===w.Linear?(e=e.map(o=>Number(o)),i=[Math.min(...e),Math.max(...e)]):i=e,i}getXScale(){let e;return this.scaleType===w.Time?e=us().range([0,this.dims.width]).domain(this.xDomain):this.scaleType===w.Linear?e=ze().range([0,this.dims.width]).domain(this.xDomain):this.scaleType===w.Ordinal&&(e=Ds().range([0,this.dims.width]).padding(.1).domain(this.xDomain)),e}addBrush(){if(this.brush)return;const e=this.height,i=this.view[0];this.brush=function Jc(){return function Ui(t){var l,n=qc,e=Qc,i=Zc,a=!0,r=Un("start","brush","end"),o=6;function h(p){var C=p.property("__brush",b).selectAll(".overlay").data([Bt("overlay")]);C.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",Ge.overlay).merge(C).each(function(){var T=ji(this).extent;Se(this).attr("x",T[0][0]).attr("y",T[0][1]).attr("width",T[1][0]-T[0][0]).attr("height",T[1][1]-T[0][1])}),p.selectAll(".selection").data([Bt("selection")]).enter().append("rect").attr("class","selection").attr("cursor",Ge.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var L=p.selectAll(".handle").data(t.handles,function(T){return T.type});L.exit().remove(),L.enter().append("rect").attr("class",function(T){return"handle handle--"+T.type}).attr("cursor",function(T){return Ge[T.type]}),p.each(c).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",u).filter(i).on("touchstart.brush",u).on("touchmove.brush",g).on("touchend.brush touchcancel.brush",f).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function c(){var p=Se(this),C=ji(this).selection;C?(p.selectAll(".selection").style("display",null).attr("x",C[0][0]).attr("y",C[0][1]).attr("width",C[1][0]-C[0][0]).attr("height",C[1][1]-C[0][1]),p.selectAll(".handle").style("display",null).attr("x",function(L){return"e"===L.type[L.type.length-1]?C[1][0]-o/2:C[0][0]-o/2}).attr("y",function(L){return"s"===L.type[0]?C[1][1]-o/2:C[0][1]-o/2}).attr("width",function(L){return"n"===L.type||"s"===L.type?C[1][0]-C[0][0]+o:o}).attr("height",function(L){return"e"===L.type||"w"===L.type?C[1][1]-C[0][1]+o:o})):p.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function m(p,C,L){var T=p.__brush.emitter;return!T||L&&T.clean?new d(p,C,L):T}function d(p,C,L){this.that=p,this.args=C,this.state=p.__brush,this.active=0,this.clean=L}function u(p){if((!l||p.touches)&&e.apply(this,arguments)){var J,B,W,K,ee,te,j,le,jt,bt,wt,C=this,L=p.target.__data__.type,T="selection"===(a&&p.metaKey?L="overlay":L)?ka:a&&p.altKey?mt:dt,M=t===gi?null:jc[L],V=t===ui?null:Uc[L],D=ji(C),q=D.extent,Z=D.selection,_e=q[0][0],de=q[0][1],Le=q[1][0],ve=q[1][1],se=0,re=0,Ct=M&&V&&a&&p.shiftKey,fe=Array.from(p.touches||[p],O=>{const ne=O.identifier;return(O=da(O,C)).point0=O.slice(),O.identifier=ne,O});Ri(C);var rt=m(C,arguments,!0).beforestart();if("overlay"===L){Z&&(jt=!0);const O=[fe[0],fe[1]||fe[0]];D.selection=Z=[[J=t===gi?_e:pe(O[0][0],O[1][0]),W=t===ui?de:pe(O[0][1],O[1][1])],[ee=t===gi?Le:ge(O[0][0],O[1][0]),j=t===ui?ve:ge(O[0][1],O[1][1])]],fe.length>1&&ie(p)}else J=Z[0][0],W=Z[0][1],ee=Z[1][0],j=Z[1][1];B=J,K=W,te=ee,le=j;var k=Se(C).attr("pointer-events","none"),$=k.selectAll(".overlay").attr("cursor",Ge[L]);if(p.touches)rt.moved=x,rt.ended=U;else{var X=Se(p.view).on("mousemove.brush",x,!0).on("mouseup.brush",U,!0);a&&X.on("keydown.brush",function ye(O){switch(O.keyCode){case 16:Ct=M&&V;break;case 18:T===dt&&(M&&(ee=te-se*M,J=B+se*M),V&&(j=le-re*V,W=K+re*V),T=mt,ie(O));break;case 32:(T===dt||T===mt)&&(M<0?ee=te-se:M>0&&(J=B-se),V<0?j=le-re:V>0&&(W=K-re),T=zi,$.attr("cursor",Ge.selection),ie(O));break;default:return}Ni(O)},!0).on("keyup.brush",function Te(O){switch(O.keyCode){case 16:Ct&&(bt=wt=Ct=!1,ie(O));break;case 18:T===mt&&(M<0?ee=te:M>0&&(J=B),V<0?j=le:V>0&&(W=K),T=dt,ie(O));break;case 32:T===zi&&(O.altKey?(M&&(ee=te-se*M,J=B+se*M),V&&(j=le-re*V,W=K+re*V),T=mt):(M<0?ee=te:M>0&&(J=B),V<0?j=le:V>0&&(W=K),T=dt),$.attr("cursor",Ge[L]),ie(O));break;default:return}Ni(O)},!0),function nl(t){var n=t.document.documentElement,e=Se(t).on("dragstart.drag",Di,Li);"onselectstart"in n?e.on("selectstart.drag",Di,Li):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}(p.view)}c.call(C),rt.start(p,T.name)}function x(O){for(const ne of O.changedTouches||[O])for(const Ut of fe)Ut.identifier===ne.identifier&&(Ut.cur=da(ne,C));if(Ct&&!bt&&!wt&&1===fe.length){const ne=fe[0];Aa(ne.cur[0]-ne[0])>Aa(ne.cur[1]-ne[1])?wt=!0:bt=!0}for(const ne of fe)ne.cur&&(ne[0]=ne.cur[0],ne[1]=ne.cur[1]);jt=!0,Ni(O),ie(O)}function ie(O){const ne=fe[0],Ut=ne.point0;var Qe;switch(se=ne[0]-Ut[0],re=ne[1]-Ut[1],T){case zi:case ka:M&&(se=ge(_e-J,pe(Le-ee,se)),B=J+se,te=ee+se),V&&(re=ge(de-W,pe(ve-j,re)),K=W+re,le=j+re);break;case dt:fe[1]?(M&&(B=ge(_e,pe(Le,fe[0][0])),te=ge(_e,pe(Le,fe[1][0])),M=1),V&&(K=ge(de,pe(ve,fe[0][1])),le=ge(de,pe(ve,fe[1][1])),V=1)):(M<0?(se=ge(_e-J,pe(Le-J,se)),B=J+se,te=ee):M>0&&(se=ge(_e-ee,pe(Le-ee,se)),B=J,te=ee+se),V<0?(re=ge(de-W,pe(ve-W,re)),K=W+re,le=j):V>0&&(re=ge(de-j,pe(ve-j,re)),K=W,le=j+re));break;case mt:M&&(B=ge(_e,pe(Le,J-se*M)),te=ge(_e,pe(Le,ee+se*M))),V&&(K=ge(de,pe(ve,W-re*V)),le=ge(de,pe(ve,j+re*V)))}te<B&&(M*=-1,Qe=J,J=ee,ee=Qe,Qe=B,B=te,te=Qe,L in Da&&$.attr("cursor",Ge[L=Da[L]])),le<K&&(V*=-1,Qe=W,W=j,j=Qe,Qe=K,K=le,le=Qe,L in Fa&&$.attr("cursor",Ge[L=Fa[L]])),D.selection&&(Z=D.selection),bt&&(B=Z[0][0],te=Z[1][0]),wt&&(K=Z[0][1],le=Z[1][1]),(Z[0][0]!==B||Z[0][1]!==K||Z[1][0]!==te||Z[1][1]!==le)&&(D.selection=[[B,K],[te,le]],c.call(C),rt.brush(O,T.name))}function U(O){if(function zc(t){t.stopImmediatePropagation()}(O),O.touches){if(O.touches.length)return;l&&clearTimeout(l),l=setTimeout(function(){l=null},500)}else(function al(t,n){var e=t.document.documentElement,i=Se(t).on("dragstart.drag",null);n&&(i.on("click.drag",Di,Li),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in e?i.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)})(O.view,jt),X.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);k.attr("pointer-events","all"),$.attr("cursor",Ge.overlay),D.selection&&(Z=D.selection),function Kc(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}(Z)&&(D.selection=null,c.call(C)),rt.end(O,T.name)}}function g(p){m(this,arguments).moved(p)}function f(p){m(this,arguments).ended(p)}function b(){var p=this.__brush||{selection:null};return p.extent=Wi(n.apply(this,arguments)),p.dim=t,p}return h.move=function(p,C,L){p.tween?p.on("start.brush",function(T){m(this,arguments).beforestart().start(T)}).on("interrupt.brush end.brush",function(T){m(this,arguments).end(T)}).tween("brush",function(){var T=this,M=T.__brush,V=m(T,arguments),D=M.selection,q=t.input("function"==typeof C?C.apply(this,arguments):C,M.extent),Z=ai(D,q);function _e(J){M.selection=1===J&&null===q?null:Z(J),c.call(T),V.brush()}return null!==D&&null!==q?_e:_e(1)}):p.each(function(){var T=this,M=arguments,V=T.__brush,D=t.input("function"==typeof C?C.apply(T,M):C,V.extent),q=m(T,M).beforestart();Ri(T),V.selection=null===D?null:D,c.call(T),q.start(L).brush(L).end(L)})},h.clear=function(p,C){h.move(p,null,C)},d.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(p,C){return this.starting?(this.starting=!1,this.emit("start",p,C)):this.emit("brush",p),this},brush:function(p,C){return this.emit("brush",p,C),this},end:function(p,C){return 0==--this.active&&(delete this.state.emitter,this.emit("end",p,C)),this},emit:function(p,C,L){var T=Se(this.that).datum();r.call(p,this.that,new Nc(p,{sourceEvent:C,target:h,selection:t.output(this.state.selection),mode:L,dispatch:r}),T)}},h.extent=function(p){return arguments.length?(n="function"==typeof p?p:Yi(Wi(p)),h):n},h.filter=function(p){return arguments.length?(e="function"==typeof p?p:Yi(!!p),h):e},h.touchable=function(p){return arguments.length?(i="function"==typeof p?p:Yi(!!p),h):i},h.handleSize=function(p){return arguments.length?(o=+p,h):o},h.keyModifiers=function(p){return arguments.length?(a=!!p,h):a},h.on=function(){var p=r.on.apply(r,arguments);return p===r?h:p},h}(ui)}().extent([[0,0],[i,e]]).on("brush end",({selection:a})=>{const o=(a||this.xScale.range()).map(this.xScale.invert);this.onDomainChange.emit(o),this.cd.markForCheck()}),Se(this.element).select(".brush").call(this.brush)}updateBrush(){this.brush&&(this.brush.extent([[0,0],[this.view[0],this.height]]),Se(this.element).select(".brush").call(this.brush),Se(this.element).select(".selection").attr("fill",void 0).attr("stroke",void 0).attr("fill-opacity",void 0),this.cd.markForCheck())}getDims(){return{width:this.view[0],height:this.height}}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT),s.rXU(s.gRc))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-timeline",""]],inputs:{view:"view",results:"results",scheme:"scheme",customColors:"customColors",legend:"legend",autoScale:"autoScale",scaleType:"scaleType",height:"height"},outputs:{select:"select",onDomainChange:"onDomainChange"},features:[s.OA$],attrs:fu,ngContentSelectors:$s,decls:7,vars:4,consts:[[1,"timeline"],["in","SourceGraphic","type","matrix","values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"],[1,"embedded-chart"],["x","0","y","0",1,"brush-background"],[1,"brush"]],template:function(e,i){1&e&&(s.NAR(),s.qSk(),s.j41(0,"g",0)(1,"filter"),s.nrm(2,"feColorMatrix",1),s.k0s(),s.j41(3,"g",2),s.SdG(4),s.k0s(),s.nrm(5,"rect",3)(6,"g",4),s.k0s()),2&e&&(s.BMQ("transform",i.transform),s.R7$(),s.BMQ("id",i.filterId),s.R7$(4),s.BMQ("width",i.view[0])("height",i.height))},styles:[".timeline .brush-background{fill:#0000000d}.timeline .brush .selection{fill:#0000001a;stroke-width:1px;stroke:#888}.timeline .brush .handle{fill-opacity:0}.timeline .embedded-chart{opacity:.6}\n"],encapsulation:2,changeDetection:0}),t})(),Ae=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[y.MD,nr,ar],y.MD,nr,ar]}),t})();function fr({width:t,height:n,margins:e,showXAxis:i=!1,showYAxis:a=!1,xAxisHeight:r=0,yAxisWidth:o=0,showXLabel:l=!1,showYLabel:h=!1,showLegend:c=!1,legendType:m=w.Ordinal,legendPosition:d=Ye.Right,columns:u=12}){let g=e[3],f=t,b=n-e[0]-e[2];return c&&d===Ye.Right&&(u-=m===w.Ordinal?2:1),f=f*u/12,f=f-e[1]-e[3],i&&(b-=5,b-=r,l&&(b-=30)),a&&(f-=5,f-=o,g+=o,g+=10,h&&(f-=30,g+=30)),f=Math.max(0,f),b=Math.max(0,b),{width:Math.floor(f),height:Math.floor(b),xOffset:Math.floor(g)}}const xr=[{name:"vivid",selectable:!0,group:w.Ordinal,domain:["#647c8a","#3f51b5","#2196f3","#00b862","#afdf0a","#a7b61a","#f3e562","#ff9800","#ff5722","#ff4514"]},{name:"natural",selectable:!0,group:w.Ordinal,domain:["#bf9d76","#e99450","#d89f59","#f2dfa7","#a5d7c6","#7794b1","#afafaf","#707160","#ba9383","#d9d5c3"]},{name:"cool",selectable:!0,group:w.Ordinal,domain:["#a8385d","#7aa3e5","#a27ea8","#aae3f5","#adcded","#a95963","#8796c0","#7ed3ed","#50abcc","#ad6886"]},{name:"fire",selectable:!0,group:w.Ordinal,domain:["#ff3d00","#bf360c","#ff8f00","#ff6f00","#ff5722","#e65100","#ffca28","#ffab00"]},{name:"solar",selectable:!0,group:w.Linear,domain:["#fff8e1","#ffecb3","#ffe082","#ffd54f","#ffca28","#ffc107","#ffb300","#ffa000","#ff8f00","#ff6f00"]},{name:"air",selectable:!0,group:w.Linear,domain:["#e1f5fe","#b3e5fc","#81d4fa","#4fc3f7","#29b6f6","#03a9f4","#039be5","#0288d1","#0277bd","#01579b"]},{name:"aqua",selectable:!0,group:w.Linear,domain:["#e0f7fa","#b2ebf2","#80deea","#4dd0e1","#26c6da","#00bcd4","#00acc1","#0097a7","#00838f","#006064"]},{name:"flame",selectable:!1,group:w.Ordinal,domain:["#A10A28","#D3342D","#EF6D49","#FAAD67","#FDDE90","#DBED91","#A9D770","#6CBA67","#2C9653","#146738"]},{name:"ocean",selectable:!1,group:w.Ordinal,domain:["#1D68FB","#33C0FC","#4AFFFE","#AFFFFF","#FFFC63","#FDBD2D","#FC8A25","#FA4F1E","#FA141B","#BA38D1"]},{name:"forest",selectable:!1,group:w.Ordinal,domain:["#55C22D","#C1F33D","#3CC099","#AFFFFF","#8CFC9D","#76CFFA","#BA60FB","#EE6490","#C42A1C","#FC9F32"]},{name:"horizon",selectable:!1,group:w.Ordinal,domain:["#2597FB","#65EBFD","#99FDD0","#FCEE4B","#FEFCFA","#FDD6E3","#FCB1A8","#EF6F7B","#CB96E8","#EFDEE0"]},{name:"neons",selectable:!1,group:w.Ordinal,domain:["#FF3333","#FF33FF","#CC33FF","#0000FF","#33CCFF","#33FFFF","#33FF66","#CCFF33","#FFCC00","#FF6600"]},{name:"picnic",selectable:!1,group:w.Ordinal,domain:["#FAC51D","#66BD6D","#FAA026","#29BB9C","#E96B56","#55ACD2","#B7332F","#2C83C9","#9166B8","#92E7E8"]},{name:"night",selectable:!1,group:w.Ordinal,domain:["#2B1B5A","#501356","#183356","#28203F","#391B3C","#1E2B3C","#120634","#2D0432","#051932","#453080","#75267D","#2C507D","#4B3880","#752F7D","#35547D"]},{name:"nightLights",selectable:!1,group:w.Ordinal,domain:["#4e31a5","#9c25a7","#3065ab","#57468b","#904497","#46648b","#32118d","#a00fb3","#1052a2","#6e51bd","#b63cc3","#6c97cb","#8671c1","#b455be","#7496c3"]}];class _r{constructor(n,e,i,a){"string"==typeof n&&(n=xr.find(r=>r.name===n)),this.colorDomain=n.domain,this.scaleType=e,this.domain=i,this.customColors=a,this.scale=this.generateColorScheme(n,e,this.domain)}generateColorScheme(n,e,i){let a;switch("string"==typeof n&&(n=xr.find(r=>r.name===n)),e){case w.Quantile:a=Fs().range(n.domain).domain(i);break;case w.Ordinal:a=vn().range(n.domain).domain(i);break;case w.Linear:{const r=[...n.domain];1===r.length&&(r.push(r[0]),this.colorDomain=r);const o=Cs(0,1,1/r.length);a=ze().range(r).domain(o)}}return a}getColor(n){if(null==n)throw new Error("Value can not be null");if(this.scaleType===w.Linear){const e=ze().domain(this.domain).range([0,1]);return this.scale(e(n))}{if("function"==typeof this.customColors)return this.customColors(n);const e=n.toString();let i;return this.customColors&&this.customColors.length>0&&(i=this.customColors.find(a=>a.name.toLowerCase()===e.toLowerCase())),i?i.value:this.scale(n)}}getLinearGradientStops(n,e){void 0===e&&(e=this.domain[0]);const i=ze().domain(this.domain).range([0,1]),a=Xt().domain(this.colorDomain).range([0,1]),r=this.getColor(n),o=i(e),l=this.getColor(e),h=i(n);let c=1,m=o;const d=[];for(d.push({color:l,offset:o,originalOffset:o,opacity:1});m<h&&c<this.colorDomain.length;){const u=this.colorDomain[c],g=a(u);if(g<=o)c++;else{if(g.toFixed(4)>=(h-a.bandwidth()).toFixed(4))break;d.push({color:u,offset:g,opacity:1}),m=g,c++}}if(d[d.length-1].offset<100&&d.push({color:r,offset:h,opacity:1}),h===o)d[0].offset=0,d[1].offset=100;else if(100!==d[d.length-1].offset)for(const u of d)u.offset=(u.offset-o)/(h-o)*100;return d}}let Zg=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),Kg=(()=>{class t{constructor(e){this.roundEdges=!0,this.gradient=!1,this.offset=0,this.isActive=!1,this.animations=!0,this.noBarWhenZero=!0,this.select=new s.bkB,this.activate=new s.bkB,this.deactivate=new s.bkB,this.hasGradient=!1,this.hideBar=!1,this.element=e.nativeElement}ngOnChanges(e){e.roundEdges&&this.loadAnimation(),this.update()}update(){this.gradientId="grad"+st().toString(),this.gradientFill=`url(#${this.gradientId})`,this.gradient||this.stops?(this.gradientStops=this.getGradient(),this.hasGradient=!0):this.hasGradient=!1,this.updatePathEl(),this.checkToHideBar()}loadAnimation(){this.path=this.getStartingPath(),setTimeout(this.update.bind(this),100)}updatePathEl(){const e=Se(this.element).select(".bar"),i=this.getPath();this.animations?e.transition().duration(500).attr("d",i):e.attr("d",i)}getGradient(){return this.stops?this.stops:[{offset:0,color:this.fill,opacity:this.getStartOpacity()},{offset:100,color:this.fill,opacity:1}]}getStartingPath(){if(!this.animations)return this.getPath();let i,e=this.getRadius();return this.roundEdges?this.orientation===ae.Vertical?(e=Math.min(this.height,e),i=je(this.x,this.y+this.height,this.width,1,0,this.edges)):this.orientation===ae.Horizontal&&(e=Math.min(this.width,e),i=je(this.x,this.y,1,this.height,0,this.edges)):this.orientation===ae.Vertical?i=je(this.x,this.y+this.height,this.width,1,0,this.edges):this.orientation===ae.Horizontal&&(i=je(this.x,this.y,1,this.height,0,this.edges)),i}getPath(){let i,e=this.getRadius();return this.roundEdges?this.orientation===ae.Vertical?(e=Math.min(this.height,e),i=je(this.x,this.y,this.width,this.height,e,this.edges)):this.orientation===ae.Horizontal&&(e=Math.min(this.width,e),i=je(this.x,this.y,this.width,this.height,e,this.edges)):i=je(this.x,this.y,this.width,this.height,e,this.edges),i}getRadius(){let e=0;return this.roundEdges&&this.height>5&&this.width>5&&(e=Math.floor(Math.min(5,this.height/2,this.width/2))),e}getStartOpacity(){return this.roundEdges?.2:.5}get edges(){let e=[!1,!1,!1,!1];return this.roundEdges&&(this.orientation===ae.Vertical?e=this.data.value>0?[!0,!0,!1,!1]:[!1,!1,!0,!0]:this.orientation===ae.Horizontal&&(e=this.data.value>0?[!1,!0,!1,!0]:[!0,!1,!0,!1])),e}onMouseEnter(){this.activate.emit(this.data)}onMouseLeave(){this.deactivate.emit(this.data)}checkToHideBar(){this.hideBar=this.noBarWhenZero&&(this.orientation===ae.Vertical&&0===this.height||this.orientation===ae.Horizontal&&0===this.width)}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-bar",""]],hostBindings:function(e,i){1&e&&s.bIt("mouseenter",function(){return i.onMouseEnter()})("mouseleave",function(){return i.onMouseLeave()})},inputs:{fill:"fill",data:"data",width:"width",height:"height",x:"x",y:"y",orientation:"orientation",roundEdges:"roundEdges",gradient:"gradient",offset:"offset",isActive:"isActive",stops:"stops",animations:"animations",ariaLabel:"ariaLabel",noBarWhenZero:"noBarWhenZero"},outputs:{select:"select",activate:"activate",deactivate:"deactivate"},features:[s.OA$],attrs:Eu,decls:2,vars:8,consts:[[4,"ngIf"],["stroke","none","role","img","tabIndex","-1",1,"bar",3,"click"],["ngx-charts-svg-linear-gradient","",3,"orientation","name","stops"]],template:function(e,i){1&e&&(s.DNE(0,Mu,2,3,"defs",0),s.qSk(),s.j41(1,"path",1),s.bIt("click",function(){return i.select.emit(i.data)}),s.k0s()),2&e&&(s.Y8G("ngIf",i.hasGradient),s.R7$(),s.AVh("active",i.isActive)("hidden",i.hideBar),s.BMQ("d",i.path)("aria-label",i.ariaLabel)("fill",i.hasGradient?i.gradientFill:i.fill))},dependencies:[Wt,y.bT],encapsulation:2,changeDetection:0}),t})();var Ue=function(t){return t.Standard="standard",t.Normalized="normalized",t.Stacked="stacked",t}(Ue||{}),Tt=function(t){return t.positive="positive",t.negative="negative",t}(Tt||{});let Jg=(()=>{class t{constructor(e){this.dimensionsChanged=new s.bkB,this.horizontalPadding=2,this.verticalPadding=5,this.element=e.nativeElement}ngOnChanges(e){this.update()}getSize(){return{height:this.element.getBoundingClientRect().height,width:this.element.getBoundingClientRect().width,negative:this.value<0}}ngAfterViewInit(){this.dimensionsChanged.emit(this.getSize())}update(){this.formatedValue=this.valueFormatting?this.valueFormatting(this.value):Nt(this.value),"horizontal"===this.orientation?(this.x=this.barX+this.barWidth,this.value<0?(this.x=this.x-this.horizontalPadding,this.textAnchor="end"):(this.x=this.x+this.horizontalPadding,this.textAnchor="start"),this.y=this.barY+this.barHeight/2):(this.x=this.barX+this.barWidth/2,this.y=this.barY+this.barHeight,this.value<0?(this.y=this.y+this.verticalPadding,this.textAnchor="end"):(this.y=this.y-this.verticalPadding,this.textAnchor="start"),this.transform=`rotate(-45, ${this.x} , ${this.y})`)}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-bar-label",""]],inputs:{value:"value",valueFormatting:"valueFormatting",barX:"barX",barY:"barY",barWidth:"barWidth",barHeight:"barHeight",orientation:"orientation"},outputs:{dimensionsChanged:"dimensionsChanged"},features:[s.OA$],attrs:Bu,decls:2,vars:5,consts:[["alignment-baseline","middle",1,"textDataLabel"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"text",0),s.EFF(1),s.k0s()),2&e&&(s.BMQ("text-anchor",i.textAnchor)("transform",i.transform)("x",i.x)("y",i.y),s.R7$(),s.SpI(" ",i.formatedValue," "))},styles:[".textDataLabel[_ngcontent-%COMP%]{font-size:11px}"],changeDetection:0}),t})(),ep=(()=>{class t{constructor(e){this.platformId=e,this.type=Ue.Standard,this.tooltipDisabled=!1,this.animations=!0,this.showDataLabel=!1,this.noBarWhenZero=!0,this.select=new s.bkB,this.activate=new s.bkB,this.deactivate=new s.bkB,this.dataLabelHeightChanged=new s.bkB,this.barsForDataLabels=[],this.barOrientation=ae,this.isSSR=!1}ngOnInit(){(0,y.Vy)(this.platformId)&&(this.isSSR=!0)}ngOnChanges(e){this.update()}update(){let e;this.updateTooltipSettings(),this.series.length&&(e=this.xScale.bandwidth()),e=Math.round(e);const i=Math.max(this.yScale.domain()[0],0),a={[Tt.positive]:0,[Tt.negative]:0};let o,r=Tt.positive;this.type===Ue.Normalized&&(o=this.series.map(l=>l.value).reduce((l,h)=>l+h,0)),this.bars=this.series.map((l,h)=>{let c=l.value;const m=this.getLabel(l),d=Nt(m);r=c>0?Tt.positive:Tt.negative;const g={value:c,label:m,roundEdges:this.roundEdges,data:l,width:e,formattedLabel:d,height:0,x:0,y:0};if(this.type===Ue.Standard)g.height=Math.abs(this.yScale(c)-this.yScale(i)),g.x=this.xScale(m),g.y=this.yScale(c<0?0:c);else if(this.type===Ue.Stacked){const b=a[r],p=b+c;a[r]+=c,g.height=this.yScale(b)-this.yScale(p),g.x=0,g.y=this.yScale(p),g.offset0=b,g.offset1=p}else if(this.type===Ue.Normalized){let b=a[r],p=b+c;a[r]+=c,o>0?(b=100*b/o,p=100*p/o):(b=0,p=0),g.height=this.yScale(b)-this.yScale(p),g.x=0,g.y=this.yScale(p),g.offset0=b,g.offset1=p,c=(p-b).toFixed(2)+"%"}this.colors.scaleType===w.Ordinal?g.color=this.colors.getColor(m):this.type===Ue.Standard?(g.color=this.colors.getColor(c),g.gradientStops=this.colors.getLinearGradientStops(c)):(g.color=this.colors.getColor(g.offset1),g.gradientStops=this.colors.getLinearGradientStops(g.offset1,g.offset0));let f=d;return g.ariaLabel=d+" "+c.toLocaleString(),null!=this.seriesName&&(f=`${this.seriesName} \u2022 ${d}`,g.data.series=this.seriesName,g.ariaLabel=this.seriesName+" "+g.ariaLabel),g.tooltipText=this.tooltipDisabled?void 0:`\n        <span class="tooltip-label">${wn(f)}</span>\n        <span class="tooltip-val">${this.dataLabelFormatting?this.dataLabelFormatting(c):c.toLocaleString()}</span>\n      `,g}),this.updateDataLabels()}updateDataLabels(){if(this.type===Ue.Stacked){this.barsForDataLabels=[];const e={};e.series=this.seriesName;const i=this.series.map(r=>r.value).reduce((r,o)=>o>0?r+o:r,0),a=this.series.map(r=>r.value).reduce((r,o)=>o<0?r+o:r,0);e.total=i+a,e.x=0,e.y=0,e.height=this.yScale(e.total>0?i:a),e.width=this.xScale.bandwidth(),this.barsForDataLabels.push(e)}else this.barsForDataLabels=this.series.map(e=>{const i={};return i.series=this.seriesName??e.label,i.total=e.value,i.x=this.xScale(e.label),i.y=this.yScale(0),i.height=this.yScale(i.total)-this.yScale(0),i.width=this.xScale.bandwidth(),i})}updateTooltipSettings(){this.tooltipPlacement=this.tooltipDisabled?void 0:R.Top,this.tooltipType=this.tooltipDisabled?void 0:zt.tooltip}isActive(e){return!!this.activeEntries&&void 0!==this.activeEntries.find(a=>e.name===a.name&&e.value===a.value)}onClick(e){this.select.emit(e)}getLabel(e){return e.label?e.label:e.name}trackBy(e,i){return i.label}trackDataLabelBy(e,i){return e+"#"+i.series+"#"+i.total}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-series-vertical",""]],inputs:{dims:"dims",type:"type",series:"series",xScale:"xScale",yScale:"yScale",colors:"colors",gradient:"gradient",activeEntries:"activeEntries",seriesName:"seriesName",tooltipDisabled:"tooltipDisabled",tooltipTemplate:"tooltipTemplate",roundEdges:"roundEdges",animations:"animations",showDataLabel:"showDataLabel",dataLabelFormatting:"dataLabelFormatting",noBarWhenZero:"noBarWhenZero"},outputs:{select:"select",activate:"activate",deactivate:"deactivate",dataLabelHeightChanged:"dataLabelHeightChanged"},features:[s.OA$],attrs:Hu,decls:3,vars:3,consts:[[4,"ngIf"],["ngx-charts-bar","","ngx-tooltip","",3,"width","height","x","y","fill","stops","data","orientation","roundEdges","gradient","ariaLabel","isActive","tooltipDisabled","tooltipPlacement","tooltipType","tooltipTitle","tooltipTemplate","tooltipContext","noBarWhenZero","animations","select","activate","deactivate",4,"ngFor","ngForOf","ngForTrackBy"],["ngx-charts-bar","","ngx-tooltip","",3,"select","activate","deactivate","width","height","x","y","fill","stops","data","orientation","roundEdges","gradient","ariaLabel","isActive","tooltipDisabled","tooltipPlacement","tooltipType","tooltipTitle","tooltipTemplate","tooltipContext","noBarWhenZero","animations"],["ngx-charts-bar-label","",3,"barX","barY","barWidth","barHeight","value","valueFormatting","orientation","dimensionsChanged",4,"ngFor","ngForOf","ngForTrackBy"],["ngx-charts-bar-label","",3,"dimensionsChanged","barX","barY","barWidth","barHeight","value","valueFormatting","orientation"]],template:function(e,i){1&e&&s.DNE(0,Yu,2,2,"g",0)(1,zu,2,2,"g",0)(2,ju,2,2,"g",0),2&e&&(s.Y8G("ngIf",!i.isSSR),s.R7$(),s.Y8G("ngIf",i.isSSR),s.R7$(),s.Y8G("ngIf",i.showDataLabel))},dependencies:[Kg,Jg,y.bT,y.Sq,Ln],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)(":leave",[(0,_.iF)({opacity:1}),(0,_.i0)(500,(0,_.iF)({opacity:0}))])])]},changeDetection:0}),t})(),tp=(()=>{class t extends kn{constructor(){super(...arguments),this.legend=!1,this.legendTitle="Legend",this.legendPosition=Ye.Right,this.tooltipDisabled=!1,this.scaleType=w.Ordinal,this.showGridLines=!0,this.activeEntries=[],this.trimXAxisTicks=!0,this.trimYAxisTicks=!0,this.rotateXAxisTicks=!0,this.maxXAxisTickLength=16,this.maxYAxisTickLength=16,this.groupPadding=16,this.barPadding=8,this.roundDomains=!1,this.roundEdges=!0,this.showDataLabel=!1,this.noBarWhenZero=!0,this.wrapTicks=!1,this.activate=new s.bkB,this.deactivate=new s.bkB,this.margin=[10,20,10,20],this.xAxisHeight=0,this.yAxisWidth=0,this.dataLabelMaxHeight={negative:0,positive:0},this.isSSR=!1,this.barOrientation=ae,this.trackBy=(e,i)=>i.name}ngOnInit(){(0,y.Vy)(this.platformId)&&(this.isSSR=!0)}update(){super.update(),this.showDataLabel||(this.dataLabelMaxHeight={negative:0,positive:0}),this.margin=[10+this.dataLabelMaxHeight.positive,20,10+this.dataLabelMaxHeight.negative,20],this.dims=fr({width:this.width,height:this.height,margins:this.margin,showXAxis:this.xAxis,showYAxis:this.yAxis,xAxisHeight:this.xAxisHeight,yAxisWidth:this.yAxisWidth,showXLabel:this.showXAxisLabel,showYLabel:this.showYAxisLabel,showLegend:this.legend,legendType:this.schemeType,legendPosition:this.legendPosition}),this.showDataLabel&&(this.dims.height-=this.dataLabelMaxHeight.negative),this.formatDates(),this.groupDomain=this.getGroupDomain(),this.innerDomain=this.getInnerDomain(),this.valueDomain=this.getValueDomain(),this.groupScale=this.getGroupScale(),this.innerScale=this.getInnerScale(),this.valueScale=this.getValueScale(),this.setColors(),this.legendOptions=this.getLegendOptions(),this.transform=`translate(${this.dims.xOffset} , ${this.margin[0]+this.dataLabelMaxHeight.negative})`}onDataLabelMaxHeightChanged(e,i){e.size.negative?this.dataLabelMaxHeight.negative=Math.max(this.dataLabelMaxHeight.negative,e.size.height):this.dataLabelMaxHeight.positive=Math.max(this.dataLabelMaxHeight.positive,e.size.height),i===this.results.length-1&&setTimeout(()=>this.update())}getGroupScale(){const e=this.groupDomain.length/(this.dims.height/this.groupPadding+1);return Xt().rangeRound([0,this.dims.width]).paddingInner(e).paddingOuter(e/2).domain(this.groupDomain)}getInnerScale(){const e=this.groupScale.bandwidth(),i=this.innerDomain.length/(e/this.barPadding+1);return Xt().rangeRound([0,e]).paddingInner(i).domain(this.innerDomain)}getValueScale(){const e=ze().range([this.dims.height,0]).domain(this.valueDomain);return this.roundDomains?e.nice():e}getGroupDomain(){const e=[];for(const i of this.results)e.includes(i.label)||e.push(i.label);return e}getInnerDomain(){const e=[];for(const i of this.results)for(const a of i.series)e.includes(a.label)||e.push(a.label);return e}getValueDomain(){const e=[];for(const r of this.results)for(const o of r.series)e.includes(o.value)||e.push(o.value);return[Math.min(0,...e),this.yScaleMax?Math.max(this.yScaleMax,...e):Math.max(0,...e)]}groupTransform(e){return`translate(${this.groupScale(e.label)}, 0)`}onClick(e,i){i&&(e.series=i.name),this.select.emit(e)}setColors(){let e;e=this.schemeType===w.Ordinal?this.innerDomain:this.valueDomain,this.colors=new _r(this.scheme,this.schemeType,e,this.customColors)}getLegendOptions(){const e={scaleType:this.schemeType,colors:void 0,domain:[],title:void 0,position:this.legendPosition};return e.scaleType===w.Ordinal?(e.domain=this.innerDomain,e.colors=this.colors,e.title=this.legendTitle):(e.domain=this.valueDomain,e.colors=this.colors.scale),e}updateYAxisWidth({width:e}){this.yAxisWidth=e,this.update()}updateXAxisHeight({height:e}){this.xAxisHeight=e,this.update()}onActivate(e,i,a=!1){const r=Object.assign({},e);i&&(r.series=i.name);const o=this.results.map(l=>l.series).flat().filter(l=>a?l.label===r.name:l.name===r.name&&l.series===r.series);this.activeEntries=[...o],this.activate.emit({value:r,entries:this.activeEntries})}onDeactivate(e,i,a=!1){const r=Object.assign({},e);i&&(r.series=i.name),this.activeEntries=this.activeEntries.filter(o=>a?o.label!==r.name:!(o.name===r.name&&o.series===r.series)),this.deactivate.emit({value:r,entries:this.activeEntries})}}return t.\u0275fac=(()=>{let n;return function(i){return(n||(n=s.xGo(t)))(i||t)}})(),t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-bar-vertical-2d"]],contentQueries:function(e,i,a){if(1&e&&s.wni(a,Hs,5),2&e){let r;s.mGM(r=s.lsd())&&(i.tooltipTemplate=r.first)}},inputs:{legend:"legend",legendTitle:"legendTitle",legendPosition:"legendPosition",xAxis:"xAxis",yAxis:"yAxis",showXAxisLabel:"showXAxisLabel",showYAxisLabel:"showYAxisLabel",xAxisLabel:"xAxisLabel",yAxisLabel:"yAxisLabel",tooltipDisabled:"tooltipDisabled",scaleType:"scaleType",gradient:"gradient",showGridLines:"showGridLines",activeEntries:"activeEntries",schemeType:"schemeType",trimXAxisTicks:"trimXAxisTicks",trimYAxisTicks:"trimYAxisTicks",rotateXAxisTicks:"rotateXAxisTicks",maxXAxisTickLength:"maxXAxisTickLength",maxYAxisTickLength:"maxYAxisTickLength",xAxisTickFormatting:"xAxisTickFormatting",yAxisTickFormatting:"yAxisTickFormatting",xAxisTicks:"xAxisTicks",yAxisTicks:"yAxisTicks",groupPadding:"groupPadding",barPadding:"barPadding",roundDomains:"roundDomains",roundEdges:"roundEdges",yScaleMax:"yScaleMax",showDataLabel:"showDataLabel",dataLabelFormatting:"dataLabelFormatting",noBarWhenZero:"noBarWhenZero",wrapTicks:"wrapTicks"},outputs:{activate:"activate",deactivate:"deactivate"},features:[s.Vt3],decls:7,vars:18,consts:[[3,"legendLabelActivate","legendLabelDeactivate","legendLabelClick","view","showLegend","legendOptions","activeEntries","animations"],[1,"bar-chart","chart"],["ngx-charts-grid-panel-series","",3,"xScale","yScale","data","dims","orient"],["ngx-charts-x-axis","",3,"xScale","dims","showLabel","labelText","trimTicks","rotateTicks","maxTickLength","tickFormatting","ticks","xAxisOffset","wrapTicks","dimensionsChanged",4,"ngIf"],["ngx-charts-y-axis","",3,"yScale","dims","showGridLines","showLabel","labelText","trimTicks","maxTickLength","tickFormatting","ticks","wrapTicks","dimensionsChanged",4,"ngIf"],[4,"ngIf"],["ngx-charts-x-axis","",3,"dimensionsChanged","xScale","dims","showLabel","labelText","trimTicks","rotateTicks","maxTickLength","tickFormatting","ticks","xAxisOffset","wrapTicks"],["ngx-charts-y-axis","",3,"dimensionsChanged","yScale","dims","showGridLines","showLabel","labelText","trimTicks","maxTickLength","tickFormatting","ticks","wrapTicks"],["ngx-charts-series-vertical","",3,"activeEntries","xScale","yScale","colors","series","dims","gradient","tooltipDisabled","tooltipTemplate","showDataLabel","dataLabelFormatting","seriesName","roundEdges","animations","noBarWhenZero","select","activate","deactivate","dataLabelHeightChanged",4,"ngFor","ngForOf","ngForTrackBy"],["ngx-charts-series-vertical","",3,"select","activate","deactivate","dataLabelHeightChanged","activeEntries","xScale","yScale","colors","series","dims","gradient","tooltipDisabled","tooltipTemplate","showDataLabel","dataLabelFormatting","seriesName","roundEdges","animations","noBarWhenZero"]],template:function(e,i){1&e&&(s.j41(0,"ngx-charts-chart",0),s.bIt("legendLabelActivate",function(r){return i.onActivate(r,void 0,!0)})("legendLabelDeactivate",function(r){return i.onDeactivate(r,void 0,!0)})("legendLabelClick",function(r){return i.onClick(r)}),s.qSk(),s.j41(1,"g",1),s.nrm(2,"g",2),s.DNE(3,Uu,1,11,"g",3)(4,Qu,1,10,"g",4)(5,Zu,2,2,"g",5),s.k0s(),s.DNE(6,Ju,2,2,"g",5),s.k0s()),2&e&&(s.Y8G("view",s.l_i(15,vt,i.width,i.height))("showLegend",i.legend)("legendOptions",i.legendOptions)("activeEntries",i.activeEntries)("animations",i.animations),s.R7$(),s.BMQ("transform",i.transform),s.R7$(),s.Y8G("xScale",i.groupScale)("yScale",i.valueScale)("data",i.results)("dims",i.dims)("orient",i.barOrientation.Vertical),s.R7$(),s.Y8G("ngIf",i.xAxis),s.R7$(),s.Y8G("ngIf",i.yAxis),s.R7$(),s.Y8G("ngIf",!i.isSSR),s.R7$(),s.Y8G("ngIf",i.isSSR))},dependencies:[Sn,cr,er,ir,ep,y.bT,y.Sq],styles:[Xs],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)(":leave",[(0,_.iF)({opacity:1,transform:"*"}),(0,_.i0)(500,(0,_.iF)({opacity:0,transform:"scale(0)"}))])])]},changeDetection:0}),t})(),yr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})();rm();let ip=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),np=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),ap=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),sp=(()=>{class t{constructor(e,i){this.element=e,this.platformId=i,this.fill="none",this.animations=!0,this.initialized=!1,this.isSSR=!1}ngOnInit(){(0,y.Vy)(this.platformId)&&(this.isSSR=!0)}ngOnChanges(e){this.initialized?this.updatePathEl():(this.initialized=!0,this.initialPath=this.path)}updatePathEl(){const e=Se(this.element.nativeElement).select(".line");this.animations?e.transition().duration(750).attr("d",this.path):e.attr("d",this.path)}}return t.\u0275fac=function(e){return new(e||t)(s.rXU(s.aKT),s.rXU(s.Agw))},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-line",""]],inputs:{path:"path",stroke:"stroke",data:"data",fill:"fill",animations:"animations"},features:[s.OA$],attrs:cg,decls:2,vars:2,consts:[[4,"ngIf"],["stroke-width","1.5px",1,"line"]],template:function(e,i){1&e&&s.DNE(0,hg,2,4,"g",0)(1,dg,2,3,"g",0),2&e&&(s.Y8G("ngIf",!i.isSSR),s.R7$(),s.Y8G("ngIf",i.isSSR))},dependencies:[y.bT],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)(":enter",[(0,_.iF)({strokeDasharray:2e3,strokeDashoffset:2e3}),(0,_.i0)(1e3,(0,_.iF)({strokeDashoffset:0}))])])]},changeDetection:0}),t})(),rp=(()=>{class t{constructor(){this.animations=!0,this.barOrientation=ae}ngOnChanges(e){this.update()}update(){this.updateGradients();const e=this.sortData(this.data.series),i=this.getLineGenerator();this.path=i(e)||"";const a=this.getAreaGenerator();if(this.areaPath=a(e)||"",this.hasRange){const r=this.getRangeGenerator();this.outerPath=r(e)||""}if(this.hasGradient){this.stroke=this.gradientUrl;const r=this.data.series.map(h=>h.value),o=Math.max(...r);o===Math.min(...r)&&(this.stroke=this.colors.getColor(o))}else this.stroke=this.colors.getColor(this.data.name)}getLineGenerator(){return Ps().x(e=>{const i=e.name;let a;return a=this.xScale(this.scaleType===w.Time?i:this.scaleType===w.Linear?Number(i):i),a}).y(e=>this.yScale(e.value)).curve(this.curve)}getRangeGenerator(){return Gs().x(e=>{const i=e.name;let a;return a=this.xScale(this.scaleType===w.Time?i:this.scaleType===w.Linear?Number(i):i),a}).y0(e=>this.yScale("number"==typeof e.min?e.min:e.value)).y1(e=>this.yScale("number"==typeof e.max?e.max:e.value)).curve(this.curve)}getAreaGenerator(){return Gs().x(i=>this.xScale(i.name)).y0(()=>this.yScale.range()[0]).y1(i=>this.yScale(i.value)).curve(this.curve)}sortData(e){return e=this.scaleType===w.Linear?function Ug(t,n,e="asc"){return t.sort((i,a)=>"asc"===e?i[n]-a[n]:a[n]-i[n])}(e,"name"):this.scaleType===w.Time?function qg(t,n,e="asc"){return t.sort((i,a)=>{const r=i[n].getTime(),o=a[n].getTime();return"asc"===e?r>o?1:o>r?-1:0:r>o?-1:o>r?1:0})}(e,"name"):function Qg(t,n,e="asc",i){return t.sort((a,r)=>{const l=r[n],h=i.indexOf(a[n]),c=i.indexOf(l);return"asc"===e?h-c:c-h})}(e,"name","asc",this.xScale.domain()),e}updateGradients(){if(this.colors.scaleType===w.Linear){this.hasGradient=!0,this.gradientId="grad"+st().toString(),this.gradientUrl=`url(#${this.gradientId})`;const e=this.data.series.map(r=>r.value),i=Math.max(...e),a=Math.min(...e);this.gradientStops=this.colors.getLinearGradientStops(i,a),this.areaGradientStops=this.colors.getLinearGradientStops(i)}else this.hasGradient=!1,this.gradientStops=void 0,this.areaGradientStops=void 0}isActive(e){return!!this.activeEntries&&void 0!==this.activeEntries.find(a=>e.name===a.name)}isInactive(e){return!(!this.activeEntries||0===this.activeEntries.length)&&void 0===this.activeEntries.find(a=>e.name===a.name)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=s.VBU({type:t,selectors:[["g","ngx-charts-line-series",""]],inputs:{data:"data",xScale:"xScale",yScale:"yScale",colors:"colors",scaleType:"scaleType",curve:"curve",activeEntries:"activeEntries",rangeFillOpacity:"rangeFillOpacity",hasRange:"hasRange",animations:"animations"},features:[s.OA$],attrs:mg,decls:6,vars:22,consts:[["ngx-charts-svg-linear-gradient","",3,"orientation","name","stops",4,"ngIf"],["ngx-charts-area","",1,"line-highlight",3,"data","path","fill","opacity","startOpacity","gradient","stops","animations"],["ngx-charts-line","",1,"line-series",3,"data","path","stroke","animations"],["ngx-charts-area","","class","line-series-range",3,"data","path","fill","active","inactive","opacity","animations",4,"ngIf"],["ngx-charts-svg-linear-gradient","",3,"orientation","name","stops"],["ngx-charts-area","",1,"line-series-range",3,"data","path","fill","opacity","animations"]],template:function(e,i){1&e&&(s.qSk(),s.j41(0,"g")(1,"defs"),s.DNE(2,ug,1,3,"g",0),s.k0s(),s.nrm(3,"g",1)(4,"g",2),s.DNE(5,gg,1,9,"g",3),s.k0s()),2&e&&(s.R7$(2),s.Y8G("ngIf",i.hasGradient),s.R7$(),s.AVh("active",i.isActive(i.data))("inactive",i.isInactive(i.data)),s.Y8G("data",i.data)("path",i.areaPath)("fill",i.hasGradient?i.gradientUrl:i.colors.getColor(i.data.name))("opacity",.25)("startOpacity",0)("gradient",!0)("stops",i.areaGradientStops)("animations",i.animations),s.R7$(),s.AVh("active",i.isActive(i.data))("inactive",i.isInactive(i.data)),s.Y8G("data",i.data)("path",i.path)("stroke",i.stroke)("animations",i.animations),s.R7$(),s.Y8G("ngIf",i.hasRange))},dependencies:[Wt,hr,sp,y.bT],encapsulation:2,changeDetection:0}),t})(),op=(()=>{class t extends kn{constructor(){super(...arguments),this.legendTitle="Legend",this.legendPosition=Ye.Right,this.showGridLines=!0,this.curve=yn,this.activeEntries=[],this.trimXAxisTicks=!0,this.trimYAxisTicks=!0,this.rotateXAxisTicks=!0,this.maxXAxisTickLength=16,this.maxYAxisTickLength=16,this.roundDomains=!1,this.tooltipDisabled=!1,this.showRefLines=!1,this.showRefLabels=!0,this.wrapTicks=!1,this.activate=new s.bkB,this.deactivate=new s.bkB,this.margin=[10,20,10,20],this.xAxisHeight=0,this.yAxisWidth=0,this.timelineHeight=50,this.timelinePadding=10,this.isSSR=!1}ngOnInit(){(0,y.Vy)(this.platformId)&&(this.isSSR=!0)}update(){super.update(),this.dims=fr({width:this.width,height:this.height,margins:this.margin,showXAxis:this.xAxis,showYAxis:this.yAxis,xAxisHeight:this.xAxisHeight,yAxisWidth:this.yAxisWidth,showXLabel:this.showXAxisLabel,showYLabel:this.showYAxisLabel,showLegend:this.legend,legendType:this.schemeType,legendPosition:this.legendPosition}),this.timeline&&(this.dims.height-=this.timelineHeight+this.margin[2]+this.timelinePadding),this.xDomain=this.getXDomain(),this.filteredDomain&&(this.xDomain=this.filteredDomain),this.yDomain=this.getYDomain(),this.seriesDomain=this.getSeriesDomain(),this.xScale=this.getXScale(this.xDomain,this.dims.width),this.yScale=this.getYScale(this.yDomain,this.dims.height),this.updateTimeline(),this.setColors(),this.legendOptions=this.getLegendOptions(),this.transform=`translate(${this.dims.xOffset} , ${this.margin[0]})`,this.clipPathId="clip"+st().toString(),this.clipPath=`url(#${this.clipPathId})`}updateTimeline(){this.timeline&&(this.timelineWidth=this.dims.width,this.timelineXDomain=this.getXDomain(),this.timelineXScale=this.getXScale(this.timelineXDomain,this.timelineWidth),this.timelineYScale=this.getYScale(this.yDomain,this.timelineHeight),this.timelineTransform=`translate(${this.dims.xOffset}, ${-this.margin[2]})`)}getXDomain(){let e=function jg(t){const n=new Set;for(const e of t)for(const i of e.series)n.add(i.name);return Array.from(n)}(this.results);this.scaleType=function vr(t,n=!0){return n&&t.every(a=>a instanceof Date)?w.Time:t.every(i=>"number"==typeof i)?w.Linear:w.Ordinal}(e);let a,r,i=[];return this.scaleType===w.Linear&&(e=e.map(o=>Number(o))),(this.scaleType===w.Time||this.scaleType===w.Linear)&&(a=this.xScaleMin?this.xScaleMin:Math.min(...e),r=this.xScaleMax?this.xScaleMax:Math.max(...e)),this.scaleType===w.Time?(i=[new Date(a),new Date(r)],this.xSet=[...e].sort((o,l)=>{const h=o.getTime(),c=l.getTime();return h>c?1:c>h?-1:0})):this.scaleType===w.Linear?(i=[a,r],this.xSet=[...e].sort((o,l)=>o-l)):(i=e,this.xSet=e),i}getYDomain(){const e=[];for(const o of this.results)for(const l of o.series)e.indexOf(l.value)<0&&e.push(l.value),void 0!==l.min&&(this.hasRange=!0,e.indexOf(l.min)<0&&e.push(l.min)),void 0!==l.max&&(this.hasRange=!0,e.indexOf(l.max)<0&&e.push(l.max));const i=[...e];return this.autoScale||i.push(0),[this.yScaleMin?this.yScaleMin:Math.min(...i),this.yScaleMax?this.yScaleMax:Math.max(...i)]}getSeriesDomain(){return this.results.map(e=>e.name)}getXScale(e,i){let a;return this.scaleType===w.Time?a=us().range([0,i]).domain(e):this.scaleType===w.Linear?(a=ze().range([0,i]).domain(e),this.roundDomains&&(a=a.nice())):this.scaleType===w.Ordinal&&(a=Ds().range([0,i]).padding(.1).domain(e)),a}getYScale(e,i){const a=ze().range([i,0]).domain(e);return this.roundDomains?a.nice():a}updateDomain(e){this.filteredDomain=e,this.xDomain=this.filteredDomain,this.xScale=this.getXScale(this.xDomain,this.dims.width)}updateHoveredVertical(e){this.hoveredVertical=e.value,this.deactivateAll()}hideCircles(){this.hoveredVertical=null,this.deactivateAll()}onClick(e){this.select.emit(e)}trackBy(e,i){return`${i.name}`}setColors(){let e;e=this.schemeType===w.Ordinal?this.seriesDomain:this.yDomain,this.colors=new _r(this.scheme,this.schemeType,e,this.customColors)}getLegendOptions(){const e={scaleType:this.schemeType,colors:void 0,domain:[],title:void 0,position:this.legendPosition};return e.scaleType===w.Ordinal?(e.domain=this.seriesDomain,e.colors=this.colors,e.title=this.legendTitle):(e.domain=this.yDomain,e.colors=this.colors.scale),e}updateYAxisWidth({width:e}){this.yAxisWidth=e,this.update()}updateXAxisHeight({height:e}){this.xAxisHeight=e,this.update()}onActivate(e){this.deactivateAll(),!(this.activeEntries.findIndex(a=>a.name===e.name&&a.value===e.value)>-1)&&(this.activeEntries=[e],this.activate.emit({value:e,entries:this.activeEntries}))}onDeactivate(e){const i=this.activeEntries.findIndex(a=>a.name===e.name&&a.value===e.value);this.activeEntries.splice(i,1),this.activeEntries=[...this.activeEntries],this.deactivate.emit({value:e,entries:this.activeEntries})}deactivateAll(){this.activeEntries=[...this.activeEntries];for(const e of this.activeEntries)this.deactivate.emit({value:e,entries:[]});this.activeEntries=[]}}return t.\u0275fac=(()=>{let n;return function(i){return(n||(n=s.xGo(t)))(i||t)}})(),t.\u0275cmp=s.VBU({type:t,selectors:[["ngx-charts-line-chart"]],contentQueries:function(e,i,a){if(1&e&&(s.wni(a,Hs,5),s.wni(a,wu,5)),2&e){let r;s.mGM(r=s.lsd())&&(i.tooltipTemplate=r.first),s.mGM(r=s.lsd())&&(i.seriesTooltipTemplate=r.first)}},hostBindings:function(e,i){1&e&&s.bIt("mouseleave",function(){return i.hideCircles()})},inputs:{legend:"legend",legendTitle:"legendTitle",legendPosition:"legendPosition",xAxis:"xAxis",yAxis:"yAxis",showXAxisLabel:"showXAxisLabel",showYAxisLabel:"showYAxisLabel",xAxisLabel:"xAxisLabel",yAxisLabel:"yAxisLabel",autoScale:"autoScale",timeline:"timeline",gradient:"gradient",showGridLines:"showGridLines",curve:"curve",activeEntries:"activeEntries",schemeType:"schemeType",rangeFillOpacity:"rangeFillOpacity",trimXAxisTicks:"trimXAxisTicks",trimYAxisTicks:"trimYAxisTicks",rotateXAxisTicks:"rotateXAxisTicks",maxXAxisTickLength:"maxXAxisTickLength",maxYAxisTickLength:"maxYAxisTickLength",xAxisTickFormatting:"xAxisTickFormatting",yAxisTickFormatting:"yAxisTickFormatting",xAxisTicks:"xAxisTicks",yAxisTicks:"yAxisTicks",roundDomains:"roundDomains",tooltipDisabled:"tooltipDisabled",showRefLines:"showRefLines",referenceLines:"referenceLines",showRefLabels:"showRefLabels",xScaleMin:"xScaleMin",xScaleMax:"xScaleMax",yScaleMin:"yScaleMin",yScaleMax:"yScaleMax",wrapTicks:"wrapTicks"},outputs:{activate:"activate",deactivate:"deactivate"},features:[s.Vt3],decls:12,vars:20,consts:[[3,"legendLabelClick","legendLabelActivate","legendLabelDeactivate","view","showLegend","legendOptions","activeEntries","animations"],[1,"line-chart","chart"],["ngx-charts-x-axis","",3,"xScale","dims","showGridLines","showLabel","labelText","trimTicks","rotateTicks","maxTickLength","tickFormatting","ticks","wrapTicks","dimensionsChanged",4,"ngIf"],["ngx-charts-y-axis","",3,"yScale","dims","showGridLines","showLabel","labelText","trimTicks","maxTickLength","tickFormatting","ticks","referenceLines","showRefLines","showRefLabels","wrapTicks","dimensionsChanged",4,"ngIf"],[4,"ngIf"],[3,"mouseleave",4,"ngIf"],["ngx-charts-timeline","",3,"results","view","height","scheme","customColors","scaleType","legend","onDomainChange",4,"ngIf"],["ngx-charts-x-axis","",3,"dimensionsChanged","xScale","dims","showGridLines","showLabel","labelText","trimTicks","rotateTicks","maxTickLength","tickFormatting","ticks","wrapTicks"],["ngx-charts-y-axis","",3,"dimensionsChanged","yScale","dims","showGridLines","showLabel","labelText","trimTicks","maxTickLength","tickFormatting","ticks","referenceLines","showRefLines","showRefLabels","wrapTicks"],[4,"ngFor","ngForOf","ngForTrackBy"],["ngx-charts-line-series","",3,"xScale","yScale","colors","data","activeEntries","scaleType","curve","rangeFillOpacity","hasRange","animations"],[3,"mouseleave"],["ngx-charts-tooltip-area","",3,"hover","dims","xSet","xScale","yScale","results","colors","tooltipDisabled","tooltipTemplate"],[4,"ngFor","ngForOf"],["ngx-charts-circle-series","",3,"select","activate","deactivate","xScale","yScale","colors","data","scaleType","visibleValue","activeEntries","tooltipDisabled","tooltipTemplate"],["ngx-charts-timeline","",3,"onDomainChange","results","view","height","scheme","customColors","scaleType","legend"],["ngx-charts-line-series","",3,"xScale","yScale","colors","data","scaleType","curve","hasRange","animations"]],template:function(e,i){1&e&&(s.j41(0,"ngx-charts-chart",0),s.bIt("legendLabelClick",function(r){return i.onClick(r)})("legendLabelActivate",function(r){return i.onActivate(r)})("legendLabelDeactivate",function(r){return i.onDeactivate(r)}),s.qSk(),s.j41(1,"defs")(2,"clipPath"),s.nrm(3,"rect"),s.k0s()(),s.j41(4,"g",1),s.DNE(5,pg,1,11,"g",2)(6,fg,1,13,"g",3),s.j41(7,"g"),s.DNE(8,_g,2,2,"g",4)(9,yg,2,2,"g",4)(10,Cg,3,9,"g",5),s.k0s()(),s.DNE(11,wg,2,13,"g",6),s.k0s()),2&e&&(s.Y8G("view",s.l_i(17,vt,i.width,i.height))("showLegend",i.legend)("legendOptions",i.legendOptions)("activeEntries",i.activeEntries)("animations",i.animations),s.R7$(2),s.BMQ("id",i.clipPathId),s.R7$(),s.BMQ("width",i.dims.width+10)("height",i.dims.height+10)("transform","translate(-5, -5)"),s.R7$(),s.BMQ("transform",i.transform),s.R7$(),s.Y8G("ngIf",i.xAxis),s.R7$(),s.Y8G("ngIf",i.yAxis),s.R7$(),s.BMQ("clip-path",i.clipPath),s.R7$(),s.Y8G("ngIf",!i.isSSR),s.R7$(),s.Y8G("ngIf",i.isSSR),s.R7$(),s.Y8G("ngIf",!i.tooltipDisabled),s.R7$(),s.Y8G("ngIf",i.timeline&&"ordinal"!=i.scaleType))},dependencies:[Sn,er,ir,rp,gr,or,pr,y.bT,y.Sq],styles:[Xs],encapsulation:2,data:{animation:[(0,_.hZ)("animationState",[(0,_.kY)(":leave",[(0,_.iF)({opacity:1}),(0,_.i0)(500,(0,_.iF)({opacity:0}))])])]},changeDetection:0}),t})(),Tr=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})();Math;let En=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),hp=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae,En,Tr]]}),t})(),mp=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),up=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),pp=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae,En,yr]]}),t})(),xp=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[[Ae]]}),t})(),Cr=(()=>{class t{constructor(){!function fp(){typeof SVGElement<"u"&&typeof SVGElement.prototype.contains>"u"&&(SVGElement.prototype.contains=HTMLDivElement.prototype.contains)}()}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=s.$C({type:t}),t.\u0275inj=s.G2t({imports:[Ae,Zg,yr,ip,np,ap,xp,Tr,hp,mp,En,up,pp]}),t})();var br=G(5644);let _p=(()=>{var t;class n{constructor(){(0,E.A)(this,"currentYear",(new Date).getFullYear()),(0,E.A)(this,"_invoiceService",(0,s.WQX)(br.p)),(0,E.A)(this,"destroyRef",(0,s.WQX)(s.abz)),(0,E.A)(this,"chartDataMock",[{name:"Invoice",series:[{name:new Date(2017,0,1,2,34,17),value:294},{name:new Date(2017,1,1,2,34,17),value:314},{name:new Date(2017,2,1,2,34,17),value:372},{name:new Date(2017,3,1,2,34,17),value:423},{name:new Date(2017,4,1,2,34,17),value:332}]},{name:"Expenses",series:[{name:new Date(2017,0,1,2,34,17),value:2942},{name:new Date(2017,1,1,2,34,17),value:3114},{name:new Date(2017,2,1,2,34,17),value:272},{name:new Date(2017,3,1,2,34,17),value:1423},{name:new Date(2017,4,1,2,34,17),value:1332}]}]),(0,E.A)(this,"chartData",[]),(0,E.A)(this,"lineChartProps",{view:[1100,300],autoScale:!0,timeline:!0,results:this.chartData,gradient:!0,xAxis:!0,yAxis:!0,legend:!0,legendTitle:"Legend",showXAxisLabel:!0,showYAxisLabel:!0,xAxisLabel:"Time",yAxisLabel:"Value"})}GetRevenueChart(i){this._invoiceService.GraphicsChart(i).pipe((0,Y.pQ)(this.destroyRef)).subscribe(a=>{if(a){const r=[{name:"Invoice",series:a.monthDataInvoice.map(o=>({name:o.monthName,value:o.paidAmount}))},{name:"Expenses",series:a.monthDataExpenses.map(o=>({name:o.monthName,value:o.paidAmount}))}];this.chartData=r,this.lineChartProps.results=this.chartData}})}ngOnInit(){this.GetRevenueChart(this.currentYear)}}return t=n,(0,E.A)(n,"\u0275fac",function(i){return new(i||t)}),(0,E.A)(n,"\u0275cmp",s.VBU({type:t,selectors:[["app-graphics-chart"]],standalone:!0,features:[s.aNF],decls:7,vars:15,consts:[[1,"flex","items-center","justify-between","p-4"],[1,"text-gray-500","font-bold"],[2,"display","grid"],[1,"container","overflow-auto"],[3,"results","view","autoScale","timeline","gradient","xAxis","yAxis","legend","showXAxisLabel","showYAxisLabel","xAxisLabel","yAxisLabel"]],template:function(i,a){1&i&&(s.j41(0,"div",0)(1,"h4",1),s.EFF(2),s.nI1(3,"translate"),s.k0s()(),s.j41(4,"div",2)(5,"div",3),s.nrm(6,"ngx-charts-line-chart",4),s.k0s()()),2&i&&(s.R7$(2),s.JRh(s.bMT(3,13,"DASHBOARD.TitleChartGraphics")),s.R7$(4),s.Y8G("results",a.lineChartProps.results)("view",a.lineChartProps.view)("autoScale",a.lineChartProps.autoScale)("timeline",a.lineChartProps.timeline)("gradient",a.lineChartProps.gradient)("xAxis",a.lineChartProps.xAxis)("yAxis",a.lineChartProps.yAxis)("legend",a.lineChartProps.legend)("showXAxisLabel",a.lineChartProps.showXAxisLabel)("showYAxisLabel",a.lineChartProps.showYAxisLabel)("xAxisLabel",a.lineChartProps.xAxisLabel)("yAxisLabel",a.lineChartProps.yAxisLabel))},dependencies:[Cr,op,H.h,H.D9]})),n})();var vp=G(4433),Mn=G(4088),wr=G(6146);const yp=()=>({text:"label",value:"value"});let Tp=(()=>{var t;class n{constructor(){(0,E.A)(this,"selectedYear",null),(0,E.A)(this,"yearsList",[]),(0,E.A)(this,"EmitSelectYear",new s.bkB);const i=(new Date).getFullYear(),a=i-10;this.yearsList=Array.from({length:i+10-a+1},(o,l)=>a+l).reverse(),this.selectedYear=i}handleSelectYear(i){this.EmitSelectYear.emit(i.value)}}return t=n,(0,E.A)(n,"\u0275fac",function(i){return new(i||t)}),(0,E.A)(n,"\u0275cmp",s.VBU({type:t,selectors:[["app-inno-select-year"]],outputs:{EmitSelectYear:"EmitSelectYear"},standalone:!0,features:[s.aNF],decls:1,vars:4,consts:[[3,"change","dataSource","fields","value"]],template:function(i,a){1&i&&(s.j41(0,"ejs-dropdownlist",0),s.bIt("change",function(o){return a.handleSelectYear(o)}),s.k0s()),2&i&&s.Y8G("dataSource",a.yearsList)("fields",s.lJ4(3,yp))("value",a.selectedYear)},dependencies:[wr.G,Mn.yi,Mn.V9]})),n})();var Cp=G(6617);let bp=(()=>{var t;class n{constructor(){(0,E.A)(this,"ListMonth",[{name:"January",value:0},{name:"February",value:0},{name:"March",value:0},{name:"April",value:0},{name:"May",value:0},{name:"June",value:0},{name:"July",value:0},{name:"August",value:0},{name:"September",value:0},{name:"October",value:0},{name:"November",value:0},{name:"December",value:0}]),(0,E.A)(this,"dataChart",[]),(0,E.A)(this,"view",[1100,600]),(0,E.A)(this,"showXAxis",!0),(0,E.A)(this,"showYAxis",!0),(0,E.A)(this,"gradient",!0),(0,E.A)(this,"showLegend",!0),(0,E.A)(this,"legendPosition",Ye.Below),(0,E.A)(this,"showXAxisLabel",!0),(0,E.A)(this,"xAxisLabel","Expenditure comparison chart"),(0,E.A)(this,"showYAxisLabel",!0),(0,E.A)(this,"yAxisLabel","Sales"),(0,E.A)(this,"timeline",!0),(0,E.A)(this,"doughnut",!0),(0,E.A)(this,"colorScheme",{domain:["#FF7F50","#50EE90"]}),(0,E.A)(this,"totalAmount",void 0),(0,E.A)(this,"totalAmountPaid",void 0),(0,E.A)(this,"currentYear",(new Date).getFullYear()),(0,E.A)(this,"_invoiceService",(0,s.WQX)(br.p)),(0,E.A)(this,"destroyRef",(0,s.WQX)(s.abz))}handleSelectYear(i){this.GetRevenueChart(i)}GetRevenueChart(i){this._invoiceService.GetRevenueChart(i).pipe((0,Y.pQ)(this.destroyRef)).subscribe(a=>{if(a){this.totalAmount=a.totalAmount,this.totalAmountPaid=a.totalAmountPaid;const r=a.monthData.map(o=>({name:o.monthName,series:[{name:"Outstanding invoices",value:o.totalAmount},{name:"Amount Spent",value:o.totalAmountPaid}]}));this.dataChart=r}})}ngOnInit(){this.GetRevenueChart(this.currentYear)}}return t=n,(0,E.A)(n,"\u0275fac",function(i){return new(i||t)}),(0,E.A)(n,"\u0275cmp",s.VBU({type:t,selectors:[["app-revenue-expenses-chart"]],standalone:!0,features:[s.aNF],decls:25,vars:32,consts:[[1,"flex","items-center","justify-between","mb-4"],[1,"text-gray-500","font-bold"],[3,"EmitSelectYear"],[1,"justify-center","items-center","w-full","h-full","overflow-auto","xl:flex","block"],[3,"view","scheme","results","gradient","xAxis","yAxis","legend","legendPosition","xAxisLabel","showXAxisLabel","showYAxisLabel"],[1,"w-full","flex","flex-col","items-end","mt-[16px]"],[1,"flex","justify-end","items-start","gap-[8px]"],[1,"text-right","text-text-primary","text-text-md-regular"],[1,"text-text-primary","text-text-md-bold","text-right","w-[160px]","shrink-0"]],template:function(i,a){1&i&&(s.j41(0,"div",0)(1,"h4",1),s.EFF(2),s.nI1(3,"translate"),s.k0s(),s.j41(4,"div")(5,"app-inno-select-year",2),s.bIt("EmitSelectYear",function(o){return a.handleSelectYear(o)}),s.k0s()()(),s.j41(6,"div",3),s.nrm(7,"ngx-charts-bar-vertical-2d",4),s.k0s(),s.j41(8,"div",5)(9,"div",6)(10,"p",7),s.EFF(11),s.nI1(12,"translate"),s.k0s(),s.j41(13,"p",8),s.EFF(14),s.nI1(15,"decimal"),s.nI1(16,"formatNumber"),s.k0s()(),s.j41(17,"div",6)(18,"p",7),s.EFF(19),s.nI1(20,"translate"),s.k0s(),s.j41(21,"p",8),s.EFF(22),s.nI1(23,"decimal"),s.nI1(24,"formatNumber"),s.k0s()()()),2&i&&(s.R7$(2),s.JRh(s.bMT(3,16,"DASHBOARD.TitleChartRevenue")),s.R7$(5),s.Y8G("view",a.view)("scheme",a.colorScheme)("results",a.dataChart)("gradient",a.gradient)("xAxis",a.showXAxis)("yAxis",a.showYAxis)("legend",a.showLegend)("legendPosition",a.legendPosition)("xAxisLabel",a.xAxisLabel)("showXAxisLabel",a.showXAxisLabel)("showYAxisLabel",a.showYAxisLabel),s.R7$(4),s.SpI(" ",s.bMT(12,18,"DASHBOARD.TotalAmount")," "),s.R7$(3),s.SpI(" $",s.bMT(16,23,s.i5U(15,20,a.totalAmount,2))," "),s.R7$(5),s.SpI(" ",s.bMT(20,25,"DASHBOARD.TotalAmountPaid")," "),s.R7$(3),s.SpI(" $",s.bMT(24,30,s.i5U(23,27,a.totalAmountPaid,2))," "))},dependencies:[Cr,tp,vp.Q,wr.G,H.D9,Cp.p,Mn.yi,Tp],styles:["[_nghost-%COMP%]     .legend-labels{display:flex!important;justify-content:center!important;margin-top:-25px!important}"]})),n})(),wp=(()=>{var t;class n{constructor(){(0,E.A)(this,"CountMember",0),(0,E.A)(this,"memberService",(0,s.WQX)(Q.G)),(0,E.A)(this,"destroyRef",(0,s.WQX)(s.abz))}CountMemberBusiness(){this.memberService.CountMemberBusiness().pipe((0,Y.pQ)(this.destroyRef)).subscribe(i=>{i&&(this.CountMember=i)})}ngOnInit(){this.CountMemberBusiness()}}return t=n,(0,E.A)(n,"\u0275fac",function(i){return new(i||t)}),(0,E.A)(n,"\u0275cmp",s.VBU({type:t,selectors:[["app-dashboard"]],standalone:!0,features:[s.aNF],decls:43,vars:19,consts:[[1,"container-full","w-full","py-[24px]","border-b","border-border-primary"],[1,"flex","justify-between","items-center","flex-wrap"],[1,"text-text-primary","font-bold","text-xl"],[1,"button-size-md","button-primary"],[1,"container-full","mt-[24px]"],[1,"grid","grid-cols-4","gap-4","mb-6","mxw1100:grid-cols-1"],[1,"bg-white","p-4","rounded","shadow"],[1,"text-gray-500","uppercase","text-sm"],[1,"flex","items-center","justify-between","mt-2"],[1,"text-2xl","font-bold"],[1,"bg-white","p-4","rounded","shadow","w-full","h-full"],[1,"bg-white","p-4","rounded","shadow","mt-4","w-full","h-full"],[1,"flex","items-center","justify-center"]],template:function(i,a){1&i&&(s.j41(0,"div",0)(1,"div",1)(2,"span",2),s.EFF(3),s.nI1(4,"translate"),s.k0s(),s.j41(5,"button",3),s.EFF(6),s.nI1(7,"translate"),s.k0s()()(),s.j41(8,"div",4)(9,"div",5)(10,"div",6)(11,"h6",7),s.EFF(12),s.nI1(13,"translate"),s.k0s(),s.j41(14,"div",8)(15,"span",9),s.EFF(16,"$0"),s.k0s()()(),s.j41(17,"div",6)(18,"h6",7),s.EFF(19),s.nI1(20,"translate"),s.k0s(),s.j41(21,"div",8)(22,"span",9),s.EFF(23),s.k0s()()(),s.j41(24,"div",6)(25,"h6",7),s.EFF(26),s.nI1(27,"translate"),s.k0s(),s.j41(28,"div",8)(29,"span",9),s.EFF(30,"0"),s.k0s()()(),s.j41(31,"div",6)(32,"h6",7),s.EFF(33),s.nI1(34,"translate"),s.k0s(),s.j41(35,"div",8)(36,"span",9),s.EFF(37,"0"),s.k0s()()()(),s.j41(38,"div",10),s.nrm(39,"app-revenue-expenses-chart"),s.k0s(),s.j41(40,"div",11)(41,"div",12),s.nrm(42,"app-graphics-chart"),s.k0s()()()),2&i&&(s.R7$(3),s.SpI(" ",s.bMT(4,7,"DASHBOARD.Title")," "),s.R7$(3),s.SpI(" ",s.bMT(7,9,"DASHBOARD.Explore")," "),s.R7$(6),s.JRh(s.bMT(13,11,"DASHBOARD.Value")),s.R7$(7),s.JRh(s.bMT(20,13,"DASHBOARD.Users")),s.R7$(4),s.JRh(a.CountMember),s.R7$(3),s.SpI(" ",s.bMT(27,15,"DASHBOARD.Orders")," "),s.R7$(7),s.SpI(" ",s.bMT(34,17,"DASHBOARD.Tickets")," "))},dependencies:[H.h,H.D9,_p,bp]})),n})()},6021:ot=>{function me(Q){return Q instanceof Buffer?Buffer.from(Q):new Q.constructor(Q.buffer.slice(),Q.byteOffset,Q.length)}ot.exports=function G(Q){if((Q=Q||{}).circles)return function E(Q){const s=[],Y=[],H=new Map;if(H.set(Date,S=>new Date(S)),H.set(Map,(S,_)=>new Map(I(Array.from(S),_))),H.set(Set,(S,_)=>new Set(I(Array.from(S),_))),Q.constructorHandlers)for(const S of Q.constructorHandlers)H.set(S[0],S[1]);let y=null;return Q.proto?function P(S){if("object"!=typeof S||null===S)return S;if(Array.isArray(S))return I(S,P);if(S.constructor!==Object&&(y=H.get(S.constructor)))return y(S,P);const _={};s.push(S),Y.push(_);for(const v in S){const F=S[v];if("object"!=typeof F||null===F)_[v]=F;else if(F.constructor!==Object&&(y=H.get(F.constructor)))_[v]=y(F,P);else if(ArrayBuffer.isView(F))_[v]=me(F);else{const N=s.indexOf(F);_[v]=-1!==N?Y[N]:P(F)}}return s.pop(),Y.pop(),_}:function A(S){if("object"!=typeof S||null===S)return S;if(Array.isArray(S))return I(S,A);if(S.constructor!==Object&&(y=H.get(S.constructor)))return y(S,A);const _={};s.push(S),Y.push(_);for(const v in S){if(!1===Object.hasOwnProperty.call(S,v))continue;const F=S[v];if("object"!=typeof F||null===F)_[v]=F;else if(F.constructor!==Object&&(y=H.get(F.constructor)))_[v]=y(F,A);else if(ArrayBuffer.isView(F))_[v]=me(F);else{const N=s.indexOf(F);_[v]=-1!==N?Y[N]:A(F)}}return s.pop(),Y.pop(),_};function I(S,_){const v=Object.keys(S),F=new Array(v.length);for(let N=0;N<v.length;N++){const be=v[N],xe=S[be];if("object"!=typeof xe||null===xe)F[be]=xe;else if(xe.constructor!==Object&&(y=H.get(xe.constructor)))F[be]=y(xe,_);else if(ArrayBuffer.isView(xe))F[be]=me(xe);else{const Be=s.indexOf(xe);F[be]=-1!==Be?Y[Be]:_(xe)}}return F}}(Q);const s=new Map;if(s.set(Date,A=>new Date(A)),s.set(Map,(A,P)=>new Map(H(Array.from(A),P))),s.set(Set,(A,P)=>new Set(H(Array.from(A),P))),Q.constructorHandlers)for(const A of Q.constructorHandlers)s.set(A[0],A[1]);let Y=null;return Q.proto?function I(A){if("object"!=typeof A||null===A)return A;if(Array.isArray(A))return H(A,I);if(A.constructor!==Object&&(Y=s.get(A.constructor)))return Y(A,I);const P={};for(const S in A){const _=A[S];P[S]="object"!=typeof _||null===_?_:_.constructor!==Object&&(Y=s.get(_.constructor))?Y(_,I):ArrayBuffer.isView(_)?me(_):I(_)}return P}:function y(A){if("object"!=typeof A||null===A)return A;if(Array.isArray(A))return H(A,y);if(A.constructor!==Object&&(Y=s.get(A.constructor)))return Y(A,y);const P={};for(const S in A){if(!1===Object.hasOwnProperty.call(A,S))continue;const _=A[S];P[S]="object"!=typeof _||null===_?_:_.constructor!==Object&&(Y=s.get(_.constructor))?Y(_,y):ArrayBuffer.isView(_)?me(_):y(_)}return P};function H(A,P){const S=Object.keys(A),_=new Array(S.length);for(let v=0;v<S.length;v++){const F=S[v],N=A[F];_[F]="object"!=typeof N||null===N?N:N.constructor!==Object&&(Y=s.get(N.constructor))?Y(N,P):ArrayBuffer.isView(N)?me(N):P(N)}return _}}},152:(ot,me,G)=>{G.d(me,{B:()=>Y});var E=G(3236),Q=G(9974),s=G(4360);function Y(H,y=E.E){return(0,Q.N)((I,A)=>{let P=null,S=null,_=null;const v=()=>{if(P){P.unsubscribe(),P=null;const N=S;S=null,A.next(N)}};function F(){const N=_+H,be=y.now();if(be<N)return P=this.schedule(void 0,N-be),void A.add(P);v()}I.subscribe((0,s._)(A,N=>{S=N,_=y.now(),P||(P=y.schedule(F,H),A.add(P))},()=>{v(),A.complete()},void 0,()=>{S=P=null}))})}}}]);