"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[7973],{359:(C,g,t)=>{t.d(g,{l:()=>e});var i=t(9842),b=t(1626),h=t(4438),M=t(33);const u=t(5312).c.HOST_API+"/api";let e=(()=>{var c;class v{constructor(){(0,i.A)(this,"http",(0,h.WQX)(b.Qq)),(0,i.A)(this,"router",(0,h.WQX)(M.Ix))}CreateUserBusiness(o){return this.http.post(u+"/Business/user-business",o)}GetUserBusiness(){return this.http.get(u+"/Business/user-business").pipe(o=>o)}GetBusinessById(o){return this.http.get(u+`/Business/user-business-byid?businessId=${o}`)}GetInfoCompany(){return this.http.get(u+"/Business/GetInfoCompany")}GetAllUserBusiness(o){const m={...o};return Object.keys(m).forEach(p=>null==m[p]&&delete m[p]),this.http.get(u+"/Business/GetAllUserBusiness",{params:m})}AddMemberBusiness(o){return this.http.post(u+"/Business/AddMemberBusiness",o)}DeleteMemberInBusiness(o){return this.http.post(u+`/Business/DeleteMemberInBusiness?memberId=${o}`,null)}userBusinessById(o){return this.http.get(u+`/Business/userBusinessById?UserId=${o}`)}UpdateRoleMember(o,m){return this.http.get(u+`/Business/UpdateRoleMember?UserId=${o}&role=${m}`)}UpdateStatus(o){return this.http.post(u+"/Business/UpdateStatus",o)}SendMailAddMember(o){return this.http.post(u+"/Business/SendMailAddMember",o)}}return c=v,(0,i.A)(v,"\u0275fac",function(o){return new(o||c)}),(0,i.A)(v,"\u0275prov",h.jDH({token:c,factory:c.\u0275fac,providedIn:"root"})),v})()},7973:(C,g,t)=>{t.r(g),t.d(g,{InviteMemberComponent:()=>T});var i=t(9842),b=t(1110),h=t(1342),M=t(7302),y=t(6146),u=t(5508),e=t(4438),c=t(9079),v=t(33),I=t(7497),o=t(5312),m=t(4006),p=t(359),R=t(467),B=t(2716),x=t(7987);let A=(()=>{var r;class a extends B.H{open(s){var n=this;return(0,R.A)(function*(){const d=yield Promise.all([t.e(2076),t.e(1631)]).then(t.bind(t,1631));return n.matDialog.open(d.DoneInviteComponent.getComponent(),{data:s,panelClass:"custom_dialog",width:"550px",disableClose:!0,scrollStrategy:new x.t0})})()}}return r=a,(0,i.A)(a,"\u0275fac",(()=>{let l;return function(n){return(l||(l=e.xGo(r)))(n||r)}})()),(0,i.A)(a,"\u0275prov",e.jDH({token:r,factory:r.\u0275fac,providedIn:"root"})),a})();var f=t(9417);function D(r,a){if(1&r){const l=e.RV6();e.j41(0,"li",17),e.bIt("click",function(){const n=e.eBV(l).$implicit,d=e.XpG();return e.Njj(d.SelectRole(n.value))}),e.j41(1,"div",18)(2,"div",19)(3,"input",20),e.mxI("ngModelChange",function(n){e.eBV(l);const d=e.XpG();return e.DH7(d.selected,n)||(d.selected=n),e.Njj(n)}),e.k0s()(),e.j41(4,"div",21)(5,"label",22)(6,"div"),e.EFF(7),e.k0s(),e.j41(8,"p",23),e.EFF(9),e.k0s()()()()()}if(2&r){const l=a.$implicit,s=e.XpG();e.R7$(3),e.R50("ngModel",s.selected),e.Y8G("value",l.value),e.R7$(4),e.JRh(l.name),e.R7$(2),e.JRh(l.depression)}}function j(r,a){1&r&&(e.j41(0,"span",25),e.EFF(1," done "),e.k0s())}function E(r,a){1&r&&(e.j41(0,"span",26),e.EFF(1," not_interested "),e.k0s())}function S(r,a){if(1&r&&(e.j41(0,"div",24),e.DNE(1,j,2,0,"span",25)(2,E,2,0,"span",26),e.j41(3,"span",27),e.EFF(4),e.k0s()()),2&r){const l=a.$implicit;e.R7$(),e.vxM(l.check?1:2),e.R7$(3),e.JRh(l.text)}}function U(r,a){if(1&r&&(e.j41(0,"span"),e.EFF(1),e.k0s(),e.Z7z(2,S,5,2,"div",24,e.fX1)),2&r){const l=a.$implicit;e.R7$(),e.JRh(l.depression),e.R7$(),e.Dyx(null==l?null:l.role)}}let T=(()=>{var r;class a{constructor(s){(0,i.A)(this,"doneInviteDialog",void 0),(0,i.A)(this,"activatedRoute",(0,e.WQX)(v.nX)),(0,i.A)(this,"userBusinessService",(0,e.WQX)(p.l)),(0,i.A)(this,"_storeService",(0,e.WQX)(b.n)),(0,i.A)(this,"spinnerService",(0,e.WQX)(h.D)),(0,i.A)(this,"Host",o.c.HOST),(0,i.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,i.A)(this,"router",(0,e.WQX)(v.Ix)),(0,i.A)(this,"dialog",(0,e.WQX)(m.bZ)),(0,i.A)(this,"email",void 0),(0,i.A)(this,"firstName",void 0),(0,i.A)(this,"lastName",void 0),(0,i.A)(this,"selected","Acountant"),(0,i.A)(this,"RoleMenu",M.c),(0,i.A)(this,"ChossedMenu",void 0),(0,i.A)(this,"RoleMember",I.J),(0,i.A)(this,"MenuItem",u.r),this.doneInviteDialog=s,this.ChossedMenu=this.RoleMenu.filter(n=>n.value==this.selected)}SelectRole(s){this.selected=s,this.ChossedMenu=this.RoleMenu.filter(n=>n.value==s)}SendInvite(){let s={email:this.email,role:this.selected,firstName:this.firstName,lastName:this.lastName};this.spinnerService.show(),this.userBusinessService.SendMailAddMember(s).pipe((0,c.pQ)(this.destroyRef)).subscribe({next:n=>{n&&(this.spinnerService.hide(),this.OpenDialogDone())}})}Cancel(){this.router.navigateByUrl("/members")}OpenDialogDone(){this.doneInviteDialog.open({}).then(n=>{n.afterClosed().subscribe(d=>{d&&this.router.navigate(["/members"])})})}ngOnInit(){this.activatedRoute.queryParams.pipe((0,c.pQ)(this.destroyRef)).subscribe(s=>{s?.email&&(this.email=s.email),s?.firstName&&(this.firstName=s.firstName),s?.lastName&&(this.lastName=s.lastName),s?.role&&this.SelectRole(s.role)})}}return r=a,(0,i.A)(a,"\u0275fac",function(s){return new(s||r)(e.rXU(A))}),(0,i.A)(a,"\u0275cmp",e.VBU({type:r,selectors:[["app-invite-member"]],standalone:!0,features:[e.aNF],decls:40,vars:5,consts:[[1,"flex","flex-col","p-3"],[1,"grid","grid-cols-2","gap-4"],[1,"line-clamp-1"],[1,"p-3","space-y-1","text-sm","text-gray-700","dark:text-gray-200"],[1,"cursor-pointer"],[1,"mb-4"],["for","email",1,"block","mb-2","text-sm","font-medium","text-gray-300"],["disabled","","type","text","id","email",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","ngModel"],[1,"flex","justify-center"],[1,"border","p-3","w-80"],[1,"flex","flex-col","mt-4"],[1,"border","mt-3"],[1,"p-3"],["href","javascript:void(0)",2,"color","rgb(25, 113, 236)","text-decoration","underline"],[1,"mt-4","flex","justify-center"],["type","button",1,"text-gray-900","bg-white","border","border-gray-300","focus:outline-none","hover:bg-gray-100","focus:ring-4","focus:ring-gray-100","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["type","button",1,"bg-green-600","hover:bg-green-700focus:outline-none","text-white","focus:ring-4","focus:ring-green-300","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],[1,"cursor-pointer",3,"click"],[1,"flex","p-2","rounded","hover:bg-gray-100","dark:hover:bg-gray-600","text-start","cursor-pointer"],[1,"flex","items-center","h-5"],["id","helper-radio-4","type","radio",1,"w-4","h-4","text-blue-600","bg-gray-100","border-gray-300","focus:ring-blue-500",3,"ngModelChange","ngModel","value"],[1,"ms-2","text-sm","cursor-pointer"],[1,"font-medium","text-gray-900","dark:text-gray-300"],[1,"text-xs","font-normal","text-gray-500"],[1,"flex","items-center","mt-2"],[1,"material-icons","text-green-500"],[1,"material-icons","text-red-500"],[1,"pl-3"]],template:function(s,n){1&s&&(e.j41(0,"div",0)(1,"div",1)(2,"div")(3,"div")(4,"h5",2),e.EFF(5),e.k0s(),e.j41(6,"ul",3),e.Z7z(7,D,10,4,"li",4,e.fX1),e.k0s()(),e.j41(9,"div",5)(10,"label",6),e.EFF(11,"To"),e.k0s(),e.j41(12,"input",7),e.mxI("ngModelChange",function(F){return e.DH7(n.email,F)||(n.email=F),F}),e.k0s()()(),e.j41(13,"div",8)(14,"div",9)(15,"h5"),e.EFF(16),e.k0s(),e.j41(17,"div",10),e.Z7z(18,U,4,1,null,null,e.fX1),e.k0s()()()(),e.j41(20,"div",11)(21,"div",12)(22,"div"),e.EFF(23," Hey "),e.j41(24,"strong"),e.EFF(25),e.k0s(),e.nrm(26,"br"),e.EFF(27," We\u2019re using innoBook to create estimates and invoices for clients, track the team's time and manage projects. "),e.k0s(),e.j41(28,"div")(29,"a",13),e.EFF(30),e.k0s()(),e.j41(31,"div"),e.EFF(32," This gives you access to our FreshBooks account. Start tracking your time and expenses in there - it's really easy to use. "),e.nrm(33,"br"),e.EFF(34," Thanks ! "),e.k0s()()(),e.j41(35,"div",14)(36,"button",15),e.bIt("click",function(){return n.Cancel()}),e.EFF(37,"Cancel"),e.k0s(),e.j41(38,"button",16),e.bIt("click",function(){return n.SendInvite()}),e.EFF(39,"Invite"),e.k0s()()()),2&s&&(e.R7$(5),e.SpI("Invite ",n.email,""),e.R7$(2),e.Dyx(n.RoleMember),e.R7$(5),e.R50("ngModel",n.email),e.R7$(4),e.JRh(n.selected),e.R7$(2),e.Dyx(n.ChossedMenu),e.R7$(7),e.JRh(n.email),e.R7$(5),e.JRh(n.Host))},dependencies:[y.G,f.me,f.Fm,f.BC,f.vS]})),a})()}}]);