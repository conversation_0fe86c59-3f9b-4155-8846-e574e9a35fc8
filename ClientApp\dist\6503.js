"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6503],{6503:(R,l,t)=>{t.r(l),t.d(l,{ResetPasswordComponent:()=>b});var s=t(9842),u=t(1342),e=t(4438),_=t(1225),r=t(9417),c=t(33),g=t(2928),f=t(3492),h=t(6146),m=t(3719),w=t(9079);function P(n,a){1&n&&(e.j41(0,"mat-error",10),e.EFF(1,"Password is required"),e.k0s())}function v(n,a){1&n&&(e.j41(0,"mat-error",10),e.EFF(1,"Password minlength 6"),e.k0s())}function C(n,a){1&n&&(e.j41(0,"mat-error",10),e.EFF(1,"ConfirmPassword is required"),e.k0s())}function E(n,a){1&n&&(e.j41(0,"mat-error",10),e.EFF(1,"Passsword and Confirm Password didn't match."),e.k0s())}let b=(()=>{var n;class a{constructor(){(0,s.A)(this,"showPassword",void 0),(0,s.A)(this,"resetForm",void 0),(0,s.A)(this,"with_button",void 0),(0,s.A)(this,"token",void 0),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"router",(0,e.WQX)(c.Ix)),(0,s.A)(this,"active_route",(0,e.WQX)(c.nX)),(0,s.A)(this,"auth_services",(0,e.WQX)(g.k)),(0,s.A)(this,"spinnerService",(0,e.WQX)(u.D)),(0,s.A)(this,"formBuilder",(0,e.WQX)(r.ze)),(0,s.A)(this,"_toastService",(0,e.WQX)(f.f)),this.resetForm=this.formBuilder.group({password:["",r.k0.compose([r.k0.required,r.k0.minLength(6)])],confirmPassword:["",r.k0.compose([r.k0.required])]},{validator:(0,_.S)("password","confirmPassword")})}togglePassword(){const i=document.getElementById("password"),o=document.getElementById("showIcon"),d=document.getElementById("hideIcon");"password"===i?.type?(i.type="text",o?.classList.remove("!hidden"),d?.classList.add("!hidden")):(i.type="password",o?.classList.add("!hidden"),d?.classList.remove("!hidden"))}ngOnInit(){this.token=this.auth_services.getParamsToken()}get f(){return this.resetForm.controls}onSubmit(){this.spinnerService.show();let i={token:decodeURIComponent(this.token).replace(/\s/g,"+"),pass:this.resetForm.controls.password.value};this.auth_services.ChangePassWithToken(i).pipe((0,w.pQ)(this.destroyRef)).subscribe(o=>{o?(this.spinnerService.hide(),this._toastService.showSuccess("","User.Reset.Password.Sucessfully"),this.router.navigate(["/login"])):(this.spinnerService.hide(),this._toastService.showError("","User.Reset.Password.Token.Invalid"))})}}return n=a,(0,s.A)(a,"\u0275fac",function(i){return new(i||n)}),(0,s.A)(a,"\u0275cmp",e.VBU({type:n,selectors:[["app-reset-password"]],standalone:!0,features:[e.aNF],decls:23,vars:6,consts:[[1,"pageReset"],[1,"wrapForm"],[1,"text-center","w-full","text-lg","font-bold","pb-4"],[3,"ngSubmit","formGroup"],[1,"form-group","mb-3"],[1,"relative","w-full"],["type","password","id","password","formControlName","password","placeholder","New password",1,"w-full","p-2.5","border","text-sm","bg-gray-50","border-gray-300","rounded","focus:ring-blue-500","focus:border-blue-500","block"],["type","button",1,"absolute","right-2","top-2","text-gray-600","hover:text-gray-900","focus:outline-none",3,"click"],["id","showIcon",1,"material-icons","!hidden","text-sm"],["id","hideIcon",1,"material-icons","text-sm"],[1,"matError"],["type","password","id","_confirmPassword","formControlName","confirmPassword","placeholder","Confirm Password","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],["type","submit",1,"text-white","w-full","bg-blue-700","hover:bg-blue-800","focus:ring-4","focus:ring-blue-300","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"disabled"]],template:function(i,o){1&i&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"span"),e.EFF(4,"Recover password"),e.k0s()(),e.j41(5,"form",3),e.bIt("ngSubmit",function(){return o.onSubmit()}),e.j41(6,"div",4)(7,"div",5),e.nrm(8,"input",6),e.j41(9,"button",7),e.bIt("click",function(){return o.togglePassword()}),e.j41(10,"span",8),e.EFF(11," visibility "),e.k0s(),e.j41(12,"span",9),e.EFF(13," visibility_off "),e.k0s()()(),e.DNE(14,P,2,0,"mat-error",10)(15,v,2,0,"mat-error",10),e.k0s(),e.j41(16,"div",4)(17,"div"),e.nrm(18,"input",11),e.k0s(),e.DNE(19,C,2,0,"mat-error",10)(20,E,2,0,"mat-error",10),e.k0s(),e.j41(21,"button",12),e.EFF(22," Save"),e.k0s()()()()),2&i&&(e.R7$(5),e.Y8G("formGroup",o.resetForm),e.R7$(9),e.vxM((o.f.password.dirty||o.f.password.touched)&&o.f.password.hasError("required")?14:-1),e.R7$(),e.vxM((o.f.password.dirty||o.f.password.touched)&&o.f.password.hasError("minlength")?15:-1),e.R7$(4),e.vxM(o.f.confirmPassword.touched&&o.f.confirmPassword.hasError("required")?19:-1),e.R7$(),e.vxM(o.f.confirmPassword.dirty&&o.f.confirmPassword.hasError("confirmPasswordValidator")?20:-1),e.R7$(),e.Y8G("disabled",!o.resetForm.valid))},dependencies:[h.G,r.qT,r.me,r.BC,r.cb,r.YS,r.j4,r.JD,m.RG,m.TL,c.iI],styles:[".pageReset[_ngcontent-%COMP%]{width:100vw;height:100vh;background-image:url(bg_login.jpg);background-size:cover;background-position:center;background-repeat:no-repeat;width:calc(100vw + 66px);transform:translate(-66px);display:flex;justify-content:center;align-items:center}@media screen and (max-width: 860px){.pageReset[_ngcontent-%COMP%]{transform:unset;width:100vw}}.pageReset[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0}.pageReset[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:22px;color:#0089ef;text-decoration:none;cursor:pointer}.pageReset[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]:hover{text-decoration:underline}.pageReset[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]{width:90%;padding:32px;background:#fff;border-radius:16px;max-width:420px}.pageReset[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]{height:auto;margin-left:auto;margin-right:auto;width:100%;display:block;justify-content:center;align-items:center;background-color:#9199af}.pageReset[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:100%;width:auto;object-fit:contain;background:#fff}"]})),a})()}}]);