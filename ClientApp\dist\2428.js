"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[2428],{3726:(I,w,l)=>{l.d(w,{R:()=>f});var s=l(8750),R=l(1985),g=l(1397),T=l(7441),h=l(8071),F=l(6450);const O=["addListener","removeListener"],A=["addEventListener","removeEventListener"],m=["on","off"];function f(a,u,c,v){if((0,h.T)(c)&&(v=c,c=void 0),v)return f(a,u,c).pipe((0,F.I)(v));const[C,H]=function k(a){return(0,h.T)(a.addEventListener)&&(0,h.T)(a.removeEventListener)}(a)?A.map(p=>E=>a[p](u,E,c)):function M(a){return(0,h.T)(a.addListener)&&(0,h.T)(a.removeListener)}(a)?O.map(D(a,u)):function y(a){return(0,h.T)(a.on)&&(0,h.T)(a.off)}(a)?m.map(D(a,u)):[];if(!C&&(0,T.X)(a))return(0,g.Z)(p=>f(p,u,c))((0,s.Tg)(a));if(!C)throw new TypeError("Invalid event target");return new R.c(p=>{const E=(...r)=>p.next(1<r.length?r:r[0]);return C(E),()=>H(E)})}function D(a,u){return c=>v=>a[c](u,v)}},6939:(I,w,l)=>{l.d(w,{A8:()=>f,I3:()=>C,VA:()=>D,aI:()=>a,jc:()=>p,lb:()=>y});var s=l(4438),R=l(177);class m{attach(t){return this._attachedHost=t,t.attach(this)}detach(){let t=this._attachedHost;null!=t&&(this._attachedHost=null,t.detach())}get isAttached(){return null!=this._attachedHost}setAttachedHost(t){this._attachedHost=t}}class f extends m{constructor(t,n,e,o,P){super(),this.component=t,this.viewContainerRef=n,this.injector=e,this.componentFactoryResolver=o,this.projectableNodes=P}}class D extends m{constructor(t,n,e,o){super(),this.templateRef=t,this.viewContainerRef=n,this.context=e,this.injector=o}get origin(){return this.templateRef.elementRef}attach(t,n=this.context){return this.context=n,super.attach(t)}detach(){return this.context=void 0,super.detach()}}class M extends m{constructor(t){super(),this.element=t instanceof s.aKT?t.nativeElement:t}}class y{constructor(){this._isDisposed=!1,this.attachDomPortal=null}hasAttached(){return!!this._attachedPortal}attach(t){return t instanceof f?(this._attachedPortal=t,this.attachComponentPortal(t)):t instanceof D?(this._attachedPortal=t,this.attachTemplatePortal(t)):this.attachDomPortal&&t instanceof M?(this._attachedPortal=t,this.attachDomPortal(t)):void 0}detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(t){this._disposeFn=t}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}}class a extends y{constructor(t,n,e,o,P){super(),this.outletElement=t,this._componentFactoryResolver=n,this._appRef=e,this._defaultInjector=o,this.attachDomPortal=_=>{const i=_.element,d=this._document.createComment("dom-portal");i.parentNode.insertBefore(d,i),this.outletElement.appendChild(i),this._attachedPortal=_,super.setDisposeFn(()=>{d.parentNode&&d.parentNode.replaceChild(i,d)})},this._document=P}attachComponentPortal(t){const e=(t.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(t.component);let o;return t.viewContainerRef?(o=t.viewContainerRef.createComponent(e,t.viewContainerRef.length,t.injector||t.viewContainerRef.injector,t.projectableNodes||void 0),this.setDisposeFn(()=>o.destroy())):(o=e.create(t.injector||this._defaultInjector||s.zZn.NULL),this._appRef.attachView(o.hostView),this.setDisposeFn(()=>{this._appRef.viewCount>0&&this._appRef.detachView(o.hostView),o.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(o)),this._attachedPortal=t,o}attachTemplatePortal(t){let n=t.viewContainerRef,e=n.createEmbeddedView(t.templateRef,t.context,{injector:t.injector});return e.rootNodes.forEach(o=>this.outletElement.appendChild(o)),e.detectChanges(),this.setDisposeFn(()=>{let o=n.indexOf(e);-1!==o&&n.remove(o)}),this._attachedPortal=t,e}dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(t){return t.hostView.rootNodes[0]}}let C=(()=>{var r;class t extends y{constructor(e,o,P){super(),this._componentFactoryResolver=e,this._viewContainerRef=o,this._isInitialized=!1,this.attached=new s.bkB,this.attachDomPortal=_=>{const i=_.element,d=this._document.createComment("dom-portal");_.setAttachedHost(this),i.parentNode.insertBefore(d,i),this._getRootNode().appendChild(i),this._attachedPortal=_,super.setDisposeFn(()=>{d.parentNode&&d.parentNode.replaceChild(i,d)})},this._document=P}get portal(){return this._attachedPortal}set portal(e){this.hasAttached()&&!e&&!this._isInitialized||(this.hasAttached()&&super.detach(),e&&super.attach(e),this._attachedPortal=e||null)}get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(e){e.setAttachedHost(this);const o=null!=e.viewContainerRef?e.viewContainerRef:this._viewContainerRef,_=(e.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(e.component),i=o.createComponent(_,o.length,e.injector||o.injector,e.projectableNodes||void 0);return o!==this._viewContainerRef&&this._getRootNode().appendChild(i.hostView.rootNodes[0]),super.setDisposeFn(()=>i.destroy()),this._attachedPortal=e,this._attachedRef=i,this.attached.emit(i),i}attachTemplatePortal(e){e.setAttachedHost(this);const o=this._viewContainerRef.createEmbeddedView(e.templateRef,e.context,{injector:e.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=e,this._attachedRef=o,this.attached.emit(o),o}_getRootNode(){const e=this._viewContainerRef.element.nativeElement;return e.nodeType===e.ELEMENT_NODE?e:e.parentNode}}return(r=t).\u0275fac=function(e){return new(e||r)(s.rXU(s.OM3),s.rXU(s.c1b),s.rXU(R.qQ))},r.\u0275dir=s.FsC({type:r,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[0,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],standalone:!0,features:[s.Vt3]}),t})(),p=(()=>{var r;class t{}return(r=t).\u0275fac=function(e){return new(e||r)},r.\u0275mod=s.$C({type:r}),r.\u0275inj=s.G2t({}),t})()}}]);