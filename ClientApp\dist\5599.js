"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5599],{5599:(Fe,It,Q)=>{Q.d(It,{x:()=>Le});var j=Q(467),E=Q(9842),y=Q(4438),$t=Q(6146);const ot=Math.min,U=Math.max,it=Math.round,st=Math.floor,H=t=>({x:t,y:t}),Wt={left:"right",right:"left",bottom:"top",top:"bottom"},jt={start:"end",end:"start"};function At(t,e,o){return U(t,ot(e,o))}function rt(t,e){return"function"==typeof t?t(e):t}function Y(t){return t.split("-")[0]}function lt(t){return t.split("-")[1]}function bt(t){return"x"===t?"y":"x"}function Ct(t){return"y"===t?"height":"width"}function K(t){return["top","bottom"].includes(Y(t))?"y":"x"}function Ot(t){return bt(K(t))}function mt(t){return t.replace(/start|end/g,e=>jt[e])}function ct(t){return t.replace(/left|right|bottom|top/g,e=>Wt[e])}function ft(t){const{x:e,y:o,width:n,height:i}=t;return{width:n,height:i,top:o,left:e,right:e+n,bottom:o+i,x:e,y:o}}function Rt(t,e,o){let{reference:n,floating:i}=t;const s=K(e),r=Ot(e),l=Ct(r),f=Y(e),a="y"===s,d=n.x+n.width/2-i.width/2,c=n.y+n.height/2-i.height/2,m=n[l]/2-i[l]/2;let u;switch(f){case"top":u={x:d,y:n.y-i.height};break;case"bottom":u={x:d,y:n.y+n.height};break;case"right":u={x:n.x+n.width,y:c};break;case"left":u={x:n.x-i.width,y:c};break;default:u={x:n.x,y:n.y}}switch(lt(e)){case"start":u[r]-=m*(o&&a?-1:1);break;case"end":u[r]+=m*(o&&a?-1:1)}return u}const Kt=function(){var t=(0,j.A)(function*(e,o,n){const{placement:i="bottom",strategy:s="absolute",middleware:r=[],platform:l}=n,f=r.filter(Boolean),a=yield null==l.isRTL?void 0:l.isRTL(o);let d=yield l.getElementRects({reference:e,floating:o,strategy:s}),{x:c,y:m}=Rt(d,i,a),u=i,g={},h=0;for(let p=0;p<f.length;p++){const{name:x,fn:v}=f[p],{x:w,y:_,data:C,reset:b}=yield v({x:c,y:m,initialPlacement:i,placement:u,strategy:s,middlewareData:g,rects:d,platform:l,elements:{reference:e,floating:o}});c=w??c,m=_??m,g={...g,[x]:{...g[x],...C}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(u=b.placement),b.rects&&(d=!0===b.rects?yield l.getElementRects({reference:e,floating:o,strategy:s}):b.rects),({x:c,y:m}=Rt(d,u,a))),p=-1)}return{x:c,y:m,placement:u,strategy:s,middlewareData:g}});return function(o,n,i){return t.apply(this,arguments)}}();function J(t,e){return gt.apply(this,arguments)}function gt(){return(gt=(0,j.A)(function*(t,e){var o;void 0===e&&(e={});const{x:n,y:i,platform:s,rects:r,elements:l,strategy:f}=t,{boundary:a="clippingAncestors",rootBoundary:d="viewport",elementContext:c="floating",altBoundary:m=!1,padding:u=0}=rt(e,t),g=function Qt(t){return"number"!=typeof t?function Xt(t){return{top:0,right:0,bottom:0,left:0,...t}}(t):{top:t,right:t,bottom:t,left:t}}(u),p=l[m?"floating"===c?"reference":"floating":c],x=ft(yield s.getClippingRect({element:null==(o=yield null==s.isElement?void 0:s.isElement(p))||o?p:p.contextElement||(yield null==s.getDocumentElement?void 0:s.getDocumentElement(l.floating)),boundary:a,rootBoundary:d,strategy:f})),v="floating"===c?{x:n,y:i,width:r.floating.width,height:r.floating.height}:r.reference,w=yield null==s.getOffsetParent?void 0:s.getOffsetParent(l.floating),_=(yield null==s.isElement?void 0:s.isElement(w))&&(yield null==s.getScale?void 0:s.getScale(w))||{x:1,y:1},C=ft(s.convertOffsetParentRelativeRectToViewportRelativeRect?yield s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:v,offsetParent:w,strategy:f}):v);return{top:(x.top-C.top+g.top)/_.y,bottom:(C.bottom-x.bottom+g.bottom)/_.y,left:(x.left-C.left+g.left)/_.x,right:(C.right-x.right+g.right)/_.x}})).apply(this,arguments)}function ht(){return(ht=(0,j.A)(function*(t,e){const{placement:o,platform:n,elements:i}=t,s=yield null==n.isRTL?void 0:n.isRTL(i.floating),r=Y(o),l=lt(o),f="y"===K(o),a=["left","top"].includes(r)?-1:1,d=s&&f?-1:1,c=rt(e,t);let{mainAxis:m,crossAxis:u,alignmentAxis:g}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return l&&"number"==typeof g&&(u="end"===l?-1*g:g),f?{x:u*d,y:m*a}:{x:m*a,y:u*d}})).apply(this,arguments)}function at(){return typeof window<"u"}function Z(t){return Et(t)?(t.nodeName||"").toLowerCase():"#document"}function L(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function N(t){var e;return null==(e=(Et(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function Et(t){return!!at()&&(t instanceof Node||t instanceof L(t).Node)}function B(t){return!!at()&&(t instanceof Element||t instanceof L(t).Element)}function I(t){return!!at()&&(t instanceof HTMLElement||t instanceof L(t).HTMLElement)}function kt(t){return!(!at()||typeof ShadowRoot>"u")&&(t instanceof ShadowRoot||t instanceof L(t).ShadowRoot)}function et(t){const{overflow:e,overflowX:o,overflowY:n,display:i}=V(t);return/auto|scroll|overlay|hidden|clip/.test(e+n+o)&&!["inline","contents"].includes(i)}function oe(t){return["table","td","th"].includes(Z(t))}function ut(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function pt(t){const e=xt(),o=B(t)?V(t):t;return"none"!==o.transform||"none"!==o.perspective||!!o.containerType&&"normal"!==o.containerType||!e&&!!o.backdropFilter&&"none"!==o.backdropFilter||!e&&!!o.filter&&"none"!==o.filter||["transform","perspective","filter"].some(n=>(o.willChange||"").includes(n))||["paint","layout","strict","content"].some(n=>(o.contain||"").includes(n))}function xt(){return!(typeof CSS>"u"||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function q(t){return["html","body","#document"].includes(Z(t))}function V(t){return L(t).getComputedStyle(t)}function dt(t){return B(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function z(t){if("html"===Z(t))return t;const e=t.assignedSlot||t.parentNode||kt(t)&&t.host||N(t);return kt(e)?e.host:e}function Lt(t){const e=z(t);return q(e)?t.ownerDocument?t.ownerDocument.body:t.body:I(e)&&et(e)?e:Lt(e)}function nt(t,e,o){var n;void 0===e&&(e=[]),void 0===o&&(o=!0);const i=Lt(t),s=i===(null==(n=t.ownerDocument)?void 0:n.body),r=L(i);if(s){const l=yt(r);return e.concat(r,r.visualViewport||[],et(i)?i:[],l&&o?nt(l):[])}return e.concat(i,nt(i,[],o))}function yt(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Dt(t){const e=V(t);let o=parseFloat(e.width)||0,n=parseFloat(e.height)||0;const i=I(t),s=i?t.offsetWidth:o,r=i?t.offsetHeight:n,l=it(o)!==s||it(n)!==r;return l&&(o=s,n=r),{width:o,height:n,$:l}}function vt(t){return B(t)?t:t.contextElement}function tt(t){const e=vt(t);if(!I(e))return H(1);const o=e.getBoundingClientRect(),{width:n,height:i,$:s}=Dt(e);let r=(s?it(o.width):o.width)/n,l=(s?it(o.height):o.height)/i;return(!r||!Number.isFinite(r))&&(r=1),(!l||!Number.isFinite(l))&&(l=1),{x:r,y:l}}const se=H(0);function Ft(t){const e=L(t);return xt()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:se}function X(t,e,o,n){void 0===e&&(e=!1),void 0===o&&(o=!1);const i=t.getBoundingClientRect(),s=vt(t);let r=H(1);e&&(n?B(n)&&(r=tt(n)):r=tt(t));const l=function re(t,e,o){return void 0===e&&(e=!1),!(!o||e&&o!==L(t))&&e}(s,o,n)?Ft(s):H(0);let f=(i.left+l.x)/r.x,a=(i.top+l.y)/r.y,d=i.width/r.x,c=i.height/r.y;if(s){const m=L(s),u=n&&B(n)?L(n):n;let g=m,h=yt(g);for(;h&&n&&u!==g;){const p=tt(h),x=h.getBoundingClientRect(),v=V(h),w=x.left+(h.clientLeft+parseFloat(v.paddingLeft))*p.x,_=x.top+(h.clientTop+parseFloat(v.paddingTop))*p.y;f*=p.x,a*=p.y,d*=p.x,c*=p.y,f+=w,a+=_,g=L(h),h=yt(g)}}return ft({width:d,height:c,x:f,y:a})}function wt(t,e){const o=dt(t).scrollLeft;return e?e.left+o:X(N(t)).left+o}function Mt(t,e,o){void 0===o&&(o=!1);const n=t.getBoundingClientRect();return{x:n.left+e.scrollLeft-(o?0:wt(t,n)),y:n.top+e.scrollTop}}function Bt(t,e,o){let n;if("viewport"===e)n=function ae(t,e){const o=L(t),n=N(t),i=o.visualViewport;let s=n.clientWidth,r=n.clientHeight,l=0,f=0;if(i){s=i.width,r=i.height;const a=xt();(!a||a&&"fixed"===e)&&(l=i.offsetLeft,f=i.offsetTop)}return{width:s,height:r,x:l,y:f}}(t,o);else if("document"===e)n=function fe(t){const e=N(t),o=dt(t),n=t.ownerDocument.body,i=U(e.scrollWidth,e.clientWidth,n.scrollWidth,n.clientWidth),s=U(e.scrollHeight,e.clientHeight,n.scrollHeight,n.clientHeight);let r=-o.scrollLeft+wt(t);const l=-o.scrollTop;return"rtl"===V(n).direction&&(r+=U(e.clientWidth,n.clientWidth)-i),{width:i,height:s,x:r,y:l}}(N(t));else if(B(e))n=function ue(t,e){const o=X(t,!0,"fixed"===e),n=o.top+t.clientTop,i=o.left+t.clientLeft,s=I(t)?tt(t):H(1);return{width:t.clientWidth*s.x,height:t.clientHeight*s.y,x:i*s.x,y:n*s.y}}(e,o);else{const i=Ft(t);n={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return ft(n)}function Vt(t,e){const o=z(t);return!(o===e||!B(o)||q(o))&&("fixed"===V(o).position||Vt(o,e))}function de(t,e){const o=e.get(t);if(o)return o;let n=nt(t,[],!1).filter(l=>B(l)&&"body"!==Z(l)),i=null;const s="fixed"===V(t).position;let r=s?z(t):t;for(;B(r)&&!q(r);){const l=V(r),f=pt(r);!f&&"fixed"===l.position&&(i=null),(s?!f&&!i:!f&&"static"===l.position&&i&&["absolute","fixed"].includes(i.position)||et(r)&&!f&&Vt(t,r))?n=n.filter(d=>d!==r):i=l,r=z(r)}return e.set(t,n),n}function he(t,e,o){const n=I(e),i=N(e),s="fixed"===o,r=X(t,!0,s,e);let l={scrollLeft:0,scrollTop:0};const f=H(0);if(n||!n&&!s)if(("body"!==Z(e)||et(i))&&(l=dt(e)),n){const m=X(e,!0,s,e);f.x=m.x+e.clientLeft,f.y=m.y+e.clientTop}else i&&(f.x=wt(i));const a=!i||n||s?H(0):Mt(i,l);return{x:r.left+l.scrollLeft-f.x-a.x,y:r.top+l.scrollTop-f.y-a.y,width:r.width,height:r.height}}function _t(t){return"static"===V(t).position}function Ht(t,e){if(!I(t)||"fixed"===V(t).position)return null;if(e)return e(t);let o=t.offsetParent;return N(t)===o&&(o=o.ownerDocument.body),o}function Nt(t,e){const o=L(t);if(ut(t))return o;if(!I(t)){let i=z(t);for(;i&&!q(i);){if(B(i)&&!_t(i))return i;i=z(i)}return o}let n=Ht(t,e);for(;n&&oe(n)&&_t(n);)n=Ht(n,e);return n&&q(n)&&_t(n)&&!pt(n)?o:n||function ie(t){let e=z(t);for(;I(e)&&!q(e);){if(pt(e))return e;if(ut(e))return null;e=z(e)}return null}(t)||o}const ye={convertOffsetParentRelativeRectToViewportRelativeRect:function le(t){let{elements:e,rect:o,offsetParent:n,strategy:i}=t;const s="fixed"===i,r=N(n),l=!!e&&ut(e.floating);if(n===r||l&&s)return o;let f={scrollLeft:0,scrollTop:0},a=H(1);const d=H(0),c=I(n);if((c||!c&&!s)&&(("body"!==Z(n)||et(r))&&(f=dt(n)),I(n))){const u=X(n);a=tt(n),d.x=u.x+n.clientLeft,d.y=u.y+n.clientTop}const m=!r||c||s?H(0):Mt(r,f,!0);return{width:o.width*a.x,height:o.height*a.y,x:o.x*a.x-f.scrollLeft*a.x+d.x+m.x,y:o.y*a.y-f.scrollTop*a.y+d.y+m.y}},getDocumentElement:N,getClippingRect:function me(t){let{element:e,boundary:o,rootBoundary:n,strategy:i}=t;const r=[..."clippingAncestors"===o?ut(e)?[]:de(e,this._c):[].concat(o),n],f=r.reduce((a,d)=>{const c=Bt(e,d,i);return a.top=U(c.top,a.top),a.right=ot(c.right,a.right),a.bottom=ot(c.bottom,a.bottom),a.left=U(c.left,a.left),a},Bt(e,r[0],i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}},getOffsetParent:Nt,getElementRects:function(){var t=(0,j.A)(function*(e){const o=this.getOffsetParent||Nt,n=this.getDimensions,i=yield n(e.floating);return{reference:he(e.reference,yield o(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}});return function(o){return t.apply(this,arguments)}}(),getClientRects:function ce(t){return Array.from(t.getClientRects())},getDimensions:function ge(t){const{width:e,height:o}=Dt(t);return{width:e,height:o}},getScale:tt,isElement:B,isRTL:function xe(t){return"rtl"===V(t).direction}};const _e=function(t){return void 0===t&&(t=0),{name:"offset",options:t,fn:e=>(0,j.A)(function*(){var o,n;const{x:i,y:s,placement:r,middlewareData:l}=e,f=yield function te(t,e){return ht.apply(this,arguments)}(e,t);return r===(null==(o=l.offset)?void 0:o.placement)&&null!=(n=l.arrow)&&n.alignmentOffset?{}:{x:i+f.x,y:s+f.y,data:{...f,placement:r}}})()}},Ae=function(t){return void 0===t&&(t={}),{name:"shift",options:t,fn:e=>(0,j.A)(function*(){const{x:o,y:n,placement:i}=e,{mainAxis:s=!0,crossAxis:r=!1,limiter:l={fn:p=>{let{x,y:v}=p;return{x,y:v}}},...f}=rt(t,e),a={x:o,y:n},d=yield J(e,f),c=K(Y(i)),m=bt(c);let u=a[m],g=a[c];s&&(u=At(u+d["y"===m?"top":"left"],u,u-d["y"===m?"bottom":"right"])),r&&(g=At(g+d["y"===c?"top":"left"],g,g-d["y"===c?"bottom":"right"]));const h=l.fn({...e,[m]:u,[c]:g});return{...h,data:{x:h.x-o,y:h.y-n,enabled:{[m]:s,[c]:r}}}})()}},be=function(t){return void 0===t&&(t={}),{name:"flip",options:t,fn:e=>(0,j.A)(function*(){var o,n;const{placement:i,middlewareData:s,rects:r,initialPlacement:l,platform:f,elements:a}=e,{mainAxis:d=!0,crossAxis:c=!0,fallbackPlacements:m,fallbackStrategy:u="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:h=!0,...p}=rt(t,e);if(null!=(o=s.arrow)&&o.alignmentOffset)return{};const x=Y(i),v=K(l),w=Y(l)===l,_=yield null==f.isRTL?void 0:f.isRTL(a.floating),C=m||(w||!h?[ct(l)]:function Gt(t){const e=ct(t);return[mt(t),e,mt(e)]}(l)),b="none"!==g;!m&&b&&C.push(...function Yt(t,e,o,n){const i=lt(t);let s=function Ut(t,e,o){const n=["left","right"],i=["right","left"],s=["top","bottom"],r=["bottom","top"];switch(t){case"top":case"bottom":return o?e?i:n:e?n:i;case"left":case"right":return e?s:r;default:return[]}}(Y(t),"start"===o,n);return i&&(s=s.map(r=>r+"-"+i),e&&(s=s.concat(s.map(mt)))),s}(l,h,g,_));const P=[l,...C],D=yield J(e,p),T=[];let A=(null==(n=s.flip)?void 0:n.overflows)||[];if(d&&T.push(D[x]),c){const R=function zt(t,e,o){void 0===o&&(o=!1);const n=lt(t),i=Ot(t),s=Ct(i);let r="x"===i?n===(o?"end":"start")?"right":"left":"start"===n?"bottom":"top";return e.reference[s]>e.floating[s]&&(r=ct(r)),[r,ct(r)]}(i,r,_);T.push(D[R[0]],D[R[1]])}if(A=[...A,{placement:i,overflows:T}],!T.every(R=>R<=0)){var O,S;const R=((null==(O=s.flip)?void 0:O.index)||0)+1,$=P[R];if($)return{data:{index:R,overflows:A},reset:{placement:$}};let F=null==(S=A.filter(M=>M.overflows[0]<=0).sort((M,W)=>M.overflows[1]-W.overflows[1])[0])?void 0:S.placement;if(!F)switch(u){case"bestFit":{var k;const M=null==(k=A.filter(W=>{if(b){const G=K(W.placement);return G===v||"y"===G}return!0}).map(W=>[W.placement,W.overflows.filter(G=>G>0).reduce((G,De)=>G+De,0)]).sort((W,G)=>W[1]-G[1])[0])?void 0:k[0];M&&(F=M);break}case"initialPlacement":F=l}if(i!==F)return{reset:{placement:F}}}return{}})()}};var Oe=Q(177);const Re=["targetElement"],Pe=["contentElement"],Te=[[["","target",""]]],Se=["[target]"];function Ee(t,e){if(1&t){const o=y.RV6();y.j41(0,"div",5,1)(2,"div",6),y.bIt("click",function(){y.eBV(o);const i=y.XpG();return y.Njj(i.handleClickOnContent())}),y.eu8(3,7),y.k0s()()}if(2&t){const o=y.XpG();y.AVh("clearPadding",o.isClearPadding),y.R7$(3),y.Y8G("ngTemplateOutlet",o.content)}}function ke(t,e){if(1&t){const o=y.RV6();y.j41(0,"div",8),y.bIt("click",function(){y.eBV(o);const i=y.XpG();return y.Njj(i.handleHideContent())}),y.k0s()}}let Le=(()=>{var t;class e{constructor(){(0,E.A)(this,"content",null),(0,E.A)(this,"position","bottom-end"),(0,E.A)(this,"isClearPadding",!1),(0,E.A)(this,"isClickOverlayToClose",!0),(0,E.A)(this,"isClickOnContentToClose",!0),(0,E.A)(this,"onOpen",new y.bkB),(0,E.A)(this,"onClose",new y.bkB),(0,E.A)(this,"isShowDefault",!1),(0,E.A)(this,"targetElement",void 0),(0,E.A)(this,"contentElement",void 0),(0,E.A)(this,"isShow",!1),(0,E.A)(this,"cleanAutoUpdateFunction",void 0)}ngAfterViewInit(){this.isShowDefault&&this.handleShowContent()}getPopoverElement(){const n={targetRef:null,contentRef:null};return!this.targetElement?.nativeElement||!this.contentElement?.nativeElement||(n.targetRef=this.targetElement.nativeElement,n.contentRef=this.contentElement.nativeElement),n}handleHideContent(){const{contentRef:n}=this.getPopoverElement();n&&(n.style.display="none",this.isShow=!1,this.cleanAutoUpdateFunction&&this.cleanAutoUpdateFunction(),this.onClose.emit())}handleShowContent(){const{targetRef:n,contentRef:i}=this.getPopoverElement();!n||!i||(i.style.display="block",this.isShow=!0,this.cleanAutoUpdateFunction=function we(t,e,o,n){void 0===n&&(n={});const{ancestorScroll:i=!0,ancestorResize:s=!0,elementResize:r="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:f=!1}=n,a=vt(t),d=i||s?[...a?nt(a):[],...nt(e)]:[];d.forEach(x=>{i&&x.addEventListener("scroll",o,{passive:!0}),s&&x.addEventListener("resize",o)});const c=a&&l?function ve(t,e){let n,o=null;const i=N(t);function s(){var l;clearTimeout(n),null==(l=o)||l.disconnect(),o=null}return function r(l,f){void 0===l&&(l=!1),void 0===f&&(f=1),s();const{left:a,top:d,width:c,height:m}=t.getBoundingClientRect();if(l||e(),!c||!m)return;const v={rootMargin:-st(d)+"px "+-st(i.clientWidth-(a+c))+"px "+-st(i.clientHeight-(d+m))+"px "+-st(a)+"px",threshold:U(0,ot(1,f))||1};let w=!0;function _(C){const b=C[0].intersectionRatio;if(b!==f){if(!w)return r();b?r(!1,b):n=setTimeout(()=>{r(!1,1e-7)},1e3)}w=!1}try{o=new IntersectionObserver(_,{...v,root:i.ownerDocument})}catch{o=new IntersectionObserver(_,v)}o.observe(t)}(!0),s}(a,o):null;let m=-1,u=null;r&&(u=new ResizeObserver(x=>{let[v]=x;v&&v.target===a&&u&&(u.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var w;null==(w=u)||w.observe(e)})),o()}),a&&!f&&u.observe(a),u.observe(e));let g,h=f?X(t):null;return f&&function p(){const x=X(t);h&&(x.x!==h.x||x.y!==h.y||x.width!==h.width||x.height!==h.height)&&o(),h=x,g=requestAnimationFrame(p)}(),o(),()=>{var x;d.forEach(v=>{i&&v.removeEventListener("scroll",o),s&&v.removeEventListener("resize",o)}),c?.(),null==(x=u)||x.disconnect(),u=null,f&&cancelAnimationFrame(g)}}(n,i,()=>{((t,e,o)=>{const i={platform:ye,...o},s={...i.platform,_c:new Map};return Kt(t,e,{...i,platform:s})})(n,i,{placement:this.position,strategy:"fixed",middleware:[_e(10),be({padding:10,mainAxis:!0}),Ae({padding:10,mainAxis:!0,crossAxis:!0})]}).then(({x:s,y:r})=>{Object.assign(i.style,{left:`${s}px`,top:`${r}px`})})}),this.onOpen.emit())}handleClickOnContent(){this.isClickOnContentToClose&&this.handleHideContent()}handleToggle(){var n=this;return(0,j.A)(function*(){n.isShow=!n.isShow,yield new Promise(r=>setTimeout(r,100));const{targetRef:i,contentRef:s}=n.getPopoverElement();!i||!s||(n.isShow?n.handleShowContent():n.handleHideContent())})()}ngOnDestroy(){this.cleanAutoUpdateFunction&&this.cleanAutoUpdateFunction()}}return t=e,(0,E.A)(e,"\u0275fac",function(n){return new(n||t)}),(0,E.A)(e,"\u0275cmp",y.VBU({type:t,selectors:[["app-inno-popover"]],viewQuery:function(n,i){if(1&n&&(y.GBs(Re,5),y.GBs(Pe,5)),2&n){let s;y.mGM(s=y.lsd())&&(i.targetElement=s.first),y.mGM(s=y.lsd())&&(i.contentElement=s.first)}},inputs:{content:"content",position:"position",isClearPadding:"isClearPadding",isClickOverlayToClose:"isClickOverlayToClose",isClickOnContentToClose:"isClickOnContentToClose",isShowDefault:"isShowDefault"},outputs:{onOpen:"onOpen",onClose:"onClose"},standalone:!0,features:[y.aNF],ngContentSelectors:Se,decls:5,vars:2,consts:[["targetElement",""],["contentElement",""],[1,"targetPopover",3,"click"],[1,"wrapperPopover",3,"clearPadding"],[1,"overlayPopover"],[1,"wrapperPopover"],[3,"click"],[3,"ngTemplateOutlet"],[1,"overlayPopover",3,"click"]],template:function(n,i){if(1&n){const s=y.RV6();y.NAR(Te),y.j41(0,"div",2,0),y.bIt("click",function(){return y.eBV(s),y.Njj(i.handleToggle())}),y.SdG(2),y.k0s(),y.DNE(3,Ee,4,3,"div",3)(4,ke,1,0,"div",4)}2&n&&(y.R7$(3),y.vxM(i.content&&i.isShow?3:-1),y.R7$(),y.vxM(i.isShow&&i.isClickOverlayToClose?4:-1))},dependencies:[$t.G,Oe.T3],styles:[".targetPopover[_ngcontent-%COMP%]{cursor:pointer}.wrapperPopover[_ngcontent-%COMP%]{position:fixed;top:0;left:0;z-index:99999;padding:4px;display:none;border-radius:8px;box-shadow:0 4px 8px 4px #0000000d;background-color:#fff}.wrapperPopover.clearPadding[_ngcontent-%COMP%]{padding:0;box-shadow:none}.overlayPopover[_ngcontent-%COMP%]{width:200vw;height:200vh;background-color:transparent;z-index:9;position:fixed;top:50%;left:50%;transform:translate(-50%,-50%)}"]})),e})()}}]);