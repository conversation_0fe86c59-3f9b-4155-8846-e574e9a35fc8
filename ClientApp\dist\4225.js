"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[4225],{4978:(g,u,o)=>{o.d(u,{I:()=>e});var n=o(9842),t=o(4438),<PERSON>=o(6146),f=o(5236);const E=["*",[["","footer",""]]],c=["*","[footer]"];function v(r,i){if(1&r){const l=t.RV6();t.j41(0,"button",7),t.bIt("click",function(){t.eBV(l);const p=t.XpG(2);return t.Njj(p.handleClose())}),t.nrm(1,"img",8),t.k0s()}}function b(r,i){if(1&r&&(t.j41(0,"div",4)(1,"p",5),t.<PERSON>(2),t.nI1(3,"translate"),t.k0s()(),t.DNE(4,v,2,0,"button",6)),2&r){const l=t.XpG();t.R7$(2),t.JRh(t.bMT(3,2,l.title)),t.R7$(2),t.vxM(l.onClose.observers.length?4:-1)}}let e=(()=>{var r;class i{constructor(){(0,n.A)(this,"title",void 0),(0,n.A)(this,"onClose",new t.bkB)}handleClose(){this.onClose.emit()}}return r=i,(0,n.A)(i,"\u0275fac",function(_){return new(_||r)}),(0,n.A)(i,"\u0275cmp",t.VBU({type:r,selectors:[["app-inno-modal-wrapper"]],inputs:{title:"title"},outputs:{onClose:"onClose"},standalone:!0,features:[t.aNF],ngContentSelectors:c,decls:7,vars:1,consts:[[1,"flex","flex-col","relative","bg-bg-primary"],[1,"w-full","sticky","top-0","z-10"],[1,"flex","flex-col","grow","overflow-auto","max-h-[70dvh]"],[1,"w-full","border-t","border-border-primary-slight"],[1,"w-full","p-[16px]","bg-bg-primary","border-b","border-border-primary-slight"],[1,"text-headline-sm-bold","text-text-primary"],["type","button",1,"button-icon","absolute","top-1","right-1"],["type","button",1,"button-icon","absolute","top-1","right-1",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon"]],template:function(_,p){1&_&&(t.NAR(E),t.j41(0,"div",0)(1,"div",1),t.DNE(2,b,5,4),t.k0s(),t.j41(3,"div",2),t.SdG(4),t.k0s(),t.j41(5,"div",3),t.SdG(6,1),t.k0s()()),2&_&&(t.R7$(2),t.vxM(p.title?2:-1))},dependencies:[M.G,f.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),i})()},4225:(g,u,o)=>{o.r(u),o.d(u,{EditProfileTeamMembersComponent:()=>R});var n=o(9842),t=o(4978),M=o(1328),f=o(1110),E=o(359),c=o(3492),v=o(2928),b=o(1342),e=o(4438),r=o(9417),i=o(33),l=o(9079),_=o(6146),p=o(3719),h=o(4006),C=o(5236),T=o(177);const A=d=>({required:d}),P=(d,m)=>({"bg-green-700 hover:bg-green-800":d,"bg-gray-400":m});let R=(()=>{var d;class m{constructor(a,s){(0,n.A)(this,"dialogRef",void 0),(0,n.A)(this,"data",void 0),(0,n.A)(this,"_id",void 0),(0,n.A)(this,"profileForm",void 0),(0,n.A)(this,"spiner_services",(0,e.WQX)(b.D)),(0,n.A)(this,"auth_services",(0,e.WQX)(v.k)),(0,n.A)(this,"router",(0,e.WQX)(i.Ix)),(0,n.A)(this,"_toastService",(0,e.WQX)(c.f)),(0,n.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,n.A)(this,"userbusiness_services",(0,e.WQX)(E.l)),(0,n.A)(this,"formBuilder",(0,e.WQX)(r.ze)),(0,n.A)(this,"activatedRoute",(0,e.WQX)(i.nX)),(0,n.A)(this,"_storeService",(0,e.WQX)(f.n)),(0,n.A)(this,"translate",(0,e.WQX)(C.c$)),this.dialogRef=a,this.data=s,this.profileForm=this.formBuilder.group({firstname:["",r.k0.compose([r.k0.required])],lastname:["",r.k0.compose([r.k0.required])],email:[{value:"",disabled:!0},r.k0.compose([r.k0.required,r.k0.email])]})}handleClose(){this.dialogRef.close()}static getComponent(){return m}get f(){return this.profileForm.controls}ngOnInit(){this.GetInforUser(this.data)}_handleData(a){this.profileForm.controls.firstname.setValue(a.firstName),this.profileForm.controls.lastname.setValue(a.lastName),this.profileForm.controls.email.setValue(a.email)}GetInforUser(a){this.userbusiness_services.userBusinessById(a).pipe((0,l.pQ)(this.destroyRef)).subscribe(s=>{s&&this._handleData(s.user)})}onSubmit(){this.spiner_services.show(),this.auth_services.UpdateUserProfileByEmail({firstName:this.profileForm.controls.firstname.value,lastName:this.profileForm.controls.lastname.value,email:this.profileForm.controls.email.value}).pipe((0,l.pQ)(this.destroyRef)).subscribe(s=>{s&&(this.spiner_services.hide(),this._toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.Success")),this.dialogRef.close(s))})}}return d=m,(0,n.A)(m,"\u0275fac",function(a){return new(a||d)(e.rXU(h.CP),e.rXU(h.Vh))}),(0,n.A)(m,"\u0275cmp",e.VBU({type:d,selectors:[["app-edit-profile-team-members"]],standalone:!0,features:[e.aNF],decls:23,vars:42,consts:[[3,"onClose","title"],[1,"input-content","rounded-md","w-full","border","border-gray-300"],[1,"p-3",3,"ngSubmit","formGroup"],[1,"grid","gap-6","mb-6","md:grid-cols-2"],[3,"label","placeholder","formControl","value","errorMessages"],[1,"form-group","mb-3"],["for","_name",1,"block","mb-2","text-sm","font-medium","text-gray-900","dark:text-white"],["type","email","id","_email","formControlName","email",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"placeholder"],[1,"w-full","text-center"],["type","submit",1,"text-white","font-medium","rounded-lg","text-sm","w-full","sm:w-auto","px-5","py-2.5","text-center",3,"disabled","ngClass"]],template:function(a,s){1&a&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return s.handleClose()}),e.j41(1,"div",1)(2,"form",2),e.bIt("ngSubmit",function(){return s.onSubmit()}),e.j41(3,"div",3),e.nrm(4,"app-inno-form-input",4),e.nI1(5,"translate"),e.nI1(6,"translate"),e.nI1(7,"translate"),e.nrm(8,"app-inno-form-input",4),e.nI1(9,"translate"),e.nI1(10,"translate"),e.nI1(11,"translate"),e.k0s(),e.j41(12,"div",5)(13,"div")(14,"label",6),e.EFF(15),e.nI1(16,"translate"),e.k0s(),e.nrm(17,"input",7),e.nI1(18,"translate"),e.k0s()(),e.j41(19,"div",8)(20,"button",9),e.EFF(21),e.nI1(22,"translate"),e.k0s()()()()()),2&a&&(e.Y8G("title","TEAMMEMBERS.AddMemberForm.ProfileMember"),e.R7$(2),e.Y8G("formGroup",s.profileForm),e.R7$(2),e.Y8G("label",e.bMT(5,17,"TEAMMEMBERS.AddMemberForm.FirstName"))("placeholder",e.bMT(6,19,"TEAMMEMBERS.AddMemberForm.FirstName"))("formControl",s.f.firstname)("value",s.f.firstname.value)("errorMessages",e.eq3(35,A,e.bMT(7,21,"TEAMMEMBERS.AddMemberForm.FirstNameRequired"))),e.R7$(4),e.Y8G("label",e.bMT(9,23,"TEAMMEMBERS.AddMemberForm.LastName"))("placeholder",e.bMT(10,25,"TEAMMEMBERS.AddMemberForm.LastName"))("formControl",s.f.lastname)("value",s.f.lastname.value)("errorMessages",e.eq3(37,A,e.bMT(11,27,"TEAMMEMBERS.AddMemberForm.LastNameRequired"))),e.R7$(7),e.SpI("",e.bMT(16,29,"TEAMMEMBERS.AddMemberForm.Email")," "),e.R7$(2),e.Y8G("placeholder",e.bMT(18,31,"TEAMMEMBERS.AddMemberForm.Email")),e.R7$(3),e.Y8G("disabled",!s.profileForm.valid)("ngClass",e.l_i(39,P,s.profileForm.valid,!s.profileForm.valid)),e.R7$(),e.JRh(e.bMT(22,33,"BUTTON.Save")))},dependencies:[i.iI,t.I,_.G,T.YU,r.qT,r.me,r.BC,r.cb,r.l_,r.j4,r.JD,C.D9,M.a,p.RG]})),m})()}}]);