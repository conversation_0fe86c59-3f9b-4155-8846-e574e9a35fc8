"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1515],{1515:($,_,c)=>{c.r(_),c.d(_,{AddServiceForProjectComponent:()=>L});var s=c(9842),u=c(1110),C=c(5402),S=c(3814),g=c(9424),x=c(5272),f=c(344),P=c(7656),E=c(6146),I=c(3200),e=c(4438),j=c(9079),A=c(4978),p=c(4006),m=c(2840),h=c(1448),R=c(5909),v=c(177),D=c(5236);const T=(r,a)=>a.id,F=r=>({"font-bold text-black":r});function b(r,a){1&r&&(e.j41(0,"div",3),e.nrm(1,"app-inno-spin"),e.k0s())}function O(r,a){1&r&&(e.j41(0,"div",6),e.nrm(1,"app-inno-empty-data",7),e.k0s()),2&r&&(e.R7$(),e.Y8G("title","EMPTY.NoResult"))}function B(r,a){if(1&r&&(e.j41(0,"span",15),e.EFF(1),e.k0s()),2&r){const n=e.XpG(3);e.R7$(),e.SpI(" ","Ascending"===n.sortDirection?"arrow_upward":"arrow_downward"," ")}}function M(r,a){if(1&r){const n=e.RV6();e.j41(0,"div",10)(1,"div",11)(2,"div",12)(3,"app-inno-form-checkbox",13),e.bIt("onChange",function(){const o=e.eBV(n).$index,i=e.XpG(3);return e.Njj(i.handleToggleCheckedIndex(o))}),e.k0s()(),e.j41(4,"p",19),e.EFF(5),e.k0s()(),e.j41(6,"p",20),e.EFF(7),e.k0s(),e.j41(8,"p",21),e.EFF(9),e.k0s(),e.j41(10,"p",21),e.EFF(11),e.k0s(),e.j41(12,"p",22),e.EFF(13),e.nI1(14,"date"),e.k0s()()}if(2&r){let n,t,o;const i=a.$implicit,l=a.$index,d=e.XpG(3);e.R7$(3),e.Y8G("checked",d.isCheckedIndex(l)),e.R7$(2),e.SpI(" ",null!==(n=i.serviceName)&&void 0!==n?n:""," "),e.R7$(2),e.SpI(" ",null!==(t=i.rate)&&void 0!==t?t:"0"," "),e.R7$(2),e.SpI(" ",null!==(o=i.description)&&void 0!==o?o:""," "),e.R7$(2),e.SpI(" ",d.getNameSelectedTaxes(null==i?null:i.taxes)," "),e.R7$(2),e.SpI(" ",e.i5U(14,6,i.createdAt,d._storeService.getdateFormat())," ")}}function k(r,a){if(1&r){const n=e.RV6();e.nrm(0,"div",8),e.j41(1,"div",9)(2,"div",10)(3,"div",11)(4,"div",12)(5,"app-inno-form-checkbox",13),e.bIt("onChange",function(o){e.eBV(n);const i=e.XpG(2);return e.Njj(i.handleCheckedAll(o))}),e.k0s()(),e.j41(6,"p",14),e.bIt("click",function(){e.eBV(n);const o=e.XpG(2);return e.Njj(o.sortName("serviceName"))}),e.EFF(7),e.nI1(8,"translate"),e.DNE(9,B,2,1,"span",15),e.k0s()(),e.j41(10,"p",16),e.EFF(11),e.nI1(12,"translate"),e.k0s(),e.j41(13,"p",17),e.EFF(14),e.nI1(15,"translate"),e.k0s(),e.j41(16,"p",17),e.EFF(17),e.nI1(18,"translate"),e.k0s(),e.j41(19,"p",17),e.EFF(20),e.nI1(21,"translate"),e.k0s()(),e.Z7z(22,M,15,9,"div",10,T),e.j41(24,"ejs-pager",18),e.bIt("click",function(o){e.eBV(n);const i=e.XpG(2);return e.Njj(i.onPageChange(o))}),e.k0s()()}if(2&r){const n=e.XpG(2);e.R7$(5),e.Y8G("checked",n.listIndexServiceSelected.length===n.listService.length),e.R7$(),e.Y8G("ngClass",e.eq3(22,F,"serviceName"===n.sortColumn)),e.R7$(),e.SpI(" ",e.bMT(8,12,"SERVICEFORPROJECT.ServiceName")," "),e.R7$(2),e.vxM("serviceName"==n.sortColumn?9:-1),e.R7$(2),e.SpI(" ",e.bMT(12,14,"SERVICEFORPROJECT.Rate")," "),e.R7$(3),e.SpI(" ",e.bMT(15,16,"SERVICEFORPROJECT.Description")," "),e.R7$(3),e.SpI(" ",e.bMT(18,18,"SERVICEFORPROJECT.Taxes")," "),e.R7$(3),e.SpI(" ",e.bMT(21,20,"SERVICEFORPROJECT.Date")," "),e.R7$(2),e.Dyx(n.listService),e.R7$(2),e.Y8G("pageSize",n.pageSizesDefault)("totalRecordsCount",n.totalPages)("currentPage",n.currentPage)("pageSizes",n.pageSizes)}}function N(r,a){if(1&r&&e.DNE(0,O,2,1,"div",6)(1,k,25,24),2&r){const n=e.XpG();e.vxM(0==n.listService.length?0:1)}}function y(r,a){if(1&r){const n=e.RV6();e.j41(0,"div",23)(1,"button",24),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handleSubmit())}),e.EFF(2),e.nI1(3,"translate"),e.k0s()(),e.j41(4,"div",25)(5,"button",26),e.bIt("click",function(){e.eBV(n);const o=e.XpG();return e.Njj(o.handleCreateNewService())}),e.nrm(6,"img",27),e.EFF(7),e.nI1(8,"translate"),e.k0s()()}2&r&&(e.R7$(2),e.SpI(" ",e.bMT(3,2,"BUTTON.Save")," "),e.R7$(5),e.SpI(" ",e.bMT(8,4,"SERVICEFORPROJECT.Buttons.CreateNewService")," "))}m.is5.Inject(m.Rav);let L=(()=>{var r;class a{constructor(t,o){(0,s.A)(this,"dialogRef",void 0),(0,s.A)(this,"data",void 0),(0,s.A)(this,"sort",void 0),(0,s.A)(this,"cancel",new e.bkB),(0,s.A)(this,"submit",new e.bkB),(0,s.A)(this,"getNameSelectedTaxes",R.Xj),(0,s.A)(this,"listService",[]),(0,s.A)(this,"listIndexServiceSelected",[]),(0,s.A)(this,"projectId",void 0),(0,s.A)(this,"projectName",void 0),(0,s.A)(this,"isFetching",!1),(0,s.A)(this,"totalPages",1),(0,s.A)(this,"currentPage",1),(0,s.A)(this,"pageSizes",[10,20,50,100]),(0,s.A)(this,"pageSizesDefault",20),(0,s.A)(this,"sortDirection","Ascending"),(0,s.A)(this,"sortColumn",""),(0,s.A)(this,"_storeService",(0,e.WQX)(u.n)),(0,s.A)(this,"serviceService",(0,e.WQX)(x.N)),(0,s.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,s.A)(this,"modifyItemAndServiceDialog",(0,e.WQX)(S.v)),this.dialogRef=t,this.data=o,this.projectId=this.data.projectId}ngOnInit(){this.sortDirection="Ascending",this.sortColumn="serviceName",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)}static getComponent(){return a}getUniqueItemsByName(t,o){return[...t.filter(i=>!o.some(l=>l.serviceName===i.serviceName)),...o.filter(i=>!t.some(l=>l.serviceName===i.serviceName))]}onPageChange(t){t?.newProp?.pageSize&&(this.pageSizesDefault=t.newProp.pageSize,this.sort?this.handleSort(this.sortDirection):this.LoadAllService()),t?.currentPage&&(this.currentPage=t.currentPage,this.sort?this.handleSort(this.sortDirection):this.LoadAllService())}LoadAllService(){this.isFetching=!0;const t={Page:this.currentPage??1,PageSize:this.pageSizesDefault,...this.sort,ProjectId:this.projectId,isInProject:!1};this.serviceService.GetAllService(t).pipe((0,j.pQ)(this.destroyRef)).subscribe({next:o=>{this.totalPages=o.totalRecords,this.listService=o.data.map(i=>({...i,projectName:i.project?.projectName,isNewItem:!0})),this.isFetching=!1,this.data.listService&&(this.listService=this.getUniqueItemsByName(this.data.listService,this.listService))}})}handleCreateNewService(){this.modifyItemAndServiceDialog.open({mode:C.Q.Service,isShowProject:!1}).then(o=>{o.afterClosed().subscribe(i=>{i&&this.LoadAllService()})})}handleCheckedAll(t){this.listIndexServiceSelected=t?this.listService.map((o,i)=>i):[]}isCheckedIndex(t){return this.listIndexServiceSelected.includes(t)}handleToggleCheckedIndex(t){const o=this.isCheckedIndex(t);let i=[...this.listIndexServiceSelected];o?i=i.filter(l=>l!==t):i.push(t),this.listIndexServiceSelected=i}sortName(t){this.sortColumn===t?(this.sortDirection="Ascending"===this.sortDirection?"Descending":"Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)):(this.sortColumn=t,this.sortDirection="Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection))}handleCancel(){this.dialogRef.close()}handleSort(t){switch(t){case"Descending":case"Ascending":this.LoadAllService()}}handleSubmit(){const t=this.listService.filter((o,i)=>this.listIndexServiceSelected.includes(i));this.dialogRef.close(t.map(({project:o,...i})=>i))}}return r=a,(0,s.A)(a,"\u0275fac",function(t){return new(t||r)(e.rXU(p.CP),e.rXU(p.Vh))}),(0,s.A)(a,"\u0275cmp",e.VBU({type:r,selectors:[["app-add-service-for-project"]],outputs:{cancel:"cancel",submit:"submit"},standalone:!0,features:[e.aNF],decls:8,vars:4,consts:[["customSubmitNewInvoice",""],[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"w-full","py-2","flex","justify-center","items-center"],["footer",""],[3,"onCancel","customSubmitButton","isDisableSubmit"],[1,"w-full"],[3,"title"],[1,"container-full","mt-[24px]","flex","items-center","justify-between","flex-wrap","gap-2"],[1,"overflow-auto","w-full"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold","text-right","cursor-pointer",3,"click","ngClass"],[1,"material-icons","pl-1","!text-[15px]"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"text-text-primary","text-text-sm-regular","text-wrap"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center","text-wrap"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center"],[1,"flex","items-center","gap-[12px]"],[1,"button-outline-primary","button-size-md",3,"click"],[1,"flex","flex-wrap","justify-center","items-center","lg:justify-between","lg:mt-0","lg:flex-nowrap"],[1,"button-size-md","button-primary","min-w-fit",3,"click"],["src","../../../assets/img/icon/ic_add_white.svg","alt","icon"]],template:function(t,o){if(1&t){const i=e.RV6();e.j41(0,"app-inno-modal-wrapper",1),e.bIt("onClose",function(){return e.eBV(i),e.Njj(o.handleCancel())}),e.j41(1,"div",2),e.DNE(2,b,2,0,"div",3)(3,N,2,1),e.k0s(),e.j41(4,"div",4)(5,"app-inno-modal-footer",5),e.bIt("onCancel",function(){return e.eBV(i),e.Njj(o.handleCancel())}),e.k0s(),e.DNE(6,y,9,6,"ng-template",null,0,e.C5r),e.k0s()()}if(2&t){const i=e.sdS(7);e.Y8G("title","SERVICEFORPROJECT.Title"),e.R7$(2),e.vxM(o.isFetching?2:3),e.R7$(3),e.Y8G("customSubmitButton",i)("isDisableSubmit",!o.listIndexServiceSelected.length)}},dependencies:[h.iov,h.BzB,I.J,g.f,A.I,f.k,P.V,E.G,v.YU,v.vh,D.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(170px,1fr) 130px 130px 100px 120px 120px}.addBorderIsTable[_ngcontent-%COMP%]{border-bottom:1px solid;padding-bottom:8px;border-color:var(--border-primary)}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),a})()}}]);