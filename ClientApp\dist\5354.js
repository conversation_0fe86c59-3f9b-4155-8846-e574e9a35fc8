"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5354],{3200:(I,h,r)=>{r.d(h,{J:()=>u});var o=r(9842),v=r(177),s=r(5236),t=r(4438);function d(l,c){if(1&l&&(t.j41(0,"p",4),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&l){const m=t.XpG();t.R7$(),t.SpI(" ",t.bMT(2,1,m.description)," ")}}let u=(()=>{var l;class c{constructor(){(0,o.A)(this,"title",""),(0,o.A)(this,"description",""),(0,o.A)(this,"icon",""),(0,o.A)(this,"defaultIcon","../../../assets/img/empty_invoice.png")}}return l=c,(0,o.A)(c,"\u0275fac",function(_){return new(_||l)}),(0,o.A)(c,"\u0275cmp",t.VBU({type:l,selectors:[["app-inno-empty-data"]],inputs:{title:"title",description:"description",icon:"icon"},standalone:!0,features:[t.aNF],decls:8,vars:7,consts:[[1,"w-full","flex","flex-col","items-center"],["alt","Icon",1,"h-[120px]",3,"src"],[1,"flex","flex-col","items-center","gap-[4px]"],[1,"text-text-tertiary","text-headline-xs-bold","text-center"],[1,"text-text-sm-regular","text-text-tertiary","text-center"]],template:function(_,g){1&_&&(t.j41(0,"div",0),t.nrm(1,"img",1),t.j41(2,"div",2)(3,"p",3),t.EFF(4),t.nI1(5,"translate"),t.nI1(6,"translate"),t.k0s(),t.DNE(7,d,3,3,"p",4),t.k0s()()),2&_&&(t.R7$(),t.Y8G("src",g.icon||g.defaultIcon,t.B4B),t.R7$(3),t.SpI(" ",g.title?t.bMT(5,3,g.title):t.bMT(6,5,"COMMON.EmptyData")," "),t.R7$(3),t.vxM(g.description?7:-1))},dependencies:[v.MD,s.h,s.D9],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}"]})),c})()},9424:(I,h,r)=>{r.d(h,{f:()=>d});var o=r(9842),v=r(177),s=r(4438);const t=(u,l,c)=>({"w-4 h-4":u,"w-6 h-6":l,"w-10 h-10":c});let d=(()=>{var u;class l{constructor(){(0,o.A)(this,"size","md")}}return u=l,(0,o.A)(l,"\u0275fac",function(m){return new(m||u)}),(0,o.A)(l,"\u0275cmp",s.VBU({type:u,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[s.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(m,_){1&m&&(s.j41(0,"div",0),s.qSk(),s.j41(1,"svg",1),s.nrm(2,"path",2)(3,"path",3),s.k0s(),s.joV(),s.j41(4,"span",4),s.EFF(5,"Loading..."),s.k0s()()),2&m&&(s.R7$(),s.Y8G("ngClass",s.sMw(1,t,"sm"===_.size,"md"===_.size,"lg"===_.size)))},dependencies:[v.MD,v.YU]})),l})()},7572:(I,h,r)=>{r.d(h,{P:()=>y});var o=r(9842),v=r(3492),s=r(1556),t=r(4438),d=r(5236),u=r(9079),l=r(9088);const c=["fileInput"];function m(p,C){if(1&p){const i=t.RV6();t.j41(0,"button",7),t.bIt("click",function(n){t.eBV(i);const a=t.XpG(2);return t.Njj(a.handleRemoveFile(n))}),t.nrm(1,"img",8),t.k0s()}}function _(p,C){if(1&p){const i=t.RV6();t.j41(0,"div",3),t.bIt("dragover",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDragOver(n))})("dragleave",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDragLeave(n))})("drop",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDrop(n))})("click",function(){t.eBV(i);const n=t.XpG();return t.Njj(n.triggerFileInput())}),t.j41(1,"div",4),t.DNE(2,m,2,0,"button",5),t.nrm(3,"img",6),t.k0s()()}if(2&p){const i=t.XpG();t.R7$(2),t.vxM(i.canEdit?2:-1),t.R7$(),t.Y8G("src",i.imageUrl,t.B4B)}}function g(p,C){if(1&p&&(t.j41(0,"div",9),t.EFF(1),t.nI1(2,"translate"),t.k0s()),2&p){const i=t.XpG(2);t.R7$(),t.SpI(" ",t.bMT(2,1,i.placeholder)," ")}}function E(p,C){if(1&p){const i=t.RV6();t.j41(0,"div",4)(1,"button",7),t.bIt("click",function(n){t.eBV(i);const a=t.XpG(3);return t.Njj(a.handleRemoveFileUpload(n))}),t.nrm(2,"img",8),t.k0s(),t.nrm(3,"img",6),t.k0s()}if(2&p){const i=t.XpG(3);t.R7$(3),t.Y8G("src",null==i.previewFileUpload?null:i.previewFileUpload[0],t.B4B)}}function M(p,C){if(1&p&&t.DNE(0,E,4,1,"div",4),2&p){const i=t.XpG(2);t.vxM("image"===i.accept?0:-1)}}function U(p,C){if(1&p){const i=t.RV6();t.j41(0,"div",3),t.bIt("dragover",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDragOver(n))})("dragleave",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDragLeave(n))})("drop",function(n){t.eBV(i);const a=t.XpG();return t.Njj(a.onDrop(n))})("click",function(){t.eBV(i);const n=t.XpG();return t.Njj(n.triggerFileInput())}),t.DNE(1,g,3,3,"div",9)(2,M,1,1),t.k0s()}if(2&p){const i=t.XpG();t.R7$(),t.vxM(i.fileDropped?2:1)}}let y=(()=>{var p;class C{constructor(){(0,o.A)(this,"isMultiple",!1),(0,o.A)(this,"imageUrl",void 0),(0,o.A)(this,"canEdit",!1),(0,o.A)(this,"isBussiness",!1),(0,o.A)(this,"accept","image"),(0,o.A)(this,"placeholder","COMMON.UploadPicture"),(0,o.A)(this,"onChange",new t.bkB),(0,o.A)(this,"fileDropped",!1),(0,o.A)(this,"previewFileUpload",[]),(0,o.A)(this,"layoutUtilsService",(0,t.WQX)(s.Z)),(0,o.A)(this,"translate",(0,t.WQX)(d.c$)),(0,o.A)(this,"toastService",(0,t.WQX)(v.f)),(0,o.A)(this,"companyService",(0,t.WQX)(l.B)),(0,o.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,o.A)(this,"fileInput",void 0)}get acceptInput(){switch(this.accept){case"image":return"image/*";case"document":return"application/*";default:return"*"}}onDragOver(e){e.preventDefault(),e.stopPropagation(),e.currentTarget.classList.add("active")}onDragLeave(e){e.currentTarget.classList.remove("active")}onDrop(e){e.preventDefault(),e.stopPropagation(),e.currentTarget.classList.remove("active");const a=e.dataTransfer?.files;a&&a.length>0&&(this.processFile(a),this.fileDropped=!0)}onFileSelect(e){this.processFile(e.target.files??[]),this.fileDropped=!0}triggerFileInput(){this.fileInput.nativeElement.click()}processFile(e){if(e.length&&"image"===(this.onChange.emit(e),this.accept))for(let n=0;n<e.length;n++){const a=e[n],f=new FileReader;f.onload=()=>{this.previewFileUpload=[],this.imageUrl="",this.previewFileUpload?.push(f.result)},f.readAsDataURL(a)}}handleRemoveFile(e){e.stopPropagation();const n=this.translate.instant("TOAST.Delete"),a=this.translate.instant("TOAST.DeleteImg");this.layoutUtilsService.alertDelete({title:n,description:a}).then(f=>{f&&this.isBussiness&&this.canEdit&&this.companyService.RemoveImgCompany().pipe((0,u.pQ)(this.destroyRef)).subscribe(D=>{D?(this.imageUrl=void 0,this.fileDropped=!1,this.previewFileUpload=[],this.onChange.emit(null),this.toastService.showSuccess(this.translate.instant("TOAST.Delete"),this.translate.instant("TOAST.Success"))):this.toastService.showError(this.translate.instant("TOAST.Fail"),this.translate.instant("TOAST.Fail"))})})}handleRemoveFileUpload(e){e.stopPropagation(),this.fileDropped=!1,this.imageUrl=void 0,this.previewFileUpload=[],this.onChange.emit(null)}}return p=C,(0,o.A)(C,"\u0275fac",function(e){return new(e||p)}),(0,o.A)(C,"\u0275cmp",t.VBU({type:p,selectors:[["app-inno-upload"]],viewQuery:function(e,n){if(1&e&&t.GBs(c,5),2&e){let a;t.mGM(a=t.lsd())&&(n.fileInput=a.first)}},inputs:{isMultiple:"isMultiple",imageUrl:"imageUrl",canEdit:"canEdit",isBussiness:"isBussiness",accept:"accept",placeholder:"placeholder"},outputs:{onChange:"onChange"},standalone:!0,features:[t.Jv_([s.Z]),t.aNF],decls:4,vars:3,consts:[["fileInput",""],[1,"drop-zone"],["type","file","hidden","",3,"change","multiple","accept"],[1,"drop-zone",3,"dragover","dragleave","drop","click"],[1,"previewImageWrapper"],[1,"buttonRemove"],["alt","Thumbnail",1,"previewImage",3,"src"],[1,"buttonRemove",3,"click"],["src","../../../assets/img/icon/ic_remove.svg","alt","Icon",1,"w-[16px]"],[1,"w-full","h-full","flex","justify-center","items-center","text-center","text-text-disabled","text-text-sm-semibold","textTitle"]],template:function(e,n){if(1&e){const a=t.RV6();t.DNE(0,_,4,2,"div",1)(1,U,3,1,"div",1),t.j41(2,"input",2,0),t.bIt("change",function(D){return t.eBV(a),t.Njj(n.onFileSelect(D))}),t.k0s()}2&e&&(t.vxM(n.imageUrl?0:1),t.R7$(2),t.FS9("accept",n.acceptInput),t.Y8G("multiple",n.isMultiple))},dependencies:[d.h,d.D9],styles:[".drop-zone[_ngcontent-%COMP%]{border-color:var(--border-secondary);border-radius:4px;background-color:var(--bg-secondary);height:100px;border:1px dashed;padding:5px;cursor:pointer;transition:background-color .3s;position:relative}.drop-zone[_ngcontent-%COMP%]:hover, .drop-zone.active[_ngcontent-%COMP%]{border-style:solid;border-color:var(--border-brand);background-color:var(--bg-brand-primary)}.drop-zone[_ngcontent-%COMP%]:hover   .textTitle[_ngcontent-%COMP%]{color:var(--border-brand)}.previewImageWrapper[_ngcontent-%COMP%]{position:absolute;width:100%;height:100%;top:0;left:0;z-index:1;border-radius:4px}.previewImage[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:4px}.buttonRemove[_ngcontent-%COMP%]{width:20px;height:20px;z-index:2;border-radius:50%;display:flex;justify-content:center;align-items:center;position:absolute;top:-5px;right:-5px;background-color:var(--text-danger)}.buttonRemove[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:brightness(0) invert(1)}"]})),C})()},9088:(I,h,r)=>{r.d(h,{B:()=>u});var o=r(9842),v=r(1626),s=r(4438);const d=r(5312).c.HOST_API+"/api";let u=(()=>{var l;class c{constructor(){(0,o.A)(this,"http",(0,s.WQX)(v.Qq))}CreateCompany(_){return this.http.post(d+"/Company",_)}UpdateCompany(_){return this.http.put(d+"/Company",_)}RemoveImgCompany(){return this.http.put(d+"/Company/RemoveImgCompany",null)}UpdateFinancial(_){return this.http.post(d+"/Company/UpdateFinancial",_)}GetCurrencyCompany(){return this.http.get(d+"/Company/GetCurrencyCompany?")}GetInforCompany(){return this.http.get(d+"/Company/GetInforCompany")}}return l=c,(0,o.A)(c,"\u0275fac",function(_){return new(_||l)}),(0,o.A)(c,"\u0275prov",s.jDH({token:l,factory:l.\u0275fac,providedIn:"root"})),c})()}}]);