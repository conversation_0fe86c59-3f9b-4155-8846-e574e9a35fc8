"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9269],{9269:(D,p,n)=>{n.r(p),n.d(p,{ActivateMemberComponent:()=>R});var a=n(9842),f=n(3492),h=n(1342),v=n(2928),e=n(4438),r=n(9417),m=n(33),b=n(1225),u=n(9079),C=n(6146),_=n(3719),g=n(5861),E=n(7552),w=n(1110),M=n(9437);function P(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"email is required"),e.k0s())}function k(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"Invalid email"),e.k0s())}function x(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"Password is required"),e.k0s())}function y(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"Password minlength 6"),e.k0s())}function O(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"ConfirmPassword is required"),e.k0s())}function A(i,d){1&i&&(e.j41(0,"mat-error",9),e.EFF(1,"Passsword and Confirm Password didn't match."),e.k0s())}function F(i,d){if(1&i){const o=e.RV6();e.j41(0,"div",1)(1,"h2",2),e.EFF(2,"InnoBooks"),e.k0s(),e.j41(3,"form",3),e.bIt("ngSubmit",function(){e.eBV(o);const s=e.XpG();return e.Njj(s.onSubmit())}),e.j41(4,"div",4)(5,"div"),e.nrm(6,"input",5),e.k0s(),e.j41(7,"div"),e.nrm(8,"input",6),e.k0s()(),e.j41(9,"div",7)(10,"div"),e.nrm(11,"input",8),e.k0s(),e.DNE(12,P,2,0,"mat-error",9)(13,k,2,0,"mat-error",9),e.k0s(),e.j41(14,"div",7)(15,"div",10),e.nrm(16,"input",11),e.j41(17,"button",12),e.bIt("click",function(){e.eBV(o);const s=e.XpG();return e.Njj(s.togglePassword())}),e.j41(18,"span",13),e.EFF(19," visibility "),e.k0s(),e.j41(20,"span",14),e.EFF(21," visibility_off "),e.k0s()()(),e.DNE(22,x,2,0,"mat-error",9)(23,y,2,0,"mat-error",9),e.k0s(),e.j41(24,"div",7)(25,"div"),e.nrm(26,"input",15),e.k0s(),e.DNE(27,O,2,0,"mat-error",9)(28,A,2,0,"mat-error",9),e.k0s(),e.j41(29,"div"),e.nrm(30,"input",16),e.j41(31,"span",17),e.EFF(32," I confirm that I have read and agree to InnoBooks Terms of Service and Privacy Policy. "),e.k0s()(),e.j41(33,"button",18),e.EFF(34,"Get Started"),e.k0s()()()}if(2&i){const o=e.XpG();e.R7$(3),e.Y8G("formGroup",o.registerForm),e.R7$(9),e.vxM((o.f.email.dirty||o.f.email.touched)&&o.f.email.hasError("required")?12:-1),e.R7$(),e.vxM((o.f.email.dirty||o.f.email.touched)&&o.f.email.hasError("email")?13:-1),e.R7$(9),e.vxM((o.f.password.dirty||o.f.password.touched)&&o.f.password.hasError("required")?22:-1),e.R7$(),e.vxM((o.f.password.dirty||o.f.password.touched)&&o.f.password.hasError("minlength")?23:-1),e.R7$(4),e.vxM(o.f.confirmPassword.touched&&o.f.confirmPassword.hasError("required")?27:-1),e.R7$(),e.vxM(o.f.confirmPassword.dirty&&o.f.confirmPassword.hasError("confirmPasswordValidator")?28:-1),e.R7$(5),e.Y8G("disabled",!o.registerForm.valid||!o.registerForm.controls.agree.value)}}let R=(()=>{var i;class d{constructor(){(0,a.A)(this,"listCompany",[]),(0,a.A)(this,"isValidating",!0),(0,a.A)(this,"activatedRoute",(0,e.WQX)(m.nX)),(0,a.A)(this,"showPassword",void 0),(0,a.A)(this,"registerForm",void 0),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"router",(0,e.WQX)(m.Ix)),(0,a.A)(this,"auth_services",(0,e.WQX)(v.k)),(0,a.A)(this,"spinnerService",(0,e.WQX)(h.D)),(0,a.A)(this,"formBuilder",(0,e.WQX)(r.ze)),(0,a.A)(this,"_toastService",(0,e.WQX)(f.f)),(0,a.A)(this,"_storeService",(0,e.WQX)(w.n)),(0,a.A)(this,"token",""),this.registerForm=this.formBuilder.group({firstname:["",r.k0.compose([r.k0.required])],lastname:["",r.k0.compose([r.k0.required])],email:["",r.k0.compose([r.k0.required,r.k0.email])],password:["",r.k0.compose([r.k0.required,r.k0.minLength(6)])],confirmPassword:["",r.k0.compose([r.k0.required])],agree:[!1,r.k0.compose([r.k0.required])]},{validator:(0,b.S)("password","confirmPassword")})}togglePassword(){const t=document.getElementById("password"),s=document.getElementById("showIcon"),l=document.getElementById("hideIcon");"password"===t?.type?(t.type="text",s?.classList.remove("!hidden"),l?.classList.add("!hidden")):(t.type="password",s?.classList.add("!hidden"),l?.classList.remove("!hidden"))}ngOnInit(){this.spinnerService.show(),this.activatedRoute.queryParams.pipe((0,u.pQ)(this.destroyRef)).subscribe(t=>{t?.email&&(this.registerForm.controls.email.setValue(t.email),this.registerForm.controls.email.disable()),t?.firstName&&this.registerForm.controls.firstname.setValue(t.firstName),t?.lastName&&this.registerForm.controls.lastname.setValue(t.lastName),t?.token&&(this.token=t.token),t?.token&&t?.email&&this.auth_services.ValidateInvitation({email:t.email,token:t.token}).pipe((0,u.pQ)(this.destroyRef),(0,M.W)(s=>(this.spinnerService.hide(),this.isValidating=!1,s))).subscribe(s=>{this._storeService.setChooseBusiness({businessId:s?.businessId}),s?.validAccount&&(this.auth_services.getAccessToken()&&s?.userId===this.auth_services.getIdUser()?this.router.navigate(["/"]):(this.auth_services.logout(),this.router.navigate(["/login"]))),this.isValidating=!1,this.spinnerService.hide()})})}get f(){return this.registerForm.controls}onSubmit(){const t=Intl.DateTimeFormat().resolvedOptions().timeZone,s=g.S[t]||t,l=g.A.find(c=>c.Moment.includes(s));this.spinnerService.show(),this.auth_services.Register({firstName:this.registerForm.controls.firstname.value,lastName:this.registerForm.controls.lastname.value,email:this.registerForm.controls.email.value,password:this.registerForm.controls.password.value,token:this.token,timeZoneId:l?.Code}).pipe((0,u.pQ)(this.destroyRef)).subscribe(c=>{c?(this.spinnerService.hide(),this.auth_services.saveToken_cookie(c.accessToken,c.refreshToken),(new E.X7).decodeToken(c.accessToken),this.router.navigate(["/"])):(this.spinnerService.hide(),this._toastService.showError("Email already exists","Error"))})}}return i=d,(0,a.A)(d,"\u0275fac",function(t){return new(t||i)}),(0,a.A)(d,"\u0275cmp",e.VBU({type:i,selectors:[["app-activate-member"]],standalone:!0,features:[e.aNF],decls:2,vars:1,consts:[[1,"pageRegister"],[1,"wrapForm"],[1,"text-center"],["autocomplete","off",3,"ngSubmit","formGroup"],[1,"grid","gap-6","mb-6","md:grid-cols-2"],["type","text","autocomplete","off","formControlName","firstname","placeholder","First name","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],["type","text","id","last_name","formControlName","lastname","autocomplete","off","placeholder","Last name","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"form-group","mb-3"],["type","email","id","_email","formControlName","email","placeholder","Email","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],[1,"matError"],[1,"relative","w-full"],["type","password","id","password","autocomplete","new-password","formControlName","password","placeholder","Enter password",1,"w-full","p-2.5","border","text-sm","bg-gray-50","border-gray-300","rounded","focus:ring-blue-500","focus:border-blue-500","block"],["type","button",1,"absolute","right-2","top-2","text-gray-600","hover:text-gray-900","focus:outline-none",3,"click"],["id","showIcon",1,"material-icons","!hidden","text-sm"],["id","hideIcon",1,"material-icons","text-sm"],["type","password","id","_confirmPassword","formControlName","confirmPassword","autocomplete","off","placeholder","Confirm Password","required","",1,"bg-gray-50","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5"],["type","checkbox","formControlName","agree","name","agree",1,"form-check-input","text-lg"],[2,"font-size","12px"],["type","submit",1,"btn","btn-success","mt-3","text-center","w-full",3,"disabled"]],template:function(t,s){1&t&&(e.j41(0,"div",0),e.DNE(1,F,35,8,"div",1),e.k0s()),2&t&&(e.R7$(),e.vxM(s.isValidating?-1:1))},dependencies:[C.G,r.qT,r.me,r.Zm,r.BC,r.cb,r.YS,r.j4,r.JD,_.RG,_.TL,m.iI],styles:[".pageRegister[_ngcontent-%COMP%]{width:100vw;height:100vh;background-image:url(bg_login.jpg);background-size:cover;overflow-x:hidden;background-position:center;background-repeat:no-repeat;width:calc(100vw + 66px);transform:translate(-66px);display:flex;justify-content:center;align-items:center}@media screen and (max-width: 860px){.pageRegister[_ngcontent-%COMP%]{transform:unset;width:100vw}}.pageRegister[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0}.pageRegister[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:22px;color:#0089ef;text-decoration:none;cursor:pointer}.pageRegister[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]:hover{text-decoration:underline}.pageRegister[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]{width:90%;max-height:600px;overflow:auto;padding:32px;background:#fff;border-radius:16px;max-width:420px}.pageRegister[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]{height:auto;margin-left:auto;margin-right:auto;width:100%;display:block;justify-content:center;align-items:center;background-color:#9199af}.pageRegister[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:100%;width:auto;object-fit:contain;background:#fff}.left-icon[_ngcontent-%COMP%]{position:absolute;top:20%;left:15px}[_ngcontent-%COMP%]::-webkit-scrollbar{width:7px;margin-top:20px;opacity:0;height:12px}[_ngcontent-%COMP%]::-webkit-scrollbar-track{-webkit-box-shadow:inset 0 0 6px #c2c2c9;border-radius:10px;margin-top:20px;margin-bottom:20px}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{border-radius:10px;-webkit-box-shadow:inset 0 0 6px #c2c2c9}"]})),d})()}}]);