"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[6194],{359:(D,v,s)=>{s.d(v,{l:()=>S});var a=s(9842),g=s(1626),m=s(4438),C=s(33);const e=s(5312).c.HOST_API+"/api";let S=(()=>{var h;class d{constructor(){(0,a.A)(this,"http",(0,m.WQX)(g.Qq)),(0,a.A)(this,"router",(0,m.WQX)(C.Ix))}CreateUserBusiness(o){return this.http.post(e+"/Business/user-business",o)}GetUserBusiness(){return this.http.get(e+"/Business/user-business").pipe(o=>o)}GetBusinessById(o){return this.http.get(e+`/Business/user-business-byid?businessId=${o}`)}GetInfoCompany(){return this.http.get(e+"/Business/GetInfoCompany")}GetAllUserBusiness(o){const u={...o};return Object.keys(u).forEach(p=>null==u[p]&&delete u[p]),this.http.get(e+"/Business/GetAllUserBusiness",{params:u})}AddMemberBusiness(o){return this.http.post(e+"/Business/AddMemberBusiness",o)}DeleteMemberInBusiness(o){return this.http.post(e+`/Business/DeleteMemberInBusiness?memberId=${o}`,null)}userBusinessById(o){return this.http.get(e+`/Business/userBusinessById?UserId=${o}`)}UpdateRoleMember(o,u){return this.http.get(e+`/Business/UpdateRoleMember?UserId=${o}&role=${u}`)}UpdateStatus(o){return this.http.post(e+"/Business/UpdateStatus",o)}SendMailAddMember(o){return this.http.post(e+"/Business/SendMailAddMember",o)}}return h=d,(0,a.A)(d,"\u0275fac",function(o){return new(o||h)}),(0,a.A)(d,"\u0275prov",m.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})),d})()},6194:(D,v,s)=>{s.r(v),s.d(v,{SwitchWorkspaceComponent:()=>w});var a=s(9842),g=s(1342),m=s(2928),C=s(1470),B=s(6146),e=s(4438),S=s(6674),h=s(359),d=s(9079),I=s(1110),o=s(4006),u=s(467),p=s(2716),b=s(7987);let A=(()=>{var i;class l extends p.H{open(n){var t=this;return(0,u.A)(function*(){const r=yield s.e(3239).then(s.bind(s,3239));return t.matDialog.open(r.ConfirmTimerComponent.getComponent(),{panelClass:"custom_dialog",data:n,width:"400px",disableClose:!0,scrollStrategy:new b.t0})})()}}return i=l,(0,a.A)(l,"\u0275fac",(()=>{let c;return function(t){return(c||(c=e.xGo(i)))(t||i)}})()),(0,a.A)(l,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),l})(),y=(()=>{var i;class l extends p.H{open(n){var t=this;return(0,u.A)(function*(){const r=yield Promise.all([s.e(3719),s.e(2076),s.e(9972)]).then(s.bind(s,9972));return t.matDialog.open(r.NewBusinessComponent.getComponent(),{panelClass:"custom_dialog",data:n,width:"500px",disableClose:!0,scrollStrategy:new b.t0})})()}}return i=l,(0,a.A)(l,"\u0275fac",(()=>{let c;return function(t){return(c||(c=e.xGo(i)))(t||i)}})()),(0,a.A)(l,"\u0275prov",e.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})),l})();var M=s(177),T=s(5236);const R=i=>({" bg-blue-500 !text-white":i});function U(i,l){1&i&&(e.j41(0,"div",13)(1,"span",15),e.EFF(2,"Active"),e.k0s()())}function x(i,l){if(1&i){const c=e.RV6();e.j41(0,"div",8),e.bIt("click",function(){const t=e.eBV(c).$implicit,r=e.XpG();return e.Njj(r.ChooseBusiness(t))}),e.j41(1,"button",9)(2,"div",10)(3,"div",11)(4,"span",12),e.EFF(5),e.k0s(),e.j41(6,"span"),e.EFF(7),e.nI1(8,"translate"),e.k0s()(),e.DNE(9,U,3,0,"div",13),e.k0s()(),e.j41(10,"span",14),e.EFF(11," info "),e.k0s()()}if(2&i){const c=l.$implicit,n=e.XpG();e.R7$(),e.Y8G("ngClass",e.eq3(6,R,n._storeService.getIdBusiness()==c.businessId)),e.R7$(4),e.JRh(c.businessName),e.R7$(2),e.JRh(c.role+(c.isOwner?" ("+e.bMT(8,4,"COMMON.Owner")+")":"")),e.R7$(2),e.vxM(0==c.status?9:-1)}}function k(i,l){1&i&&(e.j41(0,"div",4)(1,"div",16),e.qSk(),e.j41(2,"svg",17),e.nrm(3,"path",18)(4,"path",19),e.k0s(),e.joV(),e.j41(5,"span",20),e.EFF(6,"Loading..."),e.k0s()()())}let w=(()=>{var i;class l{static getComponent(){return l}constructor(n,t,r){(0,a.A)(this,"dialogRef",void 0),(0,a.A)(this,"confirmTimerDialog",void 0),(0,a.A)(this,"newBusinessDialog",void 0),(0,a.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,a.A)(this,"isloading",!1),(0,a.A)(this,"listUserBusiness",[]),(0,a.A)(this,"_spinnerService",(0,e.WQX)(g.D)),(0,a.A)(this,"businessService",(0,e.WQX)(h.l)),(0,a.A)(this,"authenticationService",(0,e.WQX)(m.k)),(0,a.A)(this,"_storeService",(0,e.WQX)(I.n)),(0,a.A)(this,"tempBusiness",void 0),this.dialogRef=n,this.confirmTimerDialog=t,this.newBusinessDialog=r}closeDialog(){this.dialogRef.close()}ngOnDestroy(){}ngOnInit(){this.isloading=!0,this.GetUserBusiness()}OpenConfirm(){this.confirmTimerDialog.open({}).then(t=>{t.afterClosed().subscribe(r=>{r?(this._spinnerService.show(),this._storeService.setChooseBusiness(this.tempBusiness),this.authenticationService.UpdateAccessToken({BusinessId:this.tempBusiness.businessId}).pipe((0,d.pQ)(this.destroyRef)).subscribe(f=>{f&&(this.authenticationService.saveToken_cookie(f.accessToken,f.refreshToken),this.dialogRef.close(f),this._spinnerService.hide(),localStorage.removeItem("isRunning"),localStorage.removeItem("ResumeData"),localStorage.removeItem("cacheClientProject"),localStorage.removeItem("cacheService"),this.tempBusiness=null)})):this.tempBusiness=null})})}GetUserBusiness(){this.businessService.GetUserBusiness().pipe((0,d.pQ)(this.destroyRef)).subscribe(n=>{this.isloading=!1,this.listUserBusiness=n,this.listUserBusiness.length>0&&!this._storeService.getChooseBusiness()&&this.DefaultChooseBusiness({businessId:this.listUserBusiness[0].businessId})})}DefaultChooseBusiness(n){this._spinnerService.show(),this._storeService.setChooseBusiness({businessId:n.businessId}),this.authenticationService.UpdateAccessToken({BusinessId:n.businessId}).pipe((0,d.pQ)(this.destroyRef)).subscribe(r=>{r&&(this.authenticationService.saveToken_cookie(r.accessToken,r.refreshToken),localStorage.removeItem("cacheClientProject"),localStorage.removeItem("cacheService"),this._spinnerService.hide(),this.dialogRef.close(r))})}ChooseBusiness(n){let t={businessId:n.businessId};this.tempBusiness=t,"true"!==localStorage.getItem("isRunning")?(this._spinnerService.show(),this._storeService.setChooseBusiness(t),this.authenticationService.UpdateAccessToken({BusinessId:n.businessId}).pipe((0,d.pQ)(this.destroyRef)).subscribe(r=>{r&&(this.authenticationService.saveToken_cookie(r.accessToken,r.refreshToken),this._spinnerService.hide(),localStorage.removeItem("cacheClientProject"),localStorage.removeItem("cacheService"),this.dialogRef.close(r))})):this.OpenConfirm()}openAddNewBusiness(){this.newBusinessDialog.open({}).then(t=>{t.afterClosed().subscribe(r=>{r&&this.GetUserBusiness()})})}}return i=l,(0,a.A)(l,"\u0275fac",function(n){return new(n||i)(e.rXU(o.CP),e.rXU(A),e.rXU(y))}),(0,a.A)(l,"\u0275cmp",e.VBU({type:i,selectors:[["app-switch-workspace"]],standalone:!0,features:[e.aNF],decls:15,vars:7,consts:[[3,"onClose"],[1,"fw-bold","m-0","w-full","text-start","pl-2.5"],[1,"list-view","mt-2","max-h-96","overflow-auto","direction",2,"direction","rtl"],[1,"flex","items-center","pl-3",2,"direction","ltr","text-align","left"],[1,"w-full","flex","justify-center"],[1,"mt-2","pl-3"],[1,"btn","btn-outline-secondary","btn-add-entry","btn-dashed",3,"click"],[1,"material-icons"],[1,"flex","items-center","pl-3",2,"direction","ltr","text-align","left",3,"click"],[1,"px-3","py-2","w-full","rounded-md","text-lg","border","border-gray-200","mb-3",3,"ngClass"],[1,"flex-row-between","gap-1"],[1,"text-lg-start","flex","flex-col"],[1,"fw-bold"],[1,"status-container"],[1,"material-icons","action","ml-2"],[1,"status"],["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"w-8","h-8","text-gray-200","animate-spin","dark:text-gray-600","fill-blue-600"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(n,t){1&n&&(e.j41(0,"app-innobook-modal-wrapper",0),e.bIt("onClose",function(){return t.closeDialog()}),e.j41(1,"div")(2,"h5",1),e.EFF(3),e.nI1(4,"translate"),e.k0s(),e.j41(5,"div",2),e.Z7z(6,x,12,8,"div",3,e.fX1),e.DNE(8,k,7,0,"div",4),e.j41(9,"div",5)(10,"button",6),e.bIt("click",function(){return t.openAddNewBusiness()}),e.j41(11,"span",7),e.EFF(12,"add"),e.k0s(),e.EFF(13),e.nI1(14,"translate"),e.k0s()()()()()),2&n&&(e.R7$(3),e.JRh(e.bMT(4,3,"COMMON.ChooseBusiness")),e.R7$(3),e.Dyx(t.listUserBusiness),e.R7$(2),e.vxM(t.isloading?8:-1),e.R7$(5),e.SpI(" ",e.bMT(14,5,"COMMON.NewEntry")," "))},dependencies:[B.G,M.YU,T.D9,C.j,S.wB],styles:[".e-dialog[_ngcontent-%COMP%]   .e-dlg-header-content[_ngcontent-%COMP%]{padding:10px 10px 0 0!important}"]})),l})()}}]);