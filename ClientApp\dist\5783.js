"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[5783],{5783:(x,g,n)=>{n.r(g),n.d(g,{ForgotPasswordComponent:()=>C});var e=n(9842),t=n(4438),o=n(9417),_=n(3719),m=n(6146),d=n(1342),P=n(3492),O=n(2928),u=n(9079),l=n(33),c=n(5236),h=n(1149),M=n(1328);const f=(a,i)=>({required:a,email:i});let C=(()=>{var a;class i{constructor(){(0,e.A)(this,"resetPasswordForm",void 0),(0,e.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,e.A)(this,"translate",(0,t.WQX)(c.c$)),(0,e.A)(this,"formBuilder",(0,t.WQX)(o.ze)),(0,e.A)(this,"auth_services",(0,t.WQX)(O.k)),(0,e.A)(this,"spinnerService",(0,t.WQX)(d.D)),(0,e.A)(this,"_toastService",(0,t.WQX)(P.f)),this.resetPasswordForm=this.formBuilder.group({email:["",[o.k0.required,o.k0.email]]})}get f(){return this.resetPasswordForm.controls}markAllControlsAsTouched(){Object.values(this.f).forEach(r=>{r.markAsTouched()})}onSubmit(){this.resetPasswordForm.invalid?this.markAllControlsAsTouched():(this.spinnerService.show(),this.auth_services.RecoverPassword(this.resetPasswordForm.controls.email.value).pipe((0,u.pQ)(this.destroyRef)).subscribe(r=>{this._toastService.showSuccess("Save",this.translate.instant("User.Reset.Password.SendMail")),this.spinnerService.hide()}))}}return a=i,(0,e.A)(i,"\u0275fac",function(r){return new(r||a)}),(0,e.A)(i,"\u0275cmp",t.VBU({type:a,selectors:[["app-forgot-password"]],standalone:!0,features:[t.aNF],decls:28,vars:29,consts:[[1,"max-w-md","w-full","space-y-8"],[1,"text-center"],[1,"text-headline-lg-bold"],[1,"mt-2","text-text-sm-regular","text-text-secondary"],[1,"mt-10",3,"ngSubmit","formGroup"],[1,"w-full"],[1,"text-text-secondary","text-text-sm-semibold","!mb-[5px]"],[1,"relative"],[1,"material-icons","absolute","top-[12px]","left-[10px]","z-10","text-text-tertiary","!text-[22px]"],["type","email","inputClassName","!h-[46px] pl-[40px]",3,"placeholder","formControl","value","errorMessages"],["type","submit",1,"button-size-md","button-primary","w-full","justify-center","mt-[34px]"],[1,"text-center","mt-6"],["routerLink","/sign-in",1,"text-text-sm-regular","font-medium","text-text-brand-primary"]],template:function(r,s){1&r&&(t.j41(0,"app-auth-layout")(1,"div",0)(2,"div",1)(3,"p",2),t.EFF(4),t.nI1(5,"translate"),t.k0s(),t.j41(6,"p",3),t.EFF(7),t.nI1(8,"translate"),t.k0s()(),t.j41(9,"form",4),t.bIt("ngSubmit",function(){return s.onSubmit()}),t.j41(10,"div",5)(11,"p",6),t.EFF(12),t.nI1(13,"translate"),t.k0s(),t.j41(14,"div",7)(15,"i",8),t.EFF(16,"email"),t.k0s(),t.nrm(17,"app-inno-form-input",9),t.nI1(18,"translate"),t.nI1(19,"translate"),t.nI1(20,"translate"),t.k0s()(),t.j41(21,"button",10),t.EFF(22),t.nI1(23,"translate"),t.k0s(),t.j41(24,"div",11)(25,"a",12),t.EFF(26),t.nI1(27,"translate"),t.k0s()()()()()),2&r&&(t.R7$(4),t.JRh(t.bMT(5,10,"FORGOTPASSWORD.Title")),t.R7$(3),t.SpI(" ",t.bMT(8,12,"FORGOTPASSWORD.Instruction")," "),t.R7$(2),t.Y8G("formGroup",s.resetPasswordForm),t.R7$(3),t.SpI(" ",t.bMT(13,14,"FORGOTPASSWORD.EmailLabel")," "),t.R7$(5),t.Y8G("placeholder",t.bMT(18,16,"FORGOTPASSWORD.EmailPlaceholder"))("formControl",s.f.email)("value",s.f.email.value)("errorMessages",t.l_i(26,f,t.bMT(19,18,"FORGOTPASSWORD.EmailRequired"),t.bMT(20,20,"FORGOTPASSWORD.EmailInvalid"))),t.R7$(5),t.SpI(" ",t.bMT(23,22,"FORGOTPASSWORD.ResetButton")," "),t.R7$(4),t.SpI(" ",t.bMT(27,24,"FORGOTPASSWORD.BackToSignIn")," "))},dependencies:[m.G,o.qT,o.BC,o.cb,o.l_,o.j4,c.D9,l.iI,l.Wk,_.RG,h.K,M.a],styles:[".linkRedirect[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:22px;color:#0089ef;text-decoration:none;cursor:pointer}.linkRedirect[_ngcontent-%COMP%]:hover{text-decoration:underline}.pageLogin[_ngcontent-%COMP%]{width:100vw;height:100vh;background-image:url(bg_login.jpg);background-size:cover;background-position:center;background-repeat:no-repeat;display:flex;justify-content:center;align-items:center}@media screen and (max-width: 860px){.pageLogin[_ngcontent-%COMP%]{transform:unset;width:100vw}}.pageLogin[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0}.pageLogin[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:22px;color:#0089ef;text-decoration:none;cursor:pointer}.pageLogin[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]:hover{text-decoration:underline}.pageLogin[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]{width:90%;padding:32px;background:#fff;border-radius:16px;max-width:420px}.pageLogin[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]{height:auto;margin-left:auto;margin-right:auto;width:100%;display:block;justify-content:center;align-items:center;background-color:#9199af}.pageLogin[_ngcontent-%COMP%]   .wrapForm[_ngcontent-%COMP%]   .wrapLogo[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{height:100%;width:auto;object-fit:contain;background:#fff}.pageLogin[_ngcontent-%COMP%]   .formLogin[_ngcontent-%COMP%]{width:100%;position:relative;display:flex;flex-direction:column;align-items:center}.pageLogin[_ngcontent-%COMP%]   .formLogin[_ngcontent-%COMP%]   .wrapTitleForm[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:8px}.pageLogin[_ngcontent-%COMP%]   .formLogin[_ngcontent-%COMP%]   .wrapTitleForm[_ngcontent-%COMP%]   .titleForm[_ngcontent-%COMP%]{font-weight:700;font-size:28px;line-height:36px;text-align:center;color:#0f182e;width:100%}.pageLogin[_ngcontent-%COMP%]   .wrapTitleForm[_ngcontent-%COMP%]   .desForm[_ngcontent-%COMP%]{font-weight:500;font-size:12px;line-height:16px;text-align:center;color:#9199af}.pageLogin[_ngcontent-%COMP%]   .wrapInputForm[_ngcontent-%COMP%]{width:100%;margin-top:15px;display:flex;flex-direction:column;gap:14px}.pageLogin[_ngcontent-%COMP%]   .rowRemember[_ngcontent-%COMP%]{margin-top:6px;width:100%;display:flex;justify-content:space-between;gap:10px;align-items:center}.pageLogin[_ngcontent-%COMP%]   .rowRemember[_ngcontent-%COMP%]   .customCheckbox[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:22px;color:#0f182e}.pageLogin[_ngcontent-%COMP%]   .actionForm[_ngcontent-%COMP%]{margin-top:15px;width:100%}.pageLogin[_ngcontent-%COMP%]   .actionForm[_ngcontent-%COMP%]   .buttonActionItem.colorPrimary[_ngcontent-%COMP%]{width:100%;height:48px;font-weight:500;font-size:16px;line-height:24px}.pageLogin[_ngcontent-%COMP%]   .actionForm[_ngcontent-%COMP%]   .textFooterForm[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:18px;color:#0f182e;text-align:center;margin-top:34px}.pageLogin[_ngcontent-%COMP%]   .actionForm[_ngcontent-%COMP%]   .textFooterForm[_ngcontent-%COMP%]   .linkRedirect[_ngcontent-%COMP%]{margin-left:5px}"]})),i})()}}]);