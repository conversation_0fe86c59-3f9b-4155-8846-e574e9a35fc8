"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[1763],{9424:(U,P,n)=>{n.d(P,{f:()=>I});var o=n(9842),t=n(177),c=n(4438);const T=(h,v,C)=>({"w-4 h-4":h,"w-6 h-6":v,"w-10 h-10":C});let I=(()=>{var h;class v{constructor(){(0,o.A)(this,"size","md")}}return h=v,(0,o.A)(v,"\u0275fac",function(a){return new(a||h)}),(0,o.A)(v,"\u0275cmp",c.VBU({type:h,selectors:[["app-inno-spin"]],inputs:{size:"size"},standalone:!0,features:[c.aNF],decls:6,vars:5,consts:[["role","status"],["aria-hidden","true","viewBox","0 0 100 101","fill","none","xmlns","http://www.w3.org/2000/svg",1,"inline","text-gray-200","animate-spin","fill-bg-brand-strong",3,"ngClass"],["d","M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z","fill","currentColor"],["d","M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z","fill","currentFill"],[1,"sr-only"]],template:function(a,d){1&a&&(c.j41(0,"div",0),c.qSk(),c.j41(1,"svg",1),c.nrm(2,"path",2)(3,"path",3),c.k0s(),c.joV(),c.j41(4,"span",4),c.EFF(5,"Loading..."),c.k0s()()),2&a&&(c.R7$(),c.Y8G("ngClass",c.sMw(1,T,"sm"===d.size,"md"===d.size,"lg"===d.size)))},dependencies:[t.MD,t.YU]})),v})()},8556:(U,P,n)=>{n.d(P,{K:()=>e});var o=n(9842),t=n(4438),c=n(5599),T=n(6146),I=n(4823),h=n(5236);function v(_,g){if(1&_){const u=t.RV6();t.j41(0,"button",4),t.bIt("click",function(){t.eBV(u);const m=t.XpG();return t.Njj(m.handleResume())}),t.nrm(1,"img",5),t.k0s()}}function C(_,g){if(1&_){const u=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(u);const m=t.XpG();return t.Njj(m.handleEdit())}),t.nrm(1,"img",7),t.k0s()}}function a(_,g){if(1&_){const u=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(u);const m=t.XpG();return t.Njj(m.handleDowload())}),t.nrm(1,"img",8),t.k0s()}}function d(_,g){if(1&_){const u=t.RV6();t.j41(0,"div",12)(1,"button",13),t.bIt("click",function(){t.eBV(u);const m=t.XpG(2);return t.Njj(m.handleArchive())}),t.EFF(2),t.nI1(3,"translate"),t.k0s(),t.j41(4,"button",14),t.bIt("click",function(){t.eBV(u);const m=t.XpG(2);return t.Njj(m.handleDelete())}),t.EFF(5),t.nI1(6,"translate"),t.k0s()()}2&_&&(t.R7$(2),t.SpI(" ",t.bMT(3,2,"COMMON.Archive")," "),t.R7$(3),t.SpI(" ",t.bMT(6,4,"COMMON.Delete")," "))}function r(_,g){if(1&_&&(t.j41(0,"app-inno-popover",9)(1,"button",10),t.nrm(2,"img",11),t.k0s()(),t.DNE(3,d,7,6,"ng-template",null,0,t.C5r)),2&_){const u=t.sdS(4);t.Y8G("content",u)}}function x(_,g){if(1&_){const u=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(u);const m=t.XpG(2);return t.Njj(m.handleArchive())}),t.nrm(1,"img",15),t.k0s()}}function D(_,g){if(1&_){const u=t.RV6();t.j41(0,"button",6),t.bIt("click",function(){t.eBV(u);const m=t.XpG(2);return t.Njj(m.handleDelete())}),t.nrm(1,"img",16),t.k0s()}}function b(_,g){if(1&_&&t.DNE(0,x,2,0,"button",3)(1,D,2,0,"button",3),2&_){const u=t.XpG();t.vxM(u.onArchive.observed?0:-1),t.R7$(),t.vxM(u.onDelete.observed?1:-1)}}let e=(()=>{var _;class g{constructor(){(0,o.A)(this,"onEdit",new t.bkB),(0,o.A)(this,"onResume",new t.bkB),(0,o.A)(this,"onDelete",new t.bkB),(0,o.A)(this,"onArchive",new t.bkB),(0,o.A)(this,"onDowload",new t.bkB)}handleResume(){this.onResume.emit()}handleEdit(){this.onEdit.emit()}handleDelete(){this.onDelete.emit()}handleArchive(){this.onArchive.emit()}handleDowload(){this.onDowload.emit()}}return _=g,(0,o.A)(g,"\u0275fac",function(f){return new(f||_)}),(0,o.A)(g,"\u0275cmp",t.VBU({type:_,selectors:[["app-inno-table-action"]],outputs:{onEdit:"onEdit",onResume:"onResume",onDelete:"onDelete",onArchive:"onArchive",onDowload:"onDowload"},standalone:!0,features:[t.aNF],decls:6,vars:4,consts:[["contentPopover",""],[1,"flex","gap-2","items-center"],["matTooltip","Resume",1,"button-icon"],[1,"button-icon"],["matTooltip","Resume",1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_play.svg","alt","Icon",1,"w-[20px]"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_download.svg","alt","Icon",1,"w-[20px]"],[3,"content"],["target","",1,"button-icon"],["src","../../../assets/img/icon/ic_three_dots_verticel.svg","alt","Icon",1,"w-[20px]"],[1,"flex","w-[78px]","flex-col"],[1,"w-full","h-[32px]","text-text-sm-regular","hover:bg-bg-secondary",3,"click"],[1,"w-full","h-[32px]","text-text-sm-regular","text-text-danger","hover:bg-bg-secondary",3,"click"],["src","../../../assets/img/icon/ic_archive.svg","alt","Icon",1,"w-[20px]"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"]],template:function(f,m){1&f&&(t.j41(0,"div",1),t.DNE(1,v,2,0,"button",2)(2,C,2,0,"button",3)(3,a,2,0,"button",3)(4,r,5,1)(5,b,2,2),t.k0s()),2&f&&(t.R7$(),t.vxM(m.onResume.observed?1:-1),t.R7$(),t.vxM(m.onEdit.observed?2:-1),t.R7$(),t.vxM(m.onDowload.observed?3:-1),t.R7$(),t.vxM(m.onArchive.observed&&m.onDelete.observed?4:5))},dependencies:[T.G,h.D9,I.oV,c.x]})),g})()},1556:(U,P,n)=>{n.d(P,{Z:()=>C});var o=n(9842),t=n(4438),c=n(467),T=n(2716),I=n(7987);let h=(()=>{var a;class d extends T.H{open(x){var D=this;return(0,c.A)(function*(){const b=yield n.e(3190).then(n.bind(n,3190));return D.matDialog.open(b.AlertConfirmComponent.getComponent(),{data:x,width:"440px",panelClass:"custom_dialog",scrollStrategy:new I.t0,disableClose:!0})})()}}return a=d,(0,o.A)(d,"\u0275fac",(()=>{let r;return function(D){return(r||(r=t.xGo(a)))(D||a)}})()),(0,o.A)(d,"\u0275prov",t.jDH({token:a,factory:a.\u0275fac,providedIn:"root"})),d})(),C=(()=>{var a;class d{constructor(x){(0,o.A)(this,"alertConfirmDialog",void 0),this.alertConfirmDialog=x}alertDelete(x){const{title:D,description:b,textSubmit:e="COMMON.Delete",textCancel:_}=x;return new Promise(g=>{this.alertConfirmDialog.open({title:D,description:b,textSubmit:e,textCancel:_,classNameSubmitButton:"bg-object-danger-primary hover:bg-bg-danger-strong-hover"}).then(f=>{f.afterClosed().subscribe(m=>{g(m??!1)})})})}alertConfirm(x){const{title:D,description:b,textSubmit:e,textCancel:_}=x;return new Promise(g=>{this.alertConfirmDialog.open({title:D,description:b,textSubmit:e,textCancel:_}).then(f=>{f.afterClosed().subscribe(m=>{g(m??!1)})})})}}return a=d,(0,o.A)(d,"\u0275fac",function(x){return new(x||a)(t.KVO(h))}),(0,o.A)(d,"\u0275prov",t.jDH({token:a,factory:a.\u0275fac})),d})()},5936:(U,P,n)=>{n.d(P,{H:()=>h});var o=n(9842),t=n(1626),c=n(4438);const I=n(5312).c.HOST_API+"/api";let h=(()=>{var v;class C{constructor(){(0,o.A)(this,"http",(0,c.WQX)(t.Qq))}GetFile(d){return this.http.get(I+`/Images/GetFile?nameFile=${d}`,{responseType:"blob"})}GetFileURL(d){return this.http.get(I+`/Images/GetFileURL?nameFile=${d}`,{responseType:"blob"})}}return v=C,(0,o.A)(C,"\u0275fac",function(d){return new(d||v)}),(0,o.A)(C,"\u0275prov",c.jDH({token:v,factory:v.\u0275fac,providedIn:"root"})),C})()},1456:(U,P,n)=>{n.d(P,{D:()=>v});var o=n(9842),t=n(5312),c=n(1626),T=n(4438),I=n(6473);const h=t.c.HOST_API+"/api";let v=(()=>{var C;class a{constructor(){(0,o.A)(this,"http",(0,T.WQX)(c.Qq))}CreateExpenses(r){return this.http.post(h+"/Expenses/CreateExpenses",r)}UpdateExpenses(r){return this.http.post(h+"/Expenses/UpdateExpenses",r)}GetExpensesById(r){return this.http.get(h+`/Expenses/GetExpensesById?Id=${r}`)}GetAllExpenses(r){const x=(0,I.yU)(r);return this.http.get(h+"/Expenses/GetAllExpenses",{params:x})}GetAllUploadExpenses(r){const x=(0,I.yU)(r);return this.http.get(h+"/Expenses/GetAllUploadExpenses",{params:x})}DeleteFileExpenses(r){return this.http.post(h+"/Expenses/DeleteExpenses",r)}DeleteExpenses(r){return this.http.post(h+"/Expenses/DeleteExpenses",r)}MarkAsPaid(r){return this.http.put(h+`/Expenses/MarkAsPaid?Id=${r}`,null)}GetExpenseItemsByExpenseIds(r){return this.http.post(h+"/Expenses/GetExpenseItemsByExpenseIds",r)}}return C=a,(0,o.A)(a,"\u0275fac",function(r){return new(r||C)}),(0,o.A)(a,"\u0275prov",T.jDH({token:C,factory:C.\u0275fac,providedIn:"root"})),a})()},1763:(U,P,n)=>{n.r(P),n.d(P,{ExpensesUploadComponent:()=>W});var o=n(9842),t=n(9424),c=n(1556),T=n(8556),I=n(6146),h=n(1413),v=n(274),C=n(152),a=n(1448),d=n(177),r=n(9115),x=n(5900),D=n(9079),b=n(3705),e=n(4438),_=n(5236),g=n(33),u=n(1110),f=n(1456),m=n(5936),O=n(9417);const R=["grid"],S=()=>({standalone:!0});function B(E,A){1&E&&(e.j41(0,"div",5),e.nrm(1,"app-inno-spin",6),e.k0s())}function j(E,A){if(1&E&&(e.j41(0,"option",11),e.EFF(1),e.k0s()),2&E){const l=A.$implicit;e.Y8G("value",l.id),e.R7$(),e.SpI(" ",l.expensesName," ")}}function y(E,A){if(1&E&&(e.j41(0,"div",19),e.nrm(1,"img",20),e.j41(2,"p",21),e.EFF(3),e.k0s()()),2&E){const l=A.$implicit,i=e.XpG(2);e.R7$(),e.Y8G("src",i.storeService.getIconFile(l.type),e.B4B),e.R7$(2),e.SpI(" ",l.filename,"")}}function L(E,A){if(1&E&&(e.j41(0,"p",22),e.EFF(1),e.nI1(2,"size"),e.k0s()),2&E){const l=A.$implicit;e.R7$(),e.SpI(" ",e.bMT(2,1,l.size),"")}}function G(E,A){if(1&E&&(e.j41(0,"p",22),e.EFF(1),e.k0s()),2&E){const l=A.$implicit;e.R7$(),e.SpI(" ",l.type,"")}}function F(E,A){if(1&E){const l=e.RV6();e.j41(0,"app-inno-table-action",23),e.bIt("onDelete",function(){const s=e.eBV(l).$implicit,p=e.XpG(2);return e.Njj(p.handleDelete(s))})("onDowload",function(){const s=e.eBV(l).$implicit,p=e.XpG(2);return e.Njj(p.handleDowload(s))})("onArchive",function(){const s=e.eBV(l).$implicit,p=e.XpG(2);return e.Njj(p.handleArchive(s))}),e.k0s()}}function K(E,A){if(1&E){const l=e.RV6();e.j41(0,"div",7)(1,"div",8)(2,"app-inno-input-search",9),e.bIt("onChange",function(s){e.eBV(l);const p=e.XpG();return e.Njj(p.handleSearch(s))}),e.k0s()(),e.j41(3,"select",10),e.mxI("ngModelChange",function(s){e.eBV(l);const p=e.XpG();return e.DH7(p.expensesId,s)||(p.expensesId=s),e.Njj(s)}),e.bIt("change",function(s){e.eBV(l);const p=e.XpG();return e.Njj(p.handleSelectExpenses(s))}),e.Z7z(4,j,2,2,"option",11,e.fX1),e.k0s()(),e.j41(6,"div",12)(7,"ejs-grid",13,0),e.bIt("actionBegin",function(s){e.eBV(l);const p=e.XpG();return e.Njj(p.onActionBegin(s))}),e.j41(9,"e-columns")(10,"e-column",14),e.nI1(11,"translate"),e.DNE(12,y,4,2,"ng-template",null,1,e.C5r),e.k0s(),e.j41(14,"e-column",15),e.nI1(15,"translate"),e.DNE(16,L,3,3,"ng-template",null,1,e.C5r),e.k0s(),e.j41(18,"e-column",16),e.nI1(19,"translate"),e.DNE(20,G,2,1,"ng-template",null,1,e.C5r),e.k0s(),e.j41(22,"e-column",17),e.nI1(23,"translate"),e.DNE(24,F,1,0,"ng-template",null,1,e.C5r),e.k0s()()(),e.j41(26,"ejs-pager",18),e.bIt("click",function(s){e.eBV(l);const p=e.XpG();return e.Njj(p.onPageChange(s))}),e.k0s()()}if(2&E){const l=e.XpG();e.R7$(2),e.Y8G("value",l.search),e.R7$(),e.R50("ngModel",l.expensesId),e.Y8G("ngModelOptions",e.lJ4(24,S)),e.R7$(),e.Dyx(l.listExpenses),e.R7$(3),e.Y8G("dataSource",l.dataSource)("allowSorting",!0)("sortSettings",l.sortOptions)("allowSelection",!0)("selectionSettings",l.selectionOptions),e.R7$(3),e.Y8G("headerText",e.bMT(11,16,"FILEUPLOAD.FileName")),e.R7$(4),e.Y8G("headerText",e.bMT(15,18,"FILEUPLOAD.Size")),e.R7$(4),e.Y8G("headerText",e.bMT(19,20,"FILEUPLOAD.Type")),e.R7$(4),e.Y8G("headerText",e.bMT(23,22,"FILEUPLOAD.Action")),e.R7$(4),e.Y8G("pageSize",l.pageSizesDefault)("totalRecordsCount",l.totalPages)("currentPage",l.currentPage)("pageSizes",l.pageSizes)}}let W=(()=>{var E;class A{constructor(i,s,p,M,N,$,w,z){(0,o.A)(this,"translate",void 0),(0,o.A)(this,"layoutUtilsService",void 0),(0,o.A)(this,"router",void 0),(0,o.A)(this,"destroyRef",void 0),(0,o.A)(this,"activatedRoute",void 0),(0,o.A)(this,"storeService",void 0),(0,o.A)(this,"expensesService",void 0),(0,o.A)(this,"cdnService",void 0),(0,o.A)(this,"isLoading",!1),(0,o.A)(this,"_subscriptions",[]),(0,o.A)(this,"search",""),(0,o.A)(this,"searchSubject",new h.B),(0,o.A)(this,"sort",void 0),(0,o.A)(this,"sortOptions",{columns:[]}),(0,o.A)(this,"selectionOptions",void 0),(0,o.A)(this,"expensesId",""),(0,o.A)(this,"listExpenses",[]),(0,o.A)(this,"dataSource",void 0),(0,o.A)(this,"totalPages",1),(0,o.A)(this,"currentPage",1),(0,o.A)(this,"pageSizes",[10,20,50,100]),(0,o.A)(this,"pageSizesDefault",10),(0,o.A)(this,"columnName",void 0),(0,o.A)(this,"direction",void 0),(0,o.A)(this,"grid",void 0),this.translate=i,this.layoutUtilsService=s,this.router=p,this.destroyRef=M,this.activatedRoute=N,this.storeService=$,this.expensesService=w,this.cdnService=z}onPageChange(i){i?.newProp?.pageSize&&(this.pageSizesDefault=i.newProp.pageSize,this.LoadUpload()),i?.currentPage&&this.router.navigate([],{relativeTo:this.activatedRoute,queryParams:{page:i.currentPage},queryParamsHandling:"merge"})}LoadUpload(){this.isLoading=!0;let i={Page:this.currentPage,PageSize:this.pageSizesDefault,Search:this.search,ExpensesId:this.expensesId,...this.sort};this.expensesId&&this.expensesService.GetAllUploadExpenses(i).pipe((0,D.pQ)(this.destroyRef)).subscribe({next:s=>{console.log("res",s.data),s&&(this.isLoading=!1,this.totalPages=s.totalRecords,this.dataSource=s.data,this.columnName&&(this.sortOptions={columns:[{field:this.columnName,direction:this.direction}]}))}})}GetData(){this.expensesService.GetAllExpenses({Page:0,PageSize:100,Search:""}).pipe((0,D.pQ)(this.destroyRef),(0,v.H)(s=>s&&(this.listExpenses=s.data,this.listExpenses.length>0&&(this.expensesId=this.listExpenses[0].id),this.expensesId)?this.expensesService.GetAllUploadExpenses({Page:this.currentPage,PageSize:10,Search:"",ExpensesId:this.expensesId}).pipe((0,D.pQ)(this.destroyRef)):[])).subscribe(s=>{s&&(this.totalPages=s.totalPage,this.dataSource=s.data)},s=>{console.error("Error:",s)})}handleSelectExpenses(i){this.expensesId=i.target.value,this.LoadUpload()}handleSearch(i){this.searchSubject.next(i)}ngOnInit(){const i=this.searchSubject.pipe((0,C.B)(550)).subscribe(s=>{this.search=s??"",this.LoadUpload()});this._subscriptions.push(i),this.activatedRoute.queryParams.pipe((0,D.pQ)(this.destroyRef)).subscribe(s=>{s?.page?(this.currentPage=s.page,this.LoadUpload()):this.GetData()})}creaFormDelete(){}handleDelete(i){const s=this.translate.instant("Delete File !"),p=this.translate.instant("Do you want to delete?");this.layoutUtilsService.alertDelete({title:s,description:p}).then(M=>{})}handleDowload(i){this.cdnService.GetFile(i.filename).pipe((0,D.pQ)(this.destroyRef)).subscribe(s=>{if(s){const p=new Blob([s],{type:i.type}),M=document.createElement("a");M.href=URL.createObjectURL(p),M.download=i.filename,document.body.appendChild(M),M.click(),document.body.removeChild(M)}})}handleArchive(i){}onActionBegin(i){if("sorting"===i.requestType){if(this.columnName=i.columnName,this.direction=i.direction,this.sort={columnName:i.columnName,direction:i.direction},this.columnName)return void this.LoadUpload();this.sortOptions={columns:[]},this.sort=null,this.LoadUpload()}}ngOnDestroy(){this._subscriptions&&this._subscriptions.forEach(i=>i.unsubscribe())}}return E=A,(0,o.A)(A,"\u0275fac",function(i){return new(i||E)(e.rXU(_.c$),e.rXU(c.Z),e.rXU(g.Ix),e.rXU(e.abz),e.rXU(g.nX),e.rXU(u.n),e.rXU(f.D),e.rXU(m.H))}),(0,o.A)(A,"\u0275cmp",e.VBU({type:E,selectors:[["app-expenses-upload"]],viewQuery:function(i,s){if(1&i&&e.GBs(R,5),2&i){let p;e.mGM(p=e.lsd())&&(s.grid=p.first)}},standalone:!0,features:[e.Jv_([c.Z]),e.aNF],decls:7,vars:4,consts:[["grid",""],["template",""],[1,"w-full","py-[24px]","border-b","border-border-primary"],[1,"container-full","flex","justify-between","items-center","flex-wrap","gap-2"],[1,"text-text-primary","text-headline-lg-bold"],[1,"container-full","h-[60dvh]","flex","justify-center","items-center"],["size","lg"],[1,"container-full","mt-[24px]","flex","flex-wrap","gap-[12px]","items-center"],[1,"w-full","max-w-[300px]"],[3,"onChange","value"],["id","project",1,"dropdown-md","min-w-52",3,"ngModelChange","change","ngModel","ngModelOptions"],[3,"value"],[1,"w-full","mt-[12px]"],[1,"customTable",3,"actionBegin","dataSource","allowSorting","sortSettings","allowSelection","selectionSettings"],["width","200","field","filename",3,"headerText"],["width","200","field","size",3,"headerText"],["width","200","field","type",3,"headerText"],["headerText","","width","100",3,"headerText"],[3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"flex","items-center"],[1,"w-4","mr-2",3,"src"],[1,"text-text-md-regular","text-text-primary","line-clamp-1"],[1,"text-text-md-regular","text-text-primary"],[3,"onDelete","onDowload","onArchive"]],template:function(i,s){1&i&&(e.j41(0,"div",2)(1,"div",3)(2,"p",4),e.EFF(3),e.nI1(4,"translate"),e.k0s()()(),e.DNE(5,B,2,0,"div",5)(6,K,27,25)),2&i&&(e.R7$(3),e.SpI(" ",e.bMT(4,2,"FILEUPLOAD.Title")," "),e.R7$(2),e.vxM(s.isLoading?5:6))},dependencies:[d.MD,a.iov,a.BzB,r.Cn,a.gFV,a._ab,a.eeu,a.rFS,a.LGG,a.cvh,I.G,O.xH,O.y7,O.wz,O.BC,O.vS,_.D9,x.M,t.f,T.K,b.M]})),A})()}}]);