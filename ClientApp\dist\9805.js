"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[9805],{7656:(T,E,a)=>{a.d(E,{V:()=>D});var r=a(9842),s=a(4438),v=a(6146),e=a(9417);const I=["*"];function S(x,u){if(1&x){const h=s.RV6();s.j41(0,"input",5),s.bIt("change",function(C){s.eBV(h);const f=s.XpG();return s.Njj(f.handleChange(C))}),s.k0s()}if(2&x){const h=s.XpG();s.Y8G("checked",h.checked)("formControl",h.formControl)}}function P(x,u){if(1&x){const h=s.RV6();s.j41(0,"input",6),s.bIt("change",function(C){s.eBV(h);const f=s.XpG();return s.Njj(f.handleChange(C))}),s.k0s()}if(2&x){const h=s.XpG();s.Y8G("checked",h.checked)}}let D=(()=>{var x;class u{constructor(){(0,r.A)(this,"checked",void 0),(0,r.A)(this,"onChange",new s.bkB),(0,r.A)(this,"formControl",void 0),(0,r.A)(this,"errorMessages",void 0)}registerOnChange(m){}registerOnTouched(m){}setDisabledState(m){}writeValue(m){}handleChange(m){this.onChange.emit(m?.target?.checked??!1)}}return x=u,(0,r.A)(u,"\u0275fac",function(m){return new(m||x)}),(0,r.A)(u,"\u0275cmp",s.VBU({type:x,selectors:[["app-inno-form-checkbox"]],inputs:{checked:"checked",formControl:"formControl",errorMessages:"errorMessages"},outputs:{onChange:"onChange"},standalone:!0,features:[s.Jv_([{provide:e.kq,useExisting:(0,s.Rfq)(()=>x),multi:!0}]),s.aNF],ngContentSelectors:I,decls:6,vars:1,consts:[[1,"flex"],[1,"flex","gap-[8px]","cursor-pointer"],["type","checkbox",1,"customCheckboxHTML",3,"checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"checked"],[1,"text-text-sm-regular","text-text-primary"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked","formControl"],["type","checkbox",1,"customCheckboxHTML",3,"change","checked"]],template:function(m,C){1&m&&(s.NAR(),s.j41(0,"div",0)(1,"label",1),s.DNE(2,S,1,2,"input",2)(3,P,1,1,"input",3),s.j41(4,"div",4),s.SdG(5),s.k0s()()()),2&m&&(s.R7$(2),s.vxM(C.formControl?2:3))},dependencies:[v.G,e.Zm,e.BC,e.l_],styles:['@charset "UTF-8";.customCheckboxHTML[_ngcontent-%COMP%]{transform:translateY(1px);width:16px;height:16px;-webkit-appearance:none;appearance:none;border:1px solid;cursor:pointer;position:relative;flex-shrink:0;border-radius:4px;background-color:var(--object-white);border-color:var(--border-secondary)}.customCheckboxHTML[_ngcontent-%COMP%]:checked{background-color:var(--object-brand-primary);border-color:var(--object-brand-primary)}.customCheckboxHTML[_ngcontent-%COMP%]:before{content:"\\2713";position:absolute;font-weight:700;font-size:10px;top:50%;left:50%;transform:translate(-50%,-50%) scale(0);transition:all .3s;color:var(--border-white)}.customCheckboxHTML[_ngcontent-%COMP%]:checked:before{transform:translate(-50%,-50%) scale(1);transition:all .3s}']})),u})()},9805:(T,E,a)=>{a.r(E),a.d(E,{SelectExpensesComponent:()=>K});var r=a(9842),s=a(4433),v=a(1110),e=a(4438),I=a(4006),S=a(3200),P=a(7656),D=a(344),x=a(4978),u=a(9424),h=a(6146),m=a(6617),C=a(4823),f=a(1970),j=a(9079),b=a(2840),k=a(1448),M=a(1456),U=a(5909),A=a(177);const B=l=>({"font-bold text-black":l});function O(l,c){1&l&&(e.j41(0,"div",4),e.nrm(1,"app-inno-spin"),e.k0s())}function F(l,c){1&l&&(e.j41(0,"div",3),e.nrm(1,"app-inno-empty-data",7),e.k0s())}function R(l,c){if(1&l&&(e.j41(0,"span",17),e.EFF(1),e.k0s()),2&l){const t=e.XpG(3);e.R7$(),e.SpI(" ","Ascending"===t.sortDirection?"arrow_upward":"arrow_downward"," ")}}function N(l,c){if(1&l&&e.EFF(0),2&l){const t=e.XpG().$implicit;e.SpI(" [",null==t?null:t.projectName,"] ")}}function y(l,c){if(1&l&&(e.j41(0,"div",23),e.nrm(1,"ngx-avatars",26),e.j41(2,"span",27),e.EFF(3),e.k0s()()),2&l){const t=e.XpG().$implicit,o=e.XpG(3);e.R7$(),e.FCK("matTooltip","",null==t.inforUser?null:t.inforUser.firstName," ",null==t.inforUser?null:t.inforUser.lastName," "),e.FS9("bgColor",o._storeService.getBgColor(null==t.inforUser?null:t.inforUser.firstName.slice(0,1))),e.Y8G("size",30)("name",(null==t.inforUser?null:t.inforUser.firstName.charAt(0))+" "+(null!=t.inforUser&&t.inforUser.lastName?null==t.inforUser?null:t.inforUser.lastName.charAt(0):"")),e.R7$(2),e.Lme(" ",null==t.inforUser?null:t.inforUser.firstName," ",null==t.inforUser?null:t.inforUser.lastName,"")}}function G(l,c){if(1&l&&(e.j41(0,"div",23),e.nrm(1,"ngx-avatars",26),e.j41(2,"span",27),e.EFF(3),e.k0s()()),2&l){const t=e.XpG().$implicit,o=e.XpG(3);e.R7$(),e.FS9("matTooltip",null==t.inforUser?null:t.inforUser.email),e.FS9("bgColor",o._storeService.getBgColor(null==t.inforUser?null:t.inforUser.email.slice(0,1))),e.Y8G("size",30)("name",null==t.inforUser?null:t.inforUser.email.slice(0,1)),e.R7$(2),e.SpI(" ",null==t.inforUser?null:t.inforUser.email,"")}}function L(l,c){if(1&l){const t=e.RV6();e.j41(0,"div",9)(1,"div",10)(2,"div",11)(3,"app-inno-form-checkbox",12),e.bIt("onChange",function(){const n=e.eBV(t).$index,i=e.XpG(3);return e.Njj(i.handleToggleCheckedIndex(n))}),e.k0s()(),e.j41(4,"p",20),e.EFF(5),e.k0s()(),e.j41(6,"p",22),e.DNE(7,N,1,1),e.EFF(8),e.k0s(),e.j41(9,"p",22),e.DNE(10,y,4,8,"div",23)(11,G,4,5,"div",23),e.k0s(),e.j41(12,"p",24),e.EFF(13),e.nI1(14,"decimal"),e.nI1(15,"formatNumber"),e.k0s(),e.j41(16,"p",25),e.EFF(17),e.nI1(18,"date"),e.k0s()()}if(2&l){let t,o;const n=c.$implicit,i=c.$index,_=e.XpG(3);e.R7$(3),e.Y8G("checked",_.isCheckedIndex(i)),e.R7$(2),e.SpI(" ",null!==(t=n.expensesName)&&void 0!==t?t:""," "),e.R7$(2),e.vxM(null!=n&&n.projectName?7:-1),e.R7$(),e.SpI(" ",null!==(o=null==n?null:n.clientName)&&void 0!==o?o:""," "),e.R7$(2),e.vxM(null!=n.inforUser&&n.inforUser.firstName?10:11),e.R7$(3),e.SpI(" $",e.bMT(15,10,e.i5U(14,7,n.total,2))," "),e.R7$(4),e.SpI(" ",e.i5U(18,12,n.date,_._storeService.getdateFormat())," ")}}function $(l,c){if(1&l){const t=e.RV6();e.j41(0,"div",8)(1,"div",9)(2,"div",10)(3,"div",11)(4,"app-inno-form-checkbox",12),e.bIt("onChange",function(n){e.eBV(t);const i=e.XpG(2);return e.Njj(i.handleCheckedAll(n))}),e.k0s()(),e.j41(5,"p",13),e.EFF(6," Expenses Name "),e.k0s()(),e.j41(7,"p",14),e.EFF(8," Project/Client "),e.k0s(),e.j41(9,"p",14),e.EFF(10," User "),e.k0s(),e.j41(11,"p",15),e.EFF(12," Line Total "),e.k0s(),e.j41(13,"p",16),e.bIt("click",function(){e.eBV(t);const n=e.XpG(2);return e.Njj(n.sortDates("date"))}),e.EFF(14," Date "),e.DNE(15,R,2,1,"span",17),e.k0s()(),e.Z7z(16,L,19,15,"div",9,e.fX1),e.k0s(),e.j41(18,"ejs-pager",18),e.bIt("click",function(n){e.eBV(t);const i=e.XpG(2);return e.Njj(i.onPageChange(n))}),e.k0s(),e.j41(19,"div",19)(20,"p",20),e.EFF(21),e.nI1(22,"async"),e.k0s(),e.j41(23,"p",21),e.EFF(24),e.nI1(25,"decimal"),e.nI1(26,"formatNumber"),e.k0s()()}if(2&l){const t=e.XpG(2);e.R7$(4),e.Y8G("checked",t.listIndexInvoiceSelected.length===t.listExpenses.length),e.R7$(9),e.Y8G("ngClass",e.eq3(16,B,"date"===t.sortColumn)),e.R7$(2),e.vxM("date"==t.sortColumn?15:-1),e.R7$(),e.Dyx(t.listExpenses),e.R7$(2),e.Y8G("pageSize",t.pageSizesDefault)("totalRecordsCount",t.totalPages)("currentPage",t.currentPage)("pageSizes",t.pageSizes),e.R7$(3),e.SpI(" Amount Due (",e.bMT(22,9,t._storeService.curencyCompany),") "),e.R7$(3),e.SpI(" $",e.bMT(26,14,e.i5U(25,11,t.totalAmount(),2))," ")}}function W(l,c){if(1&l&&e.DNE(0,F,2,0,"div",3)(1,$,27,18),2&l){const t=e.XpG();e.vxM(0==t.listExpenses.length?0:1)}}b.is5.Inject(b.Rav);let K=(()=>{var l;class c{static getComponent(){return c}constructor(o,n){(0,r.A)(this,"dialogRef",void 0),(0,r.A)(this,"data",void 0),(0,r.A)(this,"sort",void 0),(0,r.A)(this,"today",new Date),(0,r.A)(this,"totalPages",1),(0,r.A)(this,"currentPage",1),(0,r.A)(this,"pageSizes",[10,20,50,100]),(0,r.A)(this,"pageSizesDefault",20),(0,r.A)(this,"title",""),(0,r.A)(this,"clientName",""),(0,r.A)(this,"projectName",""),(0,r.A)(this,"listExpenses",[]),(0,r.A)(this,"listIndexInvoiceSelected",[]),(0,r.A)(this,"isFetchingProject",!1),(0,r.A)(this,"listProjectId",[]),(0,r.A)(this,"sortDirection","Ascending"),(0,r.A)(this,"sortColumn",""),(0,r.A)(this,"destroyRef",(0,e.WQX)(e.abz)),(0,r.A)(this,"expensesService",(0,e.WQX)(M.D)),(0,r.A)(this,"_storeService",(0,e.WQX)(v.n)),this.dialogRef=o,this.data=n,this.clientName=n?.client?.clientName??"",this.projectName=n?.project?.projectName??"",this.title=`Select expenses ${this.clientName}${""!=this.projectName?" / "+this.projectName:""}`}ngOnDestroy(){this.listProjectId=[]}handleClose(){this.dialogRef.close()}handleSort(o){const n=this.sort,i=this.currentPage;switch(o){case"Descending":case"Ascending":this.GetAllExpensesUnBillTime({page:i,textSearch:"",isGetAll:false,sort:n})}}sortDates(o){this.sortColumn===o?(this.sortDirection="Ascending"===this.sortDirection?"Descending":"Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)):(this.sortColumn=o,this.sortDirection="Ascending",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection))}GetAllExpensesUnBillTime(o){const n=this.data?.client?.id,i=this.data?.project;if(!n)return;i&&this.listProjectId.push(i.id);const _={Page:o?.page??1,Search:o?.textSearch??"",PageSize:this.pageSizesDefault,clientId:n,isGetAll:o.isGetAll,...o.sort,status:1,projectIds:this.listProjectId};this.isFetchingProject=!0,this.expensesService.GetAllExpenses(_).pipe((0,j.pQ)(this.destroyRef)).subscribe(p=>{if(p){this.totalPages=p.totalRecords;const g=this.data?.listIdTimeTrackingSelected??[];p=p?.data?p?.data?.filter(d=>!g.includes(d.id))??[]:p?.filter(d=>!g.includes(d.id))??[],this.listExpenses=p.map(d=>({expensesId:d?.id??"",date:d?.date??this.today,description:d?.note??"",total:d?.paidAmount,dateSelectItem:d.date,clientName:d?.clientName,expensesName:d?.expensesName,inforUser:d?.inforUser,projectName:d?.projectName,taxes:[]})),this.isFetchingProject=!1,o.isGetAll&&(this.pageSizesDefault=this.totalPages,this.listIndexInvoiceSelected=this.listExpenses.map((d,X)=>X))}})}onPageChange(o){if(o?.newProp?.pageSize){this.pageSizesDefault=o.newProp.pageSize;const n=this.currentPage;this.sort?this.handleSort(this.sortDirection):this.GetAllExpensesUnBillTime({page:n,textSearch:"",isGetAll:!1})}if(o?.currentPage){this.currentPage=o.currentPage;const n=this.currentPage;this.sort?this.handleSort(this.sortDirection):this.GetAllExpensesUnBillTime({page:n,textSearch:"",isGetAll:!1})}}ngOnInit(){this.sortDirection="Ascending",this.sortColumn="date",this.sort={columnName:this.sortColumn,direction:this.sortDirection},this.handleSort(this.sortDirection)}handleCheckedAll(o){o?this.GetAllExpensesUnBillTime({page:this.currentPage,textSearch:"",isGetAll:!0,sort:this.sort}):this.listIndexInvoiceSelected=[]}isCheckedIndex(o){return this.listIndexInvoiceSelected.includes(o)}handleToggleCheckedIndex(o){const n=this.isCheckedIndex(o);let i=[...this.listIndexInvoiceSelected];n?i=i.filter(_=>_!==o):i.push(o),this.listIndexInvoiceSelected=i}totalAmount(){const o=this.listIndexInvoiceSelected.map(i=>this.listExpenses[i]),n=(0,U.az)(o);return n?.subtotal+n?.grandTotalTax}handleCancel(){this.dialogRef.close()}handleSubmit(){const o=this.listIndexInvoiceSelected.map(n=>this.listExpenses[n]);this.dialogRef.close(o)}calculateTotalInvoiceItem(o,n){const i=o.split(":").map(Number);let _=0,p=0,g=0;return 3===i.length?[_,p,g]=i:2===i.length?[_,p]=i:1===i.length&&([_]=i),(_+p/60+g/3600)*n}}return l=c,(0,r.A)(c,"\u0275fac",function(o){return new(o||l)(e.rXU(I.CP),e.rXU(I.Vh))}),(0,r.A)(c,"\u0275cmp",e.VBU({type:l,selectors:[["app-select-expenses"]],standalone:!0,features:[e.aNF],decls:8,vars:3,consts:[[3,"onClose","title"],[1,"w-full","p-[16px]"],[1,"w-full","flex","flex-col","relative"],[1,"w-full"],[1,"w-full","py-2","flex","justify-center","items-center"],["footer",""],[3,"onCancel","onSubmit","isDisableSubmit"],["title","No result"],[1,"overflow-auto","w-full"],[1,"selectProjectTableLayout"],[1,"addBorderBottom","w-full","flex","gap-[8px]"],[1,"w-[16px]","shrink-0"],[3,"onChange","checked"],[1,"text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-center"],[1,"addBorderBottom","text-text-tertiary","text-text-sm-semibold","text-right","cursor-pointer",3,"click","ngClass"],[1,"material-icons","pl-1","!text-[15px]"],[1,"customTable",3,"click","pageSize","totalRecordsCount","currentPage","pageSizes"],[1,"w-full","flex","pt-[15px]","pb-[3px]","flex-wrap","justify-end","items-center","gap-[16px]"],[1,"text-text-primary","text-text-sm-regular"],[1,"text-headline-sm-bold","text-text-primary"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular"],[1,"flex","items-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-center"],[1,"addBorderBottom","text-text-primary","text-text-sm-regular","text-right"],[3,"matTooltip","size","bgColor","name"],[1,"pl-1"]],template:function(o,n){1&o&&(e.j41(0,"app-inno-modal-wrapper",0),e.bIt("onClose",function(){return n.handleClose()}),e.j41(1,"div",1)(2,"div",2)(3,"div",3),e.DNE(4,O,2,0,"div",4)(5,W,2,1),e.k0s()()(),e.j41(6,"div",5)(7,"app-inno-modal-footer",6),e.bIt("onCancel",function(){return n.handleCancel()})("onSubmit",function(){return n.handleSubmit()}),e.k0s()()()),2&o&&(e.FS9("title",n.title),e.R7$(4),e.vxM(n.isFetchingProject?4:5),e.R7$(3),e.Y8G("isDisableSubmit",!n.listIndexInvoiceSelected.length))},dependencies:[C.oV,f.mC,f.fw,h.G,A.YU,A.Jj,A.vh,k.iov,k.BzB,m.p,s.Q,x.I,D.k,P.V,u.f,S.J],styles:["p[_ngcontent-%COMP%]{margin-bottom:0}.selectProjectTableLayout[_ngcontent-%COMP%]{width:100%;display:grid;grid-template-columns:minmax(180px,1fr) 200px 200px 120px 120px}.addBorderBottom[_ngcontent-%COMP%]{border-bottom:1px solid;padding-top:8px;padding-bottom:8px;border-color:var(--border-primary)}.selectProjectTableLayout[_ngcontent-%COMP%]   .addBorderBottom[_ngcontent-%COMP%]:not(:last-child){padding-right:8px}"]})),c})()}}]);