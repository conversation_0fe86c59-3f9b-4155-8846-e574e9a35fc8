"use strict";(self.webpackChunkinnobook=self.webpackChunkinnobook||[]).push([[4263],{4263:(N,d,s)=>{s.r(d),s.d(d,{DetailCategoryComponent:()=>k});var r=s(9842),p=s(9097),C=s(6146),u=s(1342),t=s(4438),g=s(4006),y=s(1470),h=s(3492),c=s(9079),m=s(5236),v=s(177),_=s(9417);const f=(o,a)=>({"bg-green-700 hover:bg-green-800 text-white":o,"bg-gray-400":a});function b(o,a){if(1&o){const n=t.RV6();t.j41(0,"div",1)(1,"div",2)(2,"input",14),t.mxI("ngModelChange",function(e){t.eBV(n);const l=t.XpG();return t.DH7(l.categoryName,e)||(l.categoryName=e),t.Njj(e)}),t.k0s(),t.j41(3,"div",15),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.Edit())}),t.j41(4,"span",10),t.EFF(5," close "),t.k0s()()()()}if(2&o){const n=t.XpG();t.R7$(2),t.R50("ngModel",n.categoryName)}}function R(o,a){if(1&o){const n=t.RV6();t.j41(0,"span",16),t.EFF(1),t.k0s(),t.j41(2,"div",17)(3,"button",18),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.Edit())}),t.nrm(4,"img",19),t.k0s()()}if(2&o){const n=t.XpG();t.R7$(),t.SpI(" ",n.categoryName,"")}}function E(o,a){if(1&o){const n=t.RV6();t.j41(0,"div",24)(1,"button",18),t.bIt("click",function(){t.eBV(n);const e=t.XpG(),l=e.$implicit,S=e.$index,j=t.XpG(2);return t.Njj(j.RemoveItem(l.id,S))}),t.nrm(2,"img",25),t.k0s()()}}function I(o,a){if(1&o&&(t.j41(0,"div",21)(1,"div",22)(2,"div",23),t.EFF(3),t.k0s()(),t.DNE(4,E,3,0,"div",24),t.k0s()),2&o){const n=a.$implicit;t.R7$(3),t.SpI(" ",n.itemName," "),t.R7$(),t.vxM(n.canEdit?4:-1)}}function D(o,a){if(1&o&&(t.j41(0,"h6",20),t.EFF(1),t.nI1(2,"translate"),t.k0s(),t.Z7z(3,I,5,2,"div",21,t.fX1)),2&o){const n=t.XpG();t.R7$(),t.JRh(t.bMT(2,1,"CATEGORY.ListCategory")),t.R7$(2),t.Dyx(n.listCategoryItem)}}function T(o,a){1&o&&(t.j41(0,"span",10),t.EFF(1," remove "),t.k0s(),t.j41(2,"span",26),t.EFF(3),t.nI1(4,"translate"),t.k0s()),2&o&&(t.R7$(3),t.JRh(t.bMT(4,1,"CATEGORY.RemoveAll")))}function M(o,a){if(1&o){const n=t.RV6();t.j41(0,"div",5)(1,"div",27),t.EFF(2),t.k0s(),t.j41(3,"div",24)(4,"button",18),t.bIt("click",function(){const e=t.eBV(n).$index,l=t.XpG();return t.Njj(l.RemoveService(e))}),t.nrm(5,"img",25),t.k0s()()()}if(2&o){const n=a.$implicit;t.R7$(2),t.SpI(" ",n.itemName," ")}}function x(o,a){if(1&o){const n=t.RV6();t.j41(0,"input",28),t.nI1(1,"translate"),t.mxI("ngModelChange",function(e){t.eBV(n);const l=t.XpG();return t.DH7(l.categoryNameItem,e)||(l.categoryNameItem=e),t.Njj(e)}),t.k0s()}if(2&o){const n=t.XpG();t.R50("ngModel",n.categoryNameItem),t.Y8G("placeholder",t.bMT(1,2,"CATEGORY.CategoryItem"))}}function A(o,a){if(1&o){const n=t.RV6();t.j41(0,"div",29),t.bIt("click",function(){t.eBV(n);const e=t.XpG();return t.Njj(e.CreatedCategory())}),t.j41(1,"span",10),t.EFF(2," add "),t.k0s(),t.EFF(3),t.nI1(4,"translate"),t.j41(5,"span"),t.EFF(6),t.k0s()()}if(2&o){const n=t.XpG();t.R7$(3),t.SpI(" ",t.bMT(4,2,"CATEGORY.CreateCategoryItem")," "),t.R7$(3),t.SpI('" ',n.categoryNameItem,' "')}}let k=(()=>{var o;class a{static getComponent(){return a}constructor(i,e){(0,r.A)(this,"dialogRef",void 0),(0,r.A)(this,"data",void 0),(0,r.A)(this,"_spinnerService",(0,t.WQX)(u.D)),(0,r.A)(this,"destroyRef",(0,t.WQX)(t.abz)),(0,r.A)(this,"_toastService",(0,t.WQX)(h.f)),(0,r.A)(this,"_categoryService",(0,t.WQX)(p.M)),(0,r.A)(this,"translate",(0,t.WQX)(m.c$)),(0,r.A)(this,"isAddSvcMode",!0),(0,r.A)(this,"isEdit",!1),(0,r.A)(this,"listCategory",[]),(0,r.A)(this,"number",void 0),(0,r.A)(this,"listCategoryItem",[]),(0,r.A)(this,"listItemCategoryRemove",[]),(0,r.A)(this,"categoryName",void 0),(0,r.A)(this,"categoryNameItem",void 0),this.dialogRef=i,this.data=e}GetCategoryById(){this._categoryService.GetCategoryById(this.data).pipe((0,c.pQ)(this.destroyRef)).subscribe(i=>{i&&(this.listCategoryItem=i.categoryItems.slice(),this.categoryName=i.categoryName,this.number=i.number)})}ngOnInit(){this.data&&this.GetCategoryById()}RemoveItem(i,e){this.listCategoryItem.splice(e,1),this.listItemCategoryRemove.push(i)}RemoveAll(){this.listCategory=[]}RemoveService(i){this.listCategory.splice(i,1)}CreatedCategory(){this.isAddSvcMode=!1,this.listCategory.push({itemName:this.categoryNameItem,categoryId:this.data,canEdit:!0}),this.categoryNameItem=""}addCategory(){this.isAddSvcMode=!0}closeDialog(){this.dialogRef.close()}Edit(){this.isEdit=!this.isEdit}Save(){this.listCategory.length>0&&(this._spinnerService.show(),this._categoryService.CreateCategoryItem(this.listCategory).pipe((0,c.pQ)(this.destroyRef)).subscribe(i=>{i&&(this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this._spinnerService.hide(),this.dialogRef.close(i))})),this.listItemCategoryRemove.length>0&&(this._spinnerService.show(),this._categoryService.DeleteCategoryItem(this.listItemCategoryRemove).pipe((0,c.pQ)(this.destroyRef)).subscribe(i=>{i&&(this._toastService.showSuccess(this.translate.instant("TOAST.Save"),this.translate.instant("TOAST.Success")),this._spinnerService.hide(),this.dialogRef.close(i))})),this.isEdit&&this.categoryName!=this.data.categoryName&&(this._spinnerService.show(),this._categoryService.UpdateCategory({categoryName:this.categoryName,number:this.number,id:this.data}).pipe((0,c.pQ)(this.destroyRef)).subscribe(e=>{e&&(this._toastService.showSuccess(this.translate.instant("TOAST.Update"),this.translate.instant("TOAST.Success")),this._spinnerService.hide(),this.dialogRef.close(e))}))}}return o=a,(0,r.A)(a,"\u0275fac",function(i){return new(i||o)(t.rXU(g.CP),t.rXU(g.Vh))}),(0,r.A)(a,"\u0275cmp",t.VBU({type:o,selectors:[["app-detail-category"]],standalone:!0,features:[t.aNF],decls:27,vars:19,consts:[[3,"onClose"],[1,"relative","w-full","rounded-md","p-2"],[1,"flex","items-center","justify-center"],[1,"p-2"],[1,"flex","items-center","text-blue-500",3,"click"],[1,"flex","items-center"],[1,"mb-2"],["type","text",1,"bg-bg-primary","mt-2","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModel","placeholder"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500"],["type","button",1,"btn","btn-outline-secondary","btn-dashed",3,"click"],[1,"material-icons"],[1,"w-full","flex","justify-around","mt-3"],["type","button",1,"text-gray-900","bg-white","border","border-gray-300","focus:outline-none","hover:bg-gray-100","focus:ring-4","focus:ring-gray-100","font-medium","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click"],["type","button",1,"text-gray-900","focus:outline-none","hover:bg-gray-300","focus:ring-4","focus:ring-gray-100","font-medium","cursor-pointer","rounded-lg","text-sm","px-5","py-2.5","me-2","mb-2",3,"click","disabled","ngClass"],["type","text","placeholder","CategoryName",1,"bg-white","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","ngModel"],[1,"cursor-pointer",3,"click"],[1,"text-center","text-lg","font-bold"],[1,"cursor-pointer"],[1,"button-icon",3,"click"],["src","../../../assets/img/icon/ic_edit.svg","alt","Icon",1,"w-[20px]"],[1,"text-start","pl-3","mb-2","font-bold"],[1,"flex","items-center","cursor-pointer","pl-3","mb-2"],[1,"relative","w-full","border","rounded-md","p-2"],[1,"flex","items-center","w-full","justify-between"],[1,"text-end","cursor-pointer"],["src","../../../assets/img/icon/ic_trash.svg","alt","Icon",1,"w-[20px]"],[1,"ml-2","cursor-pointer"],[1,"relative","w-full","border","rounded-md","p-2","text-start"],["type","text",1,"bg-bg-primary","mt-2","border","border-gray-300","text-gray-900","text-sm","rounded-lg","focus:ring-blue-500","focus:border-blue-500","block","w-full","p-2.5",3,"ngModelChange","ngModel","placeholder"],[1,"bg-white","max-w-68","flex","items-center","p-2","cursor-pointer","hover:bg-gray-400","text-blue-500",3,"click"]],template:function(i,e){1&i&&(t.j41(0,"app-innobook-modal-wrapper",0),t.bIt("onClose",function(){return e.closeDialog()}),t.j41(1,"div",1)(2,"div",2),t.DNE(3,b,6,1,"div",1)(4,R,5,1),t.k0s()(),t.DNE(5,D,5,3),t.j41(6,"div",3)(7,"div",4),t.bIt("click",function(){return e.RemoveAll()}),t.DNE(8,T,5,3),t.k0s(),t.Z7z(9,M,6,1,"div",5,t.fX1),t.j41(11,"div")(12,"div",6),t.DNE(13,x,2,4,"input",7)(14,A,7,4,"div",8),t.k0s(),t.j41(15,"button",9),t.bIt("click",function(){return e.addCategory()}),t.j41(16,"span",10),t.EFF(17,"add"),t.k0s(),t.EFF(18),t.nI1(19,"translate"),t.k0s()(),t.j41(20,"div",11)(21,"button",12),t.bIt("click",function(){return e.closeDialog()}),t.EFF(22),t.nI1(23,"translate"),t.k0s(),t.j41(24,"button",13),t.bIt("click",function(){return e.Save()}),t.EFF(25),t.nI1(26,"translate"),t.k0s()()()()),2&i&&(t.R7$(3),t.vxM(e.isEdit?3:4),t.R7$(2),t.vxM(e.listCategoryItem.length>0?5:-1),t.R7$(3),t.vxM(e.listCategory.length>0?8:-1),t.R7$(),t.Dyx(e.listCategory),t.R7$(4),t.vxM(e.isAddSvcMode?13:-1),t.R7$(),t.vxM(e.categoryNameItem?14:-1),t.R7$(4),t.SpI(" ",t.bMT(19,10,"CATEGORY.AddNew")," "),t.R7$(4),t.JRh(t.bMT(23,12,"BUTTON.Cancel")),t.R7$(2),t.Y8G("disabled",0===e.listCategory.length&&0===e.listItemCategoryRemove.length&&!e.isEdit)("ngClass",t.l_i(16,f,e.listCategory.length>0||e.listItemCategoryRemove.length>0||e.isEdit,0==e.listCategory.length||0==e.listItemCategoryRemove.length||!e.isEdit)),t.R7$(),t.JRh(t.bMT(26,14,"BUTTON.Save")))},dependencies:[C.G,v.YU,_.me,_.BC,_.vS,m.D9,y.j]})),a})()}}]);